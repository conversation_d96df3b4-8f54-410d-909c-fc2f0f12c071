"use strict";exports.id=249,exports.ids=[249],exports.modules={2943:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},41312:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},62688:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:i="",children:a,iconNode:u,...d},h)=>(0,n.createElement)("svg",{ref:h,...c,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:o("lucide",i),...!a&&!l(d)&&{"aria-hidden":"true"},...d},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},l)=>(0,n.createElement)(u,{ref:l,iconNode:t,className:o(`lucide-${s(a(e))}`,`lucide-${e}`,r),...i}));return r.displayName=a(e),r}},84027:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95778:(e,t,r)=>{r.d(t,{dp:()=>en.dp});var n=r(49471),s=r(24636),i=r(16390);function a(e,t){if("undefined"==typeof Convex||void 0===Convex.syscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");return JSON.parse(Convex.syscall(e,JSON.stringify(t)))}async function o(e,t){let r;if("undefined"==typeof Convex||void 0===Convex.asyncSyscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");try{r=await Convex.asyncSyscall(e,JSON.stringify(t))}catch(e){if(void 0!==e.data){let t=new s.i(e.message);throw t.data=(0,i.du)(e.data),t}throw Error(e.message)}return JSON.parse(r)}r(23991);var l=Object.defineProperty,c=(e,t,r)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,u=(e,t,r)=>c(e,"symbol"!=typeof t?t+"":t,r);class d{constructor(){u(this,"_isExpression"),u(this,"_value")}}var h=Object.defineProperty,y=(e,t,r)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,m=(e,t,r)=>y(e,"symbol"!=typeof t?t+"":t,r);class p extends d{constructor(e){super(),m(this,"inner"),this.inner=e}serialize(){return this.inner}}function f(e){return e instanceof p?e.serialize():{$literal:(0,i.cy)(e)}}let v={eq(e,t){if("string"!=typeof e)throw Error("The first argument to `q.eq` must be a field name.");return new p({$eq:[f(new p({$field:e})),f(t)]})},or:(...e)=>new p({$or:e.map(f)})};var b=Object.defineProperty,g=(e,t,r)=>t in e?b(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,w=(e,t,r)=>g(e,"symbol"!=typeof t?t+"":t,r);class x{constructor(){w(this,"_isExpression"),w(this,"_value")}}var q=Object.defineProperty,E=(e,t,r)=>t in e?q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,C=(e,t,r)=>E(e,"symbol"!=typeof t?t+"":t,r);class $ extends x{constructor(e){super(),C(this,"inner"),this.inner=e}serialize(){return this.inner}}function k(e){return e instanceof $?e.serialize():{$literal:(0,i.cy)(e)}}let j={eq:(e,t)=>new $({$eq:[k(e),k(t)]}),neq:(e,t)=>new $({$neq:[k(e),k(t)]}),lt:(e,t)=>new $({$lt:[k(e),k(t)]}),lte:(e,t)=>new $({$lte:[k(e),k(t)]}),gt:(e,t)=>new $({$gt:[k(e),k(t)]}),gte:(e,t)=>new $({$gte:[k(e),k(t)]}),add:(e,t)=>new $({$add:[k(e),k(t)]}),sub:(e,t)=>new $({$sub:[k(e),k(t)]}),mul:(e,t)=>new $({$mul:[k(e),k(t)]}),div:(e,t)=>new $({$div:[k(e),k(t)]}),mod:(e,t)=>new $({$mod:[k(e),k(t)]}),neg:e=>new $({$neg:k(e)}),and:(...e)=>new $({$and:e.map(k)}),or:(...e)=>new $({$or:e.map(k)}),not:e=>new $({$not:k(e)}),field:e=>new $({$field:e})};var I=Object.defineProperty,P=(e,t,r)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,A=(e,t,r)=>P(e,"symbol"!=typeof t?t+"":t,r);class S{constructor(){A(this,"_isIndexRange")}}var O=Object.defineProperty,T=(e,t,r)=>t in e?O(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,N=(e,t,r)=>T(e,"symbol"!=typeof t?t+"":t,r);class _ extends S{constructor(e){super(),N(this,"rangeExpressions"),N(this,"isConsumed"),this.rangeExpressions=e,this.isConsumed=!1}static new(){return new _([])}consume(){if(this.isConsumed)throw Error("IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes");this.isConsumed=!0}eq(e,t){return this.consume(),new _(this.rangeExpressions.concat({type:"Eq",fieldPath:e,value:(0,i.cy)(t)}))}gt(e,t){return this.consume(),new _(this.rangeExpressions.concat({type:"Gt",fieldPath:e,value:(0,i.cy)(t)}))}gte(e,t){return this.consume(),new _(this.rangeExpressions.concat({type:"Gte",fieldPath:e,value:(0,i.cy)(t)}))}lt(e,t){return this.consume(),new _(this.rangeExpressions.concat({type:"Lt",fieldPath:e,value:(0,i.cy)(t)}))}lte(e,t){return this.consume(),new _(this.rangeExpressions.concat({type:"Lte",fieldPath:e,value:(0,i.cy)(t)}))}export(){return this.consume(),this.rangeExpressions}}var F=Object.defineProperty,J=(e,t,r)=>t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Q=(e,t,r)=>J(e,"symbol"!=typeof t?t+"":t,r);class R{constructor(){Q(this,"_isSearchFilter")}}function M(e,t,r,n){if(void 0===e)throw TypeError(`Must provide arg ${t} \`${n}\` to \`${r}\``)}var V=Object.defineProperty,z=(e,t,r)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,L=(e,t,r)=>z(e,"symbol"!=typeof t?t+"":t,r);class B extends R{constructor(e){super(),L(this,"filters"),L(this,"isConsumed"),this.filters=e,this.isConsumed=!1}static new(){return new B([])}consume(){if(this.isConsumed)throw Error("SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.");this.isConsumed=!0}search(e,t){return M(e,1,"search","fieldName"),M(t,2,"search","query"),this.consume(),new B(this.filters.concat({type:"Search",fieldPath:e,value:t}))}eq(e,t){return M(e,1,"eq","fieldName"),2!=arguments.length&&M(t,2,"search","value"),this.consume(),new B(this.filters.concat({type:"Eq",fieldPath:e,value:(0,i.cy)(t)}))}export(){return this.consume(),this.filters}}var D=r(5356),W=Object.defineProperty,G=(e,t,r)=>t in e?W(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,U=(e,t,r)=>G(e,"symbol"!=typeof t?t+"":t,r);function Z(e){throw Error("consumed"===e?"This query is closed and can't emit any more values.":"This query has been chained with another operator and can't be reused.")}Symbol.asyncIterator;class H{constructor(e){U(this,"state"),U(this,"tableNameForErrorMessages"),this.state={type:"preparing",query:e},"FullTableScan"===e.source.type?this.tableNameForErrorMessages=e.source.tableName:this.tableNameForErrorMessages=e.source.indexName.split(".")[0]}takeQuery(){if("preparing"!==this.state.type)throw Error("A query can only be chained once and can't be chained after iteration begins.");let e=this.state.query;return this.state={type:"closed"},e}startQuery(){if("executing"===this.state.type)throw Error("Iteration can only begin on a query once.");("closed"===this.state.type||"consumed"===this.state.type)&&Z(this.state.type);let{queryId:e}=a("1.0/queryStream",{query:this.state.query,version:D.r});return this.state={type:"executing",queryId:e},e}closeQuery(){"executing"===this.state.type&&a("1.0/queryCleanup",{queryId:this.state.queryId}),this.state={type:"consumed"}}order(e){M(e,1,"order","order");let t=this.takeQuery();if("Search"===t.source.type)throw Error("Search queries must always be in relevance order. Can not set order manually.");if(null!==t.source.order)throw Error("Queries may only specify order at most once");return t.source.order=e,new H(t)}filter(e){M(e,1,"filter","predicate");let t=this.takeQuery();if(t.operators.length>=256)throw Error("Can't construct query with more than 256 operators");return t.operators.push({filter:k(e(j))}),new H(t)}limit(e){M(e,1,"limit","n");let t=this.takeQuery();return t.operators.push({limit:e}),new H(t)}[Symbol.asyncIterator](){return this.startQuery(),this}async next(){("closed"===this.state.type||"consumed"===this.state.type)&&Z(this.state.type);let e="preparing"===this.state.type?this.startQuery():this.state.queryId,{value:t,done:r}=await o("1.0/queryStreamNext",{queryId:e});return r&&this.closeQuery(),{value:(0,n.du)(t),done:r}}return(){return this.closeQuery(),Promise.resolve({done:!0,value:void 0})}async paginate(e){if(M(e,1,"paginate","options"),"number"!=typeof e?.numItems||e.numItems<0)throw Error(`\`options.numItems\` must be a positive number. Received \`${e?.numItems}\`.`);let t=this.takeQuery(),r=e.numItems,s=e.cursor,i=e?.endCursor??null,a=e.maximumRowsRead??null,{page:l,isDone:c,continueCursor:u,splitCursor:d,pageStatus:h}=await o("1.0/queryPage",{query:t,cursor:s,endCursor:i,pageSize:r,maximumRowsRead:a,maximumBytesRead:e.maximumBytesRead,version:D.r});return{page:l.map(e=>(0,n.du)(e)),isDone:c,continueCursor:u,splitCursor:d,pageStatus:h}}async collect(){let e=[];for await(let t of this)e.push(t);return e}async take(e){M(e,1,"take","n");if(!Number.isInteger(e)||e<0)throw TypeError("Arg 1 `n` to `take` must be a non-negative integer");return this.limit(e).collect()}async first(){let e=await this.take(1);return 0===e.length?null:e[0]}async unique(){let e=await this.take(2);if(0===e.length)return null;if(2===e.length)throw Error(`unique() query returned more than one result from table ${this.tableNameForErrorMessages}:
 [${e[0]._id}, ${e[1]._id}, ...]`);return e[0]}}async function K(e,t){if(validateArg(e,1,"get","id"),"string"!=typeof e)throw Error(`Invalid argument \`id\` for \`db.get\`, expected string but got '${typeof e}': ${e}`);let r={id:convexToJson(e),isSystem:t,version};return jsonToConvex(await performAsyncSyscall("1.0/get",r))}async function X(e,t){if(e.startsWith("_"))throw Error("System tables (prefixed with `_`) are read-only.");return validateArg(e,1,"insert","table"),validateArg(t,2,"insert","value"),jsonToConvex(await performAsyncSyscall("1.0/insert",{table:e,value:convexToJson(t)}))._id}async function Y(e,t){validateArg(e,1,"patch","id"),validateArg(t,2,"patch","value"),await performAsyncSyscall("1.0/shallowMerge",{id:convexToJson(e),value:patchValueToJson(t)})}async function ee(e,t){validateArg(e,1,"replace","id"),validateArg(t,2,"replace","value"),await performAsyncSyscall("1.0/replace",{id:convexToJson(e),value:convexToJson(t)})}async function et(e){validateArg(e,1,"delete","id"),await performAsyncSyscall("1.0/remove",{id:convexToJson(e)})}var er=r(77954);er.v.object({numItems:er.v.number(),cursor:er.v.union(er.v.string(),er.v.null()),endCursor:er.v.optional(er.v.union(er.v.string(),er.v.null())),id:er.v.optional(er.v.number()),maximumRowsRead:er.v.optional(er.v.number()),maximumBytesRead:er.v.optional(er.v.number())});var en=r(61966),es=Object.defineProperty,ei=Object.defineProperty,ea=Object.defineProperty,eo=(e,t,r)=>t in e?ea(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,el=(e,t,r)=>eo(e,"symbol"!=typeof t?t+"":t,r);class ec{constructor(e,t){el(this,"_definition"),el(this,"_name"),this._definition=e,this._name=t,setReferencePath(this,`_reference/childComponent/${t}`)}get exports(){return function e(t,r){return new Proxy({},{get(n,s){if("string"==typeof s)return e(t,[...r,s]);if(s===toReferencePath){let e=`_reference/childComponent/${t}`;for(let t of r)e+=`/${t}`;return e}}})}(this._name,[])}}function eu(e){let t=[];for(let[r,n]of Object.entries(e)){let e;e="string"==typeof n?{type:"leaf",leaf:n}:eu(n),t.push([r,e])}return{type:"branch",branch:t}}function ed(e){return e.map(([e,t,r])=>{let n=null;if(null!==r)for(let[e,t]of(n=[],Object.entries(r)))void 0!==t&&n.push([e,{type:"value",value:JSON.stringify(convexToJson(t))}]);let s=t.componentDefinitionPath;if(!s)throw Error("no .componentPath for component definition "+JSON.stringify(t,null,2));return{name:e,path:s,args:n}})}var eh=Object.defineProperty,ey=(e,t,r)=>t in e?eh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,em=(e,t,r)=>ey(e,"symbol"!=typeof t?t+"":t,r);class ep{constructor(e){em(this,"indexes"),em(this,"searchIndexes"),em(this,"vectorIndexes"),em(this,"validator"),this.indexes=[],this.searchIndexes=[],this.vectorIndexes=[],this.validator=e}" indexes"(){return this.indexes}index(e,t){return this.indexes.push({indexDescriptor:e,fields:t}),this}searchIndex(e,t){return this.searchIndexes.push({indexDescriptor:e,searchField:t.searchField,filterFields:t.filterFields||[]}),this}vectorIndex(e,t){return this.vectorIndexes.push({indexDescriptor:e,vectorField:t.vectorField,dimensions:t.dimensions,filterFields:t.filterFields||[]}),this}self(){return this}export(){let e=this.validator.json;if("object"!=typeof e)throw Error("Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)");return{indexes:this.indexes,searchIndexes:this.searchIndexes,vectorIndexes:this.vectorIndexes,documentType:e}}}function ef(e){return new ep((0,er.d)(e)?e:er.v.object(e))}class ev{constructor(e,t){em(this,"tables"),em(this,"strictTableNameTypes"),em(this,"schemaValidation"),this.tables=e,this.schemaValidation=t?.schemaValidation===void 0||t.schemaValidation}export(){return JSON.stringify({tables:Object.entries(this.tables).map(([e,t])=>{let{indexes:r,searchIndexes:n,vectorIndexes:s,documentType:i}=t.export();return{tableName:e,indexes:r,searchIndexes:n,vectorIndexes:s,documentType:i}}),schemaValidation:this.schemaValidation})}}new ev({_scheduled_functions:ef({name:er.v.string(),args:er.v.array(er.v.any()),scheduledTime:er.v.float64(),completedTime:er.v.optional(er.v.float64()),state:er.v.union(er.v.object({kind:er.v.literal("pending")}),er.v.object({kind:er.v.literal("inProgress")}),er.v.object({kind:er.v.literal("success")}),er.v.object({kind:er.v.literal("failed"),error:er.v.string()}),er.v.object({kind:er.v.literal("canceled")}))}),_storage:ef({sha256:er.v.string(),size:er.v.float64(),contentType:er.v.optional(er.v.string())})},void 0)}};