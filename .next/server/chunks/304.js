"use strict";exports.id=304,exports.ids=[304],exports.modules={51304:(e,t,s)=>{s.d(t,{clerkDevelopmentCache:()=>r,createKeylessModeMessage:()=>l});var a=s(51288);let l=e=>`
\x1b[35m
[Clerk]:\x1b[0m You are running in keyless mode.
You can \x1b[35mclaim your keys\x1b[0m by visiting ${e.claimUrl}
`,r=function(){if((0,a.b_)())return global.__clerk_internal_keyless_logger||(global.__clerk_internal_keyless_logger={__cache:new Map,log:function({cacheKey:e,msg:t}){var s;this.__cache.has(e)&&Date.now()<((null==(s=this.__cache.get(e))?void 0:s.expiresAt)||0)||(console.log(t),this.__cache.set(e,{expiresAt:Date.now()+6e5}))},run:async function(e,{cacheKey:t,onSuccessStale:s=6e5,onErrorStale:a=6e5}){var l,r;if(this.__cache.has(t)&&Date.now()<((null==(l=this.__cache.get(t))?void 0:l.expiresAt)||0))return null==(r=this.__cache.get(t))?void 0:r.data;try{let a=await e();return this.__cache.set(t,{expiresAt:Date.now()+s,data:a}),a}catch(e){throw this.__cache.set(t,{expiresAt:Date.now()+a}),e}}}),globalThis.__clerk_internal_keyless_logger}()}};