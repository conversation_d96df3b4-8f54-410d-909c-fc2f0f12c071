"use strict";exports.id=757,exports.ids=[757],exports.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1689:(e,t,r)=>{function n(e){return"clerkError"in e}r.d(t,{LR:()=>i,_r:()=>a,$R:()=>n,u$:()=>s});function s(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn,plan:e?.meta?.plan}}}var i=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i,retryAfter:o}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.retryAfter=o,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(s):[]}(r)}},o=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function a({packageName:e,customMessages:t}){let r=e,n={...o,...t};function s(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(s(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(s(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(s(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(s(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(s(n.MissingClerkProvider,e))},throw(e){throw Error(s(e))}}}r(8777)},3497:(e,t,r)=>{r.d(t,{Fj:()=>i,MC:()=>s,b_:()=>n});var n=()=>!1,s=()=>!1,i=()=>{try{return!0}catch{}return!1}},7791:(e,t,r)=>{r.d(t,{AuthenticateWithRedirectCallback:()=>s,ClerkDegraded:()=>i,ClerkFailed:()=>o,ClerkLoaded:()=>a,ClerkLoading:()=>l,RedirectToCreateOrganization:()=>c,RedirectToOrganizationProfile:()=>d,RedirectToSignIn:()=>u,RedirectToSignUp:()=>h,RedirectToUserProfile:()=>p});var n=r(12907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthenticateWithRedirectCallback() from the server but AuthenticateWithRedirectCallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","AuthenticateWithRedirectCallback"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call ClerkDegraded() from the server but ClerkDegraded is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","ClerkDegraded"),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call ClerkFailed() from the server but ClerkFailed is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","ClerkFailed"),a=(0,n.registerClientReference)(function(){throw Error("Attempted to call ClerkLoaded() from the server but ClerkLoaded is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","ClerkLoaded"),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call ClerkLoading() from the server but ClerkLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","ClerkLoading");(0,n.registerClientReference)(function(){throw Error("Attempted to call MultisessionAppSupport() from the server but MultisessionAppSupport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","MultisessionAppSupport"),(0,n.registerClientReference)(function(){throw Error("Attempted to call Protect() from the server but Protect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","Protect");let c=(0,n.registerClientReference)(function(){throw Error("Attempted to call RedirectToCreateOrganization() from the server but RedirectToCreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","RedirectToCreateOrganization"),d=(0,n.registerClientReference)(function(){throw Error("Attempted to call RedirectToOrganizationProfile() from the server but RedirectToOrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","RedirectToOrganizationProfile"),u=(0,n.registerClientReference)(function(){throw Error("Attempted to call RedirectToSignIn() from the server but RedirectToSignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","RedirectToSignIn"),h=(0,n.registerClientReference)(function(){throw Error("Attempted to call RedirectToSignUp() from the server but RedirectToSignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","RedirectToSignUp"),p=(0,n.registerClientReference)(function(){throw Error("Attempted to call RedirectToUserProfile() from the server but RedirectToUserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","RedirectToUserProfile");(0,n.registerClientReference)(function(){throw Error("Attempted to call SignedIn() from the server but SignedIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","SignedIn"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SignedOut() from the server but SignedOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","SignedOut")},8777:(e,t,r)=>{r.d(t,{OV:()=>u,S7:()=>c,VK:()=>d,jq:()=>h});var n=Object.defineProperty,s=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,a=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||a("Cannot "+r),c=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?a("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),u=(e,t,r,n)=>(l(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},12618:(e,t,r)=>{r.d(t,{h5:()=>a,jn:()=>s,qu:()=>n,sM:()=>l,z:()=>i,zF:()=>o});var n={InvalidSecretKey:"clerk_key_invalid"},s={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},i={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},o=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},a={TokenInvalid:"token-invalid",InvalidSecretKey:"secret-key-invalid",UnexpectedError:"unexpected-error"},l=class e extends Error{constructor({message:t,code:r,status:n}){super(t),Object.setPrototypeOf(this,e.prototype),this.code=r,this.status=n}getFullMessage(){return`${this.message} (code=${this.code}, status=${this.status})`}}},12918:(e,t,r)=>{r.d(t,{__experimental_PaymentElement:()=>s,__experimental_PaymentElementProvider:()=>i,__experimental_usePaymentElement:()=>o,useAuth:()=>a,useClerk:()=>l,useEmailLink:()=>c,useOrganization:()=>d,useOrganizationList:()=>u,useReverification:()=>h,useSession:()=>p,useSessionList:()=>f,useSignIn:()=>m,useSignUp:()=>g,useUser:()=>y});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call EmailLinkErrorCode() from the server but EmailLinkErrorCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","EmailLinkErrorCode"),(0,n.registerClientReference)(function(){throw Error("Attempted to call EmailLinkErrorCodeStatus() from the server but EmailLinkErrorCodeStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","EmailLinkErrorCodeStatus");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call __experimental_PaymentElement() from the server but __experimental_PaymentElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","__experimental_PaymentElement"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call __experimental_PaymentElementProvider() from the server but __experimental_PaymentElementProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","__experimental_PaymentElementProvider"),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call __experimental_usePaymentElement() from the server but __experimental_usePaymentElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","__experimental_usePaymentElement");(0,n.registerClientReference)(function(){throw Error("Attempted to call isClerkAPIResponseError() from the server but isClerkAPIResponseError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","isClerkAPIResponseError"),(0,n.registerClientReference)(function(){throw Error("Attempted to call isClerkRuntimeError() from the server but isClerkRuntimeError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","isClerkRuntimeError"),(0,n.registerClientReference)(function(){throw Error("Attempted to call isEmailLinkError() from the server but isEmailLinkError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","isEmailLinkError"),(0,n.registerClientReference)(function(){throw Error("Attempted to call isKnownError() from the server but isKnownError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","isKnownError"),(0,n.registerClientReference)(function(){throw Error("Attempted to call isMetamaskError() from the server but isMetamaskError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","isMetamaskError"),(0,n.registerClientReference)(function(){throw Error("Attempted to call isReverificationCancelledError() from the server but isReverificationCancelledError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","isReverificationCancelledError");let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useAuth"),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call useClerk() from the server but useClerk is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useClerk"),c=(0,n.registerClientReference)(function(){throw Error("Attempted to call useEmailLink() from the server but useEmailLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useEmailLink"),d=(0,n.registerClientReference)(function(){throw Error("Attempted to call useOrganization() from the server but useOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useOrganization"),u=(0,n.registerClientReference)(function(){throw Error("Attempted to call useOrganizationList() from the server but useOrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useOrganizationList"),h=(0,n.registerClientReference)(function(){throw Error("Attempted to call useReverification() from the server but useReverification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useReverification"),p=(0,n.registerClientReference)(function(){throw Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useSession"),f=(0,n.registerClientReference)(function(){throw Error("Attempted to call useSessionList() from the server but useSessionList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useSessionList"),m=(0,n.registerClientReference)(function(){throw Error("Attempted to call useSignIn() from the server but useSignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useSignIn"),g=(0,n.registerClientReference)(function(){throw Error("Attempted to call useSignUp() from the server but useSignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useSignUp"),y=(0,n.registerClientReference)(function(){throw Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","useUser")},13920:(e,t,r)=>{r.d(t,{PromisifiedAuthProvider:()=>s});var n=r(12907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call PromisifiedAuthProvider() from the server but PromisifiedAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","PromisifiedAuthProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call usePromisifiedAuth() from the server but usePromisifiedAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","usePromisifiedAuth")},15614:(e,t,r)=>{r.d(t,{l3:()=>y,qf:()=>g,iU:()=>w,nk:()=>S,fA:()=>a,J0:()=>E});var n=r(12618),s=r(77598),i=r(42725);r(8777);var o=fetch.bind(globalThis),a={crypto:s.webcrypto,get fetch(){return o},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let s=new(r.out??Uint8Array)(n*t.bits/8|0),i=0,o=0,a=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);o=o<<t.bits|n,(i+=t.bits)>=8&&(i-=8,s[a++]=255&o>>i)}if(i>=t.bits||255&o<<8-i)throw SyntaxError("Unexpected end of data");return s})(e,c,t)},c={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},d={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},u="RSASSA-PKCS1-v1_5",h={RS256:u,RS384:u,RS512:u},p=Object.keys(d),f=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),m=(e,t)=>{let r=[t].flat().filter(e=>!!e),s=[e].flat().filter(e=>!!e);if(r.length>0&&s.length>0){if("string"==typeof e){if(!r.includes(e))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(f(e)&&!e.some(e=>r.includes(e)))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},g=e=>{if(void 0!==e&&"JWT"!==e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},y=e=>{if(!p.includes(e))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${p}.`})},k=e=>{if("string"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},b=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new n.zF({reason:n.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},_=(e,t)=>{if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()<=r.getTime()-t)throw new n.zF({reason:n.jn.TokenExpired,message:`JWT is expired. Expiry date: ${s.toUTCString()}, Current date: ${r.toUTCString()}.`})},v=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()>r.getTime()+t)throw new n.zF({reason:n.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${s.toUTCString()}; Current date: ${r.toUTCString()};`})},C=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()>r.getTime()+t)throw new n.zF({reason:n.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${s.toUTCString()}; Current date: ${r.toUTCString()};`})};async function S(e,t){let{header:r,signature:s,raw:o}=e,l=new TextEncoder().encode([o.header,o.payload].join(".")),c=function(e){let t=d[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${p.join(",")}.`);return{hash:{name:d[e]},name:h[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return a.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,i.y)(t),n=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)n[e]=r.charCodeAt(e);return n}(e),s="sign"===r?"pkcs8":"spki";return a.crypto.subtle.importKey(s,n,t,!1,[r])}(t,c,"verify");return{data:await a.crypto.subtle.verify(c.name,e,s,l)}}catch(e){return{errors:[new n.zF({reason:n.jn.TokenInvalidSignature,message:e?.message})]}}}function w(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new n.zF({reason:n.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,s,i]=t,o=new TextDecoder,a=JSON.parse(o.decode(l.parse(r,{loose:!0}))),c=JSON.parse(o.decode(l.parse(s,{loose:!0})));return{data:{header:a,payload:c,signature:l.parse(i,{loose:!0}),raw:{header:r,payload:s,signature:i,text:e}}}}async function E(e,t){let{audience:r,authorizedParties:s,clockSkewInMs:i,key:o}=t,a=i||5e3,{data:l,errors:c}=w(e);if(c)return{errors:c};let{header:d,payload:u}=l;try{let{typ:e,alg:t}=d;g(e),y(t);let{azp:n,sub:i,aud:o,iat:l,exp:c,nbf:h}=u;k(i),m([o],[r]),b(n,s),_(c,a),v(h,a),C(l,a)}catch(e){return{errors:[e]}}let{data:h,errors:p}=await S(l,o);return p?{errors:[new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${p[0]}`})]}:h?{data:u}:{errors:[new n.zF({reason:n.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},17478:(e,t)=>{function r(e){for(let t=0;t<e.length;t++){let r=e[t];if("function"!=typeof r)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof r}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return r}})},21313:(e,t,r)=>{r.d(t,{APIKeys:()=>n.Lq,CreateOrganization:()=>n.ul,GoogleOneTap:()=>n.PQ,OrganizationList:()=>n.oE,OrganizationProfile:()=>p,OrganizationSwitcher:()=>n.NC,PricingTable:()=>n.nm,SignIn:()=>f,SignInButton:()=>n.hZ,SignInWithMetamaskButton:()=>n.M_,SignOutButton:()=>n.ct,SignUp:()=>m,SignUpButton:()=>n.Ny,UserButton:()=>n.uF,UserProfile:()=>h,Waitlist:()=>n.cP});var n=r(56499),s=r(43210),i=r.n(s),o=r(41236),a=r(39631),l=r(76999);let c=(e,t,r,s=!0)=>{let o=i().useRef(0),{pagesRouter:c}=(0,l.r)(),{session:d,isLoaded:u}=(0,n.wV)();(0,a.Fj)()||i().useEffect(()=>{if(!u||r&&"path"!==r||s&&!d)return;let n=new AbortController,i=()=>{let r=c?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${r}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return c?c.pathname.match(/\[\[\.\.\..+]]/)||i():(async()=>{let t;if(o.current++,!(o.current>1)){try{let r=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(r,{signal:n.signal})}catch{}(null==t?void 0:t.status)===404&&i()}})(),()=>{o.current>1&&n.abort()}},[u])},d=()=>{let e=i().useRef(),{pagesRouter:t}=(0,l.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let n=r(16189).usePathname,s=r(16189).useParams,o=(n()||"").split("/").filter(Boolean),a=Object.values(s()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${o.slice(0,o.length-a.length).join("/")}`),e.current};function u(e,t,r=!0){let n=d(),s=(0,o.yC)(e,t,{path:n});return c(e,n,s.routing,r),s}let h=Object.assign(e=>i().createElement(n.Fv,{...u("UserProfile",e)}),{...n.Fv}),p=Object.assign(e=>i().createElement(n.nC,{...u("OrganizationProfile",e)}),{...n.nC}),f=e=>i().createElement(n.Ls,{...u("SignIn",e,!1)}),m=e=>i().createElement(n.Hx,{...u("SignUp",e,!1)})},31243:(e,t,r)=>{r.d(t,{FW:()=>c,HG:()=>l,Vc:()=>a,gE:()=>s,iM:()=>n,mG:()=>i,ub:()=>o});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],s=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],i=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],o=[".accountsstage.dev"],a="https://api.lclclerk.com",l="https://api.clerkstage.dev",c="https://api.clerk.com"},42725:(e,t,r)=>{r.d(t,{y:()=>n});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},43037:(e,t,r)=>{r.d(t,{AA:()=>x,Bs:()=>ra,y3:()=>tT,nr:()=>t1});var n=r(90052),s=r(15614),i=r(12618);r(8777);var o={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},a=new Set(["first_factor","second_factor","multi_factor"]),l=new Set(["strict_mfa","strict","moderate","lax"]),c=e=>"number"==typeof e&&e>0,d=e=>a.has(e),u=e=>l.has(e),h=e=>e.replace(/^(org:)*/,"org:"),p=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:s}=t;return(e.role||e.permission)&&r&&n&&s?e.permission?s.includes(h(e.permission)):e.role?h(n)===h(e.role):null:null},f=(e,t)=>{let{org:r,user:n}=g(e),[s,i]=t.split(":"),o=i||s;return"org"===s?r.includes(o):"user"===s?n.includes(o):[...r,...n].includes(o)},m=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?f(r,e.feature):e.plan&&n?f(n,e.plan):null},g=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},y=e=>{if(!e)return!1;let t="string"==typeof e&&u(e),r="object"==typeof e&&d(e.level)&&c(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?o[e]:e).bind(null,e)},k=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=y(e.reverification);if(!r)return null;let{level:n,afterMinutes:s}=r(),[i,o]=t,a=-1!==i?s>i:null,l=-1!==o?s>o:null;switch(n){case"first_factor":return a;case"second_factor":return -1!==o?l:a;case"multi_factor":return -1===o?a:a&&l}},b=e=>t=>{if(!e.userId)return!1;let r=m(t,e),n=p(t,e),s=k(t,e);return[r||n,s].some(e=>null===e)?[r||n,s].some(e=>!0===e):[r||n,s].every(e=>!0===e)},_=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),n=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:n}},v=e=>{let t,r,n,s,i=e.fva??null,o=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,n=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:i}=g(e.fea),{permissions:o,featurePermissionMap:a}=_({per:e.o?.per,fpm:e.o?.fpm});s=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let n=[];for(let s=0;s<e.length;s++){let i=e[s];if(s>=r.length)continue;let o=r[s];if(o)for(let e=0;e<o.length;e++)1===o[e]&&n.push(`org:${i}:${t[e]}`)}return n}({features:i,featurePermissionMap:a,permissions:o})}}else t=e.org_id,r=e.org_role,n=e.org_slug,s=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:o,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:n,orgPermissions:s,factorVerificationAge:i}},C=r(1689),S=r(76463),w=r(49343);function E(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function T(e){return e&&e.sensitive?"":"i"}var A="https://api.clerk.com",O="@clerk/backend@2.4.1",R="2025-04-10",I={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},P={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:I.DevBrowser,Handshake:I.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:I.HandshakeNonce,HandshakeFormat:"format"},x={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:I,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",SecFetchSite:"sec-fetch-site",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:P},j=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()};function U(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}function q(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var z=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.originalFrontendApi="",this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.tokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(x.Cookies.ClientUat),t=this.getCookie(x.Cookies.ClientUat),r=this.getSuffixedCookie(x.Cookies.Session)||"",n=this.getCookie(x.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:i}=(0,s.iU)(n),o=i?.payload.iat||0,{data:a}=(0,s.iU)(r),l=a?.payload.iat||0;if("0"!==e&&"0"!==t&&o>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(a);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}isCrossOriginReferrer(){if(!this.referrer||!this.origin)return!1;try{if("cross-site"===this.getHeader(x.Headers.SecFetchSite))return!0;return new URL(this.referrer).origin!==this.origin}catch{return!1}}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,n.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,n.q5)(this.publishableKey,{fatal:!0,domain:e.domain,isSatellite:e.isSatellite});this.originalFrontendApi=r.frontendApi;let s=(0,n.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=s.instanceType,this.frontendApi=s.frontendApi}initHeaderValues(){this.tokenInHeader=this.parseAuthorizationHeader(this.getHeader(x.Headers.Authorization)),this.origin=this.getHeader(x.Headers.Origin),this.host=this.getHeader(x.Headers.Host),this.forwardedHost=this.getHeader(x.Headers.ForwardedHost),this.forwardedProto=this.getHeader(x.Headers.CloudFrontForwardedProto)||this.getHeader(x.Headers.ForwardedProto),this.referrer=this.getHeader(x.Headers.Referrer),this.userAgent=this.getHeader(x.Headers.UserAgent),this.secFetchDest=this.getHeader(x.Headers.SecFetchDest),this.accept=this.getHeader(x.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(x.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(x.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(x.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(x.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(x.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(x.QueryParameters.Handshake)||this.getCookie(x.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(x.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(x.QueryParameters.HandshakeNonce)||this.getCookie(x.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,n.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,s.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,s.iU)(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.originalFrontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},N=async(e,t)=>new z(t.publishableKey?await (0,n.qS)(t.publishableKey,s.fA.crypto.subtle):"",e,t),M=RegExp("(?<!:)/{1,}","g");function D(...e){return e.filter(e=>e).join("/").replace(M,"/")}var L=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},J="/actor_tokens",H=class extends L{async create(e){return this.request({method:"POST",path:J,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:D(J,e,"revoke")})}},F="/accountless_applications",K=class extends L{async createAccountlessApplication(){return this.request({method:"POST",path:F})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:D(F,"complete")})}},$="/allowlist_identifiers",W=class extends L{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:$,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:$,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:D($,e)})}},B="/api_keys",G=class extends L{async create(e){return this.request({method:"POST",path:B,bodyParams:e})}async revoke(e){let{apiKeyId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:D(B,t,"revoke"),bodyParams:r})}async getSecret(e){return this.requireId(e),this.request({method:"GET",path:D(B,e,"secret")})}async verifySecret(e){return this.request({method:"POST",path:D(B,"verify"),bodyParams:{secret:e}})}},V=class extends L{async changeDomain(e){return this.request({method:"POST",path:D("/beta_features","change_domain"),bodyParams:e})}},X="/blocklist_identifiers",Q=class extends L{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:X,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:X,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:D(X,e)})}},Y="/clients",Z=class extends L{async getClientList(e={}){return this.request({method:"GET",path:Y,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:D(Y,e)})}verifyClient(e){return this.request({method:"POST",path:D(Y,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:D(Y,"handshake_payload"),queryParams:e})}},ee="/domains",et=class extends L{async list(){return this.request({method:"GET",path:ee})}async add(e){return this.request({method:"POST",path:ee,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:D(ee,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:D(ee,e)})}},er="/email_addresses",en=class extends L{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:D(er,e)})}async createEmailAddress(e){return this.request({method:"POST",path:er,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:D(er,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:D(er,e)})}},es=class extends L{async verifyAccessToken(e){return this.request({method:"POST",path:D("/oauth_applications/access_tokens","verify"),bodyParams:{access_token:e}})}},ei="/instance",eo=class extends L{async get(){return this.request({method:"GET",path:ei})}async update(e){return this.request({method:"PATCH",path:ei,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:D(ei,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:D(ei,"organization_settings"),bodyParams:e})}},ea="/invitations",el=class extends L{async getInvitationList(e={}){return this.request({method:"GET",path:ea,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:ea,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:D(ea,e,"revoke")})}},ec=class extends L{async verifySecret(e){return this.request({method:"POST",path:D("/m2m_tokens","verify"),bodyParams:{secret:e}})}},ed=class extends L{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},eu="/jwt_templates",eh=class extends L{async list(e={}){return this.request({method:"GET",path:eu,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:D(eu,e)})}async create(e){return this.request({method:"POST",path:eu,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:D(eu,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:D(eu,e)})}},ep="/organizations",ef=class extends L{async getOrganizationList(e){return this.request({method:"GET",path:ep,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:ep,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:D(ep,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:D(ep,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new s.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:D(ep,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:D(ep,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:D(ep,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:D(ep,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:D(ep,t,"memberships"),queryParams:r})}async getInstanceOrganizationMembershipList(e){return this.request({method:"GET",path:"/organization_memberships",queryParams:e})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:D(ep,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:D(ep,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:D(ep,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:D(ep,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:D(ep,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:D(ep,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:D(ep,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:D(ep,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:D(ep,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:D(ep,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:D(ep,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:D(ep,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:D(ep,t,"domains",r)})}},em="/oauth_applications",eg=class extends L{async list(e={}){return this.request({method:"GET",path:em,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:D(em,e)})}async create(e){return this.request({method:"POST",path:em,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:D(em,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:D(em,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:D(em,e,"rotate_secret")})}},ey="/phone_numbers",ek=class extends L{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:D(ey,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:ey,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:D(ey,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:D(ey,e)})}},eb=class extends L{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},e_="/redirect_urls",ev=class extends L{async getRedirectUrlList(){return this.request({method:"GET",path:e_,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:D(e_,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:e_,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:D(e_,e)})}},eC="/saml_connections",eS=class extends L{async getSamlConnectionList(e={}){return this.request({method:"GET",path:eC,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:eC,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:D(eC,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:D(eC,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:D(eC,e)})}},ew="/sessions",eE=class extends L{async getSessionList(e={}){return this.request({method:"GET",path:ew,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:D(ew,e)})}async createSession(e){return this.request({method:"POST",path:ew,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:D(ew,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:D(ew,e,"verify"),bodyParams:{token:t}})}async getToken(e,t,r){this.requireId(e);let n={method:"POST",path:t?D(ew,e,"tokens",t):D(ew,e,"tokens")};return void 0!==r&&(n.bodyParams={expires_in_seconds:r}),this.request(n)}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:D(ew,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},eT="/sign_in_tokens",eA=class extends L{async createSignInToken(e){return this.request({method:"POST",path:eT,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:D(eT,e,"revoke")})}},eO="/sign_ups",eR=class extends L{async get(e){return this.requireId(e),this.request({method:"GET",path:D(eO,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:D(eO,t),bodyParams:r})}},eI=class extends L{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},eP="/users",ex=class extends L{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...s}=e,[i,o]=await Promise.all([this.request({method:"GET",path:eP,queryParams:e}),this.getCount(s)]);return{data:i,totalCount:o}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:D(eP,e)})}async createUser(e){return this.request({method:"POST",path:eP,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:D(eP,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new s.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:D(eP,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:D(eP,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:D(eP,e)})}async getCount(e={}){return this.request({method:"GET",path:D(eP,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),s=r?t:`oauth_${t}`;return r&&(0,n.io)("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:D(eP,e,"oauth_access_tokens",s),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:D(eP,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:D(eP,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:D(eP,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:D(eP,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:D(eP,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:D(eP,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:D(eP,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:D(eP,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:D(eP,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:D(eP,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:D(eP,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:D(eP,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:D(eP,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:D(eP,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:D(eP,e,"totp")})}},ej="/waitlist_entries",eU=class extends L{async list(e={}){return this.request({method:"GET",path:ej,queryParams:e})}async create(e){return this.request({method:"POST",path:ej,bodyParams:e})}},eq="/webhooks",ez=class extends L{async createSvixApp(){return this.request({method:"POST",path:D(eq,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:D(eq,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:D(eq,"svix")})}},eN=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},eM=class e{constructor(e,t,r,n,s,i,o,a){this.id=e,this.status=t,this.userId=r,this.actor=n,this.token=s,this.url=i,this.createdAt=o,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},eD=class e{constructor(e,t,r,n,s,i,o){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=s,this.instanceId=i,this.invitationId=o}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},eL=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u,h,p,f,m){this.id=e,this.type=t,this.name=r,this.subject=n,this.scopes=s,this.claims=i,this.revoked=o,this.revocationReason=a,this.expired=l,this.expiration=c,this.createdBy=d,this.description=u,this.lastUsedAt=h,this.createdAt=p,this.updatedAt=f,this.secret=m}static fromJSON(t){return new e(t.id,t.type,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.description,t.last_used_at,t.created_at,t.updated_at,t.secret)}},eJ=class e{constructor(e,t,r,n,s,i){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=s,this.instanceId=i}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},eH=class e{constructor(e,t,r,n,s,i,o,a){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=s,this.browserVersion=i,this.browserName=o,this.deviceType=a}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},eF=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=s,this.expireAt=i,this.abandonAt=o,this.createdAt=a,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=d,this.actor=u}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&eH.fromJSON(t.latest_activity),t.actor)}},eK=class e{constructor(e,t,r,n,s,i,o,a){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=s,this.lastActiveSessionId=i,this.createdAt=o,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>eF.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},e$=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},eW=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},eB=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},eG=class e{constructor(e,t,r,n,s,i,o,a){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=n,this.developmentOrigin=s,this.cnameTargets=i,this.accountsPortalUrl=o,this.proxyUrl=a}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>e$.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},eV=class e{constructor(e,t,r,n,s,i,o,a,l,c,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=s,this.body=i,this.bodyPlain=o,this.status=a,this.slug=l,this.data=c,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},eX=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eQ=class e{constructor(e,t,r=null,n=null,s=null,i=null,o=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=s,this.nonce=i,this.message=o}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eY=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eQ.fromJSON(t.verification),t.linked_to.map(e=>eX.fromJSON(e)))}},eZ=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=s,this.emailAddress=i,this.firstName=o,this.lastName=a,this.imageUrl=l,this.username=c,this.phoneNumber=d,this.publicMetadata=u,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&eQ.fromJSON(t.verification))}},e0=class e{constructor(e,t,r,n,s,i,o,a,l,c,d){this.id=e,this.clientId=t,this.type=r,this.subject=n,this.scopes=s,this.revoked=i,this.revocationReason=o,this.expired=a,this.expiration=l,this.createdAt=c,this.updatedAt=d}static fromJSON(t){return new e(t.id,t.client_id,t.type,t.subject,t.scopes,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_at,t.updated_at)}},e1=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},e2=class e{constructor(e,t,r,n,s){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=n,this.ignoreDotsForGmailAddresses=s}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},e3=class e{constructor(e,t,r,n,s){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=n,this.enhancedEmailDeliverability=s}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},e7=class e{constructor(e,t,r,n,s,i,o,a){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=s,this.status=i,this.url=o,this.revoked=a,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},e8={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",ApiKey:"api_key",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",MachineToken:"machine_to_machine_token",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",IdpOAuthAccessToken:"clerk_idp_oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",SamlConnection:"saml_connection",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},e9=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u,h){this.id=e,this.name=t,this.subject=r,this.scopes=n,this.claims=s,this.revoked=i,this.revocationReason=o,this.expired=a,this.expiration=l,this.createdBy=c,this.creationReason=d,this.createdAt=u,this.updatedAt=h}static fromJSON(t){return new e(t.id,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.creation_reason,t.created_at,t.updated_at)}},e5=class e{constructor(e,t,r,n,s,i,o,a,l){this.id=e,this.name=t,this.claims=r,this.lifetime=n,this.allowedClockSkew=s,this.customSigningKey=i,this.signingAlgorithm=o,this.createdAt=a,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},e4=class e{constructor(e,t,r,n={},s,i,o,a){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=s,this.scopes=i,this.tokenSecret=o,this.expiresAt=a}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},e6=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=n,this.isPublic=s,this.scopes=i,this.redirectUris=o,this.authorizeUrl=a,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=d,this.tokenIntrospectionUrl=u,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},te=class e{constructor(e,t,r,n,s,i,o,a={},l={},c,d,u,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=s,this.createdAt=i,this.updatedAt=o,this.publicMetadata=a,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=d,this.membersCount=u,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},tt=class e{constructor(e,t,r,n,s,i,o,a,l,c,d={},u={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=n,this.organizationId=s,this.createdAt=i,this.updatedAt=o,this.expiresAt=a,this.url=l,this.status=c,this.publicMetadata=d,this.privateMetadata=u,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},tr=class e{constructor(e,t,r,n={},s={},i,o,a,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=s,this.createdAt=i,this.updatedAt=o,this.organization=a,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,te.fromJSON(t.organization),tn.fromJSON(t.public_user_data));return r._raw=t,r}},tn=class e{constructor(e,t,r,n,s,i){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=s,this.userId=i}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},ts=class e{constructor(e,t,r,n,s,i,o,a,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=n,this.creatorRole=s,this.adminDeleteEnabled=i,this.domainsEnabled=o,this.domainsEnrollmentModes=a,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},ti=class e{constructor(e,t,r,n,s,i){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=s,this.linkedTo=i}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eQ.fromJSON(t.verification),t.linked_to.map(e=>eX.fromJSON(e)))}},to=class e{constructor(e,t,r,n,s,i,o){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=n,this.successful=s,this.createdAt=i,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},ta=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},tl=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u,h,p,f,m,g,y,k,b,_){this.id=e,this.name=t,this.domain=r,this.organizationId=n,this.idpEntityId=s,this.idpSsoUrl=i,this.idpCertificate=o,this.idpMetadataUrl=a,this.idpMetadata=l,this.acsUrl=c,this.spEntityId=d,this.spMetadataUrl=u,this.active=h,this.provider=p,this.userCount=f,this.syncUserAttributes=m,this.allowSubdomains=g,this.allowIdpInitiated=y,this.createdAt=k,this.updatedAt=b,this.attributeMapping=_}static fromJSON(t){return new e(t.id,t.name,t.domain,t.organization_id,t.idp_entity_id,t.idp_sso_url,t.idp_certificate,t.idp_metadata_url,t.idp_metadata,t.acs_url,t.sp_entity_id,t.sp_metadata_url,t.active,t.provider,t.user_count,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at,t.attribute_mapping&&td.fromJSON(t.attribute_mapping))}},tc=class e{constructor(e,t,r,n,s,i,o,a,l,c){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=s,this.syncUserAttributes=i,this.allowSubdomains=o,this.allowIdpInitiated=a,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},td=class e{constructor(e,t,r,n){this.userId=e,this.emailAddress=t,this.firstName=r,this.lastName=n}static fromJSON(t){return new e(t.user_id,t.email_address,t.first_name,t.last_name)}},tu=class e{constructor(e,t,r,n,s,i,o,a,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=s,this.firstName=i,this.lastName=o,this.verification=a,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eQ.fromJSON(t.verification),t.saml_connection&&tc.fromJSON(t.saml_connection))}},th=class e{constructor(e,t,r,n,s,i,o){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=s,this.createdAt=i,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},tp=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},tf=class e{constructor(e,t,r,n){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=n}static fromJSON(t){return new e(t.email_address&&tp.fromJSON(t.email_address),t.phone_number&&tp.fromJSON(t.phone_number),t.web3_wallet&&tp.fromJSON(t.web3_wallet),t.external_account)}},tm=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u,h,p,f,m,g,y,k,b,_,v){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=n,this.missingFields=s,this.unverifiedFields=i,this.verifications=o,this.username=a,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=d,this.passwordEnabled=u,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=m,this.createdSessionId=g,this.createdUserId=y,this.abandonAt=k,this.legalAcceptedAt=b,this.publicMetadata=_,this.unsafeMetadata=v}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?tf.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},tg=class e{constructor(e,t,r,n,s,i,o){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=s,this.phoneNumberId=i,this.data=o}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},ty=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},tk=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eQ.fromJSON(t.verification))}},tb=class e{constructor(e,t,r,n,s,i,o,a,l,c,d,u,h,p,f,m,g,y,k,b={},_={},v={},C=[],S=[],w=[],E=[],T=[],A,O,R=null,I,P){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=s,this.banned=i,this.locked=o,this.createdAt=a,this.updatedAt=l,this.imageUrl=c,this.hasImage=d,this.primaryEmailAddressId=u,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=m,this.username=g,this.firstName=y,this.lastName=k,this.publicMetadata=b,this.privateMetadata=_,this.unsafeMetadata=v,this.emailAddresses=C,this.phoneNumbers=S,this.web3Wallets=w,this.externalAccounts=E,this.samlAccounts=T,this.lastActiveAt=A,this.createOrganizationEnabled=O,this.createOrganizationsLimit=R,this.deleteSelfEnabled=I,this.legalAcceptedAt=P,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eY.fromJSON(e)),(t.phone_numbers||[]).map(e=>ti.fromJSON(e)),(t.web3_wallets||[]).map(e=>tk.fromJSON(e)),(t.external_accounts||[]).map(e=>eZ.fromJSON(e)),(t.saml_accounts||[]).map(e=>tu.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},t_=class e{constructor(e,t,r,n,s,i,o){this.id=e,this.emailAddress=t,this.status=r,this.invitation=n,this.createdAt=s,this.updatedAt=i,this.isLocked=o}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&e7.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function tv(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eB.fromJSON(e);switch(e.object){case e8.AccountlessApplication:return eN.fromJSON(e);case e8.ActorToken:return eM.fromJSON(e);case e8.AllowlistIdentifier:return eD.fromJSON(e);case e8.ApiKey:return eL.fromJSON(e);case e8.BlocklistIdentifier:return eJ.fromJSON(e);case e8.Client:return eK.fromJSON(e);case e8.Cookies:return eW.fromJSON(e);case e8.Domain:return eG.fromJSON(e);case e8.EmailAddress:return eY.fromJSON(e);case e8.Email:return eV.fromJSON(e);case e8.IdpOAuthAccessToken:return e0.fromJSON(e);case e8.Instance:return e1.fromJSON(e);case e8.InstanceRestrictions:return e2.fromJSON(e);case e8.InstanceSettings:return e3.fromJSON(e);case e8.Invitation:return e7.fromJSON(e);case e8.JwtTemplate:return e5.fromJSON(e);case e8.MachineToken:return e9.fromJSON(e);case e8.OauthAccessToken:return e4.fromJSON(e);case e8.OAuthApplication:return e6.fromJSON(e);case e8.Organization:return te.fromJSON(e);case e8.OrganizationInvitation:return tt.fromJSON(e);case e8.OrganizationMembership:return tr.fromJSON(e);case e8.OrganizationSettings:return ts.fromJSON(e);case e8.PhoneNumber:return ti.fromJSON(e);case e8.ProxyCheck:return to.fromJSON(e);case e8.RedirectUrl:return ta.fromJSON(e);case e8.SamlConnection:return tl.fromJSON(e);case e8.SignInToken:return th.fromJSON(e);case e8.SignUpAttempt:return tm.fromJSON(e);case e8.Session:return eF.fromJSON(e);case e8.SmsMessage:return tg.fromJSON(e);case e8.Token:return ty.fromJSON(e);case e8.TotalCount:return e.total_count;case e8.User:return tb.fromJSON(e);case e8.WaitlistEntry:return t_.fromJSON(e);default:return e}}function tC(e){var t;return t=async t=>{let r,{secretKey:n,requireSecretKey:i=!0,apiUrl:o=A,apiVersion:a="v1",userAgent:l=O,skipApiVersionInUrl:c=!1}=e,{path:d,method:u,queryParams:h,headerParams:p,bodyParams:f,formData:m}=t;i&&q(n);let g=new URL(c?D(o,d):D(o,a,d));if(h)for(let[e,t]of Object.entries(S({...h})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let y=new Headers({"Clerk-API-Version":R,"User-Agent":l,...p});n&&y.set("Authorization",`Bearer ${n}`);try{var k;m?r=await s.fA.fetch(g.href,{method:u,headers:y,body:m}):(y.set("Content-Type","application/json"),r=await s.fA.fetch(g.href,{method:u,headers:y,...(()=>{if(!("GET"!==u&&f&&Object.keys(f).length>0))return null;let e=e=>S(e,{deep:!1});return{body:JSON.stringify(Array.isArray(f)?f.map(e):e(f))}})()}));let e=r?.headers&&r.headers?.get(x.Headers.ContentType)===x.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:tE(t),status:r?.status,statusText:r?.statusText,clerkTraceId:tS(t,r?.headers),retryAfter:tw(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>tv(e))}:(k=t)&&"object"==typeof k&&"data"in k&&Array.isArray(k.data)&&void 0!==k.data?{data:t.data.map(e=>tv(e)),totalCount:t.total_count}:{data:tv(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:tS(e,r?.headers)};return{data:null,errors:tE(e),status:r?.status,statusText:r?.statusText,clerkTraceId:tS(e,r?.headers),retryAfter:tw(r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:s,status:i,statusText:o,clerkTraceId:a,retryAfter:l}=await t(...e);if(n){let e=new C.LR(o||"",{data:[],status:i,clerkTraceId:a,retryAfter:l});throw e.errors=n,e}return void 0!==s?{data:r,totalCount:s}:r}}function tS(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function tw(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function tE(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(C.u$):[]}return[]}function tT(e){let t=tC(e);return{__experimental_accountlessApplications:new K(tC({...e,requireSecretKey:!1})),actorTokens:new H(t),allowlistIdentifiers:new W(t),apiKeys:new G(tC({...e,skipApiVersionInUrl:!0})),betaFeatures:new V(t),blocklistIdentifiers:new Q(t),clients:new Z(t),domains:new et(t),emailAddresses:new en(t),idPOAuthAccessToken:new es(tC({...e,skipApiVersionInUrl:!0})),instance:new eo(t),invitations:new el(t),jwks:new ed(t),jwtTemplates:new eh(t),machineTokens:new ec(tC({...e,skipApiVersionInUrl:!0})),oauthApplications:new eg(t),organizations:new ef(t),phoneNumbers:new ek(t),proxyChecks:new eb(t),redirectUrls:new ev(t),samlConnections:new eS(t),sessions:new eE(t),signInTokens:new eA(t),signUps:new eR(t),testingTokens:new eI(t),users:new ex(t),waitlistEntries:new eU(t),webhooks:new ez(t)}}var tA={SessionToken:"session_token",ApiKey:"api_key",MachineToken:"machine_token",OAuthToken:"oauth_token"},tO="oat_",tR=["mt_",tO,"ak_"];function tI(e){return tR.some(t=>e.startsWith(t))}function tP(e){if(e.startsWith("mt_"))return tA.MachineToken;if(e.startsWith(tO))return tA.OAuthToken;if(e.startsWith("ak_"))return tA.ApiKey;throw Error("Unknown machine token type")}var tx=(e,t)=>!!e&&("any"===t||(Array.isArray(t)?t:[t]).includes(e)),tj=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function tU(e,t){return{tokenType:tA.SessionToken,sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:tj(e),isAuthenticated:!1}}var tq=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template||void 0!==e.expiresInSeconds?t(n,e.template,e.expiresInSeconds):r:null},tz={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},tN={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",PrimaryDomainCrossOriginSync:"primary-domain-cross-origin-sync",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",TokenTypeMismatch:"token-type-mismatch",UnexpectedError:"unexpected-error"};function tM(e){let{authenticateContext:t,headers:r=new Headers,token:n}=e;return{status:tz.SignedIn,reason:null,message:null,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!0,isAuthenticated:!0,tokenType:e.tokenType,toAuth:({treatPendingAsSignedOut:r=!0}={})=>{if(e.tokenType===tA.SessionToken){let{sessionClaims:s}=e,i=function(e,t,r){let{actor:n,sessionId:s,sessionStatus:i,userId:o,orgId:a,orgRole:l,orgSlug:c,orgPermissions:d,factorVerificationAge:u}=v(r),h=tT(e),p=tq({sessionId:s,sessionToken:t,fetcher:async(e,t,r)=>(await h.sessions.getToken(e,t||"",r)).jwt});return{tokenType:tA.SessionToken,actor:n,sessionClaims:r,sessionId:s,sessionStatus:i,userId:o,orgId:a,orgRole:l,orgSlug:c,orgPermissions:d,factorVerificationAge:u,getToken:p,has:b({orgId:a,orgRole:l,orgPermissions:d,userId:o,factorVerificationAge:u,features:r.fea||"",plans:r.pla||""}),debug:tj({...e,sessionToken:t}),isAuthenticated:!0}}(t,n,s);return r&&"pending"===i.sessionStatus?tU(void 0,i.sessionStatus):i}let{machineData:s}=e;var i=e.tokenType;let o={id:s.id,subject:s.subject,getToken:()=>Promise.resolve(n),has:()=>!1,debug:tj(t),isAuthenticated:!0};switch(i){case tA.ApiKey:return{...o,tokenType:i,name:s.name,claims:s.claims,scopes:s.scopes,userId:s.subject.startsWith("user_")?s.subject:null,orgId:s.subject.startsWith("org_")?s.subject:null};case tA.MachineToken:return{...o,tokenType:i,name:s.name,claims:s.claims,scopes:s.scopes,machineId:s.subject};case tA.OAuthToken:return{...o,tokenType:i,scopes:s.scopes,userId:s.subject,clientId:s.clientId};default:throw Error(`Invalid token type: ${i}`)}},headers:r,token:n}}function tD(e){let{authenticateContext:t,headers:r=new Headers,reason:n,message:s="",tokenType:i}=e;return tL({status:tz.SignedOut,reason:n,message:s,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:i,toAuth:()=>i===tA.SessionToken?tU({...t,status:tz.SignedOut,reason:n,message:s}):function(e,t){let r={id:null,subject:null,scopes:null,has:()=>!1,getToken:()=>Promise.resolve(null),debug:tj(t),isAuthenticated:!1};switch(e){case tA.ApiKey:return{...r,tokenType:e,name:null,claims:null,scopes:null,userId:null,orgId:null};case tA.MachineToken:return{...r,tokenType:e,name:null,claims:null,scopes:null,machineId:null};case tA.OAuthToken:return{...r,tokenType:e,scopes:null,userId:null,clientId:null};default:throw Error(`Invalid token type: ${e}`)}}(i,{reason:n,message:s,headers:r}),headers:r,token:null})}var tL=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(x.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(x.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(x.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},tJ=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},tH=(...e)=>new tJ(...e),tF=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(x.Headers.ForwardedProto),n=e.headers.get(x.Headers.ForwardedHost),s=e.headers.get(x.Headers.Host),i=t.protocol,o=this.getFirstValueFromHeader(n)??s,a=this.getFirstValueFromHeader(r)??i?.replace(/[:/]/,""),l=o&&a?`${a}://${o}`:t.origin;return l===t.origin?tH(t):tH(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,w.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},tK=(...e)=>e[0]instanceof tF?e[0]:new tF(...e),t$=e=>e.split(";")[0]?.split("=")[0],tW=e=>e.split(";")[0]?.split("=")[1],tB={},tG=0;function tV(e,t=!0){tB[e.kid]=e,tG=t?Date.now():-1}var tX="local";function tQ(e){if(!tB[tX]){if(!e)throw new i.zF({action:i.z.SetClerkJWTKey,message:"Missing local JWK.",reason:i.jn.LocalJWKMissing});tV({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return tB[tX]}async function tY({secretKey:e,apiUrl:t=A,apiVersion:r="v1",kid:s,skipJwksCache:o}){if(o||function(){if(-1===tG)return!1;let e=Date.now()-tG>=3e5;return e&&(tB={}),e}()||!tB[s]){if(!e)throw new i.zF({action:i.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:i.jn.RemoteJWKFailedToLoad});let{keys:s}=await (0,n.L5)(()=>tZ(t,e,r));if(!s||!s.length)throw new i.zF({action:i.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:i.jn.RemoteJWKFailedToLoad});s.forEach(e=>tV(e))}let a=tB[s];if(!a){let e=Object.values(tB).map(e=>e.kid).sort().join(", ");throw new i.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${i.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${s}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:i.jn.JWKKidMismatch})}return a}async function tZ(e,t,r){if(!t)throw new i.zF({action:i.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:i.jn.RemoteJWKFailedToLoad});let n=new URL(e);n.pathname=D(n.pathname,r,"/jwks");let o=await s.fA.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":R,"Content-Type":"application/json","User-Agent":O}});if(!o.ok){let e=await o.json(),t=t0(e?.errors,i.qu.InvalidSecretKey);if(t){let e=i.jn.InvalidSecretKey;throw new i.zF({action:i.z.ContactSupport,message:t.message,reason:e})}throw new i.zF({action:i.z.ContactSupport,message:`Error loading Clerk JWKS from ${n.href} with code=${o.status}`,reason:i.jn.RemoteJWKFailedToLoad})}return o.json()}var t0=(e,t)=>e?e.find(e=>e.code===t):null;async function t1(e,t){let{data:r,errors:n}=(0,s.iU)(e);if(n)return{errors:n};let{header:o}=r,{kid:a}=o;try{let r;if(t.jwtKey)r=tQ(t.jwtKey);else{if(!t.secretKey)return{errors:[new i.zF({action:i.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:i.jn.JWKFailedToResolve})]};r=await tY({...t,kid:a})}return await (0,s.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}function t2(e,t,r){if((0,C.$R)(t)){let n,s;switch(t.status){case 401:n=i.h5.InvalidSecretKey,s=t.errors[0]?.message||"Invalid secret key";break;case 404:n=i.h5.TokenInvalid,s=r;break;default:n=i.h5.UnexpectedError,s="Unexpected error"}return{data:void 0,tokenType:e,errors:[new i.sM({message:s,code:n,status:t.status})]}}return{data:void 0,tokenType:e,errors:[new i.sM({message:"Unexpected error",code:i.h5.UnexpectedError,status:t.status})]}}async function t3(e,t){try{let r=tT(t);return{data:await r.machineTokens.verifySecret(e),tokenType:tA.MachineToken,errors:void 0}}catch(e){return t2(tA.MachineToken,e,"Machine token not found")}}async function t7(e,t){try{let r=tT(t);return{data:await r.idPOAuthAccessToken.verifyAccessToken(e),tokenType:tA.OAuthToken,errors:void 0}}catch(e){return t2(tA.OAuthToken,e,"OAuth token not found")}}async function t8(e,t){try{let r=tT(t);return{data:await r.apiKeys.verifySecret(e),tokenType:tA.ApiKey,errors:void 0}}catch(e){return t2(tA.ApiKey,e,"API key not found")}}async function t9(e,t){if(e.startsWith("mt_"))return t3(e,t);if(e.startsWith(tO))return t7(e,t);if(e.startsWith("ak_"))return t8(e,t);throw Error("Unknown machine token type")}async function t5(e,{key:t}){let{data:r,errors:n}=(0,s.iU)(e);if(n)throw n[0];let{header:o,payload:a}=r,{typ:l,alg:c}=o;(0,s.qf)(l),(0,s.l3)(c);let{data:d,errors:u}=await (0,s.nk)(r,t);if(u)throw new i.zF({reason:i.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${u[0]}`});if(!d)throw new i.zF({reason:i.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return a}async function t4(e,t){let r,{secretKey:n,apiUrl:o,apiVersion:a,jwksCacheTtlInMs:l,jwtKey:c,skipJwksCache:d}=t,{data:u,errors:h}=(0,s.iU)(e);if(h)throw h[0];let{kid:p}=u.header;if(c)r=tQ(c);else if(n)r=await tY({secretKey:n,apiUrl:o,apiVersion:a,kid:p,jwksCacheTtlInMs:l,skipJwksCache:d});else throw new i.zF({action:i.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:i.jn.JWKFailedToResolve});return await t5(e,{key:r})}var t6=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.startsWith("http")?this.authenticateContext.frontendApi:`https://${this.authenticateContext.frontendApi}`,n=new URL("v1/client/handshake",r=r.replace(/\/+$/,"")+"/");n.searchParams.append("redirect_url",t?.href||""),n.searchParams.append("__clerk_api_version",R),n.searchParams.append(x.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),n.searchParams.append(x.QueryParameters.HandshakeReason,e),n.searchParams.append(x.QueryParameters.HandshakeFormat,"nonce"),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&n.searchParams.append(x.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let s=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return s&&this.getOrganizationSyncQueryParams(s).forEach((e,t)=>{n.searchParams.append(t,e)}),new Headers({[x.Headers.Location]:n.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await t4(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),t$(t).startsWith(x.Cookies.Session)&&(r=tW(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(x.QueryParameters.Handshake),t.searchParams.delete(x.QueryParameters.HandshakeHelp),e.append(x.Headers.Location,t.toString()),e.set(x.Headers.CacheControl,"no-store")}if(""===r)return tD({tokenType:tA.SessionToken,authenticateContext:this.authenticateContext,reason:tN.SessionTokenMissing,message:"",headers:e});let{data:n,errors:[s]=[]}=await t1(r,this.authenticateContext);if(n)return tM({tokenType:tA.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:n,headers:e,token:r});if("development"===this.authenticateContext.instanceType&&(s?.reason===i.jn.TokenExpired||s?.reason===i.jn.TokenNotActiveYet||s?.reason===i.jn.TokenIatInTheFuture)){let t=new i.zF({action:s.action,message:s.message,reason:s.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:n,errors:[o]=[]}=await t1(r,{...this.authenticateContext,clockSkewInMs:864e5});if(n)return tM({tokenType:tA.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:n,headers:e,token:r});throw Error(o?.message||"Clerk: Handshake retry failed.")}throw Error(s?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===i.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=x.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(x.QueryParameters.DevBrowser),t.searchParams.delete(x.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},re=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,n,s,i,o,a,l;return r=void 0,n=[],s=function e(t,r,n){var s;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,s=r.exec(e.source);s;)t.push({name:s[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),s=r.exec(e.source);return e}(t,r):Array.isArray(t)?(s=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(s.join("|"),")"),T(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,s=void 0!==n&&n,i=r.start,o=r.end,a=r.encode,l=void 0===a?function(e){return e}:a,c=r.delimiter,d=r.endsWith,u="[".concat(E(void 0===d?"":d),"]|$"),h="[".concat(E(void 0===c?"/#?":c),"]"),p=void 0===i||i?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=E(l(m));else{var g=E(l(m.prefix)),y=E(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var k="*"===m.modifier?"?":"";p+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(k)}else p+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));p+="(".concat(m.pattern,")").concat(m.modifier)}else p+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===o||o)s||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(u,")"):"$";else{var b=e[e.length-1],_="string"==typeof b?h.indexOf(b[b.length-1])>-1:void 0===b;s||(p+="(?:".concat(h,"(?=").concat(u,"))?")),_||(p+="(?=".concat(h,"|").concat(u,")"))}return new RegExp(p,T(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var s="",i=r+1;i<e.length;){var o=e.charCodeAt(i);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){s+=e[i++];continue}break}if(!s)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:s}),r=i;continue}if("("===n){var a=1,l="",i=r+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '.concat(i));for(;i<e.length;){if("\\"===e[i]){l+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--a){i++;break}}else if("("===e[i]&&(a++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at ".concat(i));l+=e[i++]}if(a)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=i;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,s=void 0===n?"./":n,i=t.delimiter,o=void 0===i?"/#?":i,a=[],l=0,c=0,d="",u=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=u(e);if(void 0!==t)return t;var n=r[c],s=n.type,i=n.index;throw TypeError("Unexpected ".concat(s," at ").concat(i,", expected ").concat(e))},p=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<o.length;t++){var r=o[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=a[a.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(E(o),"]+?"):"(?:(?!".concat(E(r),")[^").concat(E(o),"])+?")};c<r.length;){var g=u("CHAR"),y=u("NAME"),k=u("PATTERN");if(y||k){var b=g||"";-1===s.indexOf(b)&&(d+=b,b=""),d&&(a.push(d),d=""),a.push({name:y||l++,prefix:b,suffix:"",pattern:k||m(b),modifier:u("MODIFIER")||""});continue}var _=g||u("ESCAPED_CHAR");if(_){d+=_;continue}if(d&&(a.push(d),d=""),u("OPEN")){var b=p(),v=u("NAME")||"",C=u("PATTERN")||"",S=p();h("CLOSE"),a.push({name:v||(C?l++:""),pattern:v&&!C?m(b):C,prefix:b,suffix:S,modifier:u("MODIFIER")||""});continue}h("END")}return a}(t,n),r,n)}(e,n,r),i=n,o=r,void 0===o&&(o={}),a=o.decode,l=void 0===a?function(e){return e}:a,function(e){var t=s.exec(e);if(!t)return!1;for(var r=t[0],n=t.index,o=Object.create(null),a=1;a<t.length;a++)!function(e){if(void 0!==t[e]){var r=i[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):o[r.name]=l(t[e],r)}}(a);return{path:r,index:n,params:o}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},rt={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};function rr(e,t,r){return tx(e,t)?null:tD({tokenType:e,authenticateContext:r,reason:tN.TokenTypeMismatch})}var rn=async(e,t)=>{let r=await N(tK(e),t);q(r.secretKey);let o=t.acceptsToken??tA.SessionToken;if(r.isSatellite){var a=r.signInUrl,l=r.secretKey;if(!a&&(0,n.Ve)(l))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let c=new re(t.organizationSyncOptions),d=new t6(r,{organizationSyncOptions:t.organizationSyncOptions},c);async function u(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:rt.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:i}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:rt.MissingSessionToken}}};if(!i)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:rt.MissingRefreshToken}}};let{data:o,errors:a}=(0,s.iU)(n);if(!o||a)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:rt.ExpiredSessionTokenDecodeFailed,errors:a}}};if(!o?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:rt.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(o.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:n||"",refresh_token:i||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:rt.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:rt.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function h(e){let{data:t,error:r}=await u(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,s="";t.forEach(e=>{n.append("Set-Cookie",e),t$(e).startsWith(x.Cookies.Session)&&(s=tW(e))});let{data:i,errors:o}=await t1(s,e);return o?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:rt.InvalidSessionToken,errors:o}}}:{data:{jwtPayload:i,sessionToken:s,headers:n},error:null}}function p(e,t,r,n){if(!d.isRequestEligibleForHandshake())return tD({tokenType:tA.SessionToken,authenticateContext:e,reason:t,message:r});let s=n??d.buildRedirectToHandshake(t);return(s.get(x.Headers.Location)&&s.set(x.Headers.CacheControl,"no-store"),d.checkAndTrackRedirectLoop(s))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),tD({tokenType:tA.SessionToken,authenticateContext:e,reason:t,message:r})):function(e,t,r="",n){return tL({status:tz.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:tA.SessionToken,toAuth:()=>null,headers:n,token:null})}(e,t,r,s)}async function f(){let{tokenInHeader:e}=r;try{let{data:t,errors:n}=await t1(e,r);if(n)throw n[0];return tM({tokenType:tA.SessionToken,authenticateContext:r,sessionClaims:t,headers:new Headers,token:e})}catch(e){return g(e,"header")}}async function m(){let e=r.clientUat,t=!!r.sessionTokenInCookie,n=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await d.resolveHandshake()}catch(e){e instanceof i.zF&&"development"===r.instanceType?d.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(x.QueryParameters.DevBrowser))return p(r,tN.DevBrowserSync,"");let o=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&o)return p(r,tN.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&o&&!r.clerkUrl.searchParams.has(x.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(x.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[x.Headers.Location]:e.toString()});return p(r,tN.SatelliteCookieNeedsSyncing,"",t)}let a=new URL(r.clerkUrl).searchParams.get(x.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&a){let e=new URL(a);r.devBrowserToken&&e.searchParams.append(x.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(x.QueryParameters.ClerkSynced,"true");let t=new Headers({[x.Headers.Location]:e.toString()});return p(r,tN.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!n)return p(r,tN.DevBrowserMissing,"");if(!e&&!t)return tD({tokenType:tA.SessionToken,authenticateContext:r,reason:tN.SessionTokenAndUATMissing});if(!e&&t)return p(r,tN.SessionTokenWithoutClientUAT,"");if(e&&!t)return p(r,tN.ClientUATWithoutSessionToken,"");let{data:l,errors:u}=(0,s.iU)(r.sessionTokenInCookie);if(u)return g(u[0],"cookie");if(l.payload.iat<r.clientUat)return p(r,tN.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await t1(r.sessionTokenInCookie,r);if(t)throw t[0];let n=tM({tokenType:tA.SessionToken,authenticateContext:r,sessionClaims:e,headers:new Headers,token:r.sessionTokenInCookie});if(!r.isSatellite&&"document"===r.secFetchDest&&r.isCrossOriginReferrer())return p(r,tN.PrimaryDomainCrossOriginSync,"Cross-origin request from satellite domain requires handshake");let s=n.toAuth();if(s.userId){let e=function(e,t){let r=c.findTarget(e.clerkUrl);if(!r)return null;let n=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(n=!0),r.organizationId&&r.organizationId!==t.orgId&&(n=!0)),"personalAccount"===r.type&&t.orgId&&(n=!0),!n)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let s=p(e,tN.ActiveOrganizationMismatch,"");return"handshake"!==s.status?null:s}(r,s);if(e)return e}return n}catch(e){return g(e,"cookie")}}async function g(t,n){let s;if(!(t instanceof i.zF))return tD({tokenType:tA.SessionToken,authenticateContext:r,reason:tN.UnexpectedError});if(t.reason===i.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await h(r);if(e)return tM({tokenType:tA.SessionToken,authenticateContext:r,sessionClaims:e.jwtPayload,headers:e.headers,token:e.sessionToken});s=t?.cause?.reason?t.cause.reason:rt.UnexpectedSDKError}else s="GET"!==e.method?rt.NonEligibleNonGet:r.refreshTokenInCookie?null:rt.NonEligibleNoCookie;return(t.tokenCarrier=n,[i.jn.TokenExpired,i.jn.TokenNotActiveYet,i.jn.TokenIatInTheFuture].includes(t.reason))?p(r,ri({tokenError:t.reason,refreshError:s}),t.getFullMessage()):tD({tokenType:tA.SessionToken,authenticateContext:r,reason:t.reason,message:t.getFullMessage()})}function y(e,t){return t instanceof i.sM?tD({tokenType:e,authenticateContext:r,reason:t.code,message:t.getFullMessage()}):tD({tokenType:e,authenticateContext:r,reason:tN.UnexpectedError})}async function k(){let{tokenInHeader:e}=r;if(!e)return g(Error("Missing token in header"),"header");if(!tI(e))return tD({tokenType:o,authenticateContext:r,reason:tN.TokenTypeMismatch,message:""});let t=rr(tP(e),o,r);if(t)return t;let{data:n,tokenType:s,errors:i}=await t9(e,r);return i?y(s,i[0]):tM({tokenType:s,authenticateContext:r,machineData:n,token:e})}async function b(){let{tokenInHeader:e}=r;if(!e)return g(Error("Missing token in header"),"header");if(tI(e)){let t=rr(tP(e),o,r);if(t)return t;let{data:n,tokenType:s,errors:i}=await t9(e,r);return i?y(s,i[0]):tM({tokenType:s,authenticateContext:r,machineData:n,token:e})}let{data:t,errors:n}=await t1(e,r);return n?g(n[0],"header"):tM({tokenType:tA.SessionToken,authenticateContext:r,sessionClaims:t,token:e})}return Array.isArray(o)&&!function(e,t){let r=null,{tokenInHeader:n}=t;return n&&(r=tI(n)?tP(n):tA.SessionToken),tx(r??tA.SessionToken,e)}(o,r)?function(){let e={isAuthenticated:!1,tokenType:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:()=>({})};return tL({status:tz.SignedOut,reason:tN.TokenTypeMismatch,message:"",proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",signInUrl:"",signUpUrl:"",afterSignInUrl:"",afterSignUpUrl:"",isSignedIn:!1,isAuthenticated:!1,tokenType:null,toAuth:()=>e,headers:new Headers,token:null})}():r.tokenInHeader?"any"===o?b():o===tA.SessionToken?f():k():o===tA.OAuthToken||o===tA.ApiKey||o===tA.MachineToken?tD({tokenType:o,authenticateContext:r,reason:"No token in header"}):m()},rs=e=>{let{isSignedIn:t,isAuthenticated:r,proxyUrl:n,reason:s,message:i,publishableKey:o,isSatellite:a,domain:l}=e;return{isSignedIn:t,isAuthenticated:r,proxyUrl:n,reason:s,message:i,publishableKey:o,isSatellite:a,domain:l}},ri=({tokenError:e,refreshError:t})=>{switch(e){case i.jn.TokenExpired:return`${tN.SessionTokenExpired}-refresh-${t}`;case i.jn.TokenNotActiveYet:return tN.SessionTokenNBF;case i.jn.TokenIatInTheFuture:return tN.SessionTokenIatInTheFuture;default:return tN.UnexpectedError}},ro={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function ra(e){let t=U(ro,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:s,apiVersion:i}=t,o=U(t,n);return rn(e,{...n,...o,apiUrl:s,apiVersion:i,apiClient:r})},debugRequestState:rs}}},45951:(e,t,r)=>{r.d(t,{b_:()=>n.b_});var n=r(3497);r(8777)},48976:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49343:(e,t)=>{t.qg=function(e,t){let o=new r,a=e.length;if(a<2)return o;let l=t?.decode||i,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),i=-1===r?a:r;if(t>i){c=e.lastIndexOf(";",t-1)+1;continue}let d=n(e,c,t),u=s(e,t,d),h=e.slice(d,u);if(void 0===o[h]){let r=n(e,t+1,i),a=s(e,i,r),c=l(e.slice(r,a));o[h]=c}c=i+1}while(c<a);return o},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function s(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function i(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},53493:(e,t,r)=>{r.d(t,{RZ:()=>d,qS:()=>h,ky:()=>p,mC:()=>u,q5:()=>l});var n=r(42725),s=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,i=r(31243),o="pk_live_";function a(e){if(!e.endsWith("$"))return!1;let t=e.slice(0,-1);return!t.includes("$")&&t.includes(".")}function l(e,t={}){let r;if(!(e=e||"")||!c(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!c(e))throw Error("Publishable key not valid.");return null}let s=e.startsWith(o)?"production":"development";try{r=(0,n.y)(e.split("_")[2])}catch{if(t.fatal)throw Error("Publishable key not valid: Failed to decode key.");return null}if(!a(r)){if(t.fatal)throw Error("Publishable key not valid: Decoded key has invalid format.");return null}let i=r.slice(0,-1);return t.proxyUrl?i=t.proxyUrl:"development"!==s&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:s,frontendApi:i}}function c(e=""){try{if(!(e.startsWith(o)||e.startsWith("pk_test_")))return!1;let t=e.split("_");if(3!==t.length)return!1;let r=t[2];if(!r)return!1;let s=(0,n.y)(r);return a(s)}catch{return!1}}function d(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=i.gE.some(e=>r.endsWith(e)),e.set(r,n)),n}}}function u(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function h(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return s(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var p=(e,t)=>`${e}_${t}`},58758:(e,t,r)=>{r.d(t,{__experimental_PaymentElement:()=>n.cl,__experimental_PaymentElementProvider:()=>n.Tn,__experimental_usePaymentElement:()=>n.Jl,useAuth:()=>s.d,useClerk:()=>n.ho,useEmailLink:()=>n.ui,useOrganization:()=>n.Z5,useOrganizationList:()=>n.D_,useReverification:()=>n.Wp,useSession:()=>n.wV,useSessionList:()=>n.g7,useSignIn:()=>n.go,useSignUp:()=>n.yC,useUser:()=>n.Jd});var n=r(56499);r(77681);var s=r(41330)},62278:(e,t,r)=>{r.d(t,{APIKeys:()=>s,CreateOrganization:()=>i,GoogleOneTap:()=>o,OrganizationList:()=>a,OrganizationProfile:()=>l,OrganizationSwitcher:()=>c,PricingTable:()=>d,SignIn:()=>u,SignInButton:()=>h,SignInWithMetamaskButton:()=>p,SignOutButton:()=>f,SignUp:()=>m,SignUpButton:()=>g,UserButton:()=>y,UserProfile:()=>k,Waitlist:()=>b});var n=r(12907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call APIKeys() from the server but APIKeys is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","APIKeys"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call CreateOrganization() from the server but CreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","CreateOrganization"),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call GoogleOneTap() from the server but GoogleOneTap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","GoogleOneTap"),a=(0,n.registerClientReference)(function(){throw Error("Attempted to call OrganizationList() from the server but OrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","OrganizationList"),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call OrganizationProfile() from the server but OrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","OrganizationProfile"),c=(0,n.registerClientReference)(function(){throw Error("Attempted to call OrganizationSwitcher() from the server but OrganizationSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","OrganizationSwitcher"),d=(0,n.registerClientReference)(function(){throw Error("Attempted to call PricingTable() from the server but PricingTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","PricingTable"),u=(0,n.registerClientReference)(function(){throw Error("Attempted to call SignIn() from the server but SignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","SignIn"),h=(0,n.registerClientReference)(function(){throw Error("Attempted to call SignInButton() from the server but SignInButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","SignInButton"),p=(0,n.registerClientReference)(function(){throw Error("Attempted to call SignInWithMetamaskButton() from the server but SignInWithMetamaskButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","SignInWithMetamaskButton"),f=(0,n.registerClientReference)(function(){throw Error("Attempted to call SignOutButton() from the server but SignOutButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","SignOutButton"),m=(0,n.registerClientReference)(function(){throw Error("Attempted to call SignUp() from the server but SignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","SignUp"),g=(0,n.registerClientReference)(function(){throw Error("Attempted to call SignUpButton() from the server but SignUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","SignUpButton"),y=(0,n.registerClientReference)(function(){throw Error("Attempted to call UserButton() from the server but UserButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","UserButton"),k=(0,n.registerClientReference)(function(){throw Error("Attempted to call UserProfile() from the server but UserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","UserProfile"),b=(0,n.registerClientReference)(function(){throw Error("Attempted to call Waitlist() from the server but Waitlist is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","Waitlist")},62765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return s}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function s(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62808:(e,t,r)=>{r.r(t),r.d(t,{KeylessCookieSync:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call KeylessCookieSync() from the server but KeylessCookieSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","KeylessCookieSync")},63441:(e,t,r)=>{r.r(t),r.d(t,{ClientClerkProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ClientClerkProvider() from the server but ClientClerkProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","ClientClerkProvider")},67218:(e,t,r)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=r(12907)},70899:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,a.isDynamicPostpone)(t)||(0,s.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),s=r(52637),i=r(51846),o=r(31162),a=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73913:(e,t,r)=>{let n=r(63033),s=r(29294),i=r(84971),o=r(76926),a=r(80023),l=r(98479);function c(){let e=s.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return d(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return d(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return h(null);default:return t}}function d(e,t){let r,n=u.get(c);return n||(r=h(e),u.set(e,r),r)}let u=new WeakMap;function h(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let f=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=s.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new l.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},74722:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return o}});let n=r(85531),s=r(35499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,s.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},76463:(e,t,r)=>{let n=r(82343),{snakeCase:s}=r(83557),i={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==i))throw Error("obj must be array of plain objects")}else if(e.constructor!==i)throw Error("obj must be an plain object");return n(e,function(e,r){var n,i,o,a,l;return[(n=t.exclude,i=e,n.some(function(e){return"string"==typeof e?e===i:e.test(i)}))?e:s(e,t.parsingOptions),r,(o=e,a=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(o,a)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},79130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return m},encryptActionBoundArgs:function(){return f}}),r(34822);let n=r(12907),s=r(52513),i=r(77855),o=r(82602),a=r(63033),l=r(84971),c=function(e){return e&&e.__esModule?e:{default:e}}(r(61120)),d=new TextEncoder,u=new TextDecoder;async function h(e,t){let r=await (0,o.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(t),s=n.slice(0,16),i=n.slice(16),a=u.decode(await (0,o.decrypt)(r,(0,o.stringToUint8Array)(s),(0,o.stringToUint8Array)(i)));if(!a.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return a.slice(e.length)}async function p(e,t){let r=await (0,o.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);a.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let s=(0,o.arrayBufferToString)(n.buffer),i=await (0,o.encrypt)(r,n,d.encode(e+t));return btoa(s+(0,o.arrayBufferToString)(i))}let f=c.default.cache(async function e(t,...r){let{clientModules:s}=(0,o.getClientReferenceManifestForRsc)(),c=Error();Error.captureStackTrace(c,e);let d=!1,u=a.workUnitAsyncStorage.getStore(),h=(null==u?void 0:u.type)==="prerender"?(0,l.createHangingInputAbortSignal)(u):void 0,f=await (0,i.streamToString)((0,n.renderToReadableStream)(r,s,{signal:h,onError(e){(null==h||!h.aborted)&&(d||(d=!0,c.message=e instanceof Error?e.message:String(e)))}}),h);if(d)throw c;if(!u)return p(t,f);let m=(0,a.getPrerenderResumeDataCache)(u),g=(0,a.getRenderResumeDataCache)(u),y=t+f,k=(null==m?void 0:m.encryptedBoundArgs.get(y))??(null==g?void 0:g.encryptedBoundArgs.get(y));if(k)return k;let b="prerender"===u.type?u.cacheSignal:void 0;null==b||b.beginRead();let _=await p(t,f);return null==b||b.endRead(),null==m||m.encryptedBoundArgs.set(y,_),_});async function m(e,t){let r,n=await t,i=a.workUnitAsyncStorage.getStore();if(i){let t="prerender"===i.type?i.cacheSignal:void 0,s=(0,a.getPrerenderResumeDataCache)(i),o=(0,a.getRenderResumeDataCache)(i);(r=(null==s?void 0:s.decryptedBoundArgs.get(n))??(null==o?void 0:o.decryptedBoundArgs.get(n)))||(null==t||t.beginRead(),r=await h(e,n),null==t||t.endRead(),null==s||s.decryptedBoundArgs.set(n,r))}else r=await h(e,n);let{edgeRscModuleMapping:l,rscModuleMapping:c}=(0,o.getClientReferenceManifestForRsc)();return await (0,s.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(d.encode(r)),(null==i?void 0:i.type)==="prerender"?i.renderSignal.aborted?e.close():i.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:c,serverModuleMap:(0,o.getServerModuleMap)()}})}},80444:(e,t,r)=>{r.d(t,{zz:()=>s});var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let s=e(r.toString());s!==r&&(n[s]=n[r],delete n[r]),"object"==typeof n[s]&&(n[s]=t(n[s]))}return n};return t};function s(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},81163:(e,t,r)=>{r.d(t,{ai:()=>_,at:()=>v,ot:()=>b});var n=r(67218);r(79130);var s=r(99933),i=r(86280);r(73913);var o=r(97576);let a=(0,r(1689)._r)({packageName:"@clerk/nextjs"});var l=r(43037);r(90052),r(15614),r(12618);var c=r(45951),d=r(94048);let u={rE:"15.3.5"},h=!(u.rE.startsWith("13.")||u.rE.startsWith("14.0"))&&(0,c.b_)()&&!d.ev,p="__clerk_keys_";async function f(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function m(){let e=process.env.PWD;if(!e)return`${p}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await f(t);return`${p}${r}`}async function g(e){let t;if(!h)return;let r=await m();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}var y=r(17478);let k={secure:!1,httpOnly:!1,sameSite:"lax"};async function b(e){let{claimUrl:t,publishableKey:r,secretKey:n,returnUrl:a}=e,c=await (0,s.U)(),d=new Request("https://placeholder.com",{headers:await (0,i.b)()}),u=await g(e=>{var t;return null==(t=c.get(e))?void 0:t.value}),h=(null==u?void 0:u.publishableKey)===r,p=(null==u?void 0:u.secretKey)===n;if(!h||!p){var f,y,b,_,v,C;if(c.set(await m(),JSON.stringify({claimUrl:t,publishableKey:r,secretKey:n}),k),f="AuthStatus",((y=l.AA.Attributes[f])in d?d[y]:void 0)||(b=d,_=l.AA.Headers[f],function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(b)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(b)?b.headers.get(_):b.headers[_]||b.headers[_.toLowerCase()]||(null==(C=null==(v=b.socket)?void 0:v._httpMessage)?void 0:C.getHeader(_))))return void(0,o.redirect)(`/clerk-sync-keyless?returnUrl=${a}`,o.RedirectType.replace)}}async function _(){if(!h)return null;let e=await r.e(872).then(r.bind(r,22872)).then(e=>e.createOrReadKeyless()).catch(()=>null);if(!e)return a.throwMissingPublishableKeyError(),null;let{clerkDevelopmentCache:t,createKeylessModeMessage:n}=await r.e(935).then(r.bind(r,85935));null==t||t.log({cacheKey:e.publishableKey,msg:n(e)});let{claimUrl:i,publishableKey:o,secretKey:l,apiKeysUrl:c}=e;return(await (0,s.U)()).set(await m(),JSON.stringify({claimUrl:i,publishableKey:o,secretKey:l}),k),{claimUrl:i,publishableKey:o,apiKeysUrl:c}}async function v(){h&&await r.e(872).then(r.bind(r,22872)).then(e=>e.removeKeyless()).catch(()=>{})}(0,y.D)([_,v,b]),(0,n.A)(_,"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a",null),(0,n.A)(v,"7f126a7a969b1105c221f5710d89250ac20d205bab",null),(0,n.A)(b,"7fc968e88d4d7d85319b0c692c8eba3563d91942c2",null)},82343:e=>{let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),s=(e,t,i,o=new WeakMap)=>{if(i={deep:!1,target:{},...i},o.has(e))return o.get(e);o.set(e,i.target);let{target:a}=i;delete i.target;let l=e=>e.map(e=>n(e)?s(e,t,i,o):e);if(Array.isArray(e))return l(e);for(let[c,d]of Object.entries(e)){let u=t(c,d,e);if(u===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=u;"__proto__"!==h&&(i.deep&&f&&n(p)&&(p=Array.isArray(p)?l(p):s(p,t,i,o)),a[h]=p)}return a};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return s(e,r,n)},e.exports.mapObjectSkip=r},82602:(e,t,r)=>{let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return a},decrypt:function(){return d},encrypt:function(){return c},getActionEncryptionKey:function(){return m},getClientReferenceManifestForRsc:function(){return f},getServerModuleMap:function(){return p},setReferenceManifestsSingleton:function(){return h},stringToUint8Array:function(){return l}});let s=r(71617),i=r(74722),o=r(29294);function a(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let n="";for(let e=0;e<r;e++)n+=String.fromCharCode(t[e]);return n}function l(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r}function c(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function d(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}let u=Symbol.for("next.server.action-manifests");function h({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:n}){var s;let o=null==(s=globalThis[u])?void 0:s.clientReferenceManifestsPerPage;globalThis[u]={clientReferenceManifestsPerPage:{...o,[(0,i.normalizeAppPath)(e)]:t},serverActionsManifest:r,serverModuleMap:n}}function p(){let e=globalThis[u];if(!e)throw Object.defineProperty(new s.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function f(){let e=globalThis[u];if(!e)throw Object.defineProperty(new s.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,r=o.workAsyncStorage.getStore();if(!r){var n=t;let e=Object.values(n),r={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)r.clientModules={...r.clientModules,...t.clientModules},r.edgeRscModuleMapping={...r.edgeRscModuleMapping,...t.edgeRscModuleMapping},r.rscModuleMapping={...r.rscModuleMapping,...t.rscModuleMapping};return r}let i=t[r.route];if(!i)throw Object.defineProperty(new s.InvariantError(`Missing Client Reference Manifest for ${r.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return i}async function m(){if(n)return n;let e=globalThis[u];if(!e)throw Object.defineProperty(new s.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new s.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",l(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},83557:(e,t,r)=>{r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)};Object.create;function s(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var i=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],o=/[^A-Z0-9]+/gi;function a(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,c=t.delimiter,d=a(a(e,void 0===r?i:r,"$1\0$2"),void 0===n?o:n,"\0"),u=0,h=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(u,h).split("\0").map(void 0===l?s:l).join(void 0===c?" ":c)}(e,n({delimiter:"."},r))}},85531:(e,t)=>{function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},85850:(e,t,r)=>{r.r(t),r.d(t,{KeylessCookieSync:()=>s});var n=r(16189);function s(e){var t;return null==(t=(0,n.useSelectedLayoutSegments)()[0])||t.startsWith("/_not-found"),e.children}r(43210),r(61389)},86280:(e,t,r)=>{Object.defineProperty(t,"b",{enumerable:!0,get:function(){return u}});let n=r(92584),s=r(29294),i=r(63033),o=r(84971),a=r(80023),l=r(68388),c=r(76926),d=(r(44523),r(8719));function u(){let e=s.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,c=t;let n=h.get(c);if(n)return n;let s=(0,l.makeHangingPromise)(c.renderSignal,"`headers()`");return h.set(c,s),Object.defineProperties(s,{append:{value:function(){let e=`\`headers().append(${f(arguments[0])}, ...)\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},delete:{value:function(){let e=`\`headers().delete(${f(arguments[0])})\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},get:{value:function(){let e=`\`headers().get(${f(arguments[0])})\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},has:{value:function(){let e=`\`headers().has(${f(arguments[0])})\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},set:{value:function(){let e=`\`headers().set(${f(arguments[0])}, ...)\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},keys:{value:function(){let e="`headers().keys()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},values:{value:function(){let e="`headers().values()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},entries:{value:function(){let e="`headers().entries()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}}}),s}else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}let h=new WeakMap;function p(e){let t=h.get(e);if(t)return t;let r=Promise.resolve(e);return h.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function f(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},86897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return l},redirect:function(){return a}});let n=r(52836),s=r(49026),i=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(s.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=s.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function a(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?s.RedirectType.push:s.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=s.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,s.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90052:(e,t,r)=>{r.d(t,{io:()=>u,qS:()=>l.qS,ky:()=>l.ky,Ve:()=>l.mC,q5:()=>l.q5,L5:()=>a}),r(8777);var n={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},s=async e=>new Promise(t=>setTimeout(t,e)),i=(e,t)=>t?e*(1+Math.random()):e,o=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=i(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await s(r()),t++}},a=async(e,t={})=>{let r=0,{shouldRetry:a,initialDelay:l,maxDelayBetweenRetries:c,factor:d,retryImmediately:u,jitter:h}={...n,...t},p=o({initialDelay:l,maxDelayBetweenRetries:c,factor:d,jitter:h});for(;;)try{return await e()}catch(e){if(!a(e,++r))throw e;u&&1===r?await s(i(100,h)):await p()}},l=r(53493),c=r(3497),d=new Set,u=(e,t,r)=>{let n=(0,c.MC)()||(0,c.Fj)(),s=r??e;d.has(s)||n||(d.add(s),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};(0,r(1689)._r)({packageName:"@clerk/backend"});var{isDevOrStagingUrl:h}=(0,l.RZ)()},92584:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return s}});let n=r(43763);class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,s);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,s)},set(t,r,s,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,s,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,a??r,s,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},93821:(e,t,r)=>{r.d(t,{AuthenticateWithRedirectCallback:()=>n.B$,ClerkDegraded:()=>n.wF,ClerkFailed:()=>n.lT,ClerkLoaded:()=>n.z0,ClerkLoading:()=>n.A0,RedirectToCreateOrganization:()=>n.rm,RedirectToOrganizationProfile:()=>n.m2,RedirectToSignIn:()=>n.W5,RedirectToSignUp:()=>n.mO,RedirectToUserProfile:()=>n.eG});var n=r(56499);r(41236)},94048:(e,t,r)=>{r.d(t,{H$:()=>c,mG:()=>o,V2:()=>d,fS:()=>h,ev:()=>g,Rg:()=>u,At:()=>l,tm:()=>p,rB:()=>a,Mh:()=>m,nN:()=>f});var n=r(53493),s=r(31243);r(8777);var i=r(80444);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let o=process.env.CLERK_API_VERSION||"v1",a=process.env.CLERK_SECRET_KEY||"",l="pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ";process.env.CLERK_ENCRYPTION_KEY;let c=process.env.CLERK_API_URL||(e=>{let t=(0,n.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&s.iM.some(e=>t?.endsWith(e))?s.FW:s.mG.some(e=>t?.endsWith(e))?s.Vc:s.ub.some(e=>t?.endsWith(e))?s.HG:s.FW})(l),d=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",u=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",h=(0,i.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1;process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL,process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL;let p={name:"@clerk/nextjs",version:"6.24.0",environment:"production"},f=(0,i.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),m=(0,i.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),g=(0,i.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},94069:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return h},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return l},appendMutableCookies:function(){return u},areCookiesMutableInCurrentPhase:function(){return f},getModifiedCookieValues:function(){return d},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return p}});let n=r(23158),s=r(43763),i=r(29294),o=r(63033);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new a}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function d(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=d(t);if(0===r.length)return!1;let s=new n.ResponseCookies(e),i=s.getAll();for(let e of r)s.set(e);for(let e of i)s.set(e);return!0}class h{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],a=new Set,l=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},d=new Proxy(r,{get(e,t,r){switch(t){case c:return o;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),d}finally{l()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),d}finally{l()}};default:return s.ReflectAdapter.get(e,t,r)}}});return d}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return s.ReflectAdapter.get(e,r,n)}}});return t}function f(e){return"action"===e.phase}function m(e){if(!f((0,o.getExpectedRequestStore)(e)))throw new a}function g(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},97576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return s.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return a.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),s=r(49026),i=r(62765),o=r(48976),a=r(70899),l=r(163);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99933:(e,t,r)=>{Object.defineProperty(t,"U",{enumerable:!0,get:function(){return h}});let n=r(94069),s=r(23158),i=r(29294),o=r(63033),a=r(84971),l=r(80023),c=r(68388),d=r(76926),u=(r(44523),r(8719));function h(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return f(n.RequestCookiesAdapter.seal(new s.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new l.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var d=t.route,h=r;let e=p.get(h);if(e)return e;let n=(0,c.makeHangingPromise)(h.renderSignal,"`cookies()`");return p.set(h,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},size:{get(){let e="`cookies().size`",t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${m(arguments[0])})\``;let t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${m(arguments[0])})\``;let t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${m(arguments[0])})\``;let t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${m(t)}, ...)\``:"`cookies().set(...)`"}let t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${m(arguments[0])})\``:`\`cookies().delete(${m(arguments[0])}, ...)\``;let t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},clear:{value:function(){let e="`cookies().clear()`",t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}},toString:{value:function(){let e="`cookies().toString()`",t=y(d,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(d,e,t,h)}}}),n}else"prerender-ppr"===r.type?(0,a.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,a.throwToInterruptStaticGeneration)(e,t,r);(0,a.trackDynamicDataInDynamicRender)(t,r)}let g=(0,o.getExpectedRequestStore)(e);return f((0,n.areCookiesMutableInCurrentPhase)(g)?g.userspaceMutableCookies:g.cookies)}let p=new WeakMap;function f(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):k.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function m(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let g=(0,d.createDedupedByCallsiteServerErrorLoggerDev)(y);function y(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function k(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}}};