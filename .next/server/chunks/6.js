"use strict";exports.id=6,exports.ids=[6],exports.modules={4431:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,n.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,s.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=r(37461),s=r(61794),n=r(97683),a=r(21351),o=r(75124),l=r(38248);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5970:(e,t,r)=>{function i(e){return"clerkError"in e}r.d(t,{LR:()=>n,_r:()=>o,$R:()=>i,u$:()=>s});function s(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn,plan:e?.meta?.plan}}}var n=class e extends Error{constructor(t,{data:r,status:i,clerkTraceId:n,retryAfter:a}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=i,this.message=t,this.clerkTraceId=n,this.retryAfter=a,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(s):[]}(r)}},a=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function o({packageName:e,customMessages:t}){let r=e,i={...a,...t};function s(e,t){if(!t)return`${r}: ${e}`;let i=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();i=i.replace(`{{${r[1]}}}`,e)}return`${r}: ${i}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(s(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(s(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(s(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(s(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(s(i.MissingClerkProvider,e))},throw(e){throw Error(s(e))}}}r(56270)},18389:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21351:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return n}});let i=r(32913),s=r(59311);function n(e){return(0,s.isRedirectError)(e)||(0,i.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24796:(e,t,r)=>{function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),r(32913).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32749:(e,t,r)=>{r.d(t,{zz:()=>s});var i=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let i={...r};for(let r of Object.keys(i)){let s=e(r.toString());s!==r&&(i[s]=i[r],delete i[r]),"object"==typeof i[s]&&(i[s]=t(i[s]))}return i};return t};function s(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}i(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),i(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},32913:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return s},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return n}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},i=new Set(Object.values(r)),s="NEXT_HTTP_ERROR_FALLBACK";function n(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===s&&i.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33781:(e,t,r)=>{r.d(t,{l3:()=>g,qf:()=>y,iU:()=>w,nk:()=>S,fA:()=>o,J0:()=>E});var i=r(58743),s=r(77598),n=r(55562);r(56270);var a=fetch.bind(globalThis),o={crypto:s.webcrypto,get fetch(){return a},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let i=e.length;for(;"="===e[i-1];)if(--i,!r.loose&&!((e.length-i)*t.bits&7))throw SyntaxError("Invalid padding");let s=new(r.out??Uint8Array)(i*t.bits/8|0),n=0,a=0,o=0;for(let r=0;r<i;++r){let i=t.codes[e[r]];if(void 0===i)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|i,(n+=t.bits)>=8&&(n-=8,s[o++]=255&a>>n)}if(n>=t.bits||255&a<<8-n)throw SyntaxError("Unexpected end of data");return s})(e,u,t)},u={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},c={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},d="RSASSA-PKCS1-v1_5",h={RS256:d,RS384:d,RS512:d},p=Object.keys(c),f=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),m=(e,t)=>{let r=[t].flat().filter(e=>!!e),s=[e].flat().filter(e=>!!e);if(r.length>0&&s.length>0){if("string"==typeof e){if(!r.includes(e))throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(f(e)&&!e.some(e=>r.includes(e)))throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},y=e=>{if(void 0!==e&&"JWT"!==e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},g=e=>{if(!p.includes(e))throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${p}.`})},k=e=>{if("string"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},_=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new i.zF({reason:i.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},b=(e,t)=>{if("number"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()<=r.getTime()-t)throw new i.zF({reason:i.jn.TokenExpired,message:`JWT is expired. Expiry date: ${s.toUTCString()}, Current date: ${r.toUTCString()}.`})},v=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()>r.getTime()+t)throw new i.zF({reason:i.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${s.toUTCString()}; Current date: ${r.toUTCString()};`})},T=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),s=new Date(0);if(s.setUTCSeconds(e),s.getTime()>r.getTime()+t)throw new i.zF({reason:i.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${s.toUTCString()}; Current date: ${r.toUTCString()};`})};async function S(e,t){let{header:r,signature:s,raw:a}=e,l=new TextEncoder().encode([a.header,a.payload].join(".")),u=function(e){let t=c[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${p.join(",")}.`);return{hash:{name:c[e]},name:h[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return o.crypto.subtle.importKey("jwk",e,t,!1,[r]);let i=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,n.y)(t),i=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)i[e]=r.charCodeAt(e);return i}(e),s="sign"===r?"pkcs8":"spki";return o.crypto.subtle.importKey(s,i,t,!1,[r])}(t,u,"verify");return{data:await o.crypto.subtle.verify(u.name,e,s,l)}}catch(e){return{errors:[new i.zF({reason:i.jn.TokenInvalidSignature,message:e?.message})]}}}function w(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new i.zF({reason:i.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,s,n]=t,a=new TextDecoder,o=JSON.parse(a.decode(l.parse(r,{loose:!0}))),u=JSON.parse(a.decode(l.parse(s,{loose:!0})));return{data:{header:o,payload:u,signature:l.parse(n,{loose:!0}),raw:{header:r,payload:s,signature:n,text:e}}}}async function E(e,t){let{audience:r,authorizedParties:s,clockSkewInMs:n,key:a}=t,o=n||5e3,{data:l,errors:u}=w(e);if(u)return{errors:u};let{header:c,payload:d}=l;try{let{typ:e,alg:t}=c;y(e),g(t);let{azp:i,sub:n,aud:a,iat:l,exp:u,nbf:h}=d;k(n),m([a],[r]),_(i,s),b(u,o),v(h,o),T(l,o)}catch(e){return{errors:[e]}}let{data:h,errors:p}=await S(l,a);return p?{errors:[new i.zF({action:i.z.EnsureClerkJWT,reason:i.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${p[0]}`})]}:h?{data:d}:{errors:[new i.zF({reason:i.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},35622:(e,t,r)=>{r.r(t),r.d(t,{snakeCase:()=>l});var i=function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)};Object.create;function s(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var n=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=i({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,i=t.stripRegexp,l=t.transform,u=t.delimiter,c=o(o(e,void 0===r?n:r,"$1\0$2"),void 0===i?a:i,"\0"),d=0,h=c.length;"\0"===c.charAt(d);)d++;for(;"\0"===c.charAt(h-1);)h--;return c.slice(d,h).split("\0").map(void 0===l?s:l).join(void 0===u?" ":u)}(e,i({delimiter:"."},r))}},37608:(e,t,r)=>{let i=r(83200),{snakeCase:s}=r(35622),n={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==n))throw Error("obj must be array of plain objects")}else if(e.constructor!==n)throw Error("obj must be an plain object");return i(e,function(e,r){var i,n,a,o,l;return[(i=t.exclude,n=e,i.some(function(e){return"string"==typeof e?e===n:e.test(n)}))?e:s(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},48308:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>l,Vc:()=>o,gE:()=>s,iM:()=>i,mG:()=>n,ub:()=>a});var i=[".lcl.dev",".lclstage.dev",".lclclerk.com"],s=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],n=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],a=[".accountsstage.dev"],o="https://api.lclclerk.com",l="https://api.clerkstage.dev",u="https://api.clerk.com"},51241:(e,t,r)=>{r.d(t,{H$:()=>u,mG:()=>a,V2:()=>c,fS:()=>h,ev:()=>y,Rg:()=>d,At:()=>l,tm:()=>p,rB:()=>o,Mh:()=>m,nN:()=>f});var i=r(62828),s=r(48308);r(56270);var n=r(32749);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let a=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",l="pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ";process.env.CLERK_ENCRYPTION_KEY;let u=process.env.CLERK_API_URL||(e=>{let t=(0,i.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&s.iM.some(e=>t?.endsWith(e))?s.FW:s.mG.some(e=>t?.endsWith(e))?s.Vc:s.ub.some(e=>t?.endsWith(e))?s.HG:s.FW})(l),c=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",d=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",h=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1;process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL,process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL;let p={name:"@clerk/nextjs",version:"6.24.0",environment:"production"},f=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),m=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),y=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},51288:(e,t,r)=>{r.d(t,{b_:()=>i.b_});var i=r(87374);r(56270)},55562:(e,t,r)=>{r.d(t,{y:()=>i});var i=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},56270:(e,t,r)=>{r.d(t,{OV:()=>d,S7:()=>u,VK:()=>c,jq:()=>h});var i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||o("Cannot "+r),u=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),c=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),d=(e,t,r,i)=>(l(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},58743:(e,t,r)=>{r.d(t,{h5:()=>o,jn:()=>s,qu:()=>i,sM:()=>l,z:()=>n,zF:()=>a});var i={InvalidSecretKey:"clerk_key_invalid"},s={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},n={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},a=class e extends Error{constructor({action:t,message:r,reason:i}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=i,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},o={TokenInvalid:"token-invalid",InvalidSecretKey:"secret-key-invalid",UnexpectedError:"unexpected-error"},l=class e extends Error{constructor({message:t,code:r,status:i}){super(t),Object.setPrototypeOf(this,e.prototype),this.code=r,this.status=i}getFullMessage(){return`${this.message} (code=${this.code}, status=${this.status})`}}},59311:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return s},RedirectType:function(){return n},isRedirectError:function(){return a}});let i=r(18389),s="NEXT_REDIRECT";var n=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,a=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===s&&("replace"===n||"push"===n)&&"string"==typeof a&&!isNaN(o)&&o in i.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61794:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return i}});let r=Symbol.for("react.postpone");function i(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},62828:(e,t,r)=>{r.d(t,{RZ:()=>c,qS:()=>h,ky:()=>p,mC:()=>d,q5:()=>l});var i=r(55562),s=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,n=r(48308),a="pk_live_";function o(e){if(!e.endsWith("$"))return!1;let t=e.slice(0,-1);return!t.includes("$")&&t.includes(".")}function l(e,t={}){let r;if(!(e=e||"")||!u(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!u(e))throw Error("Publishable key not valid.");return null}let s=e.startsWith(a)?"production":"development";try{r=(0,i.y)(e.split("_")[2])}catch{if(t.fatal)throw Error("Publishable key not valid: Failed to decode key.");return null}if(!o(r)){if(t.fatal)throw Error("Publishable key not valid: Decoded key has invalid format.");return null}let n=r.slice(0,-1);return t.proxyUrl?n=t.proxyUrl:"development"!==s&&t.domain&&t.isSatellite&&(n=`clerk.${t.domain}`),{instanceType:s,frontendApi:n}}function u(e=""){try{if(!(e.startsWith(a)||e.startsWith("pk_test_")))return!1;let t=e.split("_");if(3!==t.length)return!1;let r=t[2];if(!r)return!1;let s=(0,i.y)(r);return o(s)}catch{return!1}}function c(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,i=e.get(r);return void 0===i&&(i=n.gE.some(e=>r.endsWith(e)),e.set(r,i)),i}}}function d(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function h(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return s(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var p=(e,t)=>`${e}_${t}`},63800:(e,t)=>{t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||n,u=0;do{let t=e.indexOf("=",u);if(-1===t)break;let r=e.indexOf(";",u),n=-1===r?o:r;if(t>n){u=e.lastIndexOf(";",t-1)+1;continue}let c=i(e,u,t),d=s(e,t,c),h=e.slice(c,d);if(void 0===a[h]){let r=i(e,t+1,n),o=s(e,n,r),u=l(e.slice(r,o));a[h]=u}u=n+1}while(u<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function s(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function n(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},64665:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return s.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return n.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let i=r(68022),s=r(59311),n=r(86834),a=r(91777),o=r(24796),l=r(81452);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68022:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return o}});let i=r(18389),s=r(59311),n=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Object.defineProperty(Error(s.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=s.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",n}function o(e,t){var r;throw null!=t||(t=(null==n||null==(r=n.getStore())?void 0:r.isAction)?s.RedirectType.push:s.RedirectType.replace),a(e,t,i.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=s.RedirectType.replace),a(e,t,i.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,s.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74006:(e,t,r)=>{r.d(t,{ai:()=>_,at:()=>b,ot:()=>k});var i=r(91199);r(42087);var s=r(74208),n=r(64665);let a=(0,r(5970)._r)({packageName:"@clerk/nextjs"});var o=r(88971);r(79934),r(33781),r(58743);var l=r(51288),u=r(51241);let c={rE:"15.3.5"},d=!(c.rE.startsWith("13.")||c.rE.startsWith("14.0"))&&(0,l.b_)()&&!u.ev,h="__clerk_keys_";async function p(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function f(){let e=process.env.PWD;if(!e)return`${h}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await p(t);return`${h}${r}`}async function m(e){let t;if(!d)return;let r=await f();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}var y=r(33331);let g={secure:!1,httpOnly:!1,sameSite:"lax"};async function k(e){let{claimUrl:t,publishableKey:r,secretKey:i,returnUrl:a}=e,l=await (0,s.UL)(),u=new Request("https://placeholder.com",{headers:await (0,s.b3)()}),c=await m(e=>{var t;return null==(t=l.get(e))?void 0:t.value}),d=(null==c?void 0:c.publishableKey)===r,h=(null==c?void 0:c.secretKey)===i;if(!d||!h){var p,y,k,_,b,v;if(l.set(await f(),JSON.stringify({claimUrl:t,publishableKey:r,secretKey:i}),g),p="AuthStatus",((y=o.AA.Attributes[p])in u?u[y]:void 0)||(k=u,_=o.AA.Headers[p],function(e){try{let{headers:t,nextUrl:r,cookies:i}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==i?void 0:i.get)}catch{return!1}}(k)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(k)?k.headers.get(_):k.headers[_]||k.headers[_.toLowerCase()]||(null==(v=null==(b=k.socket)?void 0:b._httpMessage)?void 0:v.getHeader(_))))return void(0,n.redirect)(`/clerk-sync-keyless?returnUrl=${a}`,n.RedirectType.replace)}}async function _(){if(!d)return null;let e=await r.e(591).then(r.bind(r,23591)).then(e=>e.createOrReadKeyless()).catch(()=>null);if(!e)return a.throwMissingPublishableKeyError(),null;let{clerkDevelopmentCache:t,createKeylessModeMessage:i}=await r.e(304).then(r.bind(r,51304));null==t||t.log({cacheKey:e.publishableKey,msg:i(e)});let{claimUrl:n,publishableKey:o,secretKey:l,apiKeysUrl:u}=e;return(await (0,s.UL)()).set(await f(),JSON.stringify({claimUrl:n,publishableKey:o,secretKey:l}),g),{claimUrl:n,publishableKey:o,apiKeysUrl:u}}async function b(){d&&await r.e(591).then(r.bind(r,23591)).then(e=>e.removeKeyless()).catch(()=>{})}(0,y.D)([_,b,k]),(0,i.A)(_,"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a",null),(0,i.A)(b,"7f126a7a969b1105c221f5710d89250ac20d205bab",null),(0,i.A)(k,"7fc968e88d4d7d85319b0c692c8eba3563d91942c2",null)},79934:(e,t,r)=>{r.d(t,{io:()=>d,qS:()=>l.qS,ky:()=>l.ky,Ve:()=>l.mC,q5:()=>l.q5,L5:()=>o}),r(56270);var i={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},s=async e=>new Promise(t=>setTimeout(t,e)),n=(e,t)=>t?e*(1+Math.random()):e,a=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=n(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await s(r()),t++}},o=async(e,t={})=>{let r=0,{shouldRetry:o,initialDelay:l,maxDelayBetweenRetries:u,factor:c,retryImmediately:d,jitter:h}={...i,...t},p=a({initialDelay:l,maxDelayBetweenRetries:u,factor:c,jitter:h});for(;;)try{return await e()}catch(e){if(!o(e,++r))throw e;d&&1===r?await s(n(100,h)):await p()}},l=r(62828),u=r(87374),c=new Set,d=(e,t,r)=>{let i=(0,u.MC)()||(0,u.Fj)(),s=r??e;c.has(s)||i||(c.add(s),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};(0,r(5970)._r)({packageName:"@clerk/backend"});var{isDevOrStagingUrl:h}=(0,l.RZ)()},81452:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=r(4431).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83200:e=>{let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),i=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),s=(e,t,n,a=new WeakMap)=>{if(n={deep:!1,target:{},...n},a.has(e))return a.get(e);a.set(e,n.target);let{target:o}=n;delete n.target;let l=e=>e.map(e=>i(e)?s(e,t,n,a):e);if(Array.isArray(e))return l(e);for(let[u,c]of Object.entries(e)){let d=t(u,c,e);if(d===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=d;"__proto__"!==h&&(n.deep&&f&&i(p)&&(p=Array.isArray(p)?l(p):s(p,t,n,a)),o[h]=p)}return o};e.exports=(e,r,i)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return s(e,r,i)},e.exports.mapObjectSkip=r},86834:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return s}});let i=""+r(32913).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function s(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87374:(e,t,r)=>{r.d(t,{Fj:()=>n,MC:()=>s,b_:()=>i});var i=()=>!1,s=()=>!1,n=()=>{try{return!0}catch{}return!1}},88971:(e,t,r)=>{r.d(t,{AA:()=>R,Bs:()=>ro,y3:()=>tA,nr:()=>t1});var i=r(79934),s=r(33781),n=r(58743);r(56270);var a={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},o=new Set(["first_factor","second_factor","multi_factor"]),l=new Set(["strict_mfa","strict","moderate","lax"]),u=e=>"number"==typeof e&&e>0,c=e=>o.has(e),d=e=>l.has(e),h=e=>e.replace(/^(org:)*/,"org:"),p=(e,t)=>{let{orgId:r,orgRole:i,orgPermissions:s}=t;return(e.role||e.permission)&&r&&i&&s?e.permission?s.includes(h(e.permission)):e.role?h(i)===h(e.role):null:null},f=(e,t)=>{let{org:r,user:i}=y(e),[s,n]=t.split(":"),a=n||s;return"org"===s?r.includes(a):"user"===s?i.includes(a):[...r,...i].includes(a)},m=(e,t)=>{let{features:r,plans:i}=t;return e.feature&&r?f(r,e.feature):e.plan&&i?f(i,e.plan):null},y=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},g=e=>{if(!e)return!1;let t="string"==typeof e&&d(e),r="object"==typeof e&&c(e.level)&&u(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?a[e]:e).bind(null,e)},k=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=g(e.reverification);if(!r)return null;let{level:i,afterMinutes:s}=r(),[n,a]=t,o=-1!==n?s>n:null,l=-1!==a?s>a:null;switch(i){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},_=e=>t=>{if(!e.userId)return!1;let r=m(t,e),i=p(t,e),s=k(t,e);return[r||i,s].some(e=>null===e)?[r||i,s].some(e=>!0===e):[r||i,s].every(e=>!0===e)},b=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),i=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:i}},v=e=>{let t,r,i,s,n=e.fva??null,a=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,i=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:n}=y(e.fea),{permissions:a,featurePermissionMap:o}=b({per:e.o?.per,fpm:e.o?.fpm});s=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let i=[];for(let s=0;s<e.length;s++){let n=e[s];if(s>=r.length)continue;let a=r[s];if(a)for(let e=0;e<a.length;e++)1===a[e]&&i.push(`org:${n}:${t[e]}`)}return i}({features:n,featurePermissionMap:o,permissions:a})}}else t=e.org_id,r=e.org_role,i=e.org_slug,s=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:a,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:i,orgPermissions:s,factorVerificationAge:n}},T=r(5970),S=r(37608),w=r(63800);function E(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function A(e){return e&&e.sensitive?"":"i"}var O="https://api.clerk.com",I="@clerk/backend@2.4.1",C="2025-04-10",P={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},x={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:P.DevBrowser,Handshake:P.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:P.HandshakeNonce,HandshakeFormat:"format"},R={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:P,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",SecFetchSite:"sec-fetch-site",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:x},U=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let i=new URL(t);r=new URL(e,i.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()};function N(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}function q(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var j=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.originalFrontendApi="",this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.tokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(R.Cookies.ClientUat),t=this.getCookie(R.Cookies.ClientUat),r=this.getSuffixedCookie(R.Cookies.Session)||"",i=this.getCookie(R.Cookies.Session)||"";if(i&&!this.tokenHasIssuer(i))return!1;if(i&&!this.tokenBelongsToInstance(i))return!0;if(!e&&!r)return!1;let{data:n}=(0,s.iU)(i),a=n?.payload.iat||0,{data:o}=(0,s.iU)(r),l=o?.payload.iat||0;if("0"!==e&&"0"!==t&&a>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}isCrossOriginReferrer(){if(!this.referrer||!this.origin)return!1;try{if("cross-site"===this.getHeader(R.Headers.SecFetchSite))return!0;return new URL(this.referrer).origin!==this.origin}catch{return!1}}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,i.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,i.q5)(this.publishableKey,{fatal:!0,domain:e.domain,isSatellite:e.isSatellite});this.originalFrontendApi=r.frontendApi;let s=(0,i.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=s.instanceType,this.frontendApi=s.frontendApi}initHeaderValues(){this.tokenInHeader=this.parseAuthorizationHeader(this.getHeader(R.Headers.Authorization)),this.origin=this.getHeader(R.Headers.Origin),this.host=this.getHeader(R.Headers.Host),this.forwardedHost=this.getHeader(R.Headers.ForwardedHost),this.forwardedProto=this.getHeader(R.Headers.CloudFrontForwardedProto)||this.getHeader(R.Headers.ForwardedProto),this.referrer=this.getHeader(R.Headers.Referrer),this.userAgent=this.getHeader(R.Headers.UserAgent),this.secFetchDest=this.getHeader(R.Headers.SecFetchDest),this.accept=this.getHeader(R.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(R.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(R.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(R.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(R.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(R.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(R.QueryParameters.Handshake)||this.getCookie(R.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(R.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(R.QueryParameters.HandshakeNonce)||this.getCookie(R.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,i.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,s.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,s.iU)(e);if(r)return!1;let i=t.payload.iss.replace(/https?:\/\//gi,"");return this.originalFrontendApi===i}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},M=async(e,t)=>new j(t.publishableKey?await (0,i.qS)(t.publishableKey,s.fA.crypto.subtle):"",e,t),J=RegExp("(?<!:)/{1,}","g");function z(...e){return e.filter(e=>e).join("/").replace(J,"/")}var L=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},H="/actor_tokens",F=class extends L{async create(e){return this.request({method:"POST",path:H,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:z(H,e,"revoke")})}},D="/accountless_applications",K=class extends L{async createAccountlessApplication(){return this.request({method:"POST",path:D})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:z(D,"complete")})}},W="/allowlist_identifiers",B=class extends L{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:W,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:W,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:z(W,e)})}},$="/api_keys",G=class extends L{async create(e){return this.request({method:"POST",path:$,bodyParams:e})}async revoke(e){let{apiKeyId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z($,t,"revoke"),bodyParams:r})}async getSecret(e){return this.requireId(e),this.request({method:"GET",path:z($,e,"secret")})}async verifySecret(e){return this.request({method:"POST",path:z($,"verify"),bodyParams:{secret:e}})}},V=class extends L{async changeDomain(e){return this.request({method:"POST",path:z("/beta_features","change_domain"),bodyParams:e})}},Q="/blocklist_identifiers",X=class extends L{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:Q,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:Q,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:z(Q,e)})}},Y="/clients",Z=class extends L{async getClientList(e={}){return this.request({method:"GET",path:Y,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:z(Y,e)})}verifyClient(e){return this.request({method:"POST",path:z(Y,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:z(Y,"handshake_payload"),queryParams:e})}},ee="/domains",et=class extends L{async list(){return this.request({method:"GET",path:ee})}async add(e){return this.request({method:"POST",path:ee,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:z(ee,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:z(ee,e)})}},er="/email_addresses",ei=class extends L{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:z(er,e)})}async createEmailAddress(e){return this.request({method:"POST",path:er,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(er,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:z(er,e)})}},es=class extends L{async verifyAccessToken(e){return this.request({method:"POST",path:z("/oauth_applications/access_tokens","verify"),bodyParams:{access_token:e}})}},en="/instance",ea=class extends L{async get(){return this.request({method:"GET",path:en})}async update(e){return this.request({method:"PATCH",path:en,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:z(en,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:z(en,"organization_settings"),bodyParams:e})}},eo="/invitations",el=class extends L{async getInvitationList(e={}){return this.request({method:"GET",path:eo,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:eo,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:z(eo,e,"revoke")})}},eu=class extends L{async verifySecret(e){return this.request({method:"POST",path:z("/m2m_tokens","verify"),bodyParams:{secret:e}})}},ec=class extends L{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},ed="/jwt_templates",eh=class extends L{async list(e={}){return this.request({method:"GET",path:ed,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:z(ed,e)})}async create(e){return this.request({method:"POST",path:ed,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:z(ed,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:z(ed,e)})}},ep="/organizations",ef=class extends L{async getOrganizationList(e){return this.request({method:"GET",path:ep,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:ep,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:z(ep,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(ep,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new s.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:z(ep,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:z(ep,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(ep,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:z(ep,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(ep,t,"memberships"),queryParams:r})}async getInstanceOrganizationMembershipList(e){return this.request({method:"GET",path:"/organization_memberships",queryParams:e})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(ep,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...i}=e;return this.requireId(t),this.request({method:"PATCH",path:z(ep,t,"memberships",r),bodyParams:i})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...i}=e;return this.request({method:"PATCH",path:z(ep,t,"memberships",r,"metadata"),bodyParams:i})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:z(ep,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(ep,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(ep,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:z(ep,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:z(ep,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...i}=e;return this.requireId(t),this.request({method:"POST",path:z(ep,t,"invitations",r,"revoke"),bodyParams:i})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(ep,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(ep,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...i}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:z(ep,t,"domains",r),bodyParams:i})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:z(ep,t,"domains",r)})}},em="/oauth_applications",ey=class extends L{async list(e={}){return this.request({method:"GET",path:em,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:z(em,e)})}async create(e){return this.request({method:"POST",path:em,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:z(em,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:z(em,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:z(em,e,"rotate_secret")})}},eg="/phone_numbers",ek=class extends L{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:z(eg,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:eg,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(eg,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:z(eg,e)})}},e_=class extends L{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},eb="/redirect_urls",ev=class extends L{async getRedirectUrlList(){return this.request({method:"GET",path:eb,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:z(eb,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:eb,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:z(eb,e)})}},eT="/saml_connections",eS=class extends L{async getSamlConnectionList(e={}){return this.request({method:"GET",path:eT,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:eT,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:z(eT,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(eT,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:z(eT,e)})}},ew="/sessions",eE=class extends L{async getSessionList(e={}){return this.request({method:"GET",path:ew,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:z(ew,e)})}async createSession(e){return this.request({method:"POST",path:ew,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:z(ew,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:z(ew,e,"verify"),bodyParams:{token:t}})}async getToken(e,t,r){this.requireId(e);let i={method:"POST",path:t?z(ew,e,"tokens",t):z(ew,e,"tokens")};return void 0!==r&&(i.bodyParams={expires_in_seconds:r}),this.request(i)}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...i}=t;return this.request({method:"POST",path:z(ew,e,"refresh"),bodyParams:i,queryParams:{suffixed_cookies:r}})}},eA="/sign_in_tokens",eO=class extends L{async createSignInToken(e){return this.request({method:"POST",path:eA,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:z(eA,e,"revoke")})}},eI="/sign_ups",eC=class extends L{async get(e){return this.requireId(e),this.request({method:"GET",path:z(eI,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:z(eI,t),bodyParams:r})}},eP=class extends L{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},ex="/users",eR=class extends L{async getUserList(e={}){let{limit:t,offset:r,orderBy:i,...s}=e,[n,a]=await Promise.all([this.request({method:"GET",path:ex,queryParams:e}),this.getCount(s)]);return{data:n,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:z(ex,e)})}async createUser(e){return this.request({method:"POST",path:ex,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(ex,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new s.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:z(ex,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(ex,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:z(ex,e)})}async getCount(e={}){return this.request({method:"GET",path:z(ex,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),s=r?t:`oauth_${t}`;return r&&(0,i.io)("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:z(ex,e,"oauth_access_tokens",s),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:z(ex,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:z(ex,t,"organization_memberships"),queryParams:{limit:r,offset:i}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(ex,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:z(ex,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:z(ex,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:z(ex,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:z(ex,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:z(ex,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:z(ex,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:z(ex,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:z(ex,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:z(ex,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:z(ex,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:z(ex,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:z(ex,e,"totp")})}},eU="/waitlist_entries",eN=class extends L{async list(e={}){return this.request({method:"GET",path:eU,queryParams:e})}async create(e){return this.request({method:"POST",path:eU,bodyParams:e})}},eq="/webhooks",ej=class extends L{async createSvixApp(){return this.request({method:"POST",path:z(eq,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:z(eq,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:z(eq,"svix")})}},eM=class e{constructor(e,t,r,i){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=i}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},eJ=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.status=t,this.userId=r,this.actor=i,this.token=s,this.url=n,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},ez=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=i,this.updatedAt=s,this.instanceId=n,this.invitationId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},eL=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d,h,p,f,m){this.id=e,this.type=t,this.name=r,this.subject=i,this.scopes=s,this.claims=n,this.revoked=a,this.revocationReason=o,this.expired=l,this.expiration=u,this.createdBy=c,this.description=d,this.lastUsedAt=h,this.createdAt=p,this.updatedAt=f,this.secret=m}static fromJSON(t){return new e(t.id,t.type,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.description,t.last_used_at,t.created_at,t.updated_at,t.secret)}},eH=class e{constructor(e,t,r,i,s,n){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=i,this.updatedAt=s,this.instanceId=n}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},eF=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=i,this.country=s,this.browserVersion=n,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},eD=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d=null){this.id=e,this.clientId=t,this.userId=r,this.status=i,this.lastActiveAt=s,this.expireAt=n,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=u,this.latestActivity=c,this.actor=d}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&eF.fromJSON(t.latest_activity),t.actor)}},eK=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=i,this.signUpId=s,this.lastActiveSessionId=n,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>eD.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},eW=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},eB=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},e$=class e{constructor(e,t,r,i){this.object=e,this.id=t,this.slug=r,this.deleted=i}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},eG=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=i,this.developmentOrigin=s,this.cnameTargets=n,this.accountsPortalUrl=a,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>eW.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},eV=class e{constructor(e,t,r,i,s,n,a,o,l,u,c){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=i,this.subject=s,this.body=n,this.bodyPlain=a,this.status=o,this.slug=l,this.data=u,this.deliveredByClerk=c}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},eQ=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eX=class e{constructor(e,t,r=null,i=null,s=null,n=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=i,this.expireAt=s,this.nonce=n,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eY=class e{constructor(e,t,r,i){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=i}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eX.fromJSON(t.verification),t.linked_to.map(e=>eQ.fromJSON(e)))}},eZ=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=i,this.approvedScopes=s,this.emailAddress=n,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=u,this.phoneNumber=c,this.publicMetadata=d,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&eX.fromJSON(t.verification))}},e0=class e{constructor(e,t,r,i,s,n,a,o,l,u,c){this.id=e,this.clientId=t,this.type=r,this.subject=i,this.scopes=s,this.revoked=n,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdAt=u,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.client_id,t.type,t.subject,t.scopes,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_at,t.updated_at)}},e1=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},e3=class e{constructor(e,t,r,i,s){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=i,this.ignoreDotsForGmailAddresses=s}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},e2=class e{constructor(e,t,r,i,s){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=i,this.enhancedEmailDeliverability=s}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},e5=class e{constructor(e,t,r,i,s,n,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=i,this.updatedAt=s,this.status=n,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},e8={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",ApiKey:"api_key",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",MachineToken:"machine_to_machine_token",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",IdpOAuthAccessToken:"clerk_idp_oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",SamlConnection:"saml_connection",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},e4=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d,h){this.id=e,this.name=t,this.subject=r,this.scopes=i,this.claims=s,this.revoked=n,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdBy=u,this.creationReason=c,this.createdAt=d,this.updatedAt=h}static fromJSON(t){return new e(t.id,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.creation_reason,t.created_at,t.updated_at)}},e7=class e{constructor(e,t,r,i,s,n,a,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=i,this.allowedClockSkew=s,this.customSigningKey=n,this.signingAlgorithm=a,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},e9=class e{constructor(e,t,r,i={},s,n,a,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=i,this.label=s,this.scopes=n,this.tokenSecret=a,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},e6=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=i,this.isPublic=s,this.scopes=n,this.redirectUris=a,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=u,this.discoveryUrl=c,this.tokenIntrospectionUrl=d,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},te=class e{constructor(e,t,r,i,s,n,a,o={},l={},u,c,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=i,this.hasImage=s,this.createdAt=n,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=u,this.adminDeleteEnabled=c,this.membersCount=d,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},tt=class e{constructor(e,t,r,i,s,n,a,o,l,u,c={},d={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=i,this.organizationId=s,this.createdAt=n,this.updatedAt=a,this.expiresAt=o,this.url=l,this.status=u,this.publicMetadata=c,this.privateMetadata=d,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},tr=class e{constructor(e,t,r,i={},s={},n,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=i,this.privateMetadata=s,this.createdAt=n,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,te.fromJSON(t.organization),ti.fromJSON(t.public_user_data));return r._raw=t,r}},ti=class e{constructor(e,t,r,i,s,n){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=i,this.hasImage=s,this.userId=n}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},ts=class e{constructor(e,t,r,i,s,n,a,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=i,this.creatorRole=s,this.adminDeleteEnabled=n,this.domainsEnabled=a,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},tn=class e{constructor(e,t,r,i,s,n){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=i,this.verification=s,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eX.fromJSON(t.verification),t.linked_to.map(e=>eQ.fromJSON(e)))}},ta=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=i,this.successful=s,this.createdAt=n,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},to=class e{constructor(e,t,r,i){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=i}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},tl=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d,h,p,f,m,y,g,k,_,b){this.id=e,this.name=t,this.domain=r,this.organizationId=i,this.idpEntityId=s,this.idpSsoUrl=n,this.idpCertificate=a,this.idpMetadataUrl=o,this.idpMetadata=l,this.acsUrl=u,this.spEntityId=c,this.spMetadataUrl=d,this.active=h,this.provider=p,this.userCount=f,this.syncUserAttributes=m,this.allowSubdomains=y,this.allowIdpInitiated=g,this.createdAt=k,this.updatedAt=_,this.attributeMapping=b}static fromJSON(t){return new e(t.id,t.name,t.domain,t.organization_id,t.idp_entity_id,t.idp_sso_url,t.idp_certificate,t.idp_metadata_url,t.idp_metadata,t.acs_url,t.sp_entity_id,t.sp_metadata_url,t.active,t.provider,t.user_count,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at,t.attribute_mapping&&tc.fromJSON(t.attribute_mapping))}},tu=class e{constructor(e,t,r,i,s,n,a,o,l,u){this.id=e,this.name=t,this.domain=r,this.active=i,this.provider=s,this.syncUserAttributes=n,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},tc=class e{constructor(e,t,r,i){this.userId=e,this.emailAddress=t,this.firstName=r,this.lastName=i}static fromJSON(t){return new e(t.user_id,t.email_address,t.first_name,t.last_name)}},td=class e{constructor(e,t,r,i,s,n,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=i,this.emailAddress=s,this.firstName=n,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eX.fromJSON(t.verification),t.saml_connection&&tu.fromJSON(t.saml_connection))}},th=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.userId=t,this.token=r,this.status=i,this.url=s,this.createdAt=n,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},tp=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},tf=class e{constructor(e,t,r,i){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=i}static fromJSON(t){return new e(t.email_address&&tp.fromJSON(t.email_address),t.phone_number&&tp.fromJSON(t.phone_number),t.web3_wallet&&tp.fromJSON(t.web3_wallet),t.external_account)}},tm=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d,h,p,f,m,y,g,k,_,b,v){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=i,this.missingFields=s,this.unverifiedFields=n,this.verifications=a,this.username=o,this.emailAddress=l,this.phoneNumber=u,this.web3Wallet=c,this.passwordEnabled=d,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=m,this.createdSessionId=y,this.createdUserId=g,this.abandonAt=k,this.legalAcceptedAt=_,this.publicMetadata=b,this.unsafeMetadata=v}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?tf.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},ty=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=i,this.status=s,this.phoneNumberId=n,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},tg=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},tk=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eX.fromJSON(t.verification))}},t_=class e{constructor(e,t,r,i,s,n,a,o,l,u,c,d,h,p,f,m,y,g,k,_={},b={},v={},T=[],S=[],w=[],E=[],A=[],O,I,C=null,P,x){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=i,this.twoFactorEnabled=s,this.banned=n,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=u,this.hasImage=c,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=m,this.username=y,this.firstName=g,this.lastName=k,this.publicMetadata=_,this.privateMetadata=b,this.unsafeMetadata=v,this.emailAddresses=T,this.phoneNumbers=S,this.web3Wallets=w,this.externalAccounts=E,this.samlAccounts=A,this.lastActiveAt=O,this.createOrganizationEnabled=I,this.createOrganizationsLimit=C,this.deleteSelfEnabled=P,this.legalAcceptedAt=x,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eY.fromJSON(e)),(t.phone_numbers||[]).map(e=>tn.fromJSON(e)),(t.web3_wallets||[]).map(e=>tk.fromJSON(e)),(t.external_accounts||[]).map(e=>eZ.fromJSON(e)),(t.saml_accounts||[]).map(e=>td.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},tb=class e{constructor(e,t,r,i,s,n,a){this.id=e,this.emailAddress=t,this.status=r,this.invitation=i,this.createdAt=s,this.updatedAt=n,this.isLocked=a}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&e5.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function tv(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return e$.fromJSON(e);switch(e.object){case e8.AccountlessApplication:return eM.fromJSON(e);case e8.ActorToken:return eJ.fromJSON(e);case e8.AllowlistIdentifier:return ez.fromJSON(e);case e8.ApiKey:return eL.fromJSON(e);case e8.BlocklistIdentifier:return eH.fromJSON(e);case e8.Client:return eK.fromJSON(e);case e8.Cookies:return eB.fromJSON(e);case e8.Domain:return eG.fromJSON(e);case e8.EmailAddress:return eY.fromJSON(e);case e8.Email:return eV.fromJSON(e);case e8.IdpOAuthAccessToken:return e0.fromJSON(e);case e8.Instance:return e1.fromJSON(e);case e8.InstanceRestrictions:return e3.fromJSON(e);case e8.InstanceSettings:return e2.fromJSON(e);case e8.Invitation:return e5.fromJSON(e);case e8.JwtTemplate:return e7.fromJSON(e);case e8.MachineToken:return e4.fromJSON(e);case e8.OauthAccessToken:return e9.fromJSON(e);case e8.OAuthApplication:return e6.fromJSON(e);case e8.Organization:return te.fromJSON(e);case e8.OrganizationInvitation:return tt.fromJSON(e);case e8.OrganizationMembership:return tr.fromJSON(e);case e8.OrganizationSettings:return ts.fromJSON(e);case e8.PhoneNumber:return tn.fromJSON(e);case e8.ProxyCheck:return ta.fromJSON(e);case e8.RedirectUrl:return to.fromJSON(e);case e8.SamlConnection:return tl.fromJSON(e);case e8.SignInToken:return th.fromJSON(e);case e8.SignUpAttempt:return tm.fromJSON(e);case e8.Session:return eD.fromJSON(e);case e8.SmsMessage:return ty.fromJSON(e);case e8.Token:return tg.fromJSON(e);case e8.TotalCount:return e.total_count;case e8.User:return t_.fromJSON(e);case e8.WaitlistEntry:return tb.fromJSON(e);default:return e}}function tT(e){var t;return t=async t=>{let r,{secretKey:i,requireSecretKey:n=!0,apiUrl:a=O,apiVersion:o="v1",userAgent:l=I,skipApiVersionInUrl:u=!1}=e,{path:c,method:d,queryParams:h,headerParams:p,bodyParams:f,formData:m}=t;n&&q(i);let y=new URL(u?z(a,c):z(a,o,c));if(h)for(let[e,t]of Object.entries(S({...h})))t&&[t].flat().forEach(t=>y.searchParams.append(e,t));let g=new Headers({"Clerk-API-Version":C,"User-Agent":l,...p});i&&g.set("Authorization",`Bearer ${i}`);try{var k;m?r=await s.fA.fetch(y.href,{method:d,headers:g,body:m}):(g.set("Content-Type","application/json"),r=await s.fA.fetch(y.href,{method:d,headers:g,...(()=>{if(!("GET"!==d&&f&&Object.keys(f).length>0))return null;let e=e=>S(e,{deep:!1});return{body:JSON.stringify(Array.isArray(f)?f.map(e):e(f))}})()}));let e=r?.headers&&r.headers?.get(R.Headers.ContentType)===R.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:tE(t),status:r?.status,statusText:r?.statusText,clerkTraceId:tS(t,r?.headers),retryAfter:tw(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>tv(e))}:(k=t)&&"object"==typeof k&&"data"in k&&Array.isArray(k.data)&&void 0!==k.data?{data:t.data.map(e=>tv(e)),totalCount:t.total_count}:{data:tv(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:tS(e,r?.headers)};return{data:null,errors:tE(e),status:r?.status,statusText:r?.statusText,clerkTraceId:tS(e,r?.headers),retryAfter:tw(r?.headers)}}},async(...e)=>{let{data:r,errors:i,totalCount:s,status:n,statusText:a,clerkTraceId:o,retryAfter:l}=await t(...e);if(i){let e=new T.LR(a||"",{data:[],status:n,clerkTraceId:o,retryAfter:l});throw e.errors=i,e}return void 0!==s?{data:r,totalCount:s}:r}}function tS(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function tw(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function tE(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(T.u$):[]}return[]}function tA(e){let t=tT(e);return{__experimental_accountlessApplications:new K(tT({...e,requireSecretKey:!1})),actorTokens:new F(t),allowlistIdentifiers:new B(t),apiKeys:new G(tT({...e,skipApiVersionInUrl:!0})),betaFeatures:new V(t),blocklistIdentifiers:new X(t),clients:new Z(t),domains:new et(t),emailAddresses:new ei(t),idPOAuthAccessToken:new es(tT({...e,skipApiVersionInUrl:!0})),instance:new ea(t),invitations:new el(t),jwks:new ec(t),jwtTemplates:new eh(t),machineTokens:new eu(tT({...e,skipApiVersionInUrl:!0})),oauthApplications:new ey(t),organizations:new ef(t),phoneNumbers:new ek(t),proxyChecks:new e_(t),redirectUrls:new ev(t),samlConnections:new eS(t),sessions:new eE(t),signInTokens:new eO(t),signUps:new eC(t),testingTokens:new eP(t),users:new eR(t),waitlistEntries:new eN(t),webhooks:new ej(t)}}var tO={SessionToken:"session_token",ApiKey:"api_key",MachineToken:"machine_token",OAuthToken:"oauth_token"},tI="oat_",tC=["mt_",tI,"ak_"];function tP(e){return tC.some(t=>e.startsWith(t))}function tx(e){if(e.startsWith("mt_"))return tO.MachineToken;if(e.startsWith(tI))return tO.OAuthToken;if(e.startsWith("ak_"))return tO.ApiKey;throw Error("Unknown machine token type")}var tR=(e,t)=>!!e&&("any"===t||(Array.isArray(t)?t:[t]).includes(e)),tU=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function tN(e,t){return{tokenType:tO.SessionToken,sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:tU(e),isAuthenticated:!1}}var tq=e=>{let{fetcher:t,sessionToken:r,sessionId:i}=e||{};return async(e={})=>i?e.template||void 0!==e.expiresInSeconds?t(i,e.template,e.expiresInSeconds):r:null},tj={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},tM={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",PrimaryDomainCrossOriginSync:"primary-domain-cross-origin-sync",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",TokenTypeMismatch:"token-type-mismatch",UnexpectedError:"unexpected-error"};function tJ(e){let{authenticateContext:t,headers:r=new Headers,token:i}=e;return{status:tj.SignedIn,reason:null,message:null,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!0,isAuthenticated:!0,tokenType:e.tokenType,toAuth:({treatPendingAsSignedOut:r=!0}={})=>{if(e.tokenType===tO.SessionToken){let{sessionClaims:s}=e,n=function(e,t,r){let{actor:i,sessionId:s,sessionStatus:n,userId:a,orgId:o,orgRole:l,orgSlug:u,orgPermissions:c,factorVerificationAge:d}=v(r),h=tA(e),p=tq({sessionId:s,sessionToken:t,fetcher:async(e,t,r)=>(await h.sessions.getToken(e,t||"",r)).jwt});return{tokenType:tO.SessionToken,actor:i,sessionClaims:r,sessionId:s,sessionStatus:n,userId:a,orgId:o,orgRole:l,orgSlug:u,orgPermissions:c,factorVerificationAge:d,getToken:p,has:_({orgId:o,orgRole:l,orgPermissions:c,userId:a,factorVerificationAge:d,features:r.fea||"",plans:r.pla||""}),debug:tU({...e,sessionToken:t}),isAuthenticated:!0}}(t,i,s);return r&&"pending"===n.sessionStatus?tN(void 0,n.sessionStatus):n}let{machineData:s}=e;var n=e.tokenType;let a={id:s.id,subject:s.subject,getToken:()=>Promise.resolve(i),has:()=>!1,debug:tU(t),isAuthenticated:!0};switch(n){case tO.ApiKey:return{...a,tokenType:n,name:s.name,claims:s.claims,scopes:s.scopes,userId:s.subject.startsWith("user_")?s.subject:null,orgId:s.subject.startsWith("org_")?s.subject:null};case tO.MachineToken:return{...a,tokenType:n,name:s.name,claims:s.claims,scopes:s.scopes,machineId:s.subject};case tO.OAuthToken:return{...a,tokenType:n,scopes:s.scopes,userId:s.subject,clientId:s.clientId};default:throw Error(`Invalid token type: ${n}`)}},headers:r,token:i}}function tz(e){let{authenticateContext:t,headers:r=new Headers,reason:i,message:s="",tokenType:n}=e;return tL({status:tj.SignedOut,reason:i,message:s,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:n,toAuth:()=>n===tO.SessionToken?tN({...t,status:tj.SignedOut,reason:i,message:s}):function(e,t){let r={id:null,subject:null,scopes:null,has:()=>!1,getToken:()=>Promise.resolve(null),debug:tU(t),isAuthenticated:!1};switch(e){case tO.ApiKey:return{...r,tokenType:e,name:null,claims:null,scopes:null,userId:null,orgId:null};case tO.MachineToken:return{...r,tokenType:e,name:null,claims:null,scopes:null,machineId:null};case tO.OAuthToken:return{...r,tokenType:e,scopes:null,userId:null,clientId:null};default:throw Error(`Invalid token type: ${e}`)}}(n,{reason:i,message:s,headers:r}),headers:r,token:null})}var tL=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(R.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(R.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(R.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},tH=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},tF=(...e)=>new tH(...e),tD=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(R.Headers.ForwardedProto),i=e.headers.get(R.Headers.ForwardedHost),s=e.headers.get(R.Headers.Host),n=t.protocol,a=this.getFirstValueFromHeader(i)??s,o=this.getFirstValueFromHeader(r)??n?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?tF(t):tF(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,w.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},tK=(...e)=>e[0]instanceof tD?e[0]:new tD(...e),tW=e=>e.split(";")[0]?.split("=")[0],tB=e=>e.split(";")[0]?.split("=")[1],t$={},tG=0;function tV(e,t=!0){t$[e.kid]=e,tG=t?Date.now():-1}var tQ="local";function tX(e){if(!t$[tQ]){if(!e)throw new n.zF({action:n.z.SetClerkJWTKey,message:"Missing local JWK.",reason:n.jn.LocalJWKMissing});tV({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return t$[tQ]}async function tY({secretKey:e,apiUrl:t=O,apiVersion:r="v1",kid:s,skipJwksCache:a}){if(a||function(){if(-1===tG)return!1;let e=Date.now()-tG>=3e5;return e&&(t$={}),e}()||!t$[s]){if(!e)throw new n.zF({action:n.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:n.jn.RemoteJWKFailedToLoad});let{keys:s}=await (0,i.L5)(()=>tZ(t,e,r));if(!s||!s.length)throw new n.zF({action:n.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:n.jn.RemoteJWKFailedToLoad});s.forEach(e=>tV(e))}let o=t$[s];if(!o){let e=Object.values(t$).map(e=>e.kid).sort().join(", ");throw new n.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${n.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${s}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:n.jn.JWKKidMismatch})}return o}async function tZ(e,t,r){if(!t)throw new n.zF({action:n.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:n.jn.RemoteJWKFailedToLoad});let i=new URL(e);i.pathname=z(i.pathname,r,"/jwks");let a=await s.fA.fetch(i.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":C,"Content-Type":"application/json","User-Agent":I}});if(!a.ok){let e=await a.json(),t=t0(e?.errors,n.qu.InvalidSecretKey);if(t){let e=n.jn.InvalidSecretKey;throw new n.zF({action:n.z.ContactSupport,message:t.message,reason:e})}throw new n.zF({action:n.z.ContactSupport,message:`Error loading Clerk JWKS from ${i.href} with code=${a.status}`,reason:n.jn.RemoteJWKFailedToLoad})}return a.json()}var t0=(e,t)=>e?e.find(e=>e.code===t):null;async function t1(e,t){let{data:r,errors:i}=(0,s.iU)(e);if(i)return{errors:i};let{header:a}=r,{kid:o}=a;try{let r;if(t.jwtKey)r=tX(t.jwtKey);else{if(!t.secretKey)return{errors:[new n.zF({action:n.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:n.jn.JWKFailedToResolve})]};r=await tY({...t,kid:o})}return await (0,s.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}function t3(e,t,r){if((0,T.$R)(t)){let i,s;switch(t.status){case 401:i=n.h5.InvalidSecretKey,s=t.errors[0]?.message||"Invalid secret key";break;case 404:i=n.h5.TokenInvalid,s=r;break;default:i=n.h5.UnexpectedError,s="Unexpected error"}return{data:void 0,tokenType:e,errors:[new n.sM({message:s,code:i,status:t.status})]}}return{data:void 0,tokenType:e,errors:[new n.sM({message:"Unexpected error",code:n.h5.UnexpectedError,status:t.status})]}}async function t2(e,t){try{let r=tA(t);return{data:await r.machineTokens.verifySecret(e),tokenType:tO.MachineToken,errors:void 0}}catch(e){return t3(tO.MachineToken,e,"Machine token not found")}}async function t5(e,t){try{let r=tA(t);return{data:await r.idPOAuthAccessToken.verifyAccessToken(e),tokenType:tO.OAuthToken,errors:void 0}}catch(e){return t3(tO.OAuthToken,e,"OAuth token not found")}}async function t8(e,t){try{let r=tA(t);return{data:await r.apiKeys.verifySecret(e),tokenType:tO.ApiKey,errors:void 0}}catch(e){return t3(tO.ApiKey,e,"API key not found")}}async function t4(e,t){if(e.startsWith("mt_"))return t2(e,t);if(e.startsWith(tI))return t5(e,t);if(e.startsWith("ak_"))return t8(e,t);throw Error("Unknown machine token type")}async function t7(e,{key:t}){let{data:r,errors:i}=(0,s.iU)(e);if(i)throw i[0];let{header:a,payload:o}=r,{typ:l,alg:u}=a;(0,s.qf)(l),(0,s.l3)(u);let{data:c,errors:d}=await (0,s.nk)(r,t);if(d)throw new n.zF({reason:n.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${d[0]}`});if(!c)throw new n.zF({reason:n.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function t9(e,t){let r,{secretKey:i,apiUrl:a,apiVersion:o,jwksCacheTtlInMs:l,jwtKey:u,skipJwksCache:c}=t,{data:d,errors:h}=(0,s.iU)(e);if(h)throw h[0];let{kid:p}=d.header;if(u)r=tX(u);else if(i)r=await tY({secretKey:i,apiUrl:a,apiVersion:o,kid:p,jwksCacheTtlInMs:l,skipJwksCache:c});else throw new n.zF({action:n.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:n.jn.JWKFailedToResolve});return await t7(e,{key:r})}var t6=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.startsWith("http")?this.authenticateContext.frontendApi:`https://${this.authenticateContext.frontendApi}`,i=new URL("v1/client/handshake",r=r.replace(/\/+$/,"")+"/");i.searchParams.append("redirect_url",t?.href||""),i.searchParams.append("__clerk_api_version",C),i.searchParams.append(R.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),i.searchParams.append(R.QueryParameters.HandshakeReason,e),i.searchParams.append(R.QueryParameters.HandshakeFormat,"nonce"),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&i.searchParams.append(R.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let s=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return s&&this.getOrganizationSyncQueryParams(s).forEach((e,t)=>{i.searchParams.append(t,e)}),new Headers({[R.Headers.Location]:i.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await t9(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),tW(t).startsWith(R.Cookies.Session)&&(r=tB(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(R.QueryParameters.Handshake),t.searchParams.delete(R.QueryParameters.HandshakeHelp),e.append(R.Headers.Location,t.toString()),e.set(R.Headers.CacheControl,"no-store")}if(""===r)return tz({tokenType:tO.SessionToken,authenticateContext:this.authenticateContext,reason:tM.SessionTokenMissing,message:"",headers:e});let{data:i,errors:[s]=[]}=await t1(r,this.authenticateContext);if(i)return tJ({tokenType:tO.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:i,headers:e,token:r});if("development"===this.authenticateContext.instanceType&&(s?.reason===n.jn.TokenExpired||s?.reason===n.jn.TokenNotActiveYet||s?.reason===n.jn.TokenIatInTheFuture)){let t=new n.zF({action:s.action,message:s.message,reason:s.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:i,errors:[a]=[]}=await t1(r,{...this.authenticateContext,clockSkewInMs:864e5});if(i)return tJ({tokenType:tO.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:i,headers:e,token:r});throw Error(a?.message||"Clerk: Handshake retry failed.")}throw Error(s?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===n.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=R.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(R.QueryParameters.DevBrowser),t.searchParams.delete(R.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},re=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,i,s,n,a,o,l;return r=void 0,i=[],s=function e(t,r,i){var s;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,i=0,s=r.exec(e.source);s;)t.push({name:s[1]||i++,prefix:"",suffix:"",modifier:"",pattern:""}),s=r.exec(e.source);return e}(t,r):Array.isArray(t)?(s=t.map(function(t){return e(t,r,i).source}),new RegExp("(?:".concat(s.join("|"),")"),A(i))):function(e,t,r){void 0===r&&(r={});for(var i=r.strict,s=void 0!==i&&i,n=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,u=r.delimiter,c=r.endsWith,d="[".concat(E(void 0===c?"":c),"]|$"),h="[".concat(E(void 0===u?"/#?":u),"]"),p=void 0===n||n?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=E(l(m));else{var y=E(l(m.prefix)),g=E(l(m.suffix));if(m.pattern)if(t&&t.push(m),y||g)if("+"===m.modifier||"*"===m.modifier){var k="*"===m.modifier?"?":"";p+="(?:".concat(y,"((?:").concat(m.pattern,")(?:").concat(g).concat(y,"(?:").concat(m.pattern,"))*)").concat(g,")").concat(k)}else p+="(?:".concat(y,"(").concat(m.pattern,")").concat(g,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));p+="(".concat(m.pattern,")").concat(m.modifier)}else p+="(?:".concat(y).concat(g,")").concat(m.modifier)}}if(void 0===a||a)s||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(d,")"):"$";else{var _=e[e.length-1],b="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;s||(p+="(?:".concat(h,"(?=").concat(d,"))?")),b||(p+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(p,A(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var i=e[r];if("*"===i||"+"===i||"?"===i){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===i){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===i){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===i){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===i){for(var s="",n=r+1;n<e.length;){var a=e.charCodeAt(n);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){s+=e[n++];continue}break}if(!s)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:s}),r=n;continue}if("("===i){var o=1,l="",n=r+1;if("?"===e[n])throw TypeError('Pattern cannot start with "?" at '.concat(n));for(;n<e.length;){if("\\"===e[n]){l+=e[n++]+e[n++];continue}if(")"===e[n]){if(0==--o){n++;break}}else if("("===e[n]&&(o++,"?"!==e[n+1]))throw TypeError("Capturing groups are not allowed at ".concat(n));l+=e[n++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=n;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),i=t.prefixes,s=void 0===i?"./":i,n=t.delimiter,a=void 0===n?"/#?":n,o=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var i=r[u],s=i.type,n=i.index;throw TypeError("Unexpected ".concat(s," at ").concat(n,", expected ").concat(e))},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(E(a),"]+?"):"(?:(?!".concat(E(r),")[^").concat(E(a),"])+?")};u<r.length;){var y=d("CHAR"),g=d("NAME"),k=d("PATTERN");if(g||k){var _=y||"";-1===s.indexOf(_)&&(c+=_,_=""),c&&(o.push(c),c=""),o.push({name:g||l++,prefix:_,suffix:"",pattern:k||m(_),modifier:d("MODIFIER")||""});continue}var b=y||d("ESCAPED_CHAR");if(b){c+=b;continue}if(c&&(o.push(c),c=""),d("OPEN")){var _=p(),v=d("NAME")||"",T=d("PATTERN")||"",S=p();h("CLOSE"),o.push({name:v||(T?l++:""),pattern:v&&!T?m(_):T,prefix:_,suffix:S,modifier:d("MODIFIER")||""});continue}h("END")}return o}(t,i),r,i)}(e,i,r),n=i,a=r,void 0===a&&(a={}),o=a.decode,l=void 0===o?function(e){return e}:o,function(e){var t=s.exec(e);if(!t)return!1;for(var r=t[0],i=t.index,a=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=n[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):a[r.name]=l(t[e],r)}}(o);return{path:r,index:i,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},rt={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};function rr(e,t,r){return tR(e,t)?null:tz({tokenType:e,authenticateContext:r,reason:tM.TokenTypeMismatch})}var ri=async(e,t)=>{let r=await M(tK(e),t);q(r.secretKey);let a=t.acceptsToken??tO.SessionToken;if(r.isSatellite){var o=r.signInUrl,l=r.secretKey;if(!o&&(0,i.Ve)(l))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let u=new re(t.organizationSyncOptions),c=new t6(r,{organizationSyncOptions:t.organizationSyncOptions},u);async function d(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:rt.MissingApiClient}}};let{sessionToken:i,refreshTokenInCookie:n}=r;if(!i)return{data:null,error:{message:"Session token must be provided.",cause:{reason:rt.MissingSessionToken}}};if(!n)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:rt.MissingRefreshToken}}};let{data:a,errors:o}=(0,s.iU)(i);if(!a||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:rt.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!a?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:rt.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(a.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:i||"",refresh_token:n||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:rt.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:rt.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function h(e){let{data:t,error:r}=await d(e);if(!t||0===t.length)return{data:null,error:r};let i=new Headers,s="";t.forEach(e=>{i.append("Set-Cookie",e),tW(e).startsWith(R.Cookies.Session)&&(s=tB(e))});let{data:n,errors:a}=await t1(s,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:rt.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:n,sessionToken:s,headers:i},error:null}}function p(e,t,r,i){if(!c.isRequestEligibleForHandshake())return tz({tokenType:tO.SessionToken,authenticateContext:e,reason:t,message:r});let s=i??c.buildRedirectToHandshake(t);return(s.get(R.Headers.Location)&&s.set(R.Headers.CacheControl,"no-store"),c.checkAndTrackRedirectLoop(s))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),tz({tokenType:tO.SessionToken,authenticateContext:e,reason:t,message:r})):function(e,t,r="",i){return tL({status:tj.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:tO.SessionToken,toAuth:()=>null,headers:i,token:null})}(e,t,r,s)}async function f(){let{tokenInHeader:e}=r;try{let{data:t,errors:i}=await t1(e,r);if(i)throw i[0];return tJ({tokenType:tO.SessionToken,authenticateContext:r,sessionClaims:t,headers:new Headers,token:e})}catch(e){return y(e,"header")}}async function m(){let e=r.clientUat,t=!!r.sessionTokenInCookie,i=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await c.resolveHandshake()}catch(e){e instanceof n.zF&&"development"===r.instanceType?c.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(R.QueryParameters.DevBrowser))return p(r,tM.DevBrowserSync,"");let a=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&a)return p(r,tM.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&a&&!r.clerkUrl.searchParams.has(R.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(R.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[R.Headers.Location]:e.toString()});return p(r,tM.SatelliteCookieNeedsSyncing,"",t)}let o=new URL(r.clerkUrl).searchParams.get(R.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&o){let e=new URL(o);r.devBrowserToken&&e.searchParams.append(R.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(R.QueryParameters.ClerkSynced,"true");let t=new Headers({[R.Headers.Location]:e.toString()});return p(r,tM.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!i)return p(r,tM.DevBrowserMissing,"");if(!e&&!t)return tz({tokenType:tO.SessionToken,authenticateContext:r,reason:tM.SessionTokenAndUATMissing});if(!e&&t)return p(r,tM.SessionTokenWithoutClientUAT,"");if(e&&!t)return p(r,tM.ClientUATWithoutSessionToken,"");let{data:l,errors:d}=(0,s.iU)(r.sessionTokenInCookie);if(d)return y(d[0],"cookie");if(l.payload.iat<r.clientUat)return p(r,tM.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await t1(r.sessionTokenInCookie,r);if(t)throw t[0];let i=tJ({tokenType:tO.SessionToken,authenticateContext:r,sessionClaims:e,headers:new Headers,token:r.sessionTokenInCookie});if(!r.isSatellite&&"document"===r.secFetchDest&&r.isCrossOriginReferrer())return p(r,tM.PrimaryDomainCrossOriginSync,"Cross-origin request from satellite domain requires handshake");let s=i.toAuth();if(s.userId){let e=function(e,t){let r=u.findTarget(e.clerkUrl);if(!r)return null;let i=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(i=!0),r.organizationId&&r.organizationId!==t.orgId&&(i=!0)),"personalAccount"===r.type&&t.orgId&&(i=!0),!i)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let s=p(e,tM.ActiveOrganizationMismatch,"");return"handshake"!==s.status?null:s}(r,s);if(e)return e}return i}catch(e){return y(e,"cookie")}}async function y(t,i){let s;if(!(t instanceof n.zF))return tz({tokenType:tO.SessionToken,authenticateContext:r,reason:tM.UnexpectedError});if(t.reason===n.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await h(r);if(e)return tJ({tokenType:tO.SessionToken,authenticateContext:r,sessionClaims:e.jwtPayload,headers:e.headers,token:e.sessionToken});s=t?.cause?.reason?t.cause.reason:rt.UnexpectedSDKError}else s="GET"!==e.method?rt.NonEligibleNonGet:r.refreshTokenInCookie?null:rt.NonEligibleNoCookie;return(t.tokenCarrier=i,[n.jn.TokenExpired,n.jn.TokenNotActiveYet,n.jn.TokenIatInTheFuture].includes(t.reason))?p(r,rn({tokenError:t.reason,refreshError:s}),t.getFullMessage()):tz({tokenType:tO.SessionToken,authenticateContext:r,reason:t.reason,message:t.getFullMessage()})}function g(e,t){return t instanceof n.sM?tz({tokenType:e,authenticateContext:r,reason:t.code,message:t.getFullMessage()}):tz({tokenType:e,authenticateContext:r,reason:tM.UnexpectedError})}async function k(){let{tokenInHeader:e}=r;if(!e)return y(Error("Missing token in header"),"header");if(!tP(e))return tz({tokenType:a,authenticateContext:r,reason:tM.TokenTypeMismatch,message:""});let t=rr(tx(e),a,r);if(t)return t;let{data:i,tokenType:s,errors:n}=await t4(e,r);return n?g(s,n[0]):tJ({tokenType:s,authenticateContext:r,machineData:i,token:e})}async function _(){let{tokenInHeader:e}=r;if(!e)return y(Error("Missing token in header"),"header");if(tP(e)){let t=rr(tx(e),a,r);if(t)return t;let{data:i,tokenType:s,errors:n}=await t4(e,r);return n?g(s,n[0]):tJ({tokenType:s,authenticateContext:r,machineData:i,token:e})}let{data:t,errors:i}=await t1(e,r);return i?y(i[0],"header"):tJ({tokenType:tO.SessionToken,authenticateContext:r,sessionClaims:t,token:e})}return Array.isArray(a)&&!function(e,t){let r=null,{tokenInHeader:i}=t;return i&&(r=tP(i)?tx(i):tO.SessionToken),tR(r??tO.SessionToken,e)}(a,r)?function(){let e={isAuthenticated:!1,tokenType:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:()=>({})};return tL({status:tj.SignedOut,reason:tM.TokenTypeMismatch,message:"",proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",signInUrl:"",signUpUrl:"",afterSignInUrl:"",afterSignUpUrl:"",isSignedIn:!1,isAuthenticated:!1,tokenType:null,toAuth:()=>e,headers:new Headers,token:null})}():r.tokenInHeader?"any"===a?_():a===tO.SessionToken?f():k():a===tO.OAuthToken||a===tO.ApiKey||a===tO.MachineToken?tz({tokenType:a,authenticateContext:r,reason:"No token in header"}):m()},rs=e=>{let{isSignedIn:t,isAuthenticated:r,proxyUrl:i,reason:s,message:n,publishableKey:a,isSatellite:o,domain:l}=e;return{isSignedIn:t,isAuthenticated:r,proxyUrl:i,reason:s,message:n,publishableKey:a,isSatellite:o,domain:l}},rn=({tokenError:e,refreshError:t})=>{switch(e){case n.jn.TokenExpired:return`${tM.SessionTokenExpired}-refresh-${t}`;case n.jn.TokenNotActiveYet:return tM.SessionTokenNBF;case n.jn.TokenIatInTheFuture:return tM.SessionTokenIatInTheFuture;default:return tM.UnexpectedError}},ra={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function ro(e){let t=N(ra,e.options),r=e.apiClient;return{authenticateRequest:(e,i={})=>{let{apiUrl:s,apiVersion:n}=t,a=N(t,i);return ri(e,{...i,...a,apiUrl:s,apiVersion:n,apiClient:r})},debugRequestState:rs}}},91777:(e,t,r)=>{function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),r(32913).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97683:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return i},isBailoutToCSRError:function(){return s}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class i extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}}};