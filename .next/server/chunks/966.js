"use strict";exports.id=966,exports.ids=[966],exports.modules={19924:(e,l,s)=>{s.d(l,{a:()=>t});var a=s(6475);let t=(0,a.createServerReference)("7fb39e1ae80adaf031bbbe57e170bd653b7110b56a",a.callServer,void 0,a.findSourceMapURL,"createOrReadKeylessAction")},93966:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>n});var a=s(16189),t=s(43210),r=s.n(t),i=s(19924);let n=e=>{var l;let{children:s}=e,n=(null==(l=(0,a.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[o,d]=r().useActionState(i.a,null);return((0,t.useEffect)(()=>{n||r().startTransition(()=>{d()})},[n]),r().isValidElement(s))?r().cloneElement(s,{key:null==o?void 0:o.publishableKey,publishableKey:null==o?void 0:o.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==o?void 0:o.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==o?void 0:o.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}}};