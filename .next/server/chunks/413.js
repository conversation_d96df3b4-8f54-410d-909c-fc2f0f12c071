"use strict";exports.id=413,exports.ids=[413],exports.modules={11921:(e,t,i)=>{i.d(t,{$:()=>iO,A:()=>tJ,B:()=>iB,D:()=>id,E:()=>ie,G:()=>tU,L:()=>iG,N:()=>iy,O:()=>ik,P:()=>iT,Q:()=>ic,R:()=>iq,S:()=>ii,T:()=>ir,U:()=>tF,V:()=>iE,W:()=>tB,X:()=>t$,Y:()=>tQ,Z:()=>ib,_:()=>iM,a:()=>iN,a0:()=>t3,a1:()=>tx,ab:()=>iU,ac:()=>iA,ad:()=>il,ae:()=>ea,af:()=>es,ag:()=>iF,ah:()=>iL,ai:()=>iK,aj:()=>iP,ao:()=>tA,ap:()=>tN,aq:()=>tj,c:()=>iV,d:()=>io,e:()=>t0,f:()=>iH,h:()=>is,i:()=>ij,j:()=>i_,k:()=>iC,l:()=>tL,m:()=>tD,n:()=>function e(t,i,n,r){if(t.length<1)throw Error("At least one grid layout definition must be provided.");let a=[...t].map(e=>{var t,i;return{name:`${e.columns}x${e.rows}`,columns:e.columns,rows:e.rows,maxTiles:e.columns*e.rows,minWidth:null!=(t=e.minWidth)?t:0,minHeight:null!=(i=e.minHeight)?i:0,orientation:e.orientation}}).sort((e,t)=>e.maxTiles!==t.maxTiles?e.maxTiles-t.maxTiles:0!==e.minWidth||0!==t.minWidth?e.minWidth-t.minWidth:0!==e.minHeight||0!==t.minHeight?e.minHeight-t.minHeight:0);if(n<=0||r<=0)return a[0];let s=0,o=n/r>1?"landscape":"portrait",c=a.find((e,t,n)=>{s=t;let r=-1!==n.findIndex((i,n)=>{let r=!i.orientation||i.orientation===o,a=i.maxTiles===e.maxTiles;return n>t&&a&&r});return e.maxTiles>=i&&!r});if(void 0===c)if(c=a[a.length-1])tL.warn(`No layout found for: participantCount: ${i}, width/height: ${n}/${r} fallback to biggest available layout (${c}).`);else throw Error("No layout or fallback layout found.");if((n<c.minWidth||r<c.minHeight)&&s>0){let t=a[s-1];c=e(a.slice(0,s),t.maxTiles,n,r)}return c},o:()=>t7,p:()=>tM,q:()=>t8,s:()=>iS,t:()=>t6,u:()=>iz,v:()=>it,w:()=>iW,x:()=>t1,y:()=>ia,z:()=>tq});var n=i(89967),r=i(43210);let a=Math.min,s=Math.max,o=Math.round,c=Math.floor,l=e=>({x:e,y:e}),d={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function h(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function f(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(p(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>u[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>d[e])}function k(e){let{x:t,y:i,width:n,height:r}=e;return{width:n,height:r,top:i,left:t,right:t+n,bottom:i+r,x:t,y:i}}function T(e,t,i){let n,{reference:r,floating:a}=e,s=v(t),o=f(v(t)),c=g(o),l=p(t),d="y"===s,u=r.x+r.width/2-a.width/2,h=r.y+r.height/2-a.height/2,b=r[c]/2-a[c]/2;switch(l){case"top":n={x:u,y:r.y-a.height};break;case"bottom":n={x:u,y:r.y+r.height};break;case"right":n={x:r.x+r.width,y:h};break;case"left":n={x:r.x-a.width,y:h};break;default:n={x:r.x,y:r.y}}switch(m(t)){case"start":n[o]-=b*(i&&d?-1:1);break;case"end":n[o]+=b*(i&&d?-1:1)}return n}let C=async(e,t,i)=>{let{placement:n="bottom",strategy:r="absolute",middleware:a=[],platform:s}=i,o=a.filter(Boolean),c=await (null==s.isRTL?void 0:s.isRTL(t)),l=await s.getElementRects({reference:e,floating:t,strategy:r}),{x:d,y:u}=T(l,n,c),h=n,p={},m=0;for(let i=0;i<o.length;i++){let{name:a,fn:f}=o[i],{x:g,y:v,data:b,reset:y}=await f({x:d,y:u,initialPlacement:n,placement:h,strategy:r,middlewareData:p,rects:l,platform:s,elements:{reference:e,floating:t}});d=g??d,u=v??u,p={...p,[a]:{...p[a],...b}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(h=y.placement),y.rects&&(l=!0===y.rects?await s.getElementRects({reference:e,floating:t,strategy:r}):y.rects),{x:d,y:u}=T(l,h,c)),i=-1)}return{x:d,y:u,placement:h,strategy:r,middlewareData:p}};async function S(e,t){var i,n;void 0===t&&(t={});let{x:r,y:a,platform:s,rects:o,elements:c,strategy:l}=e,{boundary:d="clippingAncestors",rootBoundary:u="viewport",elementContext:p="floating",altBoundary:m=!1,padding:f=0}=h(t,e),g="number"!=typeof(n=f)?{top:0,right:0,bottom:0,left:0,...n}:{top:n,right:n,bottom:n,left:n},v=c[m?"floating"===p?"reference":"floating":p],b=k(await s.getClippingRect({element:null==(i=await (null==s.isElement?void 0:s.isElement(v)))||i?v:v.contextElement||await (null==s.getDocumentElement?void 0:s.getDocumentElement(c.floating)),boundary:d,rootBoundary:u,strategy:l})),y="floating"===p?{x:r,y:a,width:o.floating.width,height:o.floating.height}:o.reference,T=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c.floating)),C=await (null==s.isElement?void 0:s.isElement(T))&&await (null==s.getScale?void 0:s.getScale(T))||{x:1,y:1},S=k(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:y,offsetParent:T,strategy:l}):y);return{top:(b.top-S.top+g.top)/C.y,bottom:(S.bottom-b.bottom+g.bottom)/C.y,left:(b.left-S.left+g.left)/C.x,right:(S.right-b.right+g.right)/C.x}}async function w(e,t){let{placement:i,platform:n,elements:r}=e,a=await (null==n.isRTL?void 0:n.isRTL(r.floating)),s=p(i),o=m(i),c="y"===v(i),l=["left","top"].includes(s)?-1:1,d=a&&c?-1:1,u=h(t,e),{mainAxis:f,crossAxis:g,alignmentAxis:b}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return o&&"number"==typeof b&&(g="end"===o?-1*b:b),c?{x:g*d,y:f*l}:{x:f*l,y:g*d}}function E(){return"u">typeof window}function P(e){return x(e)?(e.nodeName||"").toLowerCase():"#document"}function R(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(x(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function x(e){return!!E()&&(e instanceof Node||e instanceof R(e).Node)}function O(e){return!!E()&&(e instanceof Element||e instanceof R(e).Element)}function M(e){return!!E()&&(e instanceof HTMLElement||e instanceof R(e).HTMLElement)}function D(e){return!(!E()||typeof ShadowRoot>"u")&&(e instanceof ShadowRoot||e instanceof R(e).ShadowRoot)}function A(e){let{overflow:t,overflowX:i,overflowY:n,display:r}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+i)&&!["inline","contents"].includes(r)}function N(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function _(e){let t=L(),i=O(e)?j(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!i[e]&&"none"!==i[e])||!!i.containerType&&"normal"!==i.containerType||!t&&!!i.backdropFilter&&"none"!==i.backdropFilter||!t&&!!i.filter&&"none"!==i.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(i.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(i.contain||"").includes(e))}function L(){return!(typeof CSS>"u")&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function U(e){return["html","body","#document"].includes(P(e))}function j(e){return R(e).getComputedStyle(e)}function F(e){return O(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function B(e){if("html"===P(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||I(e);return D(t)?t.host:t}function V(e,t,i){var n;void 0===t&&(t=[]),void 0===i&&(i=!0);let r=function e(t){let i=B(t);return U(i)?t.ownerDocument?t.ownerDocument.body:t.body:M(i)&&A(i)?i:e(i)}(e),a=r===(null==(n=e.ownerDocument)?void 0:n.body),s=R(r);if(a){let e=q(s);return t.concat(s,s.visualViewport||[],A(r)?r:[],e&&i?V(e):[])}return t.concat(r,V(r,[],i))}function q(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function H(e){let t=j(e),i=parseFloat(t.width)||0,n=parseFloat(t.height)||0,r=M(e),a=r?e.offsetWidth:i,s=r?e.offsetHeight:n,c=o(i)!==a||o(n)!==s;return c&&(i=a,n=s),{width:i,height:n,$:c}}function W(e){return O(e)?e:e.contextElement}function z(e){let t=W(e);if(!M(t))return l(1);let i=t.getBoundingClientRect(),{width:n,height:r,$:a}=H(t),s=(a?o(i.width):i.width)/n,c=(a?o(i.height):i.height)/r;return s&&Number.isFinite(s)||(s=1),c&&Number.isFinite(c)||(c=1),{x:s,y:c}}let G=l(0);function K(e){let t=R(e);return L()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function J(e,t,i,n){var r;void 0===t&&(t=!1),void 0===i&&(i=!1);let a=e.getBoundingClientRect(),s=W(e),o=l(1);t&&(n?O(n)&&(o=z(n)):o=z(e));let c=(void 0===(r=i)&&(r=!1),n&&(!r||n===R(s))&&r)?K(s):l(0),d=(a.left+c.x)/o.x,u=(a.top+c.y)/o.y,h=a.width/o.x,p=a.height/o.y;if(s){let e=R(s),t=n&&O(n)?R(n):n,i=e,r=q(i);for(;r&&n&&t!==i;){let e=z(r),t=r.getBoundingClientRect(),n=j(r),a=t.left+(r.clientLeft+parseFloat(n.paddingLeft))*e.x,s=t.top+(r.clientTop+parseFloat(n.paddingTop))*e.y;d*=e.x,u*=e.y,h*=e.x,p*=e.y,d+=a,u+=s,r=q(i=R(r))}}return k({width:h,height:p,x:d,y:u})}function Y(e,t){let i=F(e).scrollLeft;return t?t.left+i:J(I(e)).left+i}function Q(e,t,i){void 0===i&&(i=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(i?0:Y(e,n)),y:n.top+t.scrollTop}}function $(e,t,i){let n;if("viewport"===t)n=function(e,t){let i=R(e),n=I(e),r=i.visualViewport,a=n.clientWidth,s=n.clientHeight,o=0,c=0;if(r){a=r.width,s=r.height;let e=L();(!e||e&&"fixed"===t)&&(o=r.offsetLeft,c=r.offsetTop)}return{width:a,height:s,x:o,y:c}}(e,i);else if("document"===t)n=function(e){let t=I(e),i=F(e),n=e.ownerDocument.body,r=s(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=s(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),o=-i.scrollLeft+Y(e),c=-i.scrollTop;return"rtl"===j(n).direction&&(o+=s(t.clientWidth,n.clientWidth)-r),{width:r,height:a,x:o,y:c}}(I(e));else if(O(t))n=function(e,t){let i=J(e,!0,"fixed"===t),n=i.top+e.clientTop,r=i.left+e.clientLeft,a=M(e)?z(e):l(1),s=e.clientWidth*a.x,o=e.clientHeight*a.y;return{width:s,height:o,x:r*a.x,y:n*a.y}}(t,i);else{let i=K(e);n={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return k(n)}function X(e){return"static"===j(e).position}function Z(e,t){if(!M(e)||"fixed"===j(e).position)return null;if(t)return t(e);let i=e.offsetParent;return I(e)===i&&(i=i.ownerDocument.body),i}function ee(e,t){let i=R(e);if(N(e))return i;if(!M(e)){let t=B(e);for(;t&&!U(t);){if(O(t)&&!X(t))return t;t=B(t)}return i}let n=Z(e,t);for(;n&&["table","td","th"].includes(P(n))&&X(n);)n=Z(n,t);return n&&U(n)&&X(n)&&!_(n)?i:n||function(e){let t=B(e);for(;M(t)&&!U(t);){if(_(t))return t;if(N(t))break;t=B(t)}return null}(e)||i}let et=async function(e){let t=this.getOffsetParent||ee,i=this.getDimensions,n=await i(e.floating);return{reference:function(e,t,i){let n=M(t),r=I(t),a="fixed"===i,s=J(e,!0,a,t),o={scrollLeft:0,scrollTop:0},c=l(0);if(n||!n&&!a)if(("body"!==P(t)||A(r))&&(o=F(t)),n){let e=J(t,!0,a,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else r&&(c.x=Y(r));let d=!r||n||a?l(0):Q(r,o);return{x:s.left+o.scrollLeft-c.x-d.x,y:s.top+o.scrollTop-c.y-d.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:i,offsetParent:n,strategy:r}=e,a="fixed"===r,s=I(n),o=!!t&&N(t.floating);if(n===s||o&&a)return i;let c={scrollLeft:0,scrollTop:0},d=l(1),u=l(0),h=M(n);if((h||!h&&!a)&&(("body"!==P(n)||A(s))&&(c=F(n)),M(n))){let e=J(n);d=z(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let p=!s||h||a?l(0):Q(s,c,!0);return{width:i.width*d.x,height:i.height*d.y,x:i.x*d.x-c.scrollLeft*d.x+u.x+p.x,y:i.y*d.y-c.scrollTop*d.y+u.y+p.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:i,rootBoundary:n,strategy:r}=e,o=[..."clippingAncestors"===i?N(t)?[]:function(e,t){let i=t.get(e);if(i)return i;let n=V(e,[],!1).filter(e=>O(e)&&"body"!==P(e)),r=null,a="fixed"===j(e).position,s=a?B(e):e;for(;O(s)&&!U(s);){let t=j(s),i=_(s);i||"fixed"!==t.position||(r=null),(a?!i&&!r:!i&&"static"===t.position&&!!r&&["absolute","fixed"].includes(r.position)||A(s)&&!i&&function e(t,i){let n=B(t);return!(n===i||!O(n)||U(n))&&("fixed"===j(n).position||e(n,i))}(e,s))?n=n.filter(e=>e!==s):r=t,s=B(s)}return t.set(e,n),n}(t,this._c):[].concat(i),n],c=o[0],l=o.reduce((e,i)=>{let n=$(t,i,r);return e.top=s(n.top,e.top),e.right=a(n.right,e.right),e.bottom=a(n.bottom,e.bottom),e.left=s(n.left,e.left),e},$(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:ee,getElementRects:et,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:i}=H(e);return{width:t,height:i}},getScale:z,isElement:O,isRTL:function(e){return"rtl"===j(e).direction}};function en(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let er=(e,t,i)=>{let n=new Map,r={platform:ei,...i},a={...r.platform,_c:n};return C(e,t,{...r,platform:a})};var ea="u">typeof globalThis?globalThis:"u">typeof window?window:"u">typeof global?global:"u">typeof self?self:{};function es(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var eo,ec={exports:{}},el=ec.exports;let ed=es(function(){var e;return eo||(eo=1,e=function(){var e=function(){},t="undefined",i=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),n=["trace","debug","info","warn","error"],r={},a=null;function s(e,t){var i=e[t];if("function"==typeof i.bind)return i.bind(e);try{return Function.prototype.bind.call(i,e)}catch{return function(){return Function.prototype.apply.apply(i,[e,arguments])}}}function o(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function c(){for(var i=this.getLevel(),r=0;r<n.length;r++){var a=n[r];this[a]=r<i?e:this.methodFactory(a,i,this.name)}if(this.log=this.debug,typeof console===t&&i<this.levels.SILENT)return"No console available for logging"}function l(e){return function(){typeof console!==t&&(c.call(this),this[e].apply(this,arguments))}}function d(n,r,a){var c;return"debug"===(c=n)&&(c="log"),typeof console!==t&&("trace"===c&&i?o:void 0!==console[c]?s(console,c):void 0!==console.log?s(console,"log"):e)||l.apply(this,arguments)}function u(e,i){var s,o,l,u=this,h="loglevel";function p(){var e;if(!(typeof window===t||!h)){try{e=window.localStorage[h]}catch{}if(typeof e===t)try{var i=window.document.cookie,n=encodeURIComponent(h),r=i.indexOf(n+"=");-1!==r&&(e=/^([^;]+)/.exec(i.slice(r+n.length+1))[1])}catch{}return void 0===u.levels[e]&&(e=void 0),e}}function m(e){var t=e;if("string"==typeof t&&void 0!==u.levels[t.toUpperCase()]&&(t=u.levels[t.toUpperCase()]),"number"==typeof t&&t>=0&&t<=u.levels.SILENT)return t;throw TypeError("log.setLevel() called with invalid level: "+e)}"string"==typeof e?h+=":"+e:"symbol"==typeof e&&(h=void 0),u.name=e,u.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},u.methodFactory=i||d,u.getLevel=function(){return l??o??s},u.setLevel=function(e,i){return l=m(e),!1!==i&&function(e){var i=(n[e]||"silent").toUpperCase();if(!(typeof window===t||!h)){try{window.localStorage[h]=i;return}catch{}try{window.document.cookie=encodeURIComponent(h)+"="+i+";"}catch{}}}(l),c.call(u)},u.setDefaultLevel=function(e){o=m(e),p()||u.setLevel(e,!1)},u.resetLevel=function(){l=null,function(){if(!(typeof window===t||!h)){try{window.localStorage.removeItem(h)}catch{}try{window.document.cookie=encodeURIComponent(h)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch{}}}(),c.call(u)},u.enableAll=function(e){u.setLevel(u.levels.TRACE,e)},u.disableAll=function(e){u.setLevel(u.levels.SILENT,e)},u.rebuild=function(){if(a!==u&&(s=m(a.getLevel())),c.call(u),a===u)for(var e in r)r[e].rebuild()},s=m(a?a.getLevel():"WARN");var f=p();null!=f&&(l=m(f)),c.call(u)}(a=new u).getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw TypeError("You must supply a name when creating a logger.");var t=r[e];return t||(t=r[e]=new u(e,a.methodFactory)),t};var h=typeof window!==t?window.log:void 0;return a.noConflict=function(){return typeof window!==t&&window.log===a&&(window.log=h),a},a.getLoggers=function(){return r},a.default=a,a},ec.exports?ec.exports=e():el.log=e()),ec.exports}());var eu=function(e,t){return(eu=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)};function eh(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}eu(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}function ep(e,t){var i,n,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=o(0),s.throw=o(1),s.return=o(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(o){return function(c){var l=[o,c];if(i)throw TypeError("Generator is already executing.");for(;s&&(s=0,l[0]&&(a=0)),a;)try{if(i=1,n&&(r=2&l[0]?n.return:l[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,l[1])).done)return r;switch(n=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,n=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(r=(r=a.trys).length>0&&r[r.length-1])&&(6===l[0]||2===l[0])){a=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){a.label=l[1];break}if(6===l[0]&&a.label<r[1]){a.label=r[1],r=l;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(l);break}r[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],n=0}finally{i=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}}function em(e){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&e[t],n=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function ef(e,t){var i="function"==typeof Symbol&&e[Symbol.iterator];if(!i)return e;var n,r,a=i.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)s.push(n.value)}catch(e){r={error:e}}finally{try{n&&!n.done&&(i=a.return)&&i.call(a)}finally{if(r)throw r.error}}return s}function eg(e,t,i){if(i||2==arguments.length)for(var n,r=0,a=t.length;r<a;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))}function ev(e){return this instanceof ev?(this.v=e,this):new ev(e)}function eb(e){return"function"==typeof e}function ey(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var ek=ey(function(e){return function(t){e(this),this.message=t?t.length+` errors occurred during unsubscription:
`+t.map(function(e,t){return t+1+") "+e.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=t}});function eT(e,t){if(e){var i=e.indexOf(t);0<=i&&e.splice(i,1)}}var eC=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var e,t,i,n,r;if(!this.closed){this.closed=!0;var a=this._parentage;if(a)if(this._parentage=null,Array.isArray(a))try{for(var s=em(a),o=s.next();!o.done;o=s.next())o.value.remove(this)}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}else a.remove(this);var c=this.initialTeardown;if(eb(c))try{c()}catch(e){r=e instanceof ek?e.errors:[e]}var l=this._finalizers;if(l){this._finalizers=null;try{for(var d=em(l),u=d.next();!u.done;u=d.next()){var h=u.value;try{eE(h)}catch(e){r=r??[],e instanceof ek?r=eg(eg([],ef(r)),ef(e.errors)):r.push(e)}}}catch(e){i={error:e}}finally{try{u&&!u.done&&(n=d.return)&&n.call(d)}finally{if(i)throw i.error}}}if(r)throw new ek(r)}},t.prototype.add=function(e){var i;if(e&&e!==this)if(this.closed)eE(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!=(i=this._finalizers)?i:[]).push(e)}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&eT(t,e)},t.prototype.remove=function(e){var i=this._finalizers;i&&eT(i,e),e instanceof t&&e._removeParent(this)},(e=new t).closed=!0,t.EMPTY=e,t}(),eS=eC.EMPTY;function ew(e){return e instanceof eC||e&&"closed"in e&&eb(e.remove)&&eb(e.add)&&eb(e.unsubscribe)}function eE(e){eb(e)?e():e.unsubscribe()}var eP={Promise:void 0},eR={setTimeout:function(e,t){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];return setTimeout.apply(void 0,eg([e,t],ef(i)))}};function eI(e){eR.setTimeout(function(){throw e})}function ex(){}var eO=function(e){function t(t){var i=e.call(this)||this;return i.isStopped=!1,t?(i.destination=t,ew(t)&&t.add(i)):i.destination=eA,i}return eh(t,e),t.create=function(e,t,i){return new eD(e,t,i)},t.prototype.next=function(e){this.isStopped||this._next(e)},t.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(eC),eM=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){eI(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){eI(e)}else eI(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){eI(e)}},e}(),eD=function(e){function t(t,i,n){var r,a=e.call(this)||this;return a.destination=new eM(eb(t)||!t?{next:t??void 0,error:i??void 0,complete:n??void 0}:t),a}return eh(t,e),t}(eO),eA={closed:!0,next:ex,error:function(e){throw e},complete:ex},eN="function"==typeof Symbol&&Symbol.observable||"@@observable";function e_(e){return e}var eL=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var i=new e;return i.source=this,i.operator=t,i},e.prototype.subscribe=function(e,t,i){var n,r,a=this,s=!function(e){return e&&e instanceof eO||e&&eb(e.next)&&eb(e.error)&&eb(e.complete)&&ew(e)}(e)?new eD(e,t,i):e;return n=a.operator,r=a.source,s.add(n?n.call(s,r):r?a._subscribe(s):a._trySubscribe(s)),s},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var i=this;return new(t=eU(t))(function(t,n){var r=new eD({next:function(t){try{e(t)}catch(e){n(e),r.unsubscribe()}},error:n,complete:t});i.subscribe(r)})},e.prototype._subscribe=function(e){var t;return null==(t=this.source)?void 0:t.subscribe(e)},e.prototype[eN]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0===e.length?e_:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)})(this)},e.prototype.toPromise=function(e){var t=this;return new(e=eU(e))(function(e,i){var n;t.subscribe(function(e){return n=e},function(e){return i(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function eU(e){var t;return null!=(t=e??eP.Promise)?t:Promise}function ej(e){return function(t){if(eb(null==t?void 0:t.lift))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}function eF(e,t,i,n,r){return new eB(e,t,i,n,r)}var eB=function(e){function t(t,i,n,r,a,s){var o=e.call(this,t)||this;return o.onFinalize=a,o.shouldUnsubscribe=s,o._next=i?function(e){try{i(e)}catch(e){t.error(e)}}:e.prototype._next,o._error=r?function(e){try{r(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,o._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,o}return eh(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var i=this.closed;e.prototype.unsubscribe.call(this),i||null==(t=this.onFinalize)||t.call(this)}},t}(eO),eV=ey(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),eq=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return eh(t,e),t.prototype.lift=function(e){var t=new eH(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new eV},t.prototype.next=function(e){var t,i;if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));try{for(var n=em(this.currentObservers),r=n.next();!r.done;r=n.next())r.value.next(e)}catch(e){t={error:e}}finally{try{r&&!r.done&&(i=n.return)&&i.call(n)}finally{if(t)throw t.error}}}},t.prototype.error=function(e){if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=e;for(var t=this.observers;t.length;)t.shift().error(e)}},t.prototype.complete=function(){if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;for(var e=this.observers;e.length;)e.shift().complete()}},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null==(e=this.observers)?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,i=this.hasError,n=this.isStopped,r=this.observers;return i||n?eS:(this.currentObservers=null,r.push(e),new eC(function(){t.currentObservers=null,eT(r,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,i=this.thrownError,n=this.isStopped;t?e.error(i):n&&e.complete()},t.prototype.asObservable=function(){var e=new eL;return e.source=this,e},t.create=function(e,t){return new eH(e,t)},t}(eL),eH=function(e){function t(t,i){var n=e.call(this)||this;return n.destination=t,n.source=i,n}return eh(t,e),t.prototype.next=function(e){var t,i;null==(i=null==(t=this.destination)?void 0:t.next)||i.call(t,e)},t.prototype.error=function(e){var t,i;null==(i=null==(t=this.destination)?void 0:t.error)||i.call(t,e)},t.prototype.complete=function(){var e,t;null==(t=null==(e=this.destination)?void 0:e.complete)||t.call(e)},t.prototype._subscribe=function(e){var t,i;return null!=(i=null==(t=this.source)?void 0:t.subscribe(e))?i:eS},t}(eq),eW=function(e){function t(t){var i=e.call(this)||this;return i._value=t,i}return eh(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),t.prototype._subscribe=function(t){var i=e.prototype._subscribe.call(this,t);return i.closed||t.next(this._value),i},t.prototype.getValue=function(){var e=this.hasError,t=this.thrownError,i=this._value;if(e)throw t;return this._throwIfClosed(),i},t.prototype.next=function(t){e.prototype.next.call(this,this._value=t)},t}(eq),ez={now:function(){return Date.now()}},eG=function(e){function t(t,i){return e.call(this)||this}return eh(t,e),t.prototype.schedule=function(e,t){return this},t}(eC),eK={setInterval:function(e,t){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];return setInterval.apply(void 0,eg([e,t],ef(i)))},clearInterval:function(e){return clearInterval(e)}},eJ=function(e){function t(t,i){var n=e.call(this,t,i)||this;return n.scheduler=t,n.work=i,n.pending=!1,n}return eh(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var i,n=this.id,r=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(r,n,t)),this.pending=!0,this.delay=t,this.id=null!=(i=this.id)?i:this.requestAsyncId(r,this.id,t),this},t.prototype.requestAsyncId=function(e,t,i){return void 0===i&&(i=0),eK.setInterval(e.flush.bind(e,this),i)},t.prototype.recycleAsyncId=function(e,t,i){if(void 0===i&&(i=0),null!=i&&this.delay===i&&!1===this.pending)return t;null!=t&&eK.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var i=this._execute(e,t);if(i)return i;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var i,n=!1;try{this.work(e)}catch(e){n=!0,i=e||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),i},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,i=this.scheduler,n=i.actions;this.work=this.state=this.scheduler=null,this.pending=!1,eT(n,this),null!=t&&(this.id=this.recycleAsyncId(i,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(eG),eY=function(){function e(t,i){void 0===i&&(i=e.now),this.schedulerActionCtor=t,this.now=i}return e.prototype.schedule=function(e,t,i){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(i,t)},e.now=ez.now,e}(),eQ=new(function(e){function t(t,i){void 0===i&&(i=eY.now);var n=e.call(this,t,i)||this;return n.actions=[],n._active=!1,n}return eh(t,e),t.prototype.flush=function(e){var t,i=this.actions;if(this._active)return void i.push(e);this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=i.shift());if(this._active=!1,t){for(;e=i.shift();)e.unsubscribe();throw t}},t}(eY))(eJ);function e$(e){var t;return(t=e[e.length-1])&&eb(t.schedule)?e.pop():void 0}var eX=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function eZ(e){return eb(null==e?void 0:e.then)}function e0(e){return Symbol.asyncIterator&&eb(null==e?void 0:e[Symbol.asyncIterator])}function e1(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var e5="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function e2(e){return eb(null==e?void 0:e[e5])}function e3(e){return function(e,t,i){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,r=i.apply(e,t||[]),a=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",function(e){return function(t){return Promise.resolve(t).then(e,l)}}),n[Symbol.asyncIterator]=function(){return this},n;function s(e,t){r[e]&&(n[e]=function(t){return new Promise(function(i,n){a.push([e,t,i,n])>1||o(e,t)})},t&&(n[e]=t(n[e])))}function o(e,t){try{var i;(i=r[e](t)).value instanceof ev?Promise.resolve(i.value.v).then(c,l):d(a[0][2],i)}catch(e){d(a[0][3],e)}}function c(e){o("next",e)}function l(e){o("throw",e)}function d(e,t){e(t),a.shift(),a.length&&o(a[0][0],a[0][1])}}(this,arguments,function(){var t,i,n;return ep(this,function(r){switch(r.label){case 0:t=e.getReader(),r.label=1;case 1:r.trys.push([1,,9,10]),r.label=2;case 2:return[4,ev(t.read())];case 3:return n=(i=r.sent()).value,i.done?[4,ev(void 0)]:[3,5];case 4:return[2,r.sent()];case 5:return[4,ev(n)];case 6:return[4,r.sent()];case 7:return r.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function e9(e){return eb(null==e?void 0:e.getReader)}function e6(e){if(e instanceof eL)return e;if(null!=e){var t,i,n,r;if(eb(e[eN])){return t=e,new eL(function(e){var i=t[eN]();if(eb(i.subscribe))return i.subscribe(e);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if(eX(e)){return i=e,new eL(function(e){for(var t=0;t<i.length&&!e.closed;t++)e.next(i[t]);e.complete()})}if(eZ(e)){return n=e,new eL(function(e){n.then(function(t){e.closed||(e.next(t),e.complete())},function(t){return e.error(t)}).then(null,eI)})}if(e0(e))return e4(e);if(e2(e)){return r=e,new eL(function(e){var t,i;try{for(var n=em(r),a=n.next();!a.done;a=n.next()){var s=a.value;if(e.next(s),e.closed)return}}catch(e){t={error:e}}finally{try{a&&!a.done&&(i=n.return)&&i.call(n)}finally{if(t)throw t.error}}e.complete()})}if(e9(e))return e4(e3(e))}throw e1(e)}function e4(e){return new eL(function(t){(function(e,t){var i,n,r,a,s,o,c,l;return s=this,o=void 0,c=void 0,l=function(){var s;return ep(this,function(o){switch(o.label){case 0:o.trys.push([0,5,6,11]),i=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,i=e[Symbol.asyncIterator];return i?i.call(e):(e=em(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(i){t[i]=e[i]&&function(t){return new Promise(function(n,r){var a,s,o;a=n,s=r,o=(t=e[i](t)).done,Promise.resolve(t.value).then(function(e){a({value:e,done:o})},s)})}}}(e),o.label=1;case 1:return[4,i.next()];case 2:if((n=o.sent()).done)return[3,4];if(s=n.value,t.next(s),t.closed)return[2];o.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return r={error:o.sent()},[3,11];case 6:return o.trys.push([6,,9,10]),n&&!n.done&&(a=i.return)?[4,a.call(i)]:[3,8];case 7:o.sent(),o.label=8;case 8:return[3,10];case 9:if(r)throw r.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})},new(c||(c=Promise))(function(e,t){function i(e){try{r(l.next(e))}catch(e){t(e)}}function n(e){try{r(l.throw(e))}catch(e){t(e)}}function r(t){var r;t.done?e(t.value):((r=t.value)instanceof c?r:new c(function(e){e(r)})).then(i,n)}r((l=l.apply(s,o||[])).next())})})(e,t).catch(function(e){return t.error(e)})})}function e7(e,t,i,n,r){void 0===n&&(n=0),void 0===r&&(r=!1);var a=t.schedule(function(){i(),r?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(a),!r)return a}function e8(e,t){return void 0===t&&(t=0),ej(function(i,n){i.subscribe(eF(n,function(i){return e7(n,e,function(){return n.next(i)},t)},function(){return e7(n,e,function(){return n.complete()},t)},function(i){return e7(n,e,function(){return n.error(i)},t)}))})}function te(e,t){return void 0===t&&(t=0),ej(function(i,n){n.add(e.schedule(function(){return i.subscribe(n)},t))})}function tt(e,t){if(!e)throw Error("Iterable cannot be null");return new eL(function(i){e7(i,t,function(){var n=e[Symbol.asyncIterator]();e7(i,t,function(){n.next().then(function(e){e.done?i.complete():i.next(e.value)})},0,!0)})})}function ti(e,t){return t?function(e,t){if(null!=e){if(eb(e[eN]))return e6(e).pipe(te(t),e8(t));if(eX(e))return new eL(function(i){var n=0;return t.schedule(function(){n===e.length?i.complete():(i.next(e[n++]),i.closed||this.schedule())})});if(eZ(e))return e6(e).pipe(te(t),e8(t));if(e0(e))return tt(e,t);if(e2(e))return new eL(function(i){var n;return e7(i,t,function(){n=e[e5](),e7(i,t,function(){var e,t,r;try{t=(e=n.next()).value,r=e.done}catch(e){i.error(e);return}r?i.complete():i.next(t)},0,!0)}),function(){return eb(null==n?void 0:n.return)&&n.return()}});if(e9(e))return tt(e3(e),t)}throw e1(e)}(e,t):e6(e)}function tn(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=e$(e);return ti(e,i)}var tr=ey(function(e){return function(t){void 0===t&&(t=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t}});function ta(e){throw new tr(e)}function ts(e,t){return ej(function(i,n){var r=0;i.subscribe(eF(n,function(i){n.next(e.call(t,i,r++))}))})}var to=Array.isArray;function tc(e,t,i){return void 0===i&&(i=1/0),eb(t)?tc(function(i,n){return ts(function(e,r){return t(i,e,n,r)})(e6(e(i,n)))},i):("number"==typeof t&&(i=t),ej(function(t,n){var r,a,s,o,c,l,d;return r=i,a=[],s=0,o=0,c=!1,l=function(){!c||a.length||s||n.complete()},d=function(t){s++;var i=!1;e6(e(t,o++)).subscribe(eF(n,function(e){n.next(e)},function(){i=!0},void 0,function(){if(i)try{for(s--;a.length&&s<r;)!function(){var e=a.shift();d(e)}();l()}catch(e){n.error(e)}}))},t.subscribe(eF(n,function(e){return s<r?d(e):a.push(e)},function(){c=!0,l()})),function(){}}))}function tl(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return tc(e_,1)(ti(e,e$(e)))}var td=["addListener","removeListener"],tu=["addEventListener","removeEventListener"],th=["on","off"];function tp(e,t){return function(i){return function(n){return e[i](t,n)}}}function tm(e,t){return ej(function(i,n){var r=0;i.subscribe(eF(n,function(i){return e.call(t,i,r++)&&n.next(i)}))})}function tf(e,t){return e===t}function tg(e,t){var i;return ej((i=arguments.length>=2,function(n,r){var a=i,s=t,o=0;n.subscribe(eF(r,function(t){var i=o++;s=a?e(s,t,i):(a=!0,t),r.next(s)},void 0))}))}function tv(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=e$(e);return ej(function(t,n){(i?tl(e,t,i):tl(e,t)).subscribe(n)})}function tb(e){return ej(function(t,i){e6(e).subscribe(eF(i,function(){return i.complete()},ex)),i.closed||t.subscribe(i)})}var ty=Object.defineProperty,tk=Object.defineProperties,tT=Object.getOwnPropertyDescriptors,tC=Object.getOwnPropertySymbols,tS=Object.prototype.hasOwnProperty,tw=Object.prototype.propertyIsEnumerable,tE=(e,t,i)=>t in e?ty(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,tP=(e,t)=>{for(var i in t||(t={}))tS.call(t,i)&&tE(e,i,t[i]);if(tC)for(var i of tC(t))tw.call(t,i)&&tE(e,i,t[i]);return e},tR=(e,t)=>tk(e,tT(t)),tI=(e,t,i)=>new Promise((n,r)=>{var a=e=>{try{o(i.next(e))}catch(e){r(e)}},s=e=>{try{o(i.throw(e))}catch(e){r(e)}},o=e=>e.done?n(e.value):Promise.resolve(e.value).then(a,s);o((i=i.apply(e,t)).next())});function tx(e){var t,i,n;return!(typeof e>"u")&&(!!(t=e)&&t.hasOwnProperty("participant")&&t.hasOwnProperty("source")&&t.hasOwnProperty("track")&&"u">typeof(null==(i=t.publication)?void 0:i.track)||!!(n=e)&&n.hasOwnProperty("participant")&&n.hasOwnProperty("source")&&n.hasOwnProperty("publication")&&"u">typeof n.publication)}function tO(e){return!!e&&e.hasOwnProperty("participant")&&e.hasOwnProperty("source")&&typeof e.publication>"u"}function tM(e){if("string"==typeof e||"number"==typeof e)return`${e}`;if(tO(e))return`${e.participant.identity}_${e.source}_placeholder`;if(tx(e))return`${e.participant.identity}_${e.publication.source}_${e.publication.trackSid}`;throw Error(`Can't generate a id for the given track reference: ${e}`)}function tD(e,t){return!(typeof t>"u")&&(tx(e)?t.some(t=>t.participant.identity===e.participant.identity&&tx(t)&&t.publication.trackSid===e.publication.trackSid):!!tO(e)&&t.some(t=>t.participant.identity===e.participant.identity&&tO(t)&&t.source===e.source))}function tA(e,t,i){return function(e,t,i,n){void 0===n&&(n={});let{ancestorScroll:r=!0,ancestorResize:o=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,h=W(e),p=r||o?[...h?V(h):[],...V(t)]:[];p.forEach(e=>{r&&e.addEventListener("scroll",i,{passive:!0}),o&&e.addEventListener("resize",i)});let m=h&&d?function(e,t){let i=null,n,r=I(e);function o(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return function l(d,u){void 0===d&&(d=!1),void 0===u&&(u=1),o();let h=e.getBoundingClientRect(),{left:p,top:m,width:f,height:g}=h;if(d||t(),!f||!g)return;let v=c(m),b=c(r.clientWidth-(p+f)),y={rootMargin:-v+"px "+-b+"px "+-c(r.clientHeight-(m+g))+"px "+-c(p)+"px",threshold:s(0,a(1,u))||1},k=!0;function T(t){let i=t[0].intersectionRatio;if(i!==u){if(!k)return l();i?l(!1,i):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==i||en(h,e.getBoundingClientRect())||l(),k=!1}try{i=new IntersectionObserver(T,{...y,root:r.ownerDocument})}catch{i=new IntersectionObserver(T,y)}i.observe(e)}(!0),o}(h,i):null,f=-1,g=null;l&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===h&&g&&(g.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),i()}),h&&!u&&g.observe(h),g.observe(t));let v,b=u?J(e):null;return u&&function t(){let n=J(e);b&&!en(b,n)&&i(),b=n,v=requestAnimationFrame(t)}(),i(),()=>{var e;p.forEach(e=>{r&&e.removeEventListener("scroll",i),o&&e.removeEventListener("resize",i)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,u&&cancelAnimationFrame(v)}}(e,t,()=>tI(this,null,function*(){var n,r;let{x:o,y:c}=yield er(e,t,{placement:"top",middleware:[{name:"offset",options:6,async fn(e){var t,i;let{x:n,y:r,placement:a,middlewareData:s}=e,o=await w(e,6);return a===(null==(t=s.offset)?void 0:t.placement)&&null!=(i=s.arrow)&&i.alignmentOffset?{}:{x:n+o.x,y:r+o.y,data:{...o,placement:a}}}},(void 0===n&&(n={}),{name:"flip",options:n,async fn(e){var t,i,r,a,s;let{placement:o,middlewareData:c,rects:l,initialPlacement:d,platform:u,elements:k}=e,{mainAxis:T=!0,crossAxis:C=!0,fallbackPlacements:w,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:R=!0,...I}=h(n,e);if(null!=(t=c.arrow)&&t.alignmentOffset)return{};let x=p(o),O=v(d),M=p(d)===d,D=await (null==u.isRTL?void 0:u.isRTL(k.floating)),A=w||(M||!R?[y(d)]:function(e){let t=y(e);return[b(e),t,b(t)]}(d)),N="none"!==P;!w&&N&&A.push(...function(e,t,i,n){let r=m(e),a=function(e,t,i){let n=["left","right"],r=["right","left"];switch(e){case"top":case"bottom":return i?t?r:n:t?n:r;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===i,n);return r&&(a=a.map(e=>e+"-"+r),t&&(a=a.concat(a.map(b)))),a}(d,R,P,D));let _=[d,...A],L=await S(e,I),U=[],j=(null==(i=c.flip)?void 0:i.overflows)||[];if(T&&U.push(L[x]),C){let e=function(e,t,i){void 0===i&&(i=!1);let n=m(e),r=f(v(e)),a=g(r),s="x"===r?n===(i?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(s=y(s)),[s,y(s)]}(o,l,D);U.push(L[e[0]],L[e[1]])}if(j=[...j,{placement:o,overflows:U}],!U.every(e=>e<=0)){let e=((null==(r=c.flip)?void 0:r.index)||0)+1,t=_[e];if(t)return{data:{index:e,overflows:j},reset:{placement:t}};let i=null==(a=j.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!i)switch(E){case"bestFit":{let e=null==(s=j.filter(e=>{if(N){let t=v(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(i=e);break}case"initialPlacement":i=d}if(o!==i)return{reset:{placement:i}}}return{}}}),{name:"shift",options:r={padding:5},async fn(e){let{x:t,y:i,placement:n}=e,{mainAxis:o=!0,crossAxis:c=!1,limiter:l={fn:e=>{let{x:t,y:i}=e;return{x:t,y:i}}},...d}=h(r,e),u={x:t,y:i},m=await S(e,d),g=v(p(n)),b=f(g),y=u[b],k=u[g];if(o){let e="y"===b?"top":"left",t="y"===b?"bottom":"right",i=y+m[e],n=y-m[t];y=s(i,a(y,n))}if(c){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",i=k+m[e],n=k-m[t];k=s(i,a(k,n))}let T=l.fn({...e,[b]:y,[g]:k});return{...T,data:{x:T.x-t,y:T.y-i,enabled:{[b]:o,[g]:c}}}}}]});null==i||i(o,c)}))}function tN(e,t){return!e.contains(t.target)}var t_=[n.u9.ConnectionStateChanged,n.u9.RoomMetadataChanged,n.u9.ActiveSpeakersChanged,n.u9.ConnectionQualityChanged,n.u9.ParticipantConnected,n.u9.ParticipantDisconnected,n.u9.ParticipantPermissionsChanged,n.u9.ParticipantMetadataChanged,n.u9.ParticipantNameChanged,n.u9.ParticipantAttributesChanged,n.u9.TrackMuted,n.u9.TrackUnmuted,n.u9.TrackPublished,n.u9.TrackUnpublished,n.u9.TrackStreamStateChanged,n.u9.TrackSubscriptionFailed,n.u9.TrackSubscriptionPermissionChanged,n.u9.TrackSubscriptionStatusChanged,n.u9.LocalTrackPublished,n.u9.LocalTrackUnpublished],tL=(n.YI.TrackPublished,n.YI.TrackUnpublished,n.YI.TrackMuted,n.YI.TrackUnmuted,n.YI.TrackStreamStateChanged,n.YI.TrackSubscribed,n.YI.TrackUnsubscribed,n.YI.TrackSubscriptionPermissionChanged,n.YI.TrackSubscriptionFailed,n.YI.LocalTrackPublished,n.YI.LocalTrackUnpublished,n.YI.ConnectionQualityChanged,n.YI.IsSpeakingChanged,n.YI.ParticipantMetadataChanged,n.YI.ParticipantPermissionsChanged,n.YI.TrackMuted,n.YI.TrackUnmuted,n.YI.TrackPublished,n.YI.TrackUnpublished,n.YI.TrackStreamStateChanged,n.YI.TrackSubscriptionFailed,n.YI.TrackSubscriptionPermissionChanged,n.YI.TrackSubscriptionStatusChanged,n.YI.LocalTrackPublished,n.YI.LocalTrackUnpublished,ed.getLogger("lk-components-js"));tL.setDefaultLevel("WARN");var tU=[{columns:1,rows:1},{columns:1,rows:2,orientation:"portrait"},{columns:2,rows:1,orientation:"landscape"},{columns:2,rows:2,minWidth:560},{columns:3,rows:3,minWidth:700},{columns:4,rows:4,minWidth:960},{columns:5,rows:5,minWidth:1100}];function tj(){return"u">typeof navigator&&navigator.mediaDevices&&!!navigator.mediaDevices.getDisplayMedia}function tF(e){return"object"==typeof e}function tB(e){return Array.isArray(e)&&e.filter(tF).length>0}function tV(e,t){var i,n,r,a;return(null!=(n=null==(i=e.joinedAt)?void 0:i.getTime())?n:0)-(null!=(a=null==(r=t.joinedAt)?void 0:r.getTime())?a:0)}function tq(e){let t=[],i=[],r=[],a=[];return e.forEach(e=>{e.participant.isLocal&&e.source===n.CC.Source.Camera?t.push(e):e.source===n.CC.Source.ScreenShare?i.push(e):e.source===n.CC.Source.Camera?r.push(e):a.push(e)}),[...t,...function(e){let t=[],i=[];return e.forEach(e=>{e.participant.isLocal?t.push(e):i.push(e)}),t.sort((e,t)=>tV(e.participant,t.participant)),i.sort((e,t)=>tV(e.participant,t.participant)),[...i,...t]}(i),...function(e){let t=[],i=[];return e.forEach(e=>{e.participant.isLocal?t.push(e):i.push(e)}),i.sort((e,t)=>{var i,n,r,a,s,o,c,l,d;return e.participant.isSpeaking&&t.participant.isSpeaking?(i=e.participant,t.participant.audioLevel-i.audioLevel):e.participant.isSpeaking!==t.participant.isSpeaking?(n=e.participant,r=t.participant,n.isSpeaking===r.isSpeaking?0:n.isSpeaking?-1:1):e.participant.lastSpokeAt!==t.participant.lastSpokeAt?(a=e.participant,s=t.participant,void 0!==a.lastSpokeAt||void 0!==s.lastSpokeAt?(null!=(c=null==(o=s.lastSpokeAt)?void 0:o.getTime())?c:0)-(null!=(d=null==(l=a.lastSpokeAt)?void 0:l.getTime())?d:0):0):tx(e)!==tx(t)?tx(e)?tx(t)?0:-1:+!!tx(t):e.participant.isCameraEnabled!==t.participant.isCameraEnabled?function(e,t){let i=e.participant.isCameraEnabled;return i!==t.participant.isCameraEnabled?i?-1:1:0}(e,t):tV(e.participant,t.participant)}),[...t,...i]}(r),...a]}function tH(e,t){return Array(Math.max(e.length,t.length)).fill([]).map((i,n)=>[e[n],t[n]])}function tW(e,t,i){return e.filter(e=>!t.map(e=>i(e)).includes(i(e)))}function tz(e){return e.map(e=>"string"==typeof e||"number"==typeof e?`${e}`:tM(e))}function tG(e,t){let i=t.findIndex(t=>tM(t)===tM(e));if(-1===i)throw Error(`Element not part of the array: ${tM(e)} not in ${tz(t)}`);return i}function tK(e,t){return e.reduce((e,i,n)=>n%t==0?[...e,[i]]:[...e.slice(0,-1),[...e.slice(-1)[0],i]],[])}function tJ(e,t,i){var n,r;let a=(n=e,r=t,n.map(e=>r.find(t=>tM(e)===tM(t)||"number"!=typeof e&&tO(e)&&tx(t)&&tO(e)&&tx(t)&&t.participant.identity===e.participant.identity&&t.source===e.source)??e));if(a.length<t.length){let e=tW(t,a,tM);a=[...a,...e]}if(tH(tK(a,i),tK(t,i)).forEach(([e,t],n)=>{if(e&&t){var r;let s={dropped:tW(r=tK(a,i)[n],t,tM),added:tW(t,r,tM)};(0!==s.added.length||0!==s.dropped.length)&&(tL.debug(`Detected visual changes on page: ${n}, current: ${tz(e)}, next: ${tz(t)}`,{changes:s}),s.added.length===s.dropped.length&&tH(s.added,s.dropped).forEach(([e,t])=>{if(e&&t)a=function(e,t,i){let n=tG(e,i),r=tG(t,i);return i.splice(n,1,t),i.splice(r,1,e),i}(e,t,a);else throw Error(`For a swap action we need a addition and a removal one is missing: ${e}, ${t}`)}),0===s.added.length&&s.dropped.length>0&&s.dropped.forEach(e=>{a=function(e,t){let i=tG(e,t);return t.splice(i,1),t}(e,a)}),s.added.length>0&&0===s.dropped.length&&s.added.forEach(e=>{a=[...a,e]}))}}),a.length>t.length){let e=tW(a,t,tM);a=a.filter(t=>!e.map(tM).includes(tM(t)))}return a}function tY(e){return`lk-${e}`}function tQ(e){let t=t$(e),i=t6(e.participant).pipe(ts(()=>t$(e)),tv(t));return{className:tY(e.source===n.CC.Source.Camera||e.source===n.CC.Source.ScreenShare?"participant-media-video":"participant-media-audio"),trackObserver:i}}function t$(e){if(tx(e))return e.publication;{let{source:t,name:i,participant:n}=e;if(t&&i)return n.getTrackPublications().find(e=>e.source===t&&e.trackName===i);if(i)return n.getTrackPublicationByName(i);if(t)return n.getTrackPublication(t);throw Error("At least one of source and name needs to be defined")}}function tX(e,...t){return new eL(i=>{let n=()=>{i.next(e)};return t.forEach(t=>{e.on(t,n)}),()=>{t.forEach(t=>{e.off(t,n)})}}).pipe(tv(e))}function tZ(e,t){return new eL(i=>{let n=(...e)=>{i.next(e)};return e.on(t,n),()=>{e.off(t,n)}})}function t0(e){return tZ(e,n.u9.ConnectionStateChanged).pipe(ts(([e])=>e),tv(e.state))}function t1(e,t,i=!0){return tl(new eL(r=>{n.Wv.getLocalDevices(e,i).then(e=>{r.next(e),r.complete()}).catch(e=>{null==t||t(e),r.next([]),r.complete()})}),new eL(r=>{var a;let s=()=>tI(this,null,function*(){try{let t=yield n.Wv.getLocalDevices(e,i);r.next(t)}catch(e){null==t||t(e)}});if("u">typeof window){if(!window.isSecureContext)throw Error("Accessing media devices is available only in secure contexts (HTTPS and localhost), in some or all supporting browsers. See: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/mediaDevices");null==(a=null==navigator?void 0:navigator.mediaDevices)||a.addEventListener("devicechange",s)}return()=>{var e;null==(e=null==navigator?void 0:navigator.mediaDevices)||e.removeEventListener("devicechange",s)}}))}function t5(e){return tX(e,n.u9.AudioPlaybackStatusChanged).pipe(ts(e=>({canPlayAudio:e.canPlaybackAudio})))}function t2(e){return tX(e,n.u9.VideoPlaybackStatusChanged).pipe(ts(e=>({canPlayVideo:e.canPlaybackVideo})))}function t3(e,t){return tZ(e,n.u9.ParticipantEncryptionStatusChanged).pipe(tm(([,i])=>(null==t?void 0:t.identity)===(null==i?void 0:i.identity)||!i&&(null==t?void 0:t.identity)===e.localParticipant.identity),ts(([e])=>e),tv(null!=t&&t.isLocal?t.isE2EEEnabled:!!(null!=t&&t.isEncrypted)))}function t9(e,...t){return new eL(i=>{let n=()=>{i.next(e)};return t.forEach(t=>{e.on(t,n)}),()=>{t.forEach(t=>{e.off(t,n)})}}).pipe(tv(e))}function t6(e){return t9(e,n.YI.TrackMuted,n.YI.TrackUnmuted,n.YI.ParticipantPermissionsChanged,n.YI.TrackPublished,n.YI.TrackUnpublished,n.YI.LocalTrackPublished,n.YI.LocalTrackUnpublished,n.YI.MediaDevicesError,n.YI.TrackSubscriptionStatusChanged).pipe(ts(e=>{let{isMicrophoneEnabled:t,isCameraEnabled:i,isScreenShareEnabled:r}=e,a=e.getTrackPublication(n.CC.Source.Microphone);return{isCameraEnabled:i,isMicrophoneEnabled:t,isScreenShareEnabled:r,cameraTrack:e.getTrackPublication(n.CC.Source.Camera),microphoneTrack:a,participant:e}}))}function t4(e,t){return new eL(i=>{let n=(...e)=>{i.next(e)};return e.on(t,n),()=>{e.off(t,n)}})}function t7(e){var t,i,r,a;return t9(e.participant,n.YI.TrackMuted,n.YI.TrackUnmuted,n.YI.TrackSubscribed,n.YI.TrackUnsubscribed,n.YI.LocalTrackPublished,n.YI.LocalTrackUnpublished).pipe(ts(t=>{var i,n;let r=null!=(i=e.publication)?i:t.getTrackPublication(e.source);return null==(n=null==r?void 0:r.isMuted)||n}),tv(null==(a=null!=(r=null==(t=e.publication)?void 0:t.isMuted)?r:null==(i=e.participant.getTrackPublication(e.source))?void 0:i.isMuted)||a))}function t8(e){return t4(e,n.YI.IsSpeakingChanged).pipe(ts(([e])=>e))}function ie(e,t={}){var i;let r,a=new eL(e=>(r=e,()=>o.unsubscribe())).pipe(tv(Array.from(e.remoteParticipants.values()))),s=null!=(i=t.additionalRoomEvents)?i:t_,o=tX(e,...Array.from(new Set([n.u9.ParticipantConnected,n.u9.ParticipantDisconnected,n.u9.ConnectionStateChanged,...s]))).subscribe(e=>null==r?void 0:r.next(Array.from(e.remoteParticipants.values())));return e.remoteParticipants.size>0&&(null==r||r.next(Array.from(e.remoteParticipants.values()))),a}function it(e){return t4(e,n.YI.ParticipantPermissionsChanged).pipe(ts(()=>e.permissions),tv(e.permissions))}function ii(e,t,i,r,a){let{localParticipant:s}=t,o=(e,t)=>{let i=!1;switch(e){case n.CC.Source.Camera:i=t.isCameraEnabled;break;case n.CC.Source.Microphone:i=t.isMicrophoneEnabled;break;case n.CC.Source.ScreenShare:i=t.isScreenShareEnabled}return i},c=t6(s).pipe(ts(t=>o(e,t.participant)),tv(o(e,s))),l=new eq;return{className:tY("button"),toggle:(t,o)=>tI(this,null,function*(){try{switch(o??(o=i),l.next(!0),e){case n.CC.Source.Camera:return yield s.setCameraEnabled(t??!s.isCameraEnabled,o,r),s.isCameraEnabled;case n.CC.Source.Microphone:return yield s.setMicrophoneEnabled(t??!s.isMicrophoneEnabled,o,r),s.isMicrophoneEnabled;case n.CC.Source.ScreenShare:return yield s.setScreenShareEnabled(t??!s.isScreenShareEnabled,o,r),s.isScreenShareEnabled;default:throw TypeError("Tried to toggle unsupported source")}}catch(e){if(a&&e instanceof Error){null==a||a(e);return}throw e}finally{l.next(!1)}}),enabledObserver:c,pendingObserver:l.asObservable()}}function ir(){let e=!1,t=new eq,i=new eq;return{className:tY("button"),toggle:n=>tI(this,null,function*(){i.next(!0),e=n??!e,t.next(e),i.next(!1)}),enabledObserver:t.asObservable(),pendingObserver:i.asObservable()}}function ia(e,t,i){let r=new eW(void 0),a=tZ(t,n.u9.ActiveDeviceChanged).pipe(tm(([t])=>t===e),ts(([e,t])=>(tL.debug("activeDeviceObservable | RoomEvent.ActiveDeviceChanged",{kind:e,deviceId:t}),t)));return{className:tY("media-device-select"),activeDeviceObservable:a,setActiveMediaDevice:(i,...a)=>tI(this,[i,...a],function*(i,a={}){var s,o,c;if(t){let l;tL.debug(`Switching active device of kind "${e}" with id ${i}.`),yield t.switchActiveDevice(e,i,a.exact);let d=null!=(s=t.getActiveDevice(e))?s:i;d!==i&&"default"!==i&&tL.info(`We tried to select the device with id (${i}), but the browser decided to select the device with id (${d}) instead.`),"audioinput"===e?l=null==(o=t.localParticipant.getTrackPublication(n.CC.Source.Microphone))?void 0:o.track:"videoinput"===e&&(l=null==(c=t.localParticipant.getTrackPublication(n.CC.Source.Camera))?void 0:c.track);let u="default"===i&&!l||"default"===i&&(null==l?void 0:l.mediaStreamTrack.label.startsWith("Default"));r.next(u?i:d)}})}}function is(e){return{className:tY("disconnect-button"),disconnect:t=>{e.disconnect(t)}}}function io(e){return{className:tY("connection-quality"),connectionQualityObserver:t4(e,n.YI.ConnectionQualityChanged).pipe(ts(([e])=>e),tv(e.connectionQuality))}}function ic(e){let t="track-muted-indicator-camera";switch(e.source){case n.CC.Source.Camera:t="track-muted-indicator-camera";break;case n.CC.Source.Microphone:t="track-muted-indicator-microphone"}return{className:tY(t),mediaMutedObserver:t7(e)}}function il(e){return{className:"lk-participant-name",infoObserver:e?t9(e,n.YI.ParticipantMetadataChanged,n.YI.ParticipantNameChanged).pipe(ts(({name:e,identity:t,metadata:i})=>({name:e,identity:t,metadata:i})),tv({name:e.name,identity:e.identity,metadata:e.metadata})):void 0}}function id(){return{className:tY("participant-tile")}}var iu={CHAT:"lk.chat"},ih={CHAT:"lk-chat-topic"};function ip(e,t){return tI(this,arguments,function*(e,t,i={}){let{reliable:n,destinationIdentities:r,topic:a}=i;yield e.publishData(t,{destinationIdentities:r,topic:a,reliable:n})})}var im=new WeakMap,ig=e=>JSON.parse(new TextDecoder().decode(e)),iv=e=>new TextEncoder().encode(JSON.stringify(e));function ib(e,t){var i,r,a,s,o,c;let l=()=>{var t,i,r;return(null==(t=e.serverInfo)?void 0:t.edition)===1||!!(null!=(i=e.serverInfo)&&i.version)&&(0,n.Zy)(null==(r=e.serverInfo)?void 0:r.version,"1.8.2")>0},d=new eq,u=null!=(i=null==t?void 0:t.channelTopic)?i:iu.CHAT,h=null!=(r=null==t?void 0:t.channelTopic)?r:ih.CHAT,p=!1;im.has(e)||(p=!0);let m=null!=(a=im.get(e))?a:new Map,f=null!=(s=m.get(u))?s:new eq;m.set(u,f),im.set(e,m);let g=null!=(o=null==t?void 0:t.messageDecoder)?o:ig;if(p){e.registerTextStreamHandler(u,(t,i)=>tI(this,null,function*(){let{id:n,timestamp:r}=t.info;ti(t).pipe(tg((e,t)=>e+t),ts(t=>({id:n,timestamp:r,message:t,from:e.getParticipantByIdentity(i.identity)}))).subscribe({next:e=>f.next(e)})}));let{messageObservable:t}=function(e,t,i){let r,a=Array.isArray(t)?t:[t];return{messageObservable:tZ(e,n.u9.DataReceived).pipe(tm(([,,,e])=>void 0===t||void 0!==e&&a.includes(e)),ts(([e,t,,i])=>({payload:e,topic:i,from:t}))),isSendingObservable:new eL(e=>{r=e}),send:(t,...i)=>tI(this,[t,...i],function*(t,i={}){r.next(!0);try{yield ip(e.localParticipant,t,tP({topic:a[0]},i))}finally{r.next(!1)}})}}(e,[h]);t.pipe(ts(e=>{let t=g(e.payload);return!0==t.ignoreLegacy?void 0:tR(tP({},t),{from:e.from})}),tm(e=>!!e),tb(d)).subscribe(f)}let v=f.pipe(tg((e,t)=>{if("id"in t&&e.find(e=>{var i,n;return(null==(i=e.from)?void 0:i.identity)===(null==(n=t.from)?void 0:n.identity)&&e.id===t.id})){let i=e.findIndex(e=>e.id===t.id);if(i>-1){let n=e[i];e[i]=tR(tP({},t),{timestamp:n.timestamp,editTimestamp:t.timestamp})}return[...e]}return[...e,t]},[]),tb(d)),b=new eW(!1),y=null!=(c=null==t?void 0:t.messageEncoder)?c:iv;return e.once(n.u9.Disconnected,function(){d.next(),d.complete(),f.complete(),im.delete(e),e.unregisterTextStreamHandler(u)}),{messageObservable:v,isSendingObservable:b,send:(t,i)=>tI(this,null,function*(){i||(i={}),null!=i.topic||(i.topic=u),b.next(!0);try{let n={id:(yield e.localParticipant.sendText(t,i)).id,timestamp:Date.now(),message:t},r=tR(tP({},n),{attachedFiles:i.attachments}),a=tR(tP({},r),{from:e.localParticipant,attributes:i.attributes});f.next(a);let s=y(tR(tP({},n),{ignoreLegacy:l()}));try{yield ip(e.localParticipant,s,{reliable:!0,topic:h})}catch(e){tL.info("could not send message in legacy chat format",e)}return a}finally{b.next(!1)}})}}function iy(){return{className:tY("start-audio-button"),roomAudioPlaybackAllowedObservable:t5,handleStartAudioPlayback:e=>tI(this,null,function*(){tL.info("Start Audio for room: ",e),yield e.startAudio()})}}function ik(){return{className:tY("start-audio-button"),roomVideoPlaybackAllowedObservable:t2,handleStartVideoPlayback:e=>tI(this,null,function*(){tL.info("Start Video for room: ",e),yield e.startVideo()})}}function iT(){return{className:[tY("button"),tY("chat-toggle")].join(" ")}}function iC(){return{className:[tY("button"),tY("focus-toggle-button")].join(" ")}}function iS(){return{className:"lk-room-container"}}function iw(e,t,i=!0){let n=[e.localParticipant,...Array.from(e.remoteParticipants.values())],r=[];return n.forEach(e=>{t.forEach(t=>{let n=Array.from(e.trackPublications.values()).filter(e=>e.source===t&&(!i||e.track)).map(t=>({participant:e,publication:t,source:t.source}));r.push(...n)})}),{trackReferences:r,participants:n}}function iE(e,t,i){var r,a;let s=null!=(r=i.additionalRoomEvents)?r:t_,o=null==(a=i.onlySubscribed)||a;return tX(e,...Array.from(new Set([n.u9.ParticipantConnected,n.u9.ParticipantDisconnected,n.u9.ConnectionStateChanged,n.u9.LocalTrackPublished,n.u9.LocalTrackUnpublished,n.u9.TrackPublished,n.u9.TrackUnpublished,n.u9.TrackSubscriptionStatusChanged,...s]).values())).pipe(ts(e=>{let i=iw(e,t,o);return tL.debug(`TrackReference[] was updated. (length ${i.trackReferences.length})`,i),i}),tv(iw(e,t,o)))}function iP(e,t=1e3){var i,n;if(null===e)return tn(!1);let r=(function e(t,i,n,r){if(eb(n)&&(r=n,n=void 0),r){return e(t,i,n).pipe((a=r,ts(function(e){return to(e)?a.apply(void 0,eg([],ef(e))):a(e)})))}var a,s,o,c,l=ef(eb((s=t).addEventListener)&&eb(s.removeEventListener)?tu.map(function(e){return function(r){return t[e](i,r,n)}}):eb((o=t).addListener)&&eb(o.removeListener)?td.map(tp(t,i)):eb((c=t).on)&&eb(c.off)?th.map(tp(t,i)):[],2),d=l[0],u=l[1];if(!d&&eX(t))return tc(function(t){return e(t,i,n)})(e6(t));if(!d)throw TypeError("Invalid event target");return new eL(function(e){var t=function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return e.next(1<t.length?t:t[0])};return d(t),function(){return u(t)}})})(e,"mousemove",{passive:!0}).pipe(ts(()=>!0)),a=r.pipe(function(e,t){var i=e instanceof Date&&!isNaN(e)?{first:e}:"number"==typeof e?{each:e}:e,n=i.first,r=i.each,a=i.with,s=void 0===a?ta:a,o=i.scheduler,c=void 0===o?eQ:o,l=i.meta,d=void 0===l?null:l;if(null==n&&null==r)throw TypeError("No timeout provided.");return ej(function(e,t){var i,a,o=null,l=0,u=function(e){a=e7(t,c,function(){try{i.unsubscribe(),e6(s({meta:d,lastValue:o,seen:l})).subscribe(t)}catch(e){t.error(e)}},e)};i=e.subscribe(eF(t,function(e){null==a||a.unsubscribe(),l++,t.next(o=e),r>0&&u(r)},void 0,void 0,function(){null!=a&&a.closed||null==a||a.unsubscribe(),o=null})),l||u(null!=n?"number"==typeof n?n:n-c.now():r)})}({each:t,with:()=>tl(tn(!1),a.pipe(ej(function(e,t){var i=!1,n=eF(t,function(){null==n||n.unsubscribe(),i=!0},ex);e6(r).subscribe(n),e.subscribe(eF(t,function(e){return i&&t.next(e)}))})))}),(void 0===n&&(n=e_),i=i??tf,ej(function(e,t){var r,a=!0;e.subscribe(eF(t,function(e){var s=n(e);(a||!i(r,s))&&(a=!1,r=s,t.next(e))}))})));return a}var iR={videoEnabled:!0,audioEnabled:!0,videoDeviceId:"default",audioDeviceId:"default",username:""},{load:iI,save:ix}=function(e){return{load:()=>(function(e){if(typeof localStorage>"u")return void tL.error("Local storage is not available.");try{let t=localStorage.getItem(e);if(!t)return void tL.warn(`Item with key ${e} does not exist in local storage.`);return JSON.parse(t)}catch(e){tL.error(`Error getting item from local storage: ${e}`);return}})(e),save:t=>(function(e,t){if(typeof localStorage>"u")return void tL.error("Local storage is not available.");try{if(t){let i=Object.fromEntries(Object.entries(t).filter(([,e])=>""!==e));localStorage.setItem(e,JSON.stringify(i))}}catch(e){tL.error(`Error setting item to local storage: ${e}`)}})(e,t)}}("lk-user-choices");function iO(e,t=!1){!0!==t&&ix(e)}function iM(e,t=!1){var i,n,r,a,s;let o={videoEnabled:null!=(i=null==e?void 0:e.videoEnabled)?i:iR.videoEnabled,audioEnabled:null!=(n=null==e?void 0:e.audioEnabled)?n:iR.audioEnabled,videoDeviceId:null!=(r=null==e?void 0:e.videoDeviceId)?r:iR.videoDeviceId,audioDeviceId:null!=(a=null==e?void 0:e.audioDeviceId)?a:iR.audioDeviceId,username:null!=(s=null==e?void 0:e.username)?s:iR.username};if(t)return o;{let e=iI();return tP(tP({},o),e??{})}}var iD=null;let iA=r.createContext(void 0);function iN(){let e=r.useContext(iA);if(!e)throw Error("Tried to access LayoutContext context outside a LayoutContextProvider provider.");return e}function i_(){return r.useContext(iA)}let iL=r.createContext(void 0);function iU(){return r.useContext(iL)}function ij(e){let t=iU(),i=e??t;if(!i)throw Error("No TrackRef, make sure you are inside a TrackRefContext or pass the TrackRef explicitly");return i}let iF=r.createContext(void 0);function iB(){return r.useContext(iF)}function iV(e){let t=iB(),i=iU(),n=e??t??(null==i?void 0:i.participant);if(!n)throw Error("No participant provided, make sure you are inside a participant context or pass the participant explicitly");return n}let iq=r.createContext(void 0);function iH(){let e=r.useContext(iq);if(!e)throw Error("tried to access room context outside of livekit room component");return e}function iW(){return r.useContext(iq)}function iz(e){let t=iW(),i=e??t;if(!i)throw Error("No room provided, make sure you are inside a Room context or pass the room explicitly");return i}let iG=r.createContext(void 0);function iK(e){let t=r.useContext(iG);if(!0===e){if(t)return t;throw Error("tried to access feature context, but none is present")}return t}},46915:(e,t,i)=>{i.d(t,{A:()=>n});let n=(0,i(62688).A)("video-off",[["path",{d:"M10.66 6H14a2 2 0 0 1 2 2v2.5l5.248-3.062A.5.5 0 0 1 22 7.87v8.196",key:"w8jjjt"}],["path",{d:"M16 16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2",key:"1xawa7"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},54274:(e,t,i)=>{i.d(t,{L:()=>p,a:()=>l,c:()=>d,m:()=>o});var n=i(43210),r=i(11921),a=i(89967);function s(){for(var e,t,i=0,n="",r=arguments.length;i<r;i++)(e=arguments[i])&&(t=function e(t){var i,n,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(i=0;i<a;i++)t[i]&&(n=e(t[i]))&&(r&&(r+=" "),r+=n)}else for(n in t)t[n]&&(r&&(r+=" "),r+=n);return r}(e))&&(n&&(n+=" "),n+=t);return n}function o(...e){let t={...e[0]};for(let i=1;i<e.length;i++){let n=e[i];for(let e in n){let i=t[e],r=n[e];"function"==typeof i&&"function"==typeof r&&"o"===e[0]&&"n"===e[1]&&e.charCodeAt(2)>=65&&90>=e.charCodeAt(2)?t[e]=function(...e){return(...t)=>{for(let i of e)if("function"==typeof i)try{i(...t)}catch(e){console.error(e)}}}(i,r):("className"===e||"UNSAFE_className"===e)&&"string"==typeof i&&"string"==typeof r?t[e]=s(i,r):t[e]=void 0!==r?r:i}}return t}function c(e){return void 0!==e}function l(...e){return o(...e.filter(c))}function d(e,t,i){return n.Children.map(e,r=>n.isValidElement(r)&&n.Children.only(e)?(r.props.className&&(t??(t={}),t.className=s(r.props.className,t.className),t.style={...r.props.style,...t.style}),n.cloneElement(r,{...t,key:i})):r)}function u(e,t){return"processor"===e&&t&&"object"==typeof t&&"name"in t?t.name:"e2ee"===e&&t?"e2ee-enabled":t}let h={connect:!0,audio:!1,video:!1},p=n.forwardRef(function(e,t){let{room:i,htmlProps:s}=function(e){let{token:t,serverUrl:i,options:s,room:c,connectOptions:l,connect:d,audio:p,video:m,screen:f,onConnected:g,onDisconnected:v,onError:b,onMediaDeviceFailure:y,onEncryptionError:k,simulateParticipants:T,...C}={...h,...e};s&&c&&r.l.warn("when using a manually created room, the options object will be ignored. set the desired options directly when creating the room instead.");let[S,w]=n.useState(),E=n.useRef(d);n.useEffect(()=>{w(c??new a.Wv(s))},[c,JSON.stringify(s,u)]);let P=n.useMemo(()=>{let{className:e}=(0,r.s)();return o(C,{className:e})},[C]);return n.useEffect(()=>{if(!S)return;let e=()=>{let e=S.localParticipant;r.l.debug("trying to publish local tracks"),Promise.all([e.setMicrophoneEnabled(!!p,"boolean"!=typeof p?p:void 0),e.setCameraEnabled(!!m,"boolean"!=typeof m?m:void 0),e.setScreenShareEnabled(!!f,"boolean"!=typeof f?f:void 0)]).catch(e=>{r.l.warn(e),null==b||b(e)})},t=(e,t)=>{let i=a.l6.getFailure(e);null==y||y(i,t)},i=e=>{null==k||k(e)},n=e=>{null==v||v(e)},s=()=>{null==g||g()};return S.on(a.u9.SignalConnected,e).on(a.u9.MediaDevicesError,t).on(a.u9.EncryptionError,i).on(a.u9.Disconnected,n).on(a.u9.Connected,s),()=>{S.off(a.u9.SignalConnected,e).off(a.u9.MediaDevicesError,t).off(a.u9.EncryptionError,i).off(a.u9.Disconnected,n).off(a.u9.Connected,s)}},[S,p,m,f,b,k,y,g,v]),n.useEffect(()=>{if(S){if(T)return void S.simulateParticipants({participants:{count:T},publish:{audio:!0,useRealTracks:!0}});if(d){if(E.current=!0,r.l.debug("connecting"),!t)return void r.l.debug("no token yet");if(!i){r.l.warn("no livekit url provided"),null==b||b(Error("no livekit url provided"));return}S.connect(i,t,l).catch(e=>{r.l.warn(e),!0===E.current&&(null==b||b(e))})}else r.l.debug("disconnecting because connect is false"),E.current=!1,S.disconnect()}},[d,t,JSON.stringify(l),S,b,i,T]),n.useEffect(()=>{if(S)return()=>{r.l.info("disconnecting on onmount"),S.disconnect()}},[S]),{room:S,htmlProps:P}}(e);return n.createElement("div",{ref:t,...s},i&&n.createElement(r.R.Provider,{value:i},n.createElement(r.L.Provider,{value:e.featureFlags},e.children)))})},58887:(e,t,i)=>{i.d(t,{A:()=>n});let n=(0,i(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},73259:(e,t,i)=>{i.d(t,{A:()=>n});let n=(0,i(62688).A)("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]])},79409:(e,t,i)=>{i.d(t,{C:()=>d,D:()=>u,G:()=>Q,M:()=>M,P:()=>G,R:()=>$,S:()=>m,T:()=>N,a:()=>X,c:()=>f,d:()=>b,e:()=>y,f:()=>D});var n,r,a=i(43210),s=i(96093),o=i(54274),c=i(89967),l=i(11921);let d=a.forwardRef(function(e,t){let{mergedProps:i}=(0,s.b)({props:e});return a.createElement("button",{ref:t,...i},e.children)}),u=a.forwardRef(function(e,t){let{buttonProps:i}=(0,s.c)(e);return a.createElement("button",{ref:t,...i},e.children)}),h=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},a.createElement("path",{d:"M1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708L11 10.293V4.5A1.5 1.5 0 0 0 9.5 3H3.707zM0 4.5a1.5 1.5 0 0 1 .943-1.393l9.532 9.533c-.262.224-.603.36-.975.36h-8A1.5 1.5 0 0 1 0 11.5z"}),a.createElement("path",{d:"m15.2 3.6-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z"})),p=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},a.createElement("path",{d:"M0 4.5A1.5 1.5 0 0 1 1.5 3h8A1.5 1.5 0 0 1 11 4.5v7A1.5 1.5 0 0 1 9.5 13h-8A1.5 1.5 0 0 1 0 11.5zM15.2 3.6l-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z"})),m=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 24 24",...e},a.createElement("path",{fill:"#FFF",d:"M4.99 3.99a1 1 0 0 0-.697 1.717L10.586 12l-6.293 6.293a1 1 0 1 0 1.414 1.414L12 13.414l6.293 6.293a1 1 0 1 0 1.414-1.414L13.414 12l6.293-6.293a1 1 0 0 0-.727-1.717 1 1 0 0 0-.687.303L12 10.586 5.707 4.293a1 1 0 0 0-.717-.303z"})),f=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:18,fill:"none",...e},a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 2.75A2.75 2.75 0 0 1 2.75 0h10.5A2.75 2.75 0 0 1 16 2.75v13.594a.75.75 0 0 1-1.234.572l-3.691-3.12a1.25 1.25 0 0 0-.807-.296H2.75A2.75 2.75 0 0 1 0 10.75v-8ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v8c0 .69.56 1.25 1.25 1.25h7.518c.65 0 1.279.23 1.775.65l2.457 2.077V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z",clipRule:"evenodd"}),a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M3 4.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Z",clipRule:"evenodd"})),g=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},a.createElement("path",{fill:"currentcolor",fillRule:"evenodd",d:"M5.293 2.293a1 1 0 0 1 1.414 0l4.823 4.823a1.25 1.25 0 0 1 0 1.768l-4.823 4.823a1 1 0 0 1-1.414-1.414L9.586 8 5.293 3.707a1 1 0 0 1 0-1.414z",clipRule:"evenodd"})),v=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},a.createElement("g",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5},a.createElement("path",{d:"M10 1.75h4.25m0 0V6m0-4.25L9 7M6 14.25H1.75m0 0V10m0 4.25L7 9"}))),b=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},a.createElement("path",{fill:"currentcolor",fillRule:"evenodd",d:"M8.961.894C8.875-.298 7.125-.298 7.04.894c-.066.912-1.246 1.228-1.76.472-.67-.99-2.186-.115-1.664.96.399.824-.465 1.688-1.288 1.289-1.076-.522-1.95.994-.961 1.665.756.513.44 1.693-.472 1.759-1.192.086-1.192 1.836 0 1.922.912.066 1.228 1.246.472 1.76-.99.67-.115 2.186.96 1.664.824-.399 1.688.465 1.289 1.288-.522 1.076.994 1.95 1.665.961.513-.756 1.693-.44 1.759.472.086 1.192 1.836 1.192 1.922 0 .066-.912 1.246-1.228 1.76-.472.67.99 2.186.115 1.664-.96-.399-.824.465-1.688 1.288-1.289 1.076.522 1.95-.994.961-1.665-.756-.513-.44-1.693.472-1.759 1.192-.086 1.192-1.836 0-1.922-.912-.066-1.228-1.246-.472-1.76.99-.67.115-2.186-.96-1.664-.824.399-1.688-.465-1.289-1.288.522-1.076-.994-1.95-1.665-.961-.513.756-1.693.44-1.759-.472ZM8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Z",clipRule:"evenodd"})),y=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M2 2.75A2.75 2.75 0 0 1 4.75 0h6.5A2.75 2.75 0 0 1 14 2.75v10.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-.5a.75.75 0 0 1 1.5 0v.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25h-6.5c-.69 0-1.25.56-1.25 1.25v.5a.75.75 0 0 1-1.5 0v-.5Z",clipRule:"evenodd"}),a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M8.78 7.47a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 1 1-1.06-1.06l.97-.97H1.75a.75.75 0 0 1 0-1.5h4.69l-.97-.97a.75.75 0 0 1 1.06-1.06l2.25 2.25Z",clipRule:"evenodd"})),k=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},a.createElement("path",{fill:"currentcolor",fillRule:"evenodd",d:"M4 6.104V4a4 4 0 1 1 8 0v2.104c1.154.326 2 1.387 2 2.646v4.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-4.5c0-1.259.846-2.32 2-2.646ZM5.5 4a2.5 2.5 0 0 1 5 0v2h-5V4Z",clipRule:"evenodd"})),T=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},a.createElement("path",{d:"M12.227 11.52a5.477 5.477 0 0 0 1.246-******** 0 0 0-.995-.1 4.478 4.478 0 0 1-.962 2.359l-1.07-1.07C10.794 9.247 11 8.647 11 8V3a3 3 0 0 0-6 0v1.293L1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708zM8 12.5c.683 0 1.33-.152 1.911-.425l.743.743c-.649.359-1.378.59-2.154.66V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .995-.098A4.5 4.5 0 0 0 8 12.5z"}),a.createElement("path",{d:"M8.743 10.907 5 7.164V8a3 3 0 0 0 3.743 2.907z"})),C=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},a.createElement("path",{fillRule:"evenodd",d:"M2.975 8.002a.5.5 0 0 1 .547.449 4.5 4.5 0 0 0 8.956 0 .5.5 0 1 1 .995.098A5.502 5.502 0 0 1 8.5 13.478V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .448-.547z",clipRule:"evenodd"}),a.createElement("path",{d:"M5 3a3 3 0 1 1 6 0v5a3 3 0 0 1-6 0z"})),S=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentcolor",...e},a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"})),w=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentcolor",...e},a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("g",{opacity:.25},a.createElement("path",{d:"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("path",{d:"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}))),E=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentcolor",...e},a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("g",{opacity:.25},a.createElement("path",{d:"M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("path",{d:"M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),a.createElement("path",{d:"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}))),P=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},a.createElement("g",{opacity:.25},a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z"}),a.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z"}))),R=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:16,fill:"none",...e},a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 2.75A2.75 2.75 0 0 1 2.75 0h14.5A2.75 2.75 0 0 1 20 2.75v10.5A2.75 2.75 0 0 1 17.25 16H2.75A2.75 2.75 0 0 1 0 13.25V2.75ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v10.5c0 .69.56 1.25 1.25 1.25h14.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z",clipRule:"evenodd"}),a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M9.47 4.22a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v4.69a.75.75 0 0 1-1.5 0V6.56l-.97.97a.75.75 0 0 1-1.06-1.06l2.25-2.25Z",clipRule:"evenodd"})),I=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:16,fill:"none",...e},a.createElement("g",{fill:"currentColor"},a.createElement("path",{d:"M7.28 4.22a.75.75 0 0 0-1.06 1.06L8.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L10 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L11.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L10 6.94z"}),a.createElement("path",{fillRule:"evenodd",d:"M2.75 0A2.75 2.75 0 0 0 0 2.75v10.5A2.75 2.75 0 0 0 2.75 16h14.5A2.75 2.75 0 0 0 20 13.25V2.75A2.75 2.75 0 0 0 17.25 0zM1.5 2.75c0-.69.56-1.25 1.25-1.25h14.5c.69 0 1.25.56 1.25 1.25v10.5c0 .69-.56 1.25-1.25 1.25H2.75c-.69 0-1.25-.56-1.25-1.25z",clipRule:"evenodd"}))),x=e=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},a.createElement("g",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5},a.createElement("path",{d:"M13.25 7H9m0 0V2.75M9 7l5.25-5.25M2.75 9H7m0 0v4.25M7 9l-5.25 5.25"}))),O=a.forwardRef(function({trackRef:e,...t},i){let n=(0,l.ab)(),{mergedProps:r,inFocus:o}=(0,s.d)({trackRef:e??n,props:t});return a.createElement(l.ac.Consumer,null,e=>void 0!==e&&a.createElement("button",{ref:i,...r},t.children?t.children:o?a.createElement(x,null):a.createElement(v,null)))}),M=a.forwardRef(function({kind:e,initialSelection:t,onActiveDeviceChange:i,onDeviceListChange:n,onDeviceSelectError:r,exactMatch:d,track:u,requestPermissions:h,onError:p,...m},f){let g=(0,l.w)(),v=a.useRef("default"),b=a.useCallback(e=>{g&&g.emit(c.u9.MediaDevicesError,e),null==p||p(e)},[g,p]),{devices:y,activeDeviceId:k,setActiveMediaDevice:T,className:C}=(0,s.e)({kind:e,room:g,track:u,requestPermissions:h,onError:b});a.useEffect(()=>{void 0!==t&&T(t)},[T]),a.useEffect(()=>{"function"==typeof n&&n(y)},[n,y]),a.useEffect(()=>{k!==v.current&&(null==i||i(k)),v.current=k},[k]);let S=async e=>{try{await T(e,{exact:d??!0})}catch(e){if(e instanceof Error)null==r||r(e);else throw e}},w=a.useMemo(()=>(0,o.a)(m,{className:C},{className:"lk-list"}),[C,m]),E=!!y.find(e=>e.label.toLowerCase().startsWith("default"));function P(e,t,i){return e===t||!E&&0===i&&"default"===t}return a.createElement("ul",{ref:f,...w},y.map((e,t)=>a.createElement("li",{key:e.deviceId,id:e.deviceId,"data-lk-active":P(e.deviceId,k,t),"aria-selected":P(e.deviceId,k,t),role:"option"},a.createElement("button",{className:"lk-button",onClick:()=>S(e.deviceId)},e.label))))}),D=a.forwardRef(function({label:e,...t},i){let n=(0,l.f)(),{mergedProps:r,canPlayAudio:o}=(0,s.f)({room:n,props:t}),{mergedProps:c,canPlayVideo:d}=(0,s.g)({room:n,props:r}),{style:u,...h}=c;return u.display=o&&d?"none":"block",a.createElement("button",{ref:i,style:u,...h},e??`Start ${o?"Video":"Audio"}`)});function A(e,t){switch(e){case c.CC.Source.Microphone:return t?a.createElement(C,null):a.createElement(T,null);case c.CC.Source.Camera:return t?a.createElement(p,null):a.createElement(h,null);case c.CC.Source.ScreenShare:return t?a.createElement(I,null):a.createElement(R,null);default:return}}let N=a.forwardRef(function({showIcon:e,...t},i){let{buttonProps:n,enabled:r}=(0,s.h)(t),[o,c]=a.useState(!1);return a.useEffect(()=>{c(!0)},[]),o&&a.createElement("button",{ref:i,...n},(e??!0)&&A(t.source,r),t.children)}),_=a.forwardRef(function(e,t){let{className:i,quality:n}=(0,s.i)(e),r=a.useMemo(()=>({...(0,o.a)(e,{className:i}),"data-lk-quality":n}),[n,e,i]);return a.createElement("div",{ref:t,...r},e.children??function(e){switch(e){case c._N.Excellent:return a.createElement(S,null);case c._N.Good:return a.createElement(w,null);case c._N.Poor:return a.createElement(E,null);default:return a.createElement(P,null)}}(n))}),L=a.forwardRef(function({participant:e,...t},i){let n=(0,l.c)(e),{className:r,infoObserver:c}=a.useMemo(()=>(0,l.ad)(n),[n]),{identity:d,name:u}=(0,s.j)(c,{name:n.name,identity:n.identity,metadata:n.metadata}),h=a.useMemo(()=>(0,o.a)(t,{className:r,"data-lk-participant-name":u}),[t,r,u]);return a.createElement("span",{ref:i,...h},""!==u?u:d,t.children)}),U=a.forwardRef(function({trackRef:e,show:t="always",...i},n){let{className:r,isMuted:c}=(0,s.k)(e),l="always"===t||"muted"===t&&c||"unmuted"===t&&!c,d=a.useMemo(()=>(0,o.a)(i,{className:r}),[r,i]);return l?a.createElement("div",{ref:n,...d,"data-lk-muted":c},i.children??A(e.source,!c)):null}),j=e=>a.createElement("svg",{width:320,height:320,viewBox:"0 0 320 320",preserveAspectRatio:"xMidYMid meet",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},a.createElement("path",{d:"M160 180C204.182 180 240 144.183 240 100C240 55.8172 204.182 20 160 20C115.817 20 79.9997 55.8172 79.9997 100C79.9997 144.183 115.817 180 160 180Z",fill:"white",fillOpacity:.25}),a.createElement("path",{d:"M97.6542 194.614C103.267 191.818 109.841 192.481 115.519 195.141C129.025 201.466 144.1 205 159.999 205C175.899 205 190.973 201.466 204.48 195.141C210.158 192.481 216.732 191.818 222.345 194.614C262.703 214.719 291.985 253.736 298.591 300.062C300.15 310.997 291.045 320 280 320H39.9997C28.954 320 19.8495 310.997 21.4087 300.062C28.014 253.736 57.2966 214.72 97.6542 194.614Z",fill:"white",fillOpacity:.25}));function F(e,t={}){let[i,n]=a.useState((0,l.X)(e)),[r,s]=a.useState(null==i?void 0:i.isMuted),[c,d]=a.useState(null==i?void 0:i.isSubscribed),[u,h]=a.useState(null==i?void 0:i.track),[p,m]=a.useState("landscape"),f=a.useRef(),{className:g,trackObserver:v}=a.useMemo(()=>(0,l.Y)(e),[e.participant.sid??e.participant.identity,e.source,(0,l.a1)(e)&&e.publication.trackSid]);return a.useEffect(()=>{let e=v.subscribe(e=>{l.l.debug("update track",e),n(e),s(null==e?void 0:e.isMuted),d(null==e?void 0:e.isSubscribed),h(null==e?void 0:e.track)});return()=>null==e?void 0:e.unsubscribe()},[v]),a.useEffect(()=>{var i,n;return u&&(f.current&&u.detach(f.current),null!=(i=t.element)&&i.current&&!(e.participant.isLocal&&(null==u?void 0:u.kind)==="audio")&&u.attach(t.element.current)),f.current=null==(n=t.element)?void 0:n.current,()=>{f.current&&(null==u||u.detach(f.current))}},[u,t.element]),a.useEffect(()=>{var e,t;"number"==typeof(null==(e=null==i?void 0:i.dimensions)?void 0:e.width)&&"number"==typeof(null==(t=null==i?void 0:i.dimensions)?void 0:t.height)&&m(i.dimensions.width>i.dimensions.height?"landscape":"portrait")},[i]),{publication:i,isMuted:r,isSubscribed:c,track:u,elementProps:(0,o.a)(t.props,{className:g,"data-lk-local-participant":e.participant.isLocal,"data-lk-source":null==i?void 0:i.source,...(null==i?void 0:i.kind)==="video"&&{"data-lk-orientation":p}})}}var B=function(){if(r)return n;r=1;var e=NaN,t=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,s=/^0o[0-7]+$/i,o=parseInt,c="object"==typeof l.ae&&l.ae&&l.ae.Object===Object&&l.ae,d="object"==typeof self&&self&&self.Object===Object&&self,u=c||d||Function("return this")(),h=Object.prototype.toString,p=Math.max,m=Math.min,f=function(){return u.Date.now()};function g(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function v(n){if("number"==typeof n)return n;if("symbol"==typeof(r=n)||r&&"object"==typeof r&&"[object Symbol]"==h.call(r))return e;if(g(n)){var r,c="function"==typeof n.valueOf?n.valueOf():n;n=g(c)?c+"":c}if("string"!=typeof n)return 0===n?n:+n;n=n.replace(t,"");var l=a.test(n);return l||s.test(n)?o(n.slice(2),l?2:8):i.test(n)?e:+n}return n=function(e,t,i){var n,r,a,s,o,c,l=0,d=!1,u=!1,h=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var i=n,a=r;return n=r=void 0,l=t,s=e.apply(a,i)}function y(e){var i=e-c,n=e-l;return void 0===c||i>=t||i<0||u&&n>=a}function k(){var e,i,n,r=f();if(y(r))return T(r);o=setTimeout(k,(e=r-c,i=r-l,n=t-e,u?m(n,a-i):n))}function T(e){return o=void 0,h&&n?b(e):(n=r=void 0,s)}function C(){var e,i=f(),a=y(i);if(n=arguments,r=this,c=i,a){if(void 0===o)return l=e=c,o=setTimeout(k,t),d?b(e):s;if(u)return o=setTimeout(k,t),b(c)}return void 0===o&&(o=setTimeout(k,t)),s}return t=v(t)||0,g(i)&&(d=!!i.leading,a=(u="maxWait"in i)?p(v(i.maxWait)||0,t):a,h="trailing"in i?!!i.trailing:h),C.cancel=function(){void 0!==o&&clearTimeout(o),l=0,n=c=r=o=void 0},C.flush=function(){return void 0===o?s:T(f())},C}}();let V=(0,l.af)(B),q=a.forwardRef(function({onTrackClick:e,onClick:t,onSubscriptionStatusChanged:i,trackRef:n,manageSubscription:r,...s},o){let d=(0,l.i)(n),u=a.useRef(null);a.useImperativeHandle(o,()=>u.current);let h=function({threshold:e=0,root:t=null,rootMargin:i="0%",freezeOnceVisible:n=!1,initialIsIntersecting:r=!1,onChange:s}={}){var o;let[c,l]=(0,a.useState)(null),[d,u]=(0,a.useState)(()=>({isIntersecting:r,entry:void 0})),h=(0,a.useRef)();h.current=s;let p=(null==(o=d.entry)?void 0:o.isIntersecting)&&n;(0,a.useEffect)(()=>{if(!c||!("IntersectionObserver"in window)||p)return;let n=new IntersectionObserver(e=>{let t=Array.isArray(n.thresholds)?n.thresholds:[n.thresholds];e.forEach(e=>{let i=e.isIntersecting&&t.some(t=>e.intersectionRatio>=t);u({isIntersecting:i,entry:e}),h.current&&h.current(i,e)})},{threshold:e,root:t,rootMargin:i});return n.observe(c),()=>{n.disconnect()}},[c,JSON.stringify(e),t,i,p,n]);let m=(0,a.useRef)(null);(0,a.useEffect)(()=>{var e;c||null==(e=d.entry)||!e.target||n||p||m.current===d.entry.target||(m.current=d.entry.target,u({isIntersecting:r,entry:void 0}))},[c,d.entry,n,p,r]);let f=[l,!!d.isIntersecting,d.entry];return f.ref=f[0],f.isIntersecting=f[1],f.entry=f[2],f}({root:u.current}),[p]=function(e,t,i){let n=e instanceof Function?e():e,[r,s]=(0,a.useState)(n),o=(0,a.useRef)(n),c=function(e,t=500,i){let n=(0,a.useRef)();var r=()=>{n.current&&n.current.cancel()};let s=(0,a.useRef)(r);s.current=r,(0,a.useEffect)(()=>()=>{s.current()},[]);let o=(0,a.useMemo)(()=>{let r=V(e,t,i),a=(...e)=>r(...e);return a.cancel=()=>{r.cancel()},a.isPending=()=>!!n.current,a.flush=()=>r.flush(),a},[e,t,i]);return(0,a.useEffect)(()=>{n.current=V(e,t,i)},[e,t,i]),o}(s,3e3,void 0);return o.current===n||(c(n),o.current=n),[r,c]}(h,0);a.useEffect(()=>{r&&d.publication instanceof c.u6&&(null==p?void 0:p.isIntersecting)===!1&&(null==h?void 0:h.isIntersecting)===!1&&d.publication.setSubscribed(!1)},[p,d,r]),a.useEffect(()=>{r&&d.publication instanceof c.u6&&(null==h?void 0:h.isIntersecting)===!0&&d.publication.setSubscribed(!0)},[h,d,r]);let{elementProps:m,publication:f,isSubscribed:g}=F(d,{element:u,props:s});return a.useEffect(()=>{null==i||i(!!g)},[g,i]),a.createElement("video",{ref:u,...m,muted:!0,onClick:i=>{null==t||t(i),null==e||e({participant:null==d?void 0:d.participant,track:f})}})}),H=a.forwardRef(function({trackRef:e,onSubscriptionStatusChanged:t,volume:i,...n},r){let s=(0,l.i)(e),o=a.useRef(null);a.useImperativeHandle(r,()=>o.current);let{elementProps:d,isSubscribed:u,track:h,publication:p}=F(s,{element:o,props:n});return a.useEffect(()=>{null==t||t(!!u)},[u,t]),a.useEffect(()=>{void 0===h||void 0===i||(h instanceof c.EQ?h.setVolume(i):l.l.warn("Volume can only be set on remote audio tracks."))},[i,h]),a.useEffect(()=>{void 0===p||void 0===n.muted||(p instanceof c.u6?p.setEnabled(!n.muted):l.l.warn("Can only call setEnabled on remote track publications."))},[n.muted,p,h]),a.createElement("audio",{ref:o,...d})});function W(e){let t=!!(0,l.B)();return e.participant&&!t?a.createElement(l.ag.Provider,{value:e.participant},e.children):a.createElement(a.Fragment,null,e.children)}function z(e){let t=!!(0,l.ab)();return e.trackRef&&!t?a.createElement(l.ah.Provider,{value:e.trackRef},e.children):a.createElement(a.Fragment,null,e.children)}let G=a.forwardRef(function({trackRef:e,children:t,onParticipantClick:i,disableSpeakingIndicator:n,...r},o){var d,u;let h=(0,l.i)(e),{elementProps:p}=(0,s.l)({htmlProps:r,disableSpeakingIndicator:n,onParticipantClick:i,trackRef:h}),m=(0,s.m)(h.participant),f=(0,l.j)(),g=null==(d=(0,l.ai)())?void 0:d.autoSubscription,v=a.useCallback(e=>{h.source&&!e&&f&&f.pin.dispatch&&(0,l.m)(h,f.pin.state)&&f.pin.dispatch({msg:"clear_pin"})},[h,f]);return a.createElement("div",{ref:o,style:{position:"relative"},...p},a.createElement(z,{trackRef:h},a.createElement(W,{participant:h.participant},t??a.createElement(a.Fragment,null,(0,l.a1)(h)&&((null==(u=h.publication)?void 0:u.kind)==="video"||h.source===c.CC.Source.Camera||h.source===c.CC.Source.ScreenShare)?a.createElement(q,{trackRef:h,onSubscriptionStatusChanged:v,manageSubscription:g}):(0,l.a1)(h)&&a.createElement(H,{trackRef:h,onSubscriptionStatusChanged:v}),a.createElement("div",{className:"lk-participant-placeholder"},a.createElement(j,null)),a.createElement("div",{className:"lk-participant-metadata"},a.createElement("div",{className:"lk-participant-metadata-item"},h.source===c.CC.Source.Camera?a.createElement(a.Fragment,null,m&&a.createElement(k,{style:{marginRight:"0.25rem"}}),a.createElement(U,{trackRef:{participant:h.participant,source:c.CC.Source.Microphone},show:"muted"}),a.createElement(L,null)):a.createElement(a.Fragment,null,a.createElement(R,{style:{marginRight:"0.25rem"}}),a.createElement(L,null,"'s screen"))),a.createElement(_,{className:"lk-participant-metadata-item"}))),a.createElement(O,{trackRef:h}))))});function K({tracks:e,...t}){return a.createElement(a.Fragment,null,e.map(e=>a.createElement(l.ah.Provider,{value:e,key:(0,l.p)(e)},(0,o.c)(t.children))))}function J({totalPageCount:e,nextPage:t,prevPage:i,currentPage:n,pagesContainer:r}){let[s,o]=a.useState(!1);return a.useEffect(()=>{let e;return r&&(e=(0,l.aj)(r.current,2e3).subscribe(o)),()=>{e&&e.unsubscribe()}},[r]),a.createElement("div",{className:"lk-pagination-control","data-lk-user-interaction":s},a.createElement("button",{className:"lk-button",onClick:i},a.createElement(g,null)),a.createElement("span",{className:"lk-pagination-count"},`${n} of ${e}`),a.createElement("button",{className:"lk-button",onClick:t},a.createElement(g,null)))}let Y=a.forwardRef(function({totalPageCount:e,currentPage:t},i){let n=Array(e).fill("").map((e,i)=>i+1===t?a.createElement("span",{"data-lk-active":!0,key:i}):a.createElement("span",{key:i}));return a.createElement("div",{ref:i,className:"lk-pagination-indicator"},n)});function Q({tracks:e,...t}){let i=a.createRef(),n=a.useMemo(()=>(0,o.a)(t,{className:"lk-grid-layout"}),[t]),{layout:r}=(0,s.n)(i,e.length),c=(0,s.o)(r.maxTiles,e);return(0,s.p)(i,{onLeftSwipe:c.nextPage,onRightSwipe:c.prevPage}),a.createElement("div",{ref:i,"data-lk-pagination":c.totalPageCount>1,...n},a.createElement(K,{tracks:c.tracks},t.children),e.length>r.maxTiles&&a.createElement(a.Fragment,null,a.createElement(Y,{totalPageCount:c.totalPageCount,currentPage:c.currentPage}),a.createElement(J,{pagesContainer:i,...c})))}function $({volume:e,muted:t}){let i=(0,s.t)([c.CC.Source.Microphone,c.CC.Source.ScreenShareAudio,c.CC.Source.Unknown],{updateOnlyOn:[],onlySubscribed:!0}).filter(e=>!e.participant.isLocal&&e.publication.kind===c.CC.Kind.Audio);return a.createElement("div",{style:{display:"none"}},i.map(i=>a.createElement(H,{key:(0,l.p)(i),trackRef:i,volume:e,muted:t})))}let X=a.forwardRef(function({entry:e,hideName:t=!1,hideTimestamp:i=!1,messageFormatter:n,...r},s){var o,c,l,d;let u=a.useMemo(()=>n?n(e.message):e.message,[e.message,n]),h=!!e.editTimestamp,p=new Date(e.timestamp),m="u">typeof navigator?navigator.language:"en-US",f=(null==(o=e.from)?void 0:o.name)??(null==(c=e.from)?void 0:c.identity);return a.createElement("li",{ref:s,className:"lk-chat-entry",title:p.toLocaleTimeString(m,{timeStyle:"full"}),"data-lk-message-origin":null!=(l=e.from)&&l.isLocal?"local":"remote",...r},(!i||!t||h)&&a.createElement("span",{className:"lk-meta-data"},!t&&a.createElement("strong",{className:"lk-participant-name"},f),(!i||h)&&a.createElement("span",{className:"lk-timestamp"},h&&"edited ",p.toLocaleTimeString(m,{timeStyle:"short"}))),a.createElement("span",{className:"lk-message-body"},u),a.createElement("span",{className:"lk-message-attachements"},null==(d=e.attachedFiles)?void 0:d.map(e=>e.type.startsWith("image/")&&a.createElement("img",{style:{maxWidth:"300px",maxHeight:"300px"},key:e.name,src:URL.createObjectURL(e),alt:e.name}))))})},82719:(e,t,i)=>{i.d(t,{A:()=>n});let n=(0,i(62688).A)("mic-off",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},89967:(e,t,i)=>{let n,r;i.d(t,{CC:()=>n3,EC:()=>sn,EQ:()=>aZ,HO:()=>a9,KN:()=>F,Wv:()=>st,YI:()=>S,Zy:()=>ry,_N:()=>j,l6:()=>k,u6:()=>a8,u9:()=>C});var a,s,o,c,l,d,u,h,p,m,f,g,v,b,y,k,T,C,S,w,E,P,R,I,x,O,M,D,A,N,_,L,U,j,F,B,V=Object.defineProperty,q=(e,t,i)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,H=(e,t,i)=>q(e,"symbol"!=typeof t?t+"":t,i);class W{constructor(){H(this,"_locking"),H(this,"_locks"),this._locking=Promise.resolve(),this._locks=0}isLocked(){return this._locks>0}lock(){let e;this._locks+=1;let t=new Promise(t=>e=()=>{this._locks-=1,t()}),i=this._locking.then(()=>e);return this._locking=this._locking.then(()=>t),i}}function z(e,t){if(!e)throw Error(t)}function G(e){if("number"!=typeof e)throw Error("invalid int 32: "+typeof e);if(!Number.isInteger(e)||e>0x7fffffff||e<-0x80000000)throw Error("invalid int 32: "+e)}function K(e){if("number"!=typeof e)throw Error("invalid uint 32: "+typeof e);if(!Number.isInteger(e)||e>0xffffffff||e<0)throw Error("invalid uint 32: "+e)}function J(e){if("number"!=typeof e)throw Error("invalid float 32: "+typeof e);if(Number.isFinite(e)&&(e>34028234663852886e22||e<-34028234663852886e22))throw Error("invalid float 32: "+e)}let Y=Symbol("@bufbuild/protobuf/enum-type");function Q(e,t,i,n){e[Y]=$(t,i.map(t=>({no:t.no,name:t.name,localName:e[t.no]})))}function $(e,t,i){let n=Object.create(null),r=Object.create(null),a=[];for(let e of t){let t=X(e);a.push(t),n[e.name]=t,r[e.no]=t}return{typeName:e,values:a,findName:e=>n[e],findNumber:e=>r[e]}}function X(e){return"localName"in e?e:Object.assign(Object.assign({},e),{localName:e.name})}class Z{equals(e){return this.getType().runtime.util.equals(this.getType(),this,e)}clone(){return this.getType().runtime.util.clone(this)}fromBinary(e,t){let i=this.getType().runtime.bin,n=i.makeReadOptions(t);return i.readMessage(this,n.readerFactory(e),e.byteLength,n),this}fromJson(e,t){let i=this.getType(),n=i.runtime.json,r=n.makeReadOptions(t);return n.readMessage(i,e,r,this),this}fromJsonString(e,t){let i;try{i=JSON.parse(e)}catch(e){throw Error("cannot decode ".concat(this.getType().typeName," from JSON: ").concat(e instanceof Error?e.message:String(e)))}return this.fromJson(i,t)}toBinary(e){let t=this.getType().runtime.bin,i=t.makeWriteOptions(e),n=i.writerFactory();return t.writeMessage(this,n,i),n.finish()}toJson(e){let t=this.getType().runtime.json,i=t.makeWriteOptions(e);return t.writeMessage(this,i)}toJsonString(e){var t;return JSON.stringify(this.toJson(e),null,null!=(t=null==e?void 0:e.prettySpaces)?t:0)}toJSON(){return this.toJson({emitDefaultValues:!0})}getType(){return Object.getPrototypeOf(this).constructor}}function ee(){let e=0,t=0;for(let i=0;i<28;i+=7){let n=this.buf[this.pos++];if(e|=(127&n)<<i,(128&n)==0)return this.assertBounds(),[e,t]}let i=this.buf[this.pos++];if(e|=(15&i)<<28,t=(112&i)>>4,(128&i)==0)return this.assertBounds(),[e,t];for(let i=3;i<=31;i+=7){let n=this.buf[this.pos++];if(t|=(127&n)<<i,(128&n)==0)return this.assertBounds(),[e,t]}throw Error("invalid varint")}function et(e,t,i){for(let n=0;n<28;n+=7){let r=e>>>n,a=r>>>7!=0||0!=t,s=(a?128|r:r)&255;if(i.push(s),!a)return}let n=e>>>28&15|(7&t)<<4,r=t>>3!=0;if(i.push((r?128|n:n)&255),r){for(let e=3;e<31;e+=7){let n=t>>>e,r=n>>>7!=0,a=(r?128|n:n)&255;if(i.push(a),!r)return}i.push(t>>>31&1)}}function ei(e){let t="-"===e[0];t&&(e=e.slice(1));let i=0,n=0;function r(t,r){let a=Number(e.slice(t,r));n*=1e6,(i=1e6*i+a)>=0x100000000&&(n+=i/0x100000000|0,i%=0x100000000)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),t?ea(i,n):er(i,n)}function en(e,t){if({lo:e,hi:t}={lo:e>>>0,hi:t>>>0},t<=2097151)return String(0x100000000*t+e);let i=0xffffff&e,n=(e>>>24|t<<8)&0xffffff,r=t>>16&65535,a=i+6777216*n+6710656*r,s=n+8147497*r,o=2*r;return a>=1e7&&(s+=Math.floor(a/1e7),a%=1e7),s>=1e7&&(o+=Math.floor(s/1e7),s%=1e7),o.toString()+es(s)+es(a)}function er(e,t){return{lo:0|e,hi:0|t}}function ea(e,t){return t=~t,e?e=~e+1:t+=1,er(e,t)}let es=e=>{let t=String(e);return"0000000".slice(t.length)+t};function eo(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let i=0;i<9;i++)t.push(127&e|128),e>>=7;t.push(1)}}function ec(){let e=this.buf[this.pos++],t=127&e;if((128&e)==0||(t|=(127&(e=this.buf[this.pos++]))<<7,(128&e)==0)||(t|=(127&(e=this.buf[this.pos++]))<<14,(128&e)==0)||(t|=(127&(e=this.buf[this.pos++]))<<21,(128&e)==0))return this.assertBounds(),t;t|=(15&(e=this.buf[this.pos++]))<<28;for(let t=5;(128&e)!=0&&t<10;t++)e=this.buf[this.pos++];if((128&e)!=0)throw Error("invalid varint");return this.assertBounds(),t>>>0}let el=function(){let e=new DataView(new ArrayBuffer(8));if("function"==typeof BigInt&&"function"==typeof e.getBigInt64&&"function"==typeof e.getBigUint64&&"function"==typeof e.setBigInt64&&"function"==typeof e.setBigUint64&&("object"!=typeof process||"object"!=typeof process.env||"1"!==process.env.BUF_BIGINT_DISABLE)){let t=BigInt("-9223372036854775808"),i=BigInt("9223372036854775807"),n=BigInt("0"),r=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(e){let n="bigint"==typeof e?e:BigInt(e);if(n>i||n<t)throw Error("int64 invalid: ".concat(e));return n},uParse(e){let t="bigint"==typeof e?e:BigInt(e);if(t>r||t<n)throw Error("uint64 invalid: ".concat(e));return t},enc(t){return e.setBigInt64(0,this.parse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(t){return e.setBigInt64(0,this.uParse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(t,i)=>(e.setInt32(0,t,!0),e.setInt32(4,i,!0),e.getBigInt64(0,!0)),uDec:(t,i)=>(e.setInt32(0,t,!0),e.setInt32(4,i,!0),e.getBigUint64(0,!0))}}let t=e=>z(/^-?[0-9]+$/.test(e),"int64 invalid: ".concat(e)),i=e=>z(/^[0-9]+$/.test(e),"uint64 invalid: ".concat(e));return{zero:"0",supported:!1,parse:e=>("string"!=typeof e&&(e=e.toString()),t(e),e),uParse:e=>("string"!=typeof e&&(e=e.toString()),i(e),e),enc:e=>("string"!=typeof e&&(e=e.toString()),t(e),ei(e)),uEnc:e=>("string"!=typeof e&&(e=e.toString()),i(e),ei(e)),dec:(e,t)=>(function(e,t){let i=er(e,t),n=0x80000000&i.hi;n&&(i=ea(i.lo,i.hi));let r=en(i.lo,i.hi);return n?"-"+r:r})(e,t),uDec:(e,t)=>en(e,t)}}();function ed(e,t,i){if(t===i)return!0;if(e==o.BYTES){if(!(t instanceof Uint8Array)||!(i instanceof Uint8Array)||t.length!==i.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==i[e])return!1;return!0}switch(e){case o.UINT64:case o.FIXED64:case o.INT64:case o.SFIXED64:case o.SINT64:return t==i}return!1}function eu(e,t){switch(e){case o.BOOL:return!1;case o.UINT64:case o.FIXED64:case o.INT64:case o.SFIXED64:case o.SINT64:return 0==t?el.zero:"0";case o.DOUBLE:case o.FLOAT:return 0;case o.BYTES:return new Uint8Array(0);case o.STRING:return"";default:return 0}}function eh(e,t){switch(e){case o.BOOL:return!1===t;case o.STRING:return""===t;case o.BYTES:return t instanceof Uint8Array&&!t.byteLength;default:return 0==t}}!function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(o||(o={})),function(e){e[e.BIGINT=0]="BIGINT",e[e.STRING=1]="STRING"}(c||(c={})),function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"}(l||(l={}));class ep{constructor(e){this.stack=[],this.textEncoder=null!=e?e:new TextEncoder,this.chunks=[],this.buf=[]}finish(){this.chunks.push(new Uint8Array(this.buf));let e=0;for(let t=0;t<this.chunks.length;t++)e+=this.chunks[t].length;let t=new Uint8Array(e),i=0;for(let e=0;e<this.chunks.length;e++)t.set(this.chunks[e],i),i+=this.chunks[e].length;return this.chunks=[],t}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),t=this.stack.pop();if(!t)throw Error("invalid state, fork stack empty");return this.chunks=t.chunks,this.buf=t.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,t){return this.uint32((e<<3|t)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(K(e);e>127;)this.buf.push(127&e|128),e>>>=7;return this.buf.push(e),this}int32(e){return G(e),eo(e,this.buf),this}bool(e){return this.buf.push(+!!e),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let t=this.textEncoder.encode(e);return this.uint32(t.byteLength),this.raw(t)}float(e){J(e);let t=new Uint8Array(4);return new DataView(t.buffer).setFloat32(0,e,!0),this.raw(t)}double(e){let t=new Uint8Array(8);return new DataView(t.buffer).setFloat64(0,e,!0),this.raw(t)}fixed32(e){K(e);let t=new Uint8Array(4);return new DataView(t.buffer).setUint32(0,e,!0),this.raw(t)}sfixed32(e){G(e);let t=new Uint8Array(4);return new DataView(t.buffer).setInt32(0,e,!0),this.raw(t)}sint32(e){return G(e),eo(e=(e<<1^e>>31)>>>0,this.buf),this}sfixed64(e){let t=new Uint8Array(8),i=new DataView(t.buffer),n=el.enc(e);return i.setInt32(0,n.lo,!0),i.setInt32(4,n.hi,!0),this.raw(t)}fixed64(e){let t=new Uint8Array(8),i=new DataView(t.buffer),n=el.uEnc(e);return i.setInt32(0,n.lo,!0),i.setInt32(4,n.hi,!0),this.raw(t)}int64(e){let t=el.enc(e);return et(t.lo,t.hi,this.buf),this}sint64(e){let t=el.enc(e),i=t.hi>>31;return et(t.lo<<1^i,(t.hi<<1|t.lo>>>31)^i,this.buf),this}uint64(e){let t=el.uEnc(e);return et(t.lo,t.hi,this.buf),this}}class em{constructor(e,t){this.varint64=ee,this.uint32=ec,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.textDecoder=null!=t?t:new TextDecoder}tag(){let e=this.uint32(),t=e>>>3,i=7&e;if(t<=0||i<0||i>5)throw Error("illegal tag: field no "+t+" wire type "+i);return[t,i]}skip(e,t){let i=this.pos;switch(e){case l.Varint:for(;128&this.buf[this.pos++];);break;case l.Bit64:this.pos+=4;case l.Bit32:this.pos+=4;break;case l.LengthDelimited:let n=this.uint32();this.pos+=n;break;case l.StartGroup:for(;;){let[e,i]=this.tag();if(i===l.EndGroup){if(void 0!==t&&e!==t)throw Error("invalid end group tag");break}this.skip(i,e)}break;default:throw Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(i,this.pos)}assertBounds(){if(this.pos>this.len)throw RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return el.dec(...this.varint64())}uint64(){return el.uDec(...this.varint64())}sint64(){let[e,t]=this.varint64(),i=-(1&e);return e=(e>>>1|(1&t)<<31)^i,t=t>>>1^i,el.dec(e,t)}bool(){let[e,t]=this.varint64();return 0!==e||0!==t}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return el.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return el.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),t=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(t,t+e)}string(){return this.textDecoder.decode(this.bytes())}}function ef(e){let t=e.field.localName,i=Object.create(null);return i[t]=function(e){let t=e.field;if(t.repeated)return[];if(void 0!==t.default)return t.default;switch(t.kind){case"enum":return t.T.values[0].no;case"scalar":return eu(t.T,t.L);case"message":let i=t.T,n=new i;return i.fieldWrapper?i.fieldWrapper.unwrapField(n):n;case"map":throw"map fields are not allowed to be extensions"}}(e),[i,()=>i[t]]}let eg="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),ev=[];for(let e=0;e<eg.length;e++)ev[eg[e].charCodeAt(0)]=e;ev[45]=eg.indexOf("+"),ev[95]=eg.indexOf("/");let eb={dec(e){let t=3*e.length/4;"="==e[e.length-2]?t-=2:"="==e[e.length-1]&&(t-=1);let i=new Uint8Array(t),n=0,r=0,a,s=0;for(let t=0;t<e.length;t++){if(void 0===(a=ev[e.charCodeAt(t)]))switch(e[t]){case"=":r=0;case"\n":case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string.")}switch(r){case 0:s=a,r=1;break;case 1:i[n++]=s<<2|(48&a)>>4,s=a,r=2;break;case 2:i[n++]=(15&s)<<4|(60&a)>>2,s=a,r=3;break;case 3:i[n++]=(3&s)<<6|a,r=0}}if(1==r)throw Error("invalid base64 string.");return i.subarray(0,n)},enc(e){let t="",i=0,n,r=0;for(let a=0;a<e.length;a++)switch(n=e[a],i){case 0:t+=eg[n>>2],r=(3&n)<<4,i=1;break;case 1:t+=eg[r|n>>4],r=(15&n)<<2,i=2;break;case 2:t+=eg[r|n>>6],t+=eg[63&n],i=0}return i&&(t+=eg[r],t+="=",1==i&&(t+="=")),t}};function ey(e,t){let i=e.getType();return t.extendee.typeName===i.typeName&&!!i.runtime.bin.listUnknownFields(e).find(e=>e.no==t.field.no)}function ek(e,t){z(e.extendee.typeName==t.getType().typeName,"extension ".concat(e.typeName," can only be applied to message ").concat(e.extendee.typeName))}function eT(e,t){let i=e.localName;if(e.repeated)return t[i].length>0;if(e.oneof)return t[e.oneof.localName].case===i;switch(e.kind){case"enum":case"scalar":if(e.opt||e.req)return void 0!==t[i];if("enum"==e.kind)return t[i]!==e.T.values[0].no;return!eh(e.T,t[i]);case"message":return void 0!==t[i];case"map":return Object.keys(t[i]).length>0}}function eC(e,t){let i=e.localName,n=!e.opt&&!e.req;if(e.repeated)t[i]=[];else if(e.oneof)t[e.oneof.localName]={case:void 0};else switch(e.kind){case"map":t[i]={};break;case"enum":t[i]=n?e.T.values[0].no:void 0;break;case"scalar":t[i]=n?eu(e.T,e.L):void 0;break;case"message":t[i]=void 0}}function eS(e,t){if(null===e||"object"!=typeof e||!Object.getOwnPropertyNames(Z.prototype).every(t=>t in e&&"function"==typeof e[t]))return!1;let i=e.getType();return null!==i&&"function"==typeof i&&"typeName"in i&&"string"==typeof i.typeName&&(void 0===t||i.typeName==t.typeName)}function ew(e,t){return eS(t)||!e.fieldWrapper?t:e.fieldWrapper.wrapField(t)}o.DOUBLE,o.FLOAT,o.INT64,o.UINT64,o.INT32,o.UINT32,o.BOOL,o.STRING,o.BYTES;let eE={ignoreUnknownFields:!1},eP={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0},eR=Symbol(),eI=Symbol();function ex(e){if(null===e)return"null";switch(typeof e){case"object":return Array.isArray(e)?"array":"object";case"string":return e.length>100?"string":'"'.concat(e.split('"').join('\\"'),'"');default:return String(e)}}function eO(e,t,i,n,r){let a=i.localName;if(i.repeated){if(z("map"!=i.kind),null===t)return;if(!Array.isArray(t))throw Error("cannot decode field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(t)));let s=e[a];for(let e of t){if(null===e)throw Error("cannot decode field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(e)));switch(i.kind){case"message":s.push(i.T.fromJson(e,n));break;case"enum":let t=eD(i.T,e,n.ignoreUnknownFields,!0);t!==eI&&s.push(t);break;case"scalar":try{s.push(eM(i.T,e,i.L,!0))}catch(n){let t="cannot decode field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(e));throw n instanceof Error&&n.message.length>0&&(t+=": ".concat(n.message)),Error(t)}}}}else if("map"==i.kind){if(null===t)return;if("object"!=typeof t||Array.isArray(t))throw Error("cannot decode field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(t)));let s=e[a];for(let[e,a]of Object.entries(t)){let l;if(null===a)throw Error("cannot decode field ".concat(r.typeName,".").concat(i.name," from JSON: map value null"));try{l=function(e,t){if(e===o.BOOL)switch(t){case"true":t=!0;break;case"false":t=!1}return eM(e,t,c.BIGINT,!0).toString()}(i.K,e)}catch(n){let e="cannot decode map key for field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(t));throw n instanceof Error&&n.message.length>0&&(e+=": ".concat(n.message)),Error(e)}switch(i.V.kind){case"message":s[l]=i.V.T.fromJson(a,n);break;case"enum":let d=eD(i.V.T,a,n.ignoreUnknownFields,!0);d!==eI&&(s[l]=d);break;case"scalar":try{s[l]=eM(i.V.T,a,c.BIGINT,!0)}catch(n){let e="cannot decode map value for field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(t));throw n instanceof Error&&n.message.length>0&&(e+=": ".concat(n.message)),Error(e)}}}}else switch(i.oneof&&(e=e[i.oneof.localName]={case:a},a="value"),i.kind){case"message":let s=i.T;if(null===t&&"google.protobuf.Value"!=s.typeName)return;let l=e[a];eS(l)?l.fromJson(t,n):(e[a]=l=s.fromJson(t,n),s.fieldWrapper&&!i.oneof&&(e[a]=s.fieldWrapper.unwrapField(l)));break;case"enum":let d=eD(i.T,t,n.ignoreUnknownFields,!1);switch(d){case eR:eC(i,e);break;case eI:break;default:e[a]=d}break;case"scalar":try{let n=eM(i.T,t,i.L,!1);n===eR?eC(i,e):e[a]=n}catch(n){let e="cannot decode field ".concat(r.typeName,".").concat(i.name," from JSON: ").concat(ex(t));throw n instanceof Error&&n.message.length>0&&(e+=": ".concat(n.message)),Error(e)}}}function eM(e,t,i,n){if(null===t)return n?eu(e,i):eR;switch(e){case o.DOUBLE:case o.FLOAT:if("NaN"===t)return Number.NaN;if("Infinity"===t)return Number.POSITIVE_INFINITY;if("-Infinity"===t)return Number.NEGATIVE_INFINITY;if(""===t||"string"==typeof t&&t.trim().length!==t.length||"string"!=typeof t&&"number"!=typeof t)break;let r=Number(t);if(Number.isNaN(r)||!Number.isFinite(r))break;return e==o.FLOAT&&J(r),r;case o.INT32:case o.FIXED32:case o.SFIXED32:case o.SINT32:case o.UINT32:let a;if("number"==typeof t?a=t:"string"==typeof t&&t.length>0&&t.trim().length===t.length&&(a=Number(t)),void 0===a)break;return e==o.UINT32||e==o.FIXED32?K(a):G(a),a;case o.INT64:case o.SFIXED64:case o.SINT64:if("number"!=typeof t&&"string"!=typeof t)break;let s=el.parse(t);return i?s.toString():s;case o.FIXED64:case o.UINT64:if("number"!=typeof t&&"string"!=typeof t)break;let c=el.uParse(t);return i?c.toString():c;case o.BOOL:if("boolean"!=typeof t)break;return t;case o.STRING:if("string"!=typeof t)break;try{encodeURIComponent(t)}catch(e){throw Error("invalid UTF8")}return t;case o.BYTES:if(""===t)return new Uint8Array(0);if("string"!=typeof t)break;return eb.dec(t)}throw Error()}function eD(e,t,i,n){if(null===t)return"google.protobuf.NullValue"==e.typeName?0:n?e.values[0].no:eR;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":let r=e.findName(t);if(void 0!==r)return r.no;if(i)return eI}throw Error("cannot decode enum ".concat(e.typeName," from JSON: ").concat(ex(t)))}function eA(e,t,i){if("map"==e.kind){z("object"==typeof t&&null!=t);let n={},r=Object.entries(t);switch(e.V.kind){case"scalar":for(let[t,i]of r)n[t.toString()]=e_(e.V.T,i);break;case"message":for(let[e,t]of r)n[e.toString()]=t.toJson(i);break;case"enum":let a=e.V.T;for(let[e,t]of r)n[e.toString()]=eN(a,t,i.enumAsInteger)}return i.emitDefaultValues||r.length>0?n:void 0}if(e.repeated){z(Array.isArray(t));let n=[];switch(e.kind){case"scalar":for(let i=0;i<t.length;i++)n.push(e_(e.T,t[i]));break;case"enum":for(let r=0;r<t.length;r++)n.push(eN(e.T,t[r],i.enumAsInteger));break;case"message":for(let e=0;e<t.length;e++)n.push(t[e].toJson(i))}return i.emitDefaultValues||n.length>0?n:void 0}switch(e.kind){case"scalar":return e_(e.T,t);case"enum":return eN(e.T,t,i.enumAsInteger);case"message":return ew(e.T,t).toJson(i)}}function eN(e,t,i){var n;if(z("number"==typeof t),"google.protobuf.NullValue"==e.typeName)return null;if(i)return t;let r=e.findNumber(t);return null!=(n=null==r?void 0:r.name)?n:t}function e_(e,t){switch(e){case o.INT32:case o.SFIXED32:case o.SINT32:case o.FIXED32:case o.UINT32:return z("number"==typeof t),t;case o.FLOAT:case o.DOUBLE:if(z("number"==typeof t),Number.isNaN(t))return"NaN";if(t===Number.POSITIVE_INFINITY)return"Infinity";if(t===Number.NEGATIVE_INFINITY)return"-Infinity";return t;case o.STRING:return z("string"==typeof t),t;case o.BOOL:return z("boolean"==typeof t),t;case o.UINT64:case o.FIXED64:case o.INT64:case o.SFIXED64:case o.SINT64:return z("bigint"==typeof t||"string"==typeof t||"number"==typeof t),t.toString();case o.BYTES:return z(t instanceof Uint8Array),eb.enc(t)}}let eL=Symbol("@bufbuild/protobuf/unknown-fields"),eU={readUnknownFields:!0,readerFactory:e=>new em(e)},ej={writeUnknownFields:!0,writerFactory:()=>new ep};function eF(e,t,i,n,r){let{repeated:a,localName:s}=i;switch(i.oneof&&((e=e[i.oneof.localName]).case!=s&&delete e.value,e.case=s,s="value"),i.kind){case"scalar":case"enum":let d="enum"==i.kind?o.INT32:i.T,u=eq;if("scalar"==i.kind&&i.L>0&&(u=eV),a){let i=e[s];if(n==l.LengthDelimited&&d!=o.STRING&&d!=o.BYTES){let e=t.uint32()+t.pos;for(;t.pos<e;)i.push(u(t,d))}else i.push(u(t,d))}else e[s]=u(t,d);break;case"message":let h=i.T;a?e[s].push(eB(t,new h,r,i)):eS(e[s])?eB(t,e[s],r,i):(e[s]=eB(t,new h,r,i),!h.fieldWrapper||i.oneof||i.repeated||(e[s]=h.fieldWrapper.unwrapField(e[s])));break;case"map":let[p,m]=function(e,t,i){let n,r,a=t.uint32(),s=t.pos+a;for(;t.pos<s;){let[a]=t.tag();switch(a){case 1:n=eq(t,e.K);break;case 2:switch(e.V.kind){case"scalar":r=eq(t,e.V.T);break;case"enum":r=t.int32();break;case"message":r=eB(t,new e.V.T,i,void 0)}}}if(void 0===n&&(n=eu(e.K,c.BIGINT)),"string"!=typeof n&&"number"!=typeof n&&(n=n.toString()),void 0===r)switch(e.V.kind){case"scalar":r=eu(e.V.T,c.BIGINT);break;case"enum":r=e.V.T.values[0].no;break;case"message":r=new e.V.T}return[n,r]}(i,t,r);e[s][p]=m}}function eB(e,t,i,n){let r=t.getType().runtime.bin,a=null==n?void 0:n.delimited;return r.readMessage(t,e,a?n.no:e.uint32(),i,a),t}function eV(e,t){let i=eq(e,t);return"bigint"==typeof i?i.toString():i}function eq(e,t){switch(t){case o.STRING:return e.string();case o.BOOL:return e.bool();case o.DOUBLE:return e.double();case o.FLOAT:return e.float();case o.INT32:return e.int32();case o.INT64:return e.int64();case o.UINT64:return e.uint64();case o.FIXED64:return e.fixed64();case o.BYTES:return e.bytes();case o.FIXED32:return e.fixed32();case o.SFIXED32:return e.sfixed32();case o.SFIXED64:return e.sfixed64();case o.SINT64:return e.sint64();case o.UINT32:return e.uint32();case o.SINT32:return e.sint32()}}function eH(e,t,i,n){z(void 0!==t);let r=e.repeated;switch(e.kind){case"scalar":case"enum":let a="enum"==e.kind?o.INT32:e.T;if(r)if(z(Array.isArray(t)),e.packed)!function(e,t,i,n){if(!n.length)return;e.tag(i,l.LengthDelimited).fork();let[,r]=eG(t);for(let t=0;t<n.length;t++)e[r](n[t]);e.join()}(i,a,e.no,t);else for(let n of t)ez(i,a,e.no,n);else ez(i,a,e.no,t);break;case"message":if(r)for(let r of(z(Array.isArray(t)),t))eW(i,n,e,r);else eW(i,n,e,t);break;case"map":for(let[r,a]of(z("object"==typeof t&&null!=t),Object.entries(t)))!function(e,t,i,n,r){e.tag(i.no,l.LengthDelimited),e.fork();let a=n;switch(i.K){case o.INT32:case o.FIXED32:case o.UINT32:case o.SFIXED32:case o.SINT32:a=Number.parseInt(n);break;case o.BOOL:z("true"==n||"false"==n),a="true"==n}switch(ez(e,i.K,1,a),i.V.kind){case"scalar":ez(e,i.V.T,2,r);break;case"enum":ez(e,o.INT32,2,r);break;case"message":z(void 0!==r),e.tag(2,l.LengthDelimited).bytes(r.toBinary(t))}e.join()}(i,n,e,r,a)}}function eW(e,t,i,n){let r=ew(i.T,n);i.delimited?e.tag(i.no,l.StartGroup).raw(r.toBinary(t)).tag(i.no,l.EndGroup):e.tag(i.no,l.LengthDelimited).bytes(r.toBinary(t))}function ez(e,t,i,n){z(void 0!==n);let[r,a]=eG(t);e.tag(i,r)[a](n)}function eG(e){let t=l.Varint;switch(e){case o.BYTES:case o.STRING:t=l.LengthDelimited;break;case o.DOUBLE:case o.FIXED64:case o.SFIXED64:t=l.Bit64;break;case o.FIXED32:case o.SFIXED32:case o.FLOAT:t=l.Bit32}return[t,o[e].toLowerCase()]}function eK(e){if(void 0===e)return e;if(eS(e))return e.clone();if(e instanceof Uint8Array){let t=new Uint8Array(e.byteLength);return t.set(e),t}return e}function eJ(e){return e instanceof Uint8Array?e:new Uint8Array(e)}class eY{constructor(e,t){this._fields=e,this._normalizer=t}findJsonName(e){if(!this.jsonNames){let e={};for(let t of this.list())e[t.jsonName]=e[t.name]=t;this.jsonNames=e}return this.jsonNames[e]}find(e){if(!this.numbers){let e={};for(let t of this.list())e[t.no]=t;this.numbers=e}return this.numbers[e]}list(){return this.all||(this.all=this._normalizer(this._fields)),this.all}byNumber(){return this.numbersAsc||(this.numbersAsc=this.list().concat().sort((e,t)=>e.no-t.no)),this.numbersAsc}byMember(){if(!this.members){let e;this.members=[];let t=this.members;for(let i of this.list())i.oneof?i.oneof!==e&&(e=i.oneof,t.push(e)):t.push(i)}return this.members}}function eQ(e,t){let i=eX(e);return t?i:e2(e5(i))}let e$=eX;function eX(e){let t=!1,i=[];for(let n=0;n<e.length;n++){let r=e.charAt(n);switch(r){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":i.push(r),t=!1;break;default:t&&(t=!1,r=r.toUpperCase()),i.push(r)}}return i.join("")}let eZ=new Set(["constructor","toString","toJSON","valueOf"]),e0=new Set(["getType","clone","equals","fromBinary","fromJson","fromJsonString","toBinary","toJson","toJsonString","toObject"]),e1=e=>"".concat(e,"$"),e5=e=>e0.has(e)?e1(e):e,e2=e=>eZ.has(e)?e1(e):e;class e3{constructor(e){this.kind="oneof",this.repeated=!1,this.packed=!1,this.opt=!1,this.req=!1,this.default=void 0,this.fields=[],this.name=e,this.localName=eQ(e,!1)}addField(e){z(e.oneof===this,"field ".concat(e.name," not one of ").concat(this.name)),this.fields.push(e)}findField(e){if(!this._lookup){this._lookup=Object.create(null);for(let e=0;e<this.fields.length;e++)this._lookup[this.fields[e].localName]=this.fields[e]}return this._lookup[e]}}let e9=(a=e=>new eY(e,e=>(function(e,t){var i,n,r,a,s,l;let d,u=[];for(let t of"function"==typeof e?e():e){if(t.localName=eQ(t.name,void 0!==t.oneof),t.jsonName=null!=(i=t.jsonName)?i:e$(t.name),t.repeated=null!=(n=t.repeated)&&n,"scalar"==t.kind&&(t.L=null!=(r=t.L)?r:c.BIGINT),t.delimited=null!=(a=t.delimited)&&a,t.req=null!=(s=t.req)&&s,t.opt=null!=(l=t.opt)&&l,void 0===t.packed&&(t.packed="enum"==t.kind||"scalar"==t.kind&&t.T!=o.BYTES&&t.T!=o.STRING),void 0!==t.oneof){let e="string"==typeof t.oneof?t.oneof:t.oneof.name;d&&d.name==e||(d=new e3(e)),t.oneof=d,d.addField(t)}u.push(t)}return u})(e)),s=e=>{for(let t of e.getType().fields.byMember()){if(t.opt)continue;let i=t.localName;if(t.repeated){e[i]=[];continue}switch(t.kind){case"oneof":e[i]={case:void 0};break;case"enum":e[i]=0;break;case"map":e[i]={};break;case"scalar":e[i]=eu(t.T,t.L)}}},{syntax:"proto3",json:{makeReadOptions:function(e){return e?Object.assign(Object.assign({},eE),e):eE},makeWriteOptions:function(e){return e?Object.assign(Object.assign({},eP),e):eP},readMessage(e,t,i,n){if(null==t||Array.isArray(t)||"object"!=typeof t)throw Error("cannot decode message ".concat(e.typeName," from JSON: ").concat(ex(t)));n=null!=n?n:new e;let r=new Map,a=i.typeRegistry;for(let[s,o]of Object.entries(t)){let t=e.fields.findJsonName(s);if(t){if(t.oneof){if(null===o&&"scalar"==t.kind)continue;let i=r.get(t.oneof);if(void 0!==i)throw Error("cannot decode message ".concat(e.typeName,' from JSON: multiple keys for oneof "').concat(t.oneof.name,'" present: "').concat(i,'", "').concat(s,'"'));r.set(t.oneof,s)}eO(n,o,t,i,e)}else{let t=!1;if((null==a?void 0:a.findExtension)&&s.startsWith("[")&&s.endsWith("]")){let r=a.findExtension(s.substring(1,s.length-1));if(r&&r.extendee.typeName==e.typeName){t=!0;let[e,a]=ef(r);eO(e,o,r.field,i,r),function(e,t,i,n){ek(t,e);let r=t.runtime.bin.makeReadOptions(n),a=t.runtime.bin.makeWriteOptions(n);if(ey(e,t)){let i=e.getType().runtime.bin.listUnknownFields(e).filter(e=>e.no!=t.field.no);for(let t of(e.getType().runtime.bin.discardUnknownFields(e),i))e.getType().runtime.bin.onUnknownField(e,t.no,t.wireType,t.data)}let s=a.writerFactory(),o=t.field;o.opt||o.repeated||"enum"!=o.kind&&"scalar"!=o.kind||(o=Object.assign(Object.assign({},t.field),{opt:!0})),t.runtime.bin.writeField(o,i,s,a);let c=r.readerFactory(s.finish());for(;c.pos<c.len;){let[t,i]=c.tag(),n=c.skip(i,t);e.getType().runtime.bin.onUnknownField(e,t,i,n)}}(n,r,a(),i)}}if(!t&&!i.ignoreUnknownFields)throw Error("cannot decode message ".concat(e.typeName,' from JSON: key "').concat(s,'" is unknown'))}}return n},writeMessage(e,t){let i,n=e.getType(),r={};try{for(i of n.fields.byNumber()){if(!eT(i,e)){var a;if(i.req)throw"required field not set";if(!t.emitDefaultValues||!((a=i).repeated||"map"==a.kind||!a.oneof&&"message"!=a.kind&&!a.opt&&!a.req))continue}let n=i.oneof?e[i.oneof.localName].value:e[i.localName],s=eA(i,n,t);void 0!==s&&(r[t.useProtoFieldName?i.name:i.jsonName]=s)}let s=t.typeRegistry;if(null==s?void 0:s.findExtensionFor)for(let i of n.runtime.bin.listUnknownFields(e)){let a=s.findExtensionFor(n.typeName,i.no);if(a&&ey(e,a)){let i=function(e,t,i){ek(t,e);let n=t.runtime.bin.makeReadOptions(i),r=function(e,t){if(!t.repeated&&("enum"==t.kind||"scalar"==t.kind)){for(let i=e.length-1;i>=0;--i)if(e[i].no==t.no)return[e[i]];return[]}return e.filter(e=>e.no===t.no)}(e.getType().runtime.bin.listUnknownFields(e),t.field),[a,s]=ef(t);for(let e of r)t.runtime.bin.readField(a,n.readerFactory(e.data),t.field,e.wireType,n);return s()}(e,a,t),n=eA(a.field,i,t);void 0!==n&&(r[a.field.jsonName]=n)}}}catch(r){let e=i?"cannot encode field ".concat(n.typeName,".").concat(i.name," to JSON"):"cannot encode message ".concat(n.typeName," to JSON"),t=r instanceof Error?r.message:String(r);throw Error(e+(t.length>0?": ".concat(t):""))}return r},readScalar:(e,t,i)=>eM(e,t,null!=i?i:c.BIGINT,!0),writeScalar(e,t,i){if(void 0!==t&&(i||eh(e,t)))return e_(e,t)},debug:ex},bin:{makeReadOptions:function(e){return e?Object.assign(Object.assign({},eU),e):eU},makeWriteOptions:function(e){return e?Object.assign(Object.assign({},ej),e):ej},listUnknownFields(e){var t;return null!=(t=e[eL])?t:[]},discardUnknownFields(e){delete e[eL]},writeUnknownFields(e,t){let i=e[eL];if(i)for(let e of i)t.tag(e.no,e.wireType).raw(e.data)},onUnknownField(e,t,i,n){Array.isArray(e[eL])||(e[eL]=[]),e[eL].push({no:t,wireType:i,data:n})},readMessage(e,t,i,n,r){let a,s,o=e.getType(),c=r?t.len:t.pos+i;for(;t.pos<c&&([a,s]=t.tag(),!0!==r||s!=l.EndGroup);){let i=o.fields.find(a);if(!i){let i=t.skip(s,a);n.readUnknownFields&&this.onUnknownField(e,a,s,i);continue}eF(e,t,i,s,n)}if(r&&(s!=l.EndGroup||a!==i))throw Error("invalid end group tag")},readField:eF,writeMessage(e,t,i){let n=e.getType();for(let r of n.fields.byNumber()){if(!eT(r,e)){if(r.req)throw Error("cannot encode field ".concat(n.typeName,".").concat(r.name," to binary: required field not set"));continue}let a=r.oneof?e[r.oneof.localName].value:e[r.localName];eH(r,a,t,i)}return i.writeUnknownFields&&this.writeUnknownFields(e,t),t},writeField(e,t,i,n){void 0!==t&&eH(e,t,i,n)}},util:Object.assign(Object.assign({},{setEnumType:Q,initPartial(e,t){if(void 0!==e)for(let i of t.getType().fields.byMember()){let n=i.localName;if(null!=e[n])switch(i.kind){case"oneof":let r=e[n].case;if(void 0===r)continue;let a=i.findField(r),s=e[n].value;a&&"message"==a.kind&&!eS(s,a.T)?s=new a.T(s):a&&"scalar"===a.kind&&a.T===o.BYTES&&(s=eJ(s)),t[n]={case:r,value:s};break;case"scalar":case"enum":let c=e[n];i.T===o.BYTES&&(c=i.repeated?c.map(eJ):eJ(c)),t[n]=c;break;case"map":switch(i.V.kind){case"scalar":case"enum":if(i.V.T===o.BYTES)for(let[i,r]of Object.entries(e[n]))t[n][i]=eJ(r);else Object.assign(t[n],e[n]);break;case"message":let l=i.V.T;for(let i of Object.keys(e[n])){let r=e[n][i];l.fieldWrapper||(r=new l(r)),t[n][i]=r}}break;case"message":let d=i.T;if(i.repeated)t[n]=e[n].map(e=>eS(e,d)?e:new d(e));else{let i=e[n];d.fieldWrapper?"google.protobuf.BytesValue"===d.typeName?t[n]=eJ(i):t[n]=i:t[n]=eS(i,d)?i:new d(i)}}}},equals:(e,t,i)=>t===i||!!t&&!!i&&e.fields.byMember().every(e=>{let n=t[e.localName],r=i[e.localName];if(e.repeated){if(n.length!==r.length)return!1;switch(e.kind){case"message":return n.every((t,i)=>e.T.equals(t,r[i]));case"scalar":return n.every((t,i)=>ed(e.T,t,r[i]));case"enum":return n.every((e,t)=>ed(o.INT32,e,r[t]))}throw Error("repeated cannot contain ".concat(e.kind))}switch(e.kind){case"message":let a=n,s=r;return e.T.fieldWrapper&&(void 0===a||eS(a)||(a=e.T.fieldWrapper.wrapField(a)),void 0===s||eS(s)||(s=e.T.fieldWrapper.wrapField(s))),e.T.equals(a,s);case"enum":return ed(o.INT32,n,r);case"scalar":return ed(e.T,n,r);case"oneof":if(n.case!==r.case)return!1;let c=e.findField(n.case);if(void 0===c)return!0;switch(c.kind){case"message":return c.T.equals(n.value,r.value);case"enum":return ed(o.INT32,n.value,r.value);case"scalar":return ed(c.T,n.value,r.value)}throw Error("oneof cannot contain ".concat(c.kind));case"map":let l=Object.keys(n).concat(Object.keys(r));switch(e.V.kind){case"message":let d=e.V.T;return l.every(e=>d.equals(n[e],r[e]));case"enum":return l.every(e=>ed(o.INT32,n[e],r[e]));case"scalar":let u=e.V.T;return l.every(e=>ed(u,n[e],r[e]))}}}),clone(e){let t=e.getType(),i=new t;for(let n of t.fields.byMember()){let t,r=e[n.localName];if(n.repeated)t=r.map(eK);else if("map"==n.kind)for(let[e,a]of(t=i[n.localName],Object.entries(r)))t[e]=eK(a);else t="oneof"==n.kind?n.findField(r.case)?{case:r.case,value:eK(r.value)}:{case:void 0}:eK(r);i[n.localName]=t}for(let n of t.runtime.bin.listUnknownFields(e))t.runtime.bin.onUnknownField(i,n.no,n.wireType,n.data);return i}}),{newFieldList:a,initFields:s}),makeMessageType(e,t,i){return function(e,t,i,n){var r;let a=null!=(r=null==n?void 0:n.localName)?r:t.substring(t.lastIndexOf(".")+1),s={[a]:function(t){e.util.initFields(this),e.util.initPartial(t,this)}}[a];return Object.setPrototypeOf(s.prototype,new Z),Object.assign(s,{runtime:e,typeName:t,fields:e.util.newFieldList(i),fromBinary:(e,t)=>new s().fromBinary(e,t),fromJson:(e,t)=>new s().fromJson(e,t),fromJsonString:(e,t)=>new s().fromJsonString(e,t),equals:(t,i)=>e.util.equals(s,t,i)}),s}(this,e,t,i)},makeEnum:function(e,t,i){let n={};for(let e of t){let t=X(e);n[t.localName]=t.no,n[t.no]=t.localName}return Q(n,e,t),n},makeEnumType:$,getEnumType:function(e){let t=e[Y];return z(t,"missing enum type on enum object"),t},makeExtension(e,t,i){var n;let r;return n=this,{typeName:e,extendee:t,get field(){if(!r){let t="function"==typeof i?i():i;t.name=e.split(".").pop(),t.jsonName="[".concat(e,"]"),r=n.util.newFieldList([t]).list()[0]}return r},runtime:n}}});class e6 extends Z{constructor(e){super(),this.seconds=el.zero,this.nanos=0,e9.util.initPartial(e,this)}fromJson(e,t){if("string"!=typeof e)throw Error("cannot decode google.protobuf.Timestamp from JSON: ".concat(e9.json.debug(e)));let i=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!i)throw Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");let n=Date.parse(i[1]+"-"+i[2]+"-"+i[3]+"T"+i[4]+":"+i[5]+":"+i[6]+(i[8]?i[8]:"Z"));if(Number.isNaN(n))throw Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");if(n<Date.parse("0001-01-01T00:00:00Z")||n>Date.parse("9999-12-31T23:59:59Z"))throw Error("cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");return this.seconds=el.parse(n/1e3),this.nanos=0,i[7]&&(this.nanos=parseInt("1"+i[7]+"0".repeat(9-i[7].length))-1e9),this}toJson(e){let t=1e3*Number(this.seconds);if(t<Date.parse("0001-01-01T00:00:00Z")||t>Date.parse("9999-12-31T23:59:59Z"))throw Error("cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");if(this.nanos<0)throw Error("cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative");let i="Z";if(this.nanos>0){let e=(this.nanos+1e9).toString().substring(1);i="000000"===e.substring(3)?"."+e.substring(0,3)+"Z":"000"===e.substring(6)?"."+e.substring(0,6)+"Z":"."+e+"Z"}return new Date(t).toISOString().replace(".000Z",i)}toDate(){return new Date(1e3*Number(this.seconds)+Math.ceil(this.nanos/1e6))}static now(){return e6.fromDate(new Date)}static fromDate(e){let t=e.getTime();return new e6({seconds:el.parse(Math.floor(t/1e3)),nanos:t%1e3*1e6})}static fromBinary(e,t){return new e6().fromBinary(e,t)}static fromJson(e,t){return new e6().fromJson(e,t)}static fromJsonString(e,t){return new e6().fromJsonString(e,t)}static equals(e,t){return e9.util.equals(e6,e,t)}}e6.runtime=e9,e6.typeName="google.protobuf.Timestamp",e6.fields=e9.util.newFieldList(()=>[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}]);let e4=e9.makeMessageType("livekit.MetricsBatch",()=>[{no:1,name:"timestamp_ms",kind:"scalar",T:3},{no:2,name:"normalized_timestamp",kind:"message",T:e6},{no:3,name:"str_data",kind:"scalar",T:9,repeated:!0},{no:4,name:"time_series",kind:"message",T:e7,repeated:!0},{no:5,name:"events",kind:"message",T:te,repeated:!0}]),e7=e9.makeMessageType("livekit.TimeSeriesMetric",()=>[{no:1,name:"label",kind:"scalar",T:13},{no:2,name:"participant_identity",kind:"scalar",T:13},{no:3,name:"track_sid",kind:"scalar",T:13},{no:4,name:"samples",kind:"message",T:e8,repeated:!0},{no:5,name:"rid",kind:"scalar",T:13}]),e8=e9.makeMessageType("livekit.MetricSample",()=>[{no:1,name:"timestamp_ms",kind:"scalar",T:3},{no:2,name:"normalized_timestamp",kind:"message",T:e6},{no:3,name:"value",kind:"scalar",T:2}]),te=e9.makeMessageType("livekit.EventMetric",()=>[{no:1,name:"label",kind:"scalar",T:13},{no:2,name:"participant_identity",kind:"scalar",T:13},{no:3,name:"track_sid",kind:"scalar",T:13},{no:4,name:"start_timestamp_ms",kind:"scalar",T:3},{no:5,name:"end_timestamp_ms",kind:"scalar",T:3,opt:!0},{no:6,name:"normalized_start_timestamp",kind:"message",T:e6},{no:7,name:"normalized_end_timestamp",kind:"message",T:e6,opt:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"rid",kind:"scalar",T:13}]),tt=e9.makeEnum("livekit.BackupCodecPolicy",[{no:0,name:"PREFER_REGRESSION"},{no:1,name:"SIMULCAST"},{no:2,name:"REGRESSION"}]),ti=e9.makeEnum("livekit.TrackType",[{no:0,name:"AUDIO"},{no:1,name:"VIDEO"},{no:2,name:"DATA"}]),tn=e9.makeEnum("livekit.TrackSource",[{no:0,name:"UNKNOWN"},{no:1,name:"CAMERA"},{no:2,name:"MICROPHONE"},{no:3,name:"SCREEN_SHARE"},{no:4,name:"SCREEN_SHARE_AUDIO"}]),tr=e9.makeEnum("livekit.VideoQuality",[{no:0,name:"LOW"},{no:1,name:"MEDIUM"},{no:2,name:"HIGH"},{no:3,name:"OFF"}]),ta=e9.makeEnum("livekit.ConnectionQuality",[{no:0,name:"POOR"},{no:1,name:"GOOD"},{no:2,name:"EXCELLENT"},{no:3,name:"LOST"}]),ts=e9.makeEnum("livekit.ClientConfigSetting",[{no:0,name:"UNSET"},{no:1,name:"DISABLED"},{no:2,name:"ENABLED"}]),to=e9.makeEnum("livekit.DisconnectReason",[{no:0,name:"UNKNOWN_REASON"},{no:1,name:"CLIENT_INITIATED"},{no:2,name:"DUPLICATE_IDENTITY"},{no:3,name:"SERVER_SHUTDOWN"},{no:4,name:"PARTICIPANT_REMOVED"},{no:5,name:"ROOM_DELETED"},{no:6,name:"STATE_MISMATCH"},{no:7,name:"JOIN_FAILURE"},{no:8,name:"MIGRATION"},{no:9,name:"SIGNAL_CLOSE"},{no:10,name:"ROOM_CLOSED"},{no:11,name:"USER_UNAVAILABLE"},{no:12,name:"USER_REJECTED"},{no:13,name:"SIP_TRUNK_FAILURE"},{no:14,name:"CONNECTION_TIMEOUT"},{no:15,name:"MEDIA_FAILURE"}]),tc=e9.makeEnum("livekit.ReconnectReason",[{no:0,name:"RR_UNKNOWN"},{no:1,name:"RR_SIGNAL_DISCONNECTED"},{no:2,name:"RR_PUBLISHER_FAILED"},{no:3,name:"RR_SUBSCRIBER_FAILED"},{no:4,name:"RR_SWITCH_CANDIDATE"}]),tl=e9.makeEnum("livekit.SubscriptionError",[{no:0,name:"SE_UNKNOWN"},{no:1,name:"SE_CODEC_UNSUPPORTED"},{no:2,name:"SE_TRACK_NOTFOUND"}]),td=e9.makeEnum("livekit.AudioTrackFeature",[{no:0,name:"TF_STEREO"},{no:1,name:"TF_NO_DTX"},{no:2,name:"TF_AUTO_GAIN_CONTROL"},{no:3,name:"TF_ECHO_CANCELLATION"},{no:4,name:"TF_NOISE_SUPPRESSION"},{no:5,name:"TF_ENHANCED_NOISE_CANCELLATION"},{no:6,name:"TF_PRECONNECT_BUFFER"}]),tu=e9.makeMessageType("livekit.Room",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"empty_timeout",kind:"scalar",T:13},{no:14,name:"departure_timeout",kind:"scalar",T:13},{no:4,name:"max_participants",kind:"scalar",T:13},{no:5,name:"creation_time",kind:"scalar",T:3},{no:15,name:"creation_time_ms",kind:"scalar",T:3},{no:6,name:"turn_password",kind:"scalar",T:9},{no:7,name:"enabled_codecs",kind:"message",T:th,repeated:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"num_participants",kind:"scalar",T:13},{no:11,name:"num_publishers",kind:"scalar",T:13},{no:10,name:"active_recording",kind:"scalar",T:8},{no:13,name:"version",kind:"message",T:tH}]),th=e9.makeMessageType("livekit.Codec",()=>[{no:1,name:"mime",kind:"scalar",T:9},{no:2,name:"fmtp_line",kind:"scalar",T:9}]),tp=e9.makeMessageType("livekit.ParticipantPermission",()=>[{no:1,name:"can_subscribe",kind:"scalar",T:8},{no:2,name:"can_publish",kind:"scalar",T:8},{no:3,name:"can_publish_data",kind:"scalar",T:8},{no:9,name:"can_publish_sources",kind:"enum",T:e9.getEnumType(tn),repeated:!0},{no:7,name:"hidden",kind:"scalar",T:8},{no:8,name:"recorder",kind:"scalar",T:8},{no:10,name:"can_update_metadata",kind:"scalar",T:8},{no:11,name:"agent",kind:"scalar",T:8},{no:12,name:"can_subscribe_metrics",kind:"scalar",T:8}]),tm=e9.makeMessageType("livekit.ParticipantInfo",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"identity",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:e9.getEnumType(tf)},{no:4,name:"tracks",kind:"message",T:tk,repeated:!0},{no:5,name:"metadata",kind:"scalar",T:9},{no:6,name:"joined_at",kind:"scalar",T:3},{no:17,name:"joined_at_ms",kind:"scalar",T:3},{no:9,name:"name",kind:"scalar",T:9},{no:10,name:"version",kind:"scalar",T:13},{no:11,name:"permission",kind:"message",T:tp},{no:12,name:"region",kind:"scalar",T:9},{no:13,name:"is_publisher",kind:"scalar",T:8},{no:14,name:"kind",kind:"enum",T:e9.getEnumType(tg)},{no:15,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:16,name:"disconnect_reason",kind:"enum",T:e9.getEnumType(to)},{no:18,name:"kind_details",kind:"enum",T:e9.getEnumType(tv),repeated:!0}]),tf=e9.makeEnum("livekit.ParticipantInfo.State",[{no:0,name:"JOINING"},{no:1,name:"JOINED"},{no:2,name:"ACTIVE"},{no:3,name:"DISCONNECTED"}]),tg=e9.makeEnum("livekit.ParticipantInfo.Kind",[{no:0,name:"STANDARD"},{no:1,name:"INGRESS"},{no:2,name:"EGRESS"},{no:3,name:"SIP"},{no:4,name:"AGENT"}]),tv=e9.makeEnum("livekit.ParticipantInfo.KindDetail",[{no:0,name:"CLOUD_AGENT"},{no:1,name:"FORWARDED"}]),tb=e9.makeEnum("livekit.Encryption.Type",[{no:0,name:"NONE"},{no:1,name:"GCM"},{no:2,name:"CUSTOM"}]),ty=e9.makeMessageType("livekit.SimulcastCodecInfo",()=>[{no:1,name:"mime_type",kind:"scalar",T:9},{no:2,name:"mid",kind:"scalar",T:9},{no:3,name:"cid",kind:"scalar",T:9},{no:4,name:"layers",kind:"message",T:tT,repeated:!0}]),tk=e9.makeMessageType("livekit.TrackInfo",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"type",kind:"enum",T:e9.getEnumType(ti)},{no:3,name:"name",kind:"scalar",T:9},{no:4,name:"muted",kind:"scalar",T:8},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"simulcast",kind:"scalar",T:8},{no:8,name:"disable_dtx",kind:"scalar",T:8},{no:9,name:"source",kind:"enum",T:e9.getEnumType(tn)},{no:10,name:"layers",kind:"message",T:tT,repeated:!0},{no:11,name:"mime_type",kind:"scalar",T:9},{no:12,name:"mid",kind:"scalar",T:9},{no:13,name:"codecs",kind:"message",T:ty,repeated:!0},{no:14,name:"stereo",kind:"scalar",T:8},{no:15,name:"disable_red",kind:"scalar",T:8},{no:16,name:"encryption",kind:"enum",T:e9.getEnumType(tb)},{no:17,name:"stream",kind:"scalar",T:9},{no:18,name:"version",kind:"message",T:tH},{no:19,name:"audio_features",kind:"enum",T:e9.getEnumType(td),repeated:!0},{no:20,name:"backup_codec_policy",kind:"enum",T:e9.getEnumType(tt)}]),tT=e9.makeMessageType("livekit.VideoLayer",()=>[{no:1,name:"quality",kind:"enum",T:e9.getEnumType(tr)},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"ssrc",kind:"scalar",T:13},{no:6,name:"spatial_layer",kind:"scalar",T:5},{no:7,name:"rid",kind:"scalar",T:9}]),tC=e9.makeMessageType("livekit.DataPacket",()=>[{no:1,name:"kind",kind:"enum",T:e9.getEnumType(tS)},{no:4,name:"participant_identity",kind:"scalar",T:9},{no:5,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:2,name:"user",kind:"message",T:tP,oneof:"value"},{no:3,name:"speaker",kind:"message",T:tw,oneof:"value"},{no:6,name:"sip_dtmf",kind:"message",T:tR,oneof:"value"},{no:7,name:"transcription",kind:"message",T:tI,oneof:"value"},{no:8,name:"metrics",kind:"message",T:e4,oneof:"value"},{no:9,name:"chat_message",kind:"message",T:tO,oneof:"value"},{no:10,name:"rpc_request",kind:"message",T:tM,oneof:"value"},{no:11,name:"rpc_ack",kind:"message",T:tD,oneof:"value"},{no:12,name:"rpc_response",kind:"message",T:tA,oneof:"value"},{no:13,name:"stream_header",kind:"message",T:tK,oneof:"value"},{no:14,name:"stream_chunk",kind:"message",T:tJ,oneof:"value"},{no:15,name:"stream_trailer",kind:"message",T:tY,oneof:"value"},{no:16,name:"sequence",kind:"scalar",T:13},{no:17,name:"participant_sid",kind:"scalar",T:9}]),tS=e9.makeEnum("livekit.DataPacket.Kind",[{no:0,name:"RELIABLE"},{no:1,name:"LOSSY"}]),tw=e9.makeMessageType("livekit.ActiveSpeakerUpdate",()=>[{no:1,name:"speakers",kind:"message",T:tE,repeated:!0}]),tE=e9.makeMessageType("livekit.SpeakerInfo",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"level",kind:"scalar",T:2},{no:3,name:"active",kind:"scalar",T:8}]),tP=e9.makeMessageType("livekit.UserPacket",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:5,name:"participant_identity",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:12},{no:3,name:"destination_sids",kind:"scalar",T:9,repeated:!0},{no:6,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:4,name:"topic",kind:"scalar",T:9,opt:!0},{no:8,name:"id",kind:"scalar",T:9,opt:!0},{no:9,name:"start_time",kind:"scalar",T:4,opt:!0},{no:10,name:"end_time",kind:"scalar",T:4,opt:!0},{no:11,name:"nonce",kind:"scalar",T:12}]),tR=e9.makeMessageType("livekit.SipDTMF",()=>[{no:3,name:"code",kind:"scalar",T:13},{no:4,name:"digit",kind:"scalar",T:9}]),tI=e9.makeMessageType("livekit.Transcription",()=>[{no:2,name:"transcribed_participant_identity",kind:"scalar",T:9},{no:3,name:"track_id",kind:"scalar",T:9},{no:4,name:"segments",kind:"message",T:tx,repeated:!0}]),tx=e9.makeMessageType("livekit.TranscriptionSegment",()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"text",kind:"scalar",T:9},{no:3,name:"start_time",kind:"scalar",T:4},{no:4,name:"end_time",kind:"scalar",T:4},{no:5,name:"final",kind:"scalar",T:8},{no:6,name:"language",kind:"scalar",T:9}]),tO=e9.makeMessageType("livekit.ChatMessage",()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"timestamp",kind:"scalar",T:3},{no:3,name:"edit_timestamp",kind:"scalar",T:3,opt:!0},{no:4,name:"message",kind:"scalar",T:9},{no:5,name:"deleted",kind:"scalar",T:8},{no:6,name:"generated",kind:"scalar",T:8}]),tM=e9.makeMessageType("livekit.RpcRequest",()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"method",kind:"scalar",T:9},{no:3,name:"payload",kind:"scalar",T:9},{no:4,name:"response_timeout_ms",kind:"scalar",T:13},{no:5,name:"version",kind:"scalar",T:13}]),tD=e9.makeMessageType("livekit.RpcAck",()=>[{no:1,name:"request_id",kind:"scalar",T:9}]),tA=e9.makeMessageType("livekit.RpcResponse",()=>[{no:1,name:"request_id",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:9,oneof:"value"},{no:3,name:"error",kind:"message",T:tN,oneof:"value"}]),tN=e9.makeMessageType("livekit.RpcError",()=>[{no:1,name:"code",kind:"scalar",T:13},{no:2,name:"message",kind:"scalar",T:9},{no:3,name:"data",kind:"scalar",T:9}]),t_=e9.makeMessageType("livekit.ParticipantTracks",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sids",kind:"scalar",T:9,repeated:!0}]),tL=e9.makeMessageType("livekit.ServerInfo",()=>[{no:1,name:"edition",kind:"enum",T:e9.getEnumType(tU)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"region",kind:"scalar",T:9},{no:5,name:"node_id",kind:"scalar",T:9},{no:6,name:"debug_info",kind:"scalar",T:9},{no:7,name:"agent_protocol",kind:"scalar",T:5}]),tU=e9.makeEnum("livekit.ServerInfo.Edition",[{no:0,name:"Standard"},{no:1,name:"Cloud"}]),tj=e9.makeMessageType("livekit.ClientInfo",()=>[{no:1,name:"sdk",kind:"enum",T:e9.getEnumType(tF)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"os",kind:"scalar",T:9},{no:5,name:"os_version",kind:"scalar",T:9},{no:6,name:"device_model",kind:"scalar",T:9},{no:7,name:"browser",kind:"scalar",T:9},{no:8,name:"browser_version",kind:"scalar",T:9},{no:9,name:"address",kind:"scalar",T:9},{no:10,name:"network",kind:"scalar",T:9},{no:11,name:"other_sdks",kind:"scalar",T:9}]),tF=e9.makeEnum("livekit.ClientInfo.SDK",[{no:0,name:"UNKNOWN"},{no:1,name:"JS"},{no:2,name:"SWIFT"},{no:3,name:"ANDROID"},{no:4,name:"FLUTTER"},{no:5,name:"GO"},{no:6,name:"UNITY"},{no:7,name:"REACT_NATIVE"},{no:8,name:"RUST"},{no:9,name:"PYTHON"},{no:10,name:"CPP"},{no:11,name:"UNITY_WEB"},{no:12,name:"NODE"},{no:13,name:"UNREAL"},{no:14,name:"ESP32"}]),tB=e9.makeMessageType("livekit.ClientConfiguration",()=>[{no:1,name:"video",kind:"message",T:tV},{no:2,name:"screen",kind:"message",T:tV},{no:3,name:"resume_connection",kind:"enum",T:e9.getEnumType(ts)},{no:4,name:"disabled_codecs",kind:"message",T:tq},{no:5,name:"force_relay",kind:"enum",T:e9.getEnumType(ts)}]),tV=e9.makeMessageType("livekit.VideoConfiguration",()=>[{no:1,name:"hardware_encoder",kind:"enum",T:e9.getEnumType(ts)}]),tq=e9.makeMessageType("livekit.DisabledCodecs",()=>[{no:1,name:"codecs",kind:"message",T:th,repeated:!0},{no:2,name:"publish",kind:"message",T:th,repeated:!0}]),tH=e9.makeMessageType("livekit.TimedVersion",()=>[{no:1,name:"unix_micro",kind:"scalar",T:3},{no:2,name:"ticks",kind:"scalar",T:5}]),tW=e9.makeEnum("livekit.DataStream.OperationType",[{no:0,name:"CREATE"},{no:1,name:"UPDATE"},{no:2,name:"DELETE"},{no:3,name:"REACTION"}]),tz=e9.makeMessageType("livekit.DataStream.TextHeader",()=>[{no:1,name:"operation_type",kind:"enum",T:e9.getEnumType(tW)},{no:2,name:"version",kind:"scalar",T:5},{no:3,name:"reply_to_stream_id",kind:"scalar",T:9},{no:4,name:"attached_stream_ids",kind:"scalar",T:9,repeated:!0},{no:5,name:"generated",kind:"scalar",T:8}],{localName:"DataStream_TextHeader"}),tG=e9.makeMessageType("livekit.DataStream.ByteHeader",()=>[{no:1,name:"name",kind:"scalar",T:9}],{localName:"DataStream_ByteHeader"}),tK=e9.makeMessageType("livekit.DataStream.Header",()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"timestamp",kind:"scalar",T:3},{no:3,name:"topic",kind:"scalar",T:9},{no:4,name:"mime_type",kind:"scalar",T:9},{no:5,name:"total_length",kind:"scalar",T:4,opt:!0},{no:7,name:"encryption_type",kind:"enum",T:e9.getEnumType(tb)},{no:8,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:9,name:"text_header",kind:"message",T:tz,oneof:"content_header"},{no:10,name:"byte_header",kind:"message",T:tG,oneof:"content_header"}],{localName:"DataStream_Header"}),tJ=e9.makeMessageType("livekit.DataStream.Chunk",()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"chunk_index",kind:"scalar",T:4},{no:3,name:"content",kind:"scalar",T:12},{no:4,name:"version",kind:"scalar",T:5},{no:5,name:"iv",kind:"scalar",T:12,opt:!0}],{localName:"DataStream_Chunk"}),tY=e9.makeMessageType("livekit.DataStream.Trailer",()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"reason",kind:"scalar",T:9},{no:3,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}}],{localName:"DataStream_Trailer"}),tQ=e9.makeEnum("livekit.SignalTarget",[{no:0,name:"PUBLISHER"},{no:1,name:"SUBSCRIBER"}]),t$=e9.makeEnum("livekit.StreamState",[{no:0,name:"ACTIVE"},{no:1,name:"PAUSED"}]),tX=e9.makeEnum("livekit.CandidateProtocol",[{no:0,name:"UDP"},{no:1,name:"TCP"},{no:2,name:"TLS"}]),tZ=e9.makeMessageType("livekit.SignalRequest",()=>[{no:1,name:"offer",kind:"message",T:t8,oneof:"message"},{no:2,name:"answer",kind:"message",T:t8,oneof:"message"},{no:3,name:"trickle",kind:"message",T:t2,oneof:"message"},{no:4,name:"add_track",kind:"message",T:t5,oneof:"message"},{no:5,name:"mute",kind:"message",T:t3,oneof:"message"},{no:6,name:"subscription",kind:"message",T:it,oneof:"message"},{no:7,name:"track_setting",kind:"message",T:ii,oneof:"message"},{no:8,name:"leave",kind:"message",T:is,oneof:"message"},{no:10,name:"update_layers",kind:"message",T:ic,oneof:"message"},{no:11,name:"subscription_permission",kind:"message",T:iC,oneof:"message"},{no:12,name:"sync_state",kind:"message",T:iE,oneof:"message"},{no:13,name:"simulate",kind:"message",T:iI,oneof:"message"},{no:14,name:"ping",kind:"scalar",T:3,oneof:"message"},{no:15,name:"update_metadata",kind:"message",T:il,oneof:"message"},{no:16,name:"ping_req",kind:"message",T:ix,oneof:"message"},{no:17,name:"update_audio_track",kind:"message",T:ir,oneof:"message"},{no:18,name:"update_video_track",kind:"message",T:ia,oneof:"message"}]),t0=e9.makeMessageType("livekit.SignalResponse",()=>[{no:1,name:"join",kind:"message",T:t9,oneof:"message"},{no:2,name:"answer",kind:"message",T:t8,oneof:"message"},{no:3,name:"offer",kind:"message",T:t8,oneof:"message"},{no:4,name:"trickle",kind:"message",T:t2,oneof:"message"},{no:5,name:"update",kind:"message",T:ie,oneof:"message"},{no:6,name:"track_published",kind:"message",T:t4,oneof:"message"},{no:8,name:"leave",kind:"message",T:is,oneof:"message"},{no:9,name:"mute",kind:"message",T:t3,oneof:"message"},{no:10,name:"speakers_changed",kind:"message",T:iu,oneof:"message"},{no:11,name:"room_update",kind:"message",T:ih,oneof:"message"},{no:12,name:"connection_quality",kind:"message",T:im,oneof:"message"},{no:13,name:"stream_state_update",kind:"message",T:iv,oneof:"message"},{no:14,name:"subscribed_quality_update",kind:"message",T:ik,oneof:"message"},{no:15,name:"subscription_permission_update",kind:"message",T:iS,oneof:"message"},{no:16,name:"refresh_token",kind:"scalar",T:9,oneof:"message"},{no:17,name:"track_unpublished",kind:"message",T:t7,oneof:"message"},{no:18,name:"pong",kind:"scalar",T:3,oneof:"message"},{no:19,name:"reconnect",kind:"message",T:t6,oneof:"message"},{no:20,name:"pong_resp",kind:"message",T:iO,oneof:"message"},{no:21,name:"subscription_response",kind:"message",T:iA,oneof:"message"},{no:22,name:"request_response",kind:"message",T:iN,oneof:"message"},{no:23,name:"track_subscribed",kind:"message",T:iL,oneof:"message"},{no:24,name:"room_moved",kind:"message",T:iw,oneof:"message"}]),t1=e9.makeMessageType("livekit.SimulcastCodec",()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"cid",kind:"scalar",T:9}]),t5=e9.makeMessageType("livekit.AddTrackRequest",()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:e9.getEnumType(ti)},{no:4,name:"width",kind:"scalar",T:13},{no:5,name:"height",kind:"scalar",T:13},{no:6,name:"muted",kind:"scalar",T:8},{no:7,name:"disable_dtx",kind:"scalar",T:8},{no:8,name:"source",kind:"enum",T:e9.getEnumType(tn)},{no:9,name:"layers",kind:"message",T:tT,repeated:!0},{no:10,name:"simulcast_codecs",kind:"message",T:t1,repeated:!0},{no:11,name:"sid",kind:"scalar",T:9},{no:12,name:"stereo",kind:"scalar",T:8},{no:13,name:"disable_red",kind:"scalar",T:8},{no:14,name:"encryption",kind:"enum",T:e9.getEnumType(tb)},{no:15,name:"stream",kind:"scalar",T:9},{no:16,name:"backup_codec_policy",kind:"enum",T:e9.getEnumType(tt)},{no:17,name:"audio_features",kind:"enum",T:e9.getEnumType(td),repeated:!0}]),t2=e9.makeMessageType("livekit.TrickleRequest",()=>[{no:1,name:"candidateInit",kind:"scalar",T:9},{no:2,name:"target",kind:"enum",T:e9.getEnumType(tQ)},{no:3,name:"final",kind:"scalar",T:8}]),t3=e9.makeMessageType("livekit.MuteTrackRequest",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"muted",kind:"scalar",T:8}]),t9=e9.makeMessageType("livekit.JoinResponse",()=>[{no:1,name:"room",kind:"message",T:tu},{no:2,name:"participant",kind:"message",T:tm},{no:3,name:"other_participants",kind:"message",T:tm,repeated:!0},{no:4,name:"server_version",kind:"scalar",T:9},{no:5,name:"ice_servers",kind:"message",T:id,repeated:!0},{no:6,name:"subscriber_primary",kind:"scalar",T:8},{no:7,name:"alternative_url",kind:"scalar",T:9},{no:8,name:"client_configuration",kind:"message",T:tB},{no:9,name:"server_region",kind:"scalar",T:9},{no:10,name:"ping_timeout",kind:"scalar",T:5},{no:11,name:"ping_interval",kind:"scalar",T:5},{no:12,name:"server_info",kind:"message",T:tL},{no:13,name:"sif_trailer",kind:"scalar",T:12},{no:14,name:"enabled_publish_codecs",kind:"message",T:th,repeated:!0},{no:15,name:"fast_publish",kind:"scalar",T:8}]),t6=e9.makeMessageType("livekit.ReconnectResponse",()=>[{no:1,name:"ice_servers",kind:"message",T:id,repeated:!0},{no:2,name:"client_configuration",kind:"message",T:tB},{no:3,name:"server_info",kind:"message",T:tL},{no:4,name:"last_message_seq",kind:"scalar",T:13}]),t4=e9.makeMessageType("livekit.TrackPublishedResponse",()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"track",kind:"message",T:tk}]),t7=e9.makeMessageType("livekit.TrackUnpublishedResponse",()=>[{no:1,name:"track_sid",kind:"scalar",T:9}]),t8=e9.makeMessageType("livekit.SessionDescription",()=>[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"sdp",kind:"scalar",T:9},{no:3,name:"id",kind:"scalar",T:13}]),ie=e9.makeMessageType("livekit.ParticipantUpdate",()=>[{no:1,name:"participants",kind:"message",T:tm,repeated:!0}]),it=e9.makeMessageType("livekit.UpdateSubscription",()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:2,name:"subscribe",kind:"scalar",T:8},{no:3,name:"participant_tracks",kind:"message",T:t_,repeated:!0}]),ii=e9.makeMessageType("livekit.UpdateTrackSettings",()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:3,name:"disabled",kind:"scalar",T:8},{no:4,name:"quality",kind:"enum",T:e9.getEnumType(tr)},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"fps",kind:"scalar",T:13},{no:8,name:"priority",kind:"scalar",T:13}]),ir=e9.makeMessageType("livekit.UpdateLocalAudioTrack",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"features",kind:"enum",T:e9.getEnumType(td),repeated:!0}]),ia=e9.makeMessageType("livekit.UpdateLocalVideoTrack",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13}]),is=e9.makeMessageType("livekit.LeaveRequest",()=>[{no:1,name:"can_reconnect",kind:"scalar",T:8},{no:2,name:"reason",kind:"enum",T:e9.getEnumType(to)},{no:3,name:"action",kind:"enum",T:e9.getEnumType(io)},{no:4,name:"regions",kind:"message",T:iM}]),io=e9.makeEnum("livekit.LeaveRequest.Action",[{no:0,name:"DISCONNECT"},{no:1,name:"RESUME"},{no:2,name:"RECONNECT"}]),ic=e9.makeMessageType("livekit.UpdateVideoLayers",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"layers",kind:"message",T:tT,repeated:!0}]),il=e9.makeMessageType("livekit.UpdateParticipantMetadata",()=>[{no:1,name:"metadata",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:4,name:"request_id",kind:"scalar",T:13}]),id=e9.makeMessageType("livekit.ICEServer",()=>[{no:1,name:"urls",kind:"scalar",T:9,repeated:!0},{no:2,name:"username",kind:"scalar",T:9},{no:3,name:"credential",kind:"scalar",T:9}]),iu=e9.makeMessageType("livekit.SpeakersChanged",()=>[{no:1,name:"speakers",kind:"message",T:tE,repeated:!0}]),ih=e9.makeMessageType("livekit.RoomUpdate",()=>[{no:1,name:"room",kind:"message",T:tu}]),ip=e9.makeMessageType("livekit.ConnectionQualityInfo",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"quality",kind:"enum",T:e9.getEnumType(ta)},{no:3,name:"score",kind:"scalar",T:2}]),im=e9.makeMessageType("livekit.ConnectionQualityUpdate",()=>[{no:1,name:"updates",kind:"message",T:ip,repeated:!0}]),ig=e9.makeMessageType("livekit.StreamStateInfo",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:e9.getEnumType(t$)}]),iv=e9.makeMessageType("livekit.StreamStateUpdate",()=>[{no:1,name:"stream_states",kind:"message",T:ig,repeated:!0}]),ib=e9.makeMessageType("livekit.SubscribedQuality",()=>[{no:1,name:"quality",kind:"enum",T:e9.getEnumType(tr)},{no:2,name:"enabled",kind:"scalar",T:8}]),iy=e9.makeMessageType("livekit.SubscribedCodec",()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"qualities",kind:"message",T:ib,repeated:!0}]),ik=e9.makeMessageType("livekit.SubscribedQualityUpdate",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"subscribed_qualities",kind:"message",T:ib,repeated:!0},{no:3,name:"subscribed_codecs",kind:"message",T:iy,repeated:!0}]),iT=e9.makeMessageType("livekit.TrackPermission",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"all_tracks",kind:"scalar",T:8},{no:3,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:4,name:"participant_identity",kind:"scalar",T:9}]),iC=e9.makeMessageType("livekit.SubscriptionPermission",()=>[{no:1,name:"all_participants",kind:"scalar",T:8},{no:2,name:"track_permissions",kind:"message",T:iT,repeated:!0}]),iS=e9.makeMessageType("livekit.SubscriptionPermissionUpdate",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"allowed",kind:"scalar",T:8}]),iw=e9.makeMessageType("livekit.RoomMovedResponse",()=>[{no:1,name:"room",kind:"message",T:tu},{no:2,name:"token",kind:"scalar",T:9},{no:3,name:"participant",kind:"message",T:tm},{no:4,name:"other_participants",kind:"message",T:tm,repeated:!0}]),iE=e9.makeMessageType("livekit.SyncState",()=>[{no:1,name:"answer",kind:"message",T:t8},{no:2,name:"subscription",kind:"message",T:it},{no:3,name:"publish_tracks",kind:"message",T:t4,repeated:!0},{no:4,name:"data_channels",kind:"message",T:iR,repeated:!0},{no:5,name:"offer",kind:"message",T:t8},{no:6,name:"track_sids_disabled",kind:"scalar",T:9,repeated:!0},{no:7,name:"datachannel_receive_states",kind:"message",T:iP,repeated:!0}]),iP=e9.makeMessageType("livekit.DataChannelReceiveState",()=>[{no:1,name:"publisher_sid",kind:"scalar",T:9},{no:2,name:"last_seq",kind:"scalar",T:13}]),iR=e9.makeMessageType("livekit.DataChannelInfo",()=>[{no:1,name:"label",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:13},{no:3,name:"target",kind:"enum",T:e9.getEnumType(tQ)}]),iI=e9.makeMessageType("livekit.SimulateScenario",()=>[{no:1,name:"speaker_update",kind:"scalar",T:5,oneof:"scenario"},{no:2,name:"node_failure",kind:"scalar",T:8,oneof:"scenario"},{no:3,name:"migration",kind:"scalar",T:8,oneof:"scenario"},{no:4,name:"server_leave",kind:"scalar",T:8,oneof:"scenario"},{no:5,name:"switch_candidate_protocol",kind:"enum",T:e9.getEnumType(tX),oneof:"scenario"},{no:6,name:"subscriber_bandwidth",kind:"scalar",T:3,oneof:"scenario"},{no:7,name:"disconnect_signal_on_resume",kind:"scalar",T:8,oneof:"scenario"},{no:8,name:"disconnect_signal_on_resume_no_messages",kind:"scalar",T:8,oneof:"scenario"},{no:9,name:"leave_request_full_reconnect",kind:"scalar",T:8,oneof:"scenario"}]),ix=e9.makeMessageType("livekit.Ping",()=>[{no:1,name:"timestamp",kind:"scalar",T:3},{no:2,name:"rtt",kind:"scalar",T:3}]),iO=e9.makeMessageType("livekit.Pong",()=>[{no:1,name:"last_ping_timestamp",kind:"scalar",T:3},{no:2,name:"timestamp",kind:"scalar",T:3}]),iM=e9.makeMessageType("livekit.RegionSettings",()=>[{no:1,name:"regions",kind:"message",T:iD,repeated:!0}]),iD=e9.makeMessageType("livekit.RegionInfo",()=>[{no:1,name:"region",kind:"scalar",T:9},{no:2,name:"url",kind:"scalar",T:9},{no:3,name:"distance",kind:"scalar",T:3}]),iA=e9.makeMessageType("livekit.SubscriptionResponse",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"err",kind:"enum",T:e9.getEnumType(tl)}]),iN=e9.makeMessageType("livekit.RequestResponse",()=>[{no:1,name:"request_id",kind:"scalar",T:13},{no:2,name:"reason",kind:"enum",T:e9.getEnumType(i_)},{no:3,name:"message",kind:"scalar",T:9}]),i_=e9.makeEnum("livekit.RequestResponse.Reason",[{no:0,name:"OK"},{no:1,name:"NOT_FOUND"},{no:2,name:"NOT_ALLOWED"},{no:3,name:"LIMIT_EXCEEDED"}]),iL=e9.makeMessageType("livekit.TrackSubscribed",()=>[{no:1,name:"track_sid",kind:"scalar",T:9}]);var iU={exports:{}},ij=iU.exports,iF=function(){var e;return d?iU.exports:(d=1,e=function(){var e=function(){},t="undefined",i=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),n=["trace","debug","info","warn","error"],r={},a=null;function s(e,t){var i=e[t];if("function"==typeof i.bind)return i.bind(e);try{return Function.prototype.bind.call(i,e)}catch(t){return function(){return Function.prototype.apply.apply(i,[e,arguments])}}}function o(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function c(){for(var i=this.getLevel(),r=0;r<n.length;r++){var a=n[r];this[a]=r<i?e:this.methodFactory(a,i,this.name)}if(this.log=this.debug,typeof console===t&&i<this.levels.SILENT)return"No console available for logging"}function l(e){return function(){typeof console!==t&&(c.call(this),this[e].apply(this,arguments))}}function d(n,r,a){var c;return"debug"===(c=n)&&(c="log"),typeof console!==t&&("trace"===c&&i?o:void 0!==console[c]?s(console,c):void 0!==console.log?s(console,"log"):e)||l.apply(this,arguments)}function u(e,i){var s,o,l,u=this,h="loglevel";function p(){var e;if(typeof window!==t&&h){try{e=window.localStorage[h]}catch(e){}if(typeof e===t)try{var i=window.document.cookie,n=encodeURIComponent(h),r=i.indexOf(n+"=");-1!==r&&(e=/^([^;]+)/.exec(i.slice(r+n.length+1))[1])}catch(e){}return void 0===u.levels[e]&&(e=void 0),e}}function m(e){var t=e;if("string"==typeof t&&void 0!==u.levels[t.toUpperCase()]&&(t=u.levels[t.toUpperCase()]),"number"==typeof t&&t>=0&&t<=u.levels.SILENT)return t;throw TypeError("log.setLevel() called with invalid level: "+e)}"string"==typeof e?h+=":"+e:"symbol"==typeof e&&(h=void 0),u.name=e,u.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},u.methodFactory=i||d,u.getLevel=function(){return null!=l?l:null!=o?o:s},u.setLevel=function(e,i){return l=m(e),!1!==i&&function(e){var i=(n[e]||"silent").toUpperCase();if(typeof window!==t&&h){try{window.localStorage[h]=i;return}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"="+i+";"}catch(e){}}}(l),c.call(u)},u.setDefaultLevel=function(e){o=m(e),p()||u.setLevel(e,!1)},u.resetLevel=function(){if(l=null,typeof window!==t&&h){try{window.localStorage.removeItem(h)}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}c.call(u)},u.enableAll=function(e){u.setLevel(u.levels.TRACE,e)},u.disableAll=function(e){u.setLevel(u.levels.SILENT,e)},u.rebuild=function(){if(a!==u&&(s=m(a.getLevel())),c.call(u),a===u)for(var e in r)r[e].rebuild()},s=m(a?a.getLevel():"WARN");var f=p();null!=f&&(l=m(f)),c.call(u)}(a=new u).getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw TypeError("You must supply a name when creating a logger.");var t=r[e];return t||(t=r[e]=new u(e,a.methodFactory)),t};var h=typeof window!==t?window.log:void 0;return a.noConflict=function(){return typeof window!==t&&window.log===a&&(window.log=h),a},a.getLoggers=function(){return r},a.default=a,a},iU.exports?iU.exports=e():ij.log=e(),iU.exports)}();!function(e){e[e.trace=0]="trace",e[e.debug=1]="debug",e[e.info=2]="info",e[e.warn=3]="warn",e[e.error=4]="error",e[e.silent=5]="silent"}(u||(u={})),function(e){e.Default="livekit",e.Room="livekit-room",e.Participant="livekit-participant",e.Track="livekit-track",e.Publication="livekit-track-publication",e.Engine="livekit-engine",e.Signal="livekit-signal",e.PCManager="livekit-pc-manager",e.PCTransport="livekit-pc-transport",e.E2EE="lk-e2ee"}(h||(h={}));let iB=iF.getLogger("livekit");function iV(e){let t=iF.getLogger(e);return t.setDefaultLevel(iB.getLevel()),t}Object.values(h).map(e=>iF.getLogger(e)),iB.setDefaultLevel(u.info);let iq=iF.getLogger("lk-e2ee"),iH=[0,300,1200,2700,4800,7e3,7e3,7e3,7e3,7e3];class iW{constructor(e){this._retryDelays=void 0!==e?[...e]:iH}nextRetryDelayInMs(e){if(e.retryCount>=this._retryDelays.length)return null;let t=this._retryDelays[e.retryCount];return e.retryCount<=1?t:t+1e3*Math.random()}}function iz(e,t,i,n){return new(i||(i=Promise))(function(r,a){function s(e){try{c(n.next(e))}catch(e){a(e)}}function o(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?r(e.value):((t=e.value)instanceof i?t:new i(function(e){e(t)})).then(s,o)}c((n=n.apply(e,t||[])).next())})}function iG(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,i=e[Symbol.asyncIterator];return i?i.call(e):(e=function(e){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&e[t],n=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(i){t[i]=e[i]&&function(t){return new Promise(function(n,r){var a,s,o;a=n,s=r,o=(t=e[i](t)).done,Promise.resolve(t.value).then(function(e){a({value:e,done:o})},s)})}}}"function"==typeof SuppressedError&&SuppressedError;var iK={exports:{}},iJ=function(){if(p)return iK.exports;p=1;var e,t="object"==typeof Reflect?Reflect:null,i=t&&"function"==typeof t.apply?t.apply:function(e,t,i){return Function.prototype.apply.call(e,t,i)};e=t&&"function"==typeof t.ownKeys?t.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var n=Number.isNaN||function(e){return e!=e};function r(){r.init.call(this)}iK.exports=r,iK.exports.once=function(e,t){return new Promise(function(i,n){var r,a,s;function o(i){e.removeListener(t,c),n(i)}function c(){"function"==typeof e.removeListener&&e.removeListener("error",o),i([].slice.call(arguments))}f(e,t,c,{once:!0}),"error"!==t&&(r=e,a=o,s={once:!0},"function"==typeof r.on&&f(r,"error",a,s))})},r.EventEmitter=r,r.prototype._events=void 0,r.prototype._eventsCount=0,r.prototype._maxListeners=void 0;var a=10;function s(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function o(e){return void 0===e._maxListeners?r.defaultMaxListeners:e._maxListeners}function c(e,t,i,n){if(s(i),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,i.listener?i.listener:i),a=e._events),c=a[t]),void 0===c)c=a[t]=i,++e._eventsCount;else if("function"==typeof c?c=a[t]=n?[i,c]:[c,i]:n?c.unshift(i):c.push(i),(r=o(e))>0&&c.length>r&&!c.warned){c.warned=!0;var r,a,c,l=Error("Possible EventEmitter memory leak detected. "+c.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=c.length,console&&console.warn&&console.warn(l)}return e}function l(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,i){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:i},r=l.bind(n);return r.listener=i,n.wrapFn=r,r}function u(e,t,i){var n=e._events;if(void 0===n)return[];var r=n[t];return void 0===r?[]:"function"==typeof r?i?[r.listener||r]:[r]:i?function(e){for(var t=Array(e.length),i=0;i<t.length;++i)t[i]=e[i].listener||e[i];return t}(r):m(r,r.length)}function h(e){var t=this._events;if(void 0!==t){var i=t[e];if("function"==typeof i)return 1;if(void 0!==i)return i.length}return 0}function m(e,t){for(var i=Array(t),n=0;n<t;++n)i[n]=e[n];return i}function f(e,t,i,n){if("function"==typeof e.on)n.once?e.once(t,i):e.on(t,i);else if("function"==typeof e.addEventListener)e.addEventListener(t,function r(a){n.once&&e.removeEventListener(t,r),i(a)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}return Object.defineProperty(r,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||n(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),r.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},r.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||n(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},r.prototype.getMaxListeners=function(){return o(this)},r.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,a=this._events;if(void 0!==a)r=r&&void 0===a.error;else if(!r)return!1;if(r){if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var s,o=Error("Unhandled error."+(s?" ("+s.message+")":""));throw o.context=s,o}var c=a[e];if(void 0===c)return!1;if("function"==typeof c)i(c,this,t);else for(var l=c.length,d=m(c,l),n=0;n<l;++n)i(d[n],this,t);return!0},r.prototype.addListener=function(e,t){return c(this,e,t,!1)},r.prototype.on=r.prototype.addListener,r.prototype.prependListener=function(e,t){return c(this,e,t,!0)},r.prototype.once=function(e,t){return s(t),this.on(e,d(this,e,t)),this},r.prototype.prependOnceListener=function(e,t){return s(t),this.prependListener(e,d(this,e,t)),this},r.prototype.removeListener=function(e,t){var i,n,r,a,o;if(s(t),void 0===(n=this._events)||void 0===(i=n[e]))return this;if(i===t||i.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,i.listener||t));else if("function"!=typeof i){for(r=-1,a=i.length-1;a>=0;a--)if(i[a]===t||i[a].listener===t){o=i[a].listener,r=a;break}if(r<0)return this;0===r?i.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(i,r),1===i.length&&(n[e]=i[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},r.prototype.off=r.prototype.removeListener,r.prototype.removeAllListeners=function(e){var t,i,n;if(void 0===(i=this._events))return this;if(void 0===i.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==i[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete i[e]),this;if(0==arguments.length){var r,a=Object.keys(i);for(n=0;n<a.length;++n)"removeListener"!==(r=a[n])&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=i[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},r.prototype.listeners=function(e){return u(this,e,!0)},r.prototype.rawListeners=function(e){return u(this,e,!1)},r.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},r.prototype.listenerCount=h,r.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]},iK.exports}();let iY=!0,iQ=!0;function i$(e,t,i){let n=e.match(t);return n&&n.length>=i&&parseFloat(n[i],10)}function iX(e,t,i){if(!e.RTCPeerConnection)return;let n=e.RTCPeerConnection.prototype,r=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return r.apply(this,arguments);let a=e=>{let t=i(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,a),r.apply(this,[e,a])};let a=n.removeEventListener;n.removeEventListener=function(e,i){if(e!==t||!this._eventMap||!this._eventMap[t]||!this._eventMap[t].has(i))return a.apply(this,arguments);let n=this._eventMap[t].get(i);return this._eventMap[t].delete(i),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,a.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function iZ(e){return"boolean"!=typeof e?Error("Argument type: "+typeof e+". Please use a boolean."):(iY=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function i0(e){return"boolean"!=typeof e?Error("Argument type: "+typeof e+". Please use a boolean."):(iQ=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function i1(){"object"==typeof window&&(iY||"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments))}function i5(e,t){iQ&&console.warn(e+" is deprecated, please use "+t+" instead.")}function i2(e){return"[object Object]"===Object.prototype.toString.call(e)}function i3(e,t,i){let n=i?"outbound-rtp":"inbound-rtp",r=new Map;if(null===t)return r;let a=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&a.push(e)}),a.forEach(t=>{e.forEach(i=>{i.type===n&&i.trackId===t.id&&function e(t,i,n){!i||n.has(i.id)||(n.set(i.id,i),Object.keys(i).forEach(r=>{r.endsWith("Id")?e(t,t.get(i[r]),n):r.endsWith("Ids")&&i[r].forEach(i=>{e(t,t.get(i),n)})}))}(e,i,r)})}),r}function i9(e,t){let i=e&&e.navigator;if(!i.mediaDevices)return;let n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;let t={};return Object.keys(e).forEach(i=>{if("require"===i||"advanced"===i||"mediaSource"===i)return;let n="object"==typeof e[i]?e[i]:{ideal:e[i]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);let r=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let e={};"number"==typeof n.ideal?(e[r("min",i)]=n.ideal,t.optional.push(e),(e={})[r("max",i)]=n.ideal):e[r("",i)]=n.ideal,t.optional.push(e)}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[r("",i)]=n.exact):["min","max"].forEach(e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[r(e,i)]=n[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},r=function(e,r){if(t.version>=61)return r(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){let t=function(e,t,i){t in e&&!(i in e)&&(e[i]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let a=e.video.facingMode;a=a&&("object"==typeof a?a:{ideal:a});let s=t.version<66;if(a&&("user"===a.exact||"environment"===a.exact||"user"===a.ideal||"environment"===a.ideal)&&!(i.mediaDevices.getSupportedConstraints&&i.mediaDevices.getSupportedConstraints().facingMode&&!s)){let t;if(delete e.video.facingMode,"environment"===a.exact||"environment"===a.ideal?t=["back","rear"]:("user"===a.exact||"user"===a.ideal)&&(t=["front"]),t)return i.mediaDevices.enumerateDevices().then(i=>{let s=(i=i.filter(e=>"videoinput"===e.kind)).find(e=>t.some(t=>e.label.toLowerCase().includes(t)));return!s&&i.length&&t.includes("back")&&(s=i[i.length-1]),s&&(e.video.deviceId=a.exact?{exact:s.deviceId}:{ideal:s.deviceId}),e.video=n(e.video),i1("chrome: "+JSON.stringify(e)),r(e)})}e.video=n(e.video)}return i1("chrome: "+JSON.stringify(e)),r(e)},a=function(e){return t.version>=64?e:{name:({PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"})[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(i.getUserMedia=(function(e,t,n){r(e,e=>{i.webkitGetUserMedia(e,t,e=>{n&&n(a(e))})})}).bind(i),i.mediaDevices.getUserMedia){let e=i.mediaDevices.getUserMedia.bind(i.mediaDevices);i.mediaDevices.getUserMedia=function(t){return r(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(a(e))))}}}function i6(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function i4(e){if("object"!=typeof e||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)iX(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});let t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",i=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===i.track.id):{track:i.track};let r=new Event("track");r.track=i.track,r.receiver=n,r.transceiver={receiver:n},r.streams=[t.stream],this.dispatchEvent(r)}),t.stream.getTracks().forEach(i=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===i.id):{track:i};let r=new Event("track");r.track=i,r.receiver=n,r.transceiver={receiver:n},r.streams=[t.stream],this.dispatchEvent(r)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}}function i7(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){let t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};let i=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){let r=i.apply(this,arguments);return r||(r=t(this,e),this._senders.push(r)),r};let n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);let t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}let i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],i.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};let n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{let t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){let t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function i8(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){let t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});let i=e.RTCPeerConnection.prototype.addTrack;i&&(e.RTCPeerConnection.prototype.addTrack=function(){let e=i.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){let e=this;return this._pc.getStats().then(t=>i3(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){let t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),iX(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){let e=this;return this._pc.getStats().then(t=>i3(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;let t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){let e,t,i,n=arguments[0];return(this.getSenders().forEach(t=>{t.track===n&&(e?i=!0:e=t)}),this.getReceivers().forEach(e=>(e.track===n&&(t?i=!0:t=e),e.track===n)),i||e&&t)?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):e?e.getStats():t?t.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function ne(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};let t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,i){if(!i)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};let n=t.apply(this,arguments);return this._shimmedLocalStreams[i.id]?-1===this._shimmedLocalStreams[i.id].indexOf(n)&&this._shimmedLocalStreams[i.id].push(n):this._shimmedLocalStreams[i.id]=[i,n],n};let i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});let t=this.getSenders();i.apply(this,arguments);let n=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(n)};let n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],n.apply(this,arguments)};let r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{let i=this._shimmedLocalStreams[t].indexOf(e);-1!==i&&this._shimmedLocalStreams[t].splice(i,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),r.apply(this,arguments)}}function nt(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return ne(e);let i=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){let e=i.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};let n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){let i=new e.MediaStream(t.getTracks());this._streams[t.id]=i,this._reverseStreams[i.id]=t,t=i}n.apply(this,[t])};let r=e.RTCPeerConnection.prototype.removeStream;function a(e,t){let i=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{let n=e._reverseStreams[t],r=e._streams[n.id];i=i.replace(RegExp(r.id,"g"),n.id)}),new RTCSessionDescription({type:t.type,sdp:i})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},r.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,i){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");let n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(e=>e.track===t))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};let r=this._streams[i.id];if(r)r.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{let n=new e.MediaStream([t]);this._streams[i.id]=n,this._reverseStreams[n.id]=i,this.addStream(n)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach(function(t){let i=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){let e=arguments,t=arguments.length&&"function"==typeof arguments[0];return t?i.apply(this,[t=>{let i=a(this,t);e[0].apply(null,[i])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):i.apply(this,arguments).then(e=>a(this,e))}})[t]});let s=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){var e,t;let i;return arguments.length&&arguments[0].type?(arguments[0]=(e=this,t=arguments[0],i=t.sdp,Object.keys(e._reverseStreams||[]).forEach(t=>{let n=e._reverseStreams[t],r=e._streams[n.id];i=i.replace(RegExp(n.id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:i})),s.apply(this,arguments)):s.apply(this,arguments)};let o=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){let e=o.get.apply(this);return""===e.type?e:a(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){let t;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{},Object.keys(this._streams).forEach(i=>{this._streams[i].getTracks().find(t=>e.track===t)&&(t=this._streams[i])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function ni(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let i=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}})[t]})}function nn(e,t){iX(e,"negotiationneeded",e=>{let i=e.target;if(!(t.version<72)&&(!i.getConfiguration||"plan-b"!==i.getConfiguration().sdpSemantics)||"stable"===i.signalingState)return e})}var nr=Object.freeze({__proto__:null,fixNegotiationNeeded:nn,shimAddTrackRemoveTrack:nt,shimAddTrackRemoveTrackWithNative:ne,shimGetSendersWithDtmf:i7,shimGetUserMedia:i9,shimMediaStream:i6,shimOnTrack:i4,shimPeerConnection:ni,shimSenderReceiverGetStats:i8});function na(e,t){let i=e&&e.navigator,n=e&&e.MediaStreamTrack;if(i.getUserMedia=function(e,t,n){i5("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),i.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in i.mediaDevices.getSupportedConstraints())){let e=function(e,t,i){t in e&&!(i in e)&&(e[i]=e[t],delete e[t])},t=i.mediaDevices.getUserMedia.bind(i.mediaDevices);if(i.mediaDevices.getUserMedia=function(i){return"object"==typeof i&&"object"==typeof i.audio&&(e((i=JSON.parse(JSON.stringify(i))).audio,"autoGainControl","mozAutoGainControl"),e(i.audio,"noiseSuppression","mozNoiseSuppression")),t(i)},n&&n.prototype.getSettings){let t=n.prototype.getSettings;n.prototype.getSettings=function(){let i=t.apply(this,arguments);return e(i,"mozAutoGainControl","autoGainControl"),e(i,"mozNoiseSuppression","noiseSuppression"),i}}if(n&&n.prototype.applyConstraints){let t=n.prototype.applyConstraints;n.prototype.applyConstraints=function(i){return"audio"===this.kind&&"object"==typeof i&&(e(i=JSON.parse(JSON.stringify(i)),"autoGainControl","mozAutoGainControl"),e(i,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[i])}}}}function ns(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function no(e,t){if("object"!=typeof e||!(e.RTCPeerConnection||e.mozRTCPeerConnection))return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let i=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}})[t]});let i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){let[e,r,a]=arguments;return n.apply(this,[e||null]).then(e=>{if(t.version<53&&!r)try{e.forEach(e=>{e.type=i[e.type]||e.type})}catch(t){if("TypeError"!==t.name)throw t;e.forEach((t,n)=>{e.set(n,Object.assign({},t,{type:i[t.type]||t.type}))})}return e}).then(r,a)}}function nc(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;let t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});let i=e.RTCPeerConnection.prototype.addTrack;i&&(e.RTCPeerConnection.prototype.addTrack=function(){let e=i.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function nl(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;let t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),iX(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function nd(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){i5("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function nu(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function nh(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]);let i=(e=[...e]).length>0;i&&e.forEach(e=>{if("rid"in e&&!/^[a-z0-9]{0,16}$/i.test(e.rid))throw TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw RangeError("max_framerate must be >= 0.0")});let n=t.apply(this,arguments);if(i){let{sender:t}=n,i=t.getParameters();"encodings"in i&&(1!==i.encodings.length||0!==Object.keys(i.encodings[0]).length)||(i.encodings=e,t.sendEncodings=e,this.setParametersPromises.push(t.setParameters(i).then(()=>{delete t.sendEncodings}).catch(()=>{delete t.sendEncodings})))}return n})}function np(e){if(!("object"==typeof e&&e.RTCRtpSender))return;let t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){let e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function nm(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function nf(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}var ng=Object.freeze({__proto__:null,shimAddTransceiver:nh,shimCreateAnswer:nf,shimCreateOffer:nm,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(i){if(!(i&&i.video)){let e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===i.video?i.video={mediaSource:t}:i.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(i)})},shimGetParameters:np,shimGetUserMedia:na,shimOnTrack:ns,shimPeerConnection:no,shimRTCDataChannel:nu,shimReceiverGetStats:nl,shimRemoveStream:nd,shimSenderGetStats:nc});function nv(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){let t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(i=>t.call(this,i,e)),e.getVideoTracks().forEach(i=>t.call(this,i,e))},e.RTCPeerConnection.prototype.addTrack=function(e){for(var i=arguments.length,n=Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];return n&&n.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);let t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);let i=e.getTracks();this.getSenders().forEach(e=>{i.includes(e.track)&&this.removeTrack(e)})})}}function nb(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);let t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});let t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){let e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);let i=new Event("addstream");i.stream=t,e.dispatchEvent(i)})}),t.apply(e,arguments)}}}function ny(e){if("object"!=typeof e||!e.RTCPeerConnection)return;let t=e.RTCPeerConnection.prototype,i=t.createOffer,n=t.createAnswer,r=t.setLocalDescription,a=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(e,t){let n=arguments.length>=2?arguments[2]:arguments[0],r=i.apply(this,[n]);return t?(r.then(e,t),Promise.resolve()):r},t.createAnswer=function(e,t){let i=arguments.length>=2?arguments[2]:arguments[0],r=n.apply(this,[i]);return t?(r.then(e,t),Promise.resolve()):r};let o=function(e,t,i){let n=r.apply(this,[e]);return i?(n.then(t,i),Promise.resolve()):n};t.setLocalDescription=o,t.setRemoteDescription=o=function(e,t,i){let n=a.apply(this,[e]);return i?(n.then(t,i),Promise.resolve()):n},t.addIceCandidate=function(e,t,i){let n=s.apply(this,[e]);return i?(n.then(t,i),Promise.resolve()):n}}function nk(e){let t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){let e=t.mediaDevices,i=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>i(nT(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=(function(e,i,n){t.mediaDevices.getUserMedia(e).then(i,n)}).bind(t))}function nT(e){return e&&void 0!==e.video?Object.assign({},e,{video:function e(t){return i2(t)?Object.keys(t).reduce(function(i,n){let r=i2(t[n]),a=r?e(t[n]):t[n],s=r&&!Object.keys(a).length;return void 0===a||s?i:Object.assign(i,{[n]:a})},{}):t}(e.video)}):e}function nC(e){if(!e.RTCPeerConnection)return;let t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,i){if(e&&e.iceServers){let t=[];for(let i=0;i<e.iceServers.length;i++){let n=e.iceServers[i];void 0===n.urls&&n.url?(i5("RTCIceServer.url","RTCIceServer.urls"),(n=JSON.parse(JSON.stringify(n))).urls=n.url,delete n.url,t.push(n)):t.push(e.iceServers[i])}e.iceServers=t}return new t(e,i)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function nS(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function nw(e){let t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);let t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);let i=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&i?"sendrecv"===i.direction?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":"recvonly"===i.direction&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):!0!==e.offerToReceiveVideo||i||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function nE(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var nP=Object.freeze({__proto__:null,shimAudioContext:nE,shimCallbacksAPI:ny,shimConstraints:nT,shimCreateOfferLegacy:nw,shimGetUserMedia:nk,shimLocalStreamsAPI:nv,shimRTCIceServerUrls:nC,shimRemoteStreamsAPI:nb,shimTrackEventTransceiver:nS}),nR={exports:{}},nI=function(){if(m)return nR.exports;m=1;let e={};return e.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},e.localCName=e.generateIdentifier(),e.splitLines=function(e){return e.trim().split("\n").map(e=>e.trim())},e.splitSections=function(e){return e.split("\nm=").map((e,t)=>(t>0?"m="+e:e).trim()+"\r\n")},e.getDescription=function(t){let i=e.splitSections(t);return i&&i[0]},e.getMediaSections=function(t){let i=e.splitSections(t);return i.shift(),i},e.matchPrefix=function(t,i){return e.splitLines(t).filter(e=>0===e.indexOf(i))},e.parseCandidate=function(e){let t,i={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":i.relatedAddress=t[e+1];break;case"rport":i.relatedPort=parseInt(t[e+1],10);break;case"tcptype":i.tcpType=t[e+1];break;case"ufrag":i.ufrag=t[e+1],i.usernameFragment=t[e+1];break;default:void 0===i[t[e]]&&(i[t[e]]=t[e+1])}return i},e.writeCandidate=function(e){let t=[];t.push(e.foundation);let i=e.component;"rtp"===i?t.push(1):"rtcp"===i?t.push(2):t.push(i),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);let n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},e.parseIceOptions=function(e){return e.substring(14).split(" ")},e.parseRtpMap=function(e){let t=e.substring(9).split(" "),i={payloadType:parseInt(t.shift(),10)};return i.name=(t=t[0].split("/"))[0],i.clockRate=parseInt(t[1],10),i.channels=3===t.length?parseInt(t[2],10):1,i.numChannels=i.channels,i},e.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);let i=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==i?"/"+i:"")+"\r\n"},e.parseExtmap=function(e){let t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},e.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},e.parseFmtp=function(e){let t,i={},n=e.substring(e.indexOf(" ")+1).split(";");for(let e=0;e<n.length;e++)i[(t=n[e].trim().split("="))[0].trim()]=t[1];return i},e.writeFmtp=function(e){let t="",i=e.payloadType;if(void 0!==e.preferredPayloadType&&(i=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){let n=[];Object.keys(e.parameters).forEach(t=>{void 0!==e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)}),t+="a=fmtp:"+i+" "+n.join(";")+"\r\n"}return t},e.parseRtcpFb=function(e){let t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},e.writeRtcpFb=function(e){let t="",i=e.payloadType;return void 0!==e.preferredPayloadType&&(i=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(e=>{t+="a=rtcp-fb:"+i+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},e.parseSsrcMedia=function(e){let t=e.indexOf(" "),i={ssrc:parseInt(e.substring(7,t),10)},n=e.indexOf(":",t);return n>-1?(i.attribute=e.substring(t+1,n),i.value=e.substring(n+1)):i.attribute=e.substring(t+1),i},e.parseSsrcGroup=function(e){let t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(e=>parseInt(e,10))}},e.getMid=function(t){let i=e.matchPrefix(t,"a=mid:")[0];if(i)return i.substring(6)},e.parseFingerprint=function(e){let t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},e.getDtlsParameters=function(t,i){return{role:"auto",fingerprints:e.matchPrefix(t+i,"a=fingerprint:").map(e.parseFingerprint)}},e.writeDtlsParameters=function(e,t){let i="a=setup:"+t+"\r\n";return e.fingerprints.forEach(e=>{i+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),i},e.parseCryptoLine=function(e){let t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},e.writeCryptoLine=function(t){return"a=crypto:"+t.tag+" "+t.cryptoSuite+" "+("object"==typeof t.keyParams?e.writeCryptoKeyParams(t.keyParams):t.keyParams)+(t.sessionParams?" "+t.sessionParams.join(" "):"")+"\r\n"},e.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;let t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},e.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},e.getCryptoParameters=function(t,i){return e.matchPrefix(t+i,"a=crypto:").map(e.parseCryptoLine)},e.getIceParameters=function(t,i){let n=e.matchPrefix(t+i,"a=ice-ufrag:")[0],r=e.matchPrefix(t+i,"a=ice-pwd:")[0];return n&&r?{usernameFragment:n.substring(12),password:r.substring(10)}:null},e.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},e.parseRtpParameters=function(t){let i={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=e.splitLines(t)[0].split(" ");i.profile=n[2];for(let r=3;r<n.length;r++){let a=n[r],s=e.matchPrefix(t,"a=rtpmap:"+a+" ")[0];if(s){let n=e.parseRtpMap(s),r=e.matchPrefix(t,"a=fmtp:"+a+" ");switch(n.parameters=r.length?e.parseFmtp(r[0]):{},n.rtcpFeedback=e.matchPrefix(t,"a=rtcp-fb:"+a+" ").map(e.parseRtcpFb),i.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":i.fecMechanisms.push(n.name.toUpperCase())}}}e.matchPrefix(t,"a=extmap:").forEach(t=>{i.headerExtensions.push(e.parseExtmap(t))});let r=e.matchPrefix(t,"a=rtcp-fb:* ").map(e.parseRtcpFb);return i.codecs.forEach(e=>{r.forEach(t=>{e.rtcpFeedback.find(e=>e.type===t.type&&e.parameter===t.parameter)||e.rtcpFeedback.push(t)})}),i},e.writeRtpDescription=function(t,i){let n="";n+="m="+t+" ",n+=i.codecs.length>0?"9":"0",n+=" "+(i.profile||"UDP/TLS/RTP/SAVPF")+" ",n+=i.codecs.map(e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",i.codecs.forEach(t=>{n+=e.writeRtpMap(t),n+=e.writeFmtp(t),n+=e.writeRtcpFb(t)});let r=0;return i.codecs.forEach(e=>{e.maxptime>r&&(r=e.maxptime)}),r>0&&(n+="a=maxptime:"+r+"\r\n"),i.headerExtensions&&i.headerExtensions.forEach(t=>{n+=e.writeExtmap(t)}),n},e.parseRtpEncodingParameters=function(t){let i,n=[],r=e.parseRtpParameters(t),a=-1!==r.fecMechanisms.indexOf("RED"),s=-1!==r.fecMechanisms.indexOf("ULPFEC"),o=e.matchPrefix(t,"a=ssrc:").map(t=>e.parseSsrcMedia(t)).filter(e=>"cname"===e.attribute),c=o.length>0&&o[0].ssrc,l=e.matchPrefix(t,"a=ssrc-group:FID").map(e=>e.substring(17).split(" ").map(e=>parseInt(e,10)));l.length>0&&l[0].length>1&&l[0][0]===c&&(i=l[0][1]),r.codecs.forEach(e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&i&&(t.rtx={ssrc:i}),n.push(t),a&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:s?"red+ulpfec":"red"},n.push(t))}}),0===n.length&&c&&n.push({ssrc:c});let d=e.matchPrefix(t,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substring(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substring(5),10)*.95-16e3:void 0,n.forEach(e=>{e.maxBitrate=d})),n},e.parseRtcpParameters=function(t){let i={},n=e.matchPrefix(t,"a=ssrc:").map(t=>e.parseSsrcMedia(t)).filter(e=>"cname"===e.attribute)[0];n&&(i.cname=n.value,i.ssrc=n.ssrc);let r=e.matchPrefix(t,"a=rtcp-rsize");return i.reducedSize=r.length>0,i.compound=0===r.length,i.mux=e.matchPrefix(t,"a=rtcp-mux").length>0,i},e.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},e.parseMsid=function(t){let i,n=e.matchPrefix(t,"a=msid:");if(1===n.length)return{stream:(i=n[0].substring(7).split(" "))[0],track:i[1]};let r=e.matchPrefix(t,"a=ssrc:").map(t=>e.parseSsrcMedia(t)).filter(e=>"msid"===e.attribute);if(r.length>0)return{stream:(i=r[0].value.split(" "))[0],track:i[1]}},e.parseSctpDescription=function(t){let i,n=e.parseMLine(t),r=e.matchPrefix(t,"a=max-message-size:");r.length>0&&(i=parseInt(r[0].substring(19),10)),isNaN(i)&&(i=65536);let a=e.matchPrefix(t,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substring(12),10),protocol:n.fmt,maxMessageSize:i};let s=e.matchPrefix(t,"a=sctpmap:");if(s.length>0){let e=s[0].substring(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:i}}},e.writeSctpDescription=function(e,t){let i=[];return i="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&i.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),i.join("")},e.generateSessionId=function(){return Math.random().toString().substr(2,22)},e.writeSessionBoilerplate=function(t,i,n){return"v=0\r\no="+(n||"thisisadapterortc")+" "+(t||e.generateSessionId())+" "+(void 0!==i?i:2)+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},e.getDirection=function(t,i){let n=e.splitLines(t);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substring(2)}return i?e.getDirection(i):"sendrecv"},e.getKind=function(t){return e.splitLines(t)[0].split(" ")[0].substring(2)},e.isRejected=function(e){return"0"===e.split(" ",2)[1]},e.parseMLine=function(t){let i=e.splitLines(t)[0].substring(2).split(" ");return{kind:i[0],port:parseInt(i[1],10),protocol:i[2],fmt:i.slice(3).join(" ")}},e.parseOLine=function(t){let i=e.matchPrefix(t,"o=")[0].substring(2).split(" ");return{username:i[0],sessionId:i[1],sessionVersion:parseInt(i[2],10),netType:i[3],addressType:i[4],address:i[5]}},e.isValidSDP=function(t){if("string"!=typeof t||0===t.length)return!1;let i=e.splitLines(t);for(let e=0;e<i.length;e++)if(i[e].length<2||"="!==i[e].charAt(1))return!1;return!0},nR.exports=e,nR.exports}(),nx=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(nI),nO=function(e,t){return t.forEach(function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach(function(i){if("default"!==i&&!(i in e)){var n=Object.getOwnPropertyDescriptor(t,i);Object.defineProperty(e,i,n.get?n:{enumerable:!0,get:function(){return t[i]}})}})}),Object.freeze(e)}({__proto__:null,default:nx},[nI]);function nM(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;let t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){let i=new t(e),n=nx.parseCandidate(e.candidate);for(let e in n)e in i||Object.defineProperty(i,e,{value:n[e]});return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,iX(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function nD(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||iX(e,"icecandidate",e=>{if(e.candidate){let t=nx.parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol=({0:"tls",1:"tcp",2:"udp"})[t.priority>>24])}return e})}function nA(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});let i=function(e){if(!e||!e.sdp)return!1;let t=nx.splitSections(e.sdp);return t.shift(),t.some(e=>{let t=nx.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},n=function(e){let t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return -1;let i=parseInt(t[1],10);return i!=i?-1:i},r=function(e){let i=65536;return"firefox"===t.browser&&(i=t.version<57?-1===e?16384:0x7ffffff5:t.version<60?57===t.version?65535:65536:0x7ffffff5),i},a=function(e,i){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);let r=nx.matchPrefix(e.sdp,"a=max-message-size:");return r.length>0?n=parseInt(r[0].substring(19),10):"firefox"===t.browser&&-1!==i&&(n=0x7ffffff5),n},s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){let{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(i(arguments[0])){let e,t=n(arguments[0]),i=r(t),s=a(arguments[0],t);e=0===i&&0===s?Number.POSITIVE_INFINITY:0===i||0===s?Math.max(i,s):Math.min(i,s);let o={};Object.defineProperty(o,"maxMessageSize",{get:()=>e}),this._sctp=o}return s.apply(this,arguments)}}function nN(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){let i=e.send;e.send=function(){let n=arguments[0],r=n.length||n.size||n.byteLength;if("open"===e.readyState&&t.sctp&&r>t.sctp.maxMessageSize)throw TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return i.apply(e,arguments)}}let i=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){let e=i.apply(this,arguments);return t(e,this),e},iX(e,"datachannel",e=>(t(e.channel,e.target),e))}function n_(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;let t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return({completed:"connected",checking:"connecting"})[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{let i=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{let t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;let i=new Event("connectionstatechange",e);t.dispatchEvent(i)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),i.apply(this,arguments)}})}function nL(e,t){if(!e.RTCPeerConnection||"chrome"===t.browser&&t.version>=71||"safari"===t.browser&&t._safariVersion>=13.1)return;let i=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){let i=t.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:i}):t.sdp=i}return i.apply(this,arguments)}}function nU(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;let i=e.RTCPeerConnection.prototype.addIceCandidate;i&&0!==i.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():i.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function nj(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;let i=e.RTCPeerConnection.prototype.setLocalDescription;i&&0!==i.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return i.apply(this,arguments);if(!(e={type:e.type,sdp:e.sdp}).type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}return e.sdp||"offer"!==e.type&&"answer"!==e.type?i.apply(this,[e]):("offer"===e.type?this.createOffer:this.createAnswer).apply(this).then(e=>i.apply(this,[e]))})}var nF=Object.freeze({__proto__:null,removeExtmapAllowMixed:nL,shimAddIceCandidateNullOrEmpty:nU,shimConnectionState:n_,shimMaxMessageSize:nA,shimParameterlessSetLocalDescription:nj,shimRTCIceCandidate:nM,shimRTCIceCandidateRelayProtocol:nD,shimSendThrowTypeError:nN});!function(){let{window:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0},i=function(e){let t={browser:null,version:null};if(void 0===e||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;let{navigator:i}=e;if(i.userAgentData&&i.userAgentData.brands){let e=i.userAgentData.brands.find(e=>"Chromium"===e.brand);if(e)return{browser:"chrome",version:parseInt(e.version,10)}}return i.mozGetUserMedia?(t.browser="firefox",t.version=parseInt(i$(i.userAgent,/Firefox\/(\d+)\./,1))):i.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection?(t.browser="chrome",t.version=parseInt(i$(i.userAgent,/Chrom(e|ium)\/(\d+)\./,2))):e.RTCPeerConnection&&i.userAgent.match(/AppleWebKit\/(\d+)\./)?(t.browser="safari",t.version=parseInt(i$(i.userAgent,/AppleWebKit\/(\d+)\./,1)),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype,t._safariVersion=i$(i.userAgent,/Version\/(\d+(\.?\d+))/,1)):t.browser="Not a supported browser.",t}(e),n={browserDetails:i,commonShim:nF,extractVersion:i$,disableLog:iZ,disableWarnings:i0,sdp:nO};switch(i.browser){case"chrome":if(!nr||!ni||!t.shimChrome){i1("Chrome shim is not included in this adapter release.");break}if(null===i.version){i1("Chrome shim can not determine version, not shimming.");break}i1("adapter.js shimming chrome."),n.browserShim=nr,nU(e,i),nj(e),i9(e,i),i6(e),ni(e,i),i4(e),nt(e,i),i7(e),i8(e),nn(e,i),nM(e),nD(e),n_(e),nA(e,i),nN(e),nL(e,i);break;case"firefox":if(!ng||!no||!t.shimFirefox){i1("Firefox shim is not included in this adapter release.");break}i1("adapter.js shimming firefox."),n.browserShim=ng,nU(e,i),nj(e),na(e,i),no(e,i),ns(e),nd(e),nc(e),nl(e),nu(e),nh(e),np(e),nm(e),nf(e),nM(e),n_(e),nA(e,i),nN(e);break;case"safari":if(!nP||!t.shimSafari){i1("Safari shim is not included in this adapter release.");break}i1("adapter.js shimming safari."),n.browserShim=nP,nU(e,i),nj(e),nC(e),nw(e),ny(e),nv(e),nb(e),nS(e),nk(e),nE(e),nM(e),nD(e),nA(e,i),nN(e),nL(e,i);break;default:i1("Unsupported browser!")}}({window:"undefined"==typeof window?void 0:window});let nB="lk_e2ee";function nV(){return void 0!==window.RTCRtpScriptTransform}!function(e){e.SetKey="setKey",e.RatchetRequest="ratchetRequest",e.KeyRatcheted="keyRatcheted"}(f||(f={})),(g||(g={})).KeyRatcheted="keyRatcheted",function(e){e.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",e.EncryptionError="encryptionError"}(v||(v={})),(b||(b={})).Error="cryptorError";iJ.EventEmitter;class nq extends Error{constructor(e,t){super(t||"an error has occured"),this.name="LiveKitError",this.code=e}}!function(e){e[e.NotAllowed=0]="NotAllowed",e[e.ServerUnreachable=1]="ServerUnreachable",e[e.InternalError=2]="InternalError",e[e.Cancelled=3]="Cancelled",e[e.LeaveRequest=4]="LeaveRequest",e[e.Timeout=5]="Timeout"}(y||(y={}));class nH extends nq{constructor(e,t,i,n){super(1,e),this.name="ConnectionError",this.status=i,this.reason=t,this.context=n,this.reasonName=y[t]}}class nW extends nq{constructor(e){super(21,null!=e?e:"device is unsupported"),this.name="DeviceUnsupportedError"}}class nz extends nq{constructor(e){super(20,null!=e?e:"track is invalid"),this.name="TrackInvalidError"}}class nG extends nq{constructor(e){super(10,null!=e?e:"unsupported server"),this.name="UnsupportedServer"}}class nK extends nq{constructor(e){super(12,null!=e?e:"unexpected connection state"),this.name="UnexpectedConnectionState"}}class nJ extends nq{constructor(e){super(13,null!=e?e:"unable to negotiate"),this.name="NegotiationError"}}class nY extends nq{constructor(e,t){super(15,e),this.name="PublishTrackError",this.status=t}}class nQ extends nq{constructor(e,t){super(15,e),this.reason=t,this.reasonName="string"==typeof t?t:i_[t]}}!function(e){e.PermissionDenied="PermissionDenied",e.NotFound="NotFound",e.DeviceInUse="DeviceInUse",e.Other="Other"}(k||(k={})),function(e){e.getFailure=function(t){if(t&&"name"in t)return"NotFoundError"===t.name||"DevicesNotFoundError"===t.name?e.NotFound:"NotAllowedError"===t.name||"PermissionDeniedError"===t.name?e.PermissionDenied:"NotReadableError"===t.name||"TrackStartError"===t.name?e.DeviceInUse:e.Other}}(k||(k={})),function(e){e[e.InvalidKey=0]="InvalidKey",e[e.MissingKey=1]="MissingKey",e[e.InternalError=2]="InternalError"}(T||(T={})),function(e){e.Connected="connected",e.Reconnecting="reconnecting",e.SignalReconnecting="signalReconnecting",e.Reconnected="reconnected",e.Disconnected="disconnected",e.ConnectionStateChanged="connectionStateChanged",e.Moved="moved",e.MediaDevicesChanged="mediaDevicesChanged",e.ParticipantConnected="participantConnected",e.ParticipantDisconnected="participantDisconnected",e.TrackPublished="trackPublished",e.TrackSubscribed="trackSubscribed",e.TrackSubscriptionFailed="trackSubscriptionFailed",e.TrackUnpublished="trackUnpublished",e.TrackUnsubscribed="trackUnsubscribed",e.TrackMuted="trackMuted",e.TrackUnmuted="trackUnmuted",e.LocalTrackPublished="localTrackPublished",e.LocalTrackUnpublished="localTrackUnpublished",e.LocalAudioSilenceDetected="localAudioSilenceDetected",e.ActiveSpeakersChanged="activeSpeakersChanged",e.ParticipantMetadataChanged="participantMetadataChanged",e.ParticipantNameChanged="participantNameChanged",e.ParticipantAttributesChanged="participantAttributesChanged",e.ParticipantActive="participantActive",e.RoomMetadataChanged="roomMetadataChanged",e.DataReceived="dataReceived",e.SipDTMFReceived="sipDTMFReceived",e.TranscriptionReceived="transcriptionReceived",e.ConnectionQualityChanged="connectionQualityChanged",e.TrackStreamStateChanged="trackStreamStateChanged",e.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",e.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",e.AudioPlaybackStatusChanged="audioPlaybackChanged",e.VideoPlaybackStatusChanged="videoPlaybackChanged",e.MediaDevicesError="mediaDevicesError",e.ParticipantPermissionsChanged="participantPermissionsChanged",e.SignalConnected="signalConnected",e.RecordingStatusChanged="recordingStatusChanged",e.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",e.EncryptionError="encryptionError",e.DCBufferStatusChanged="dcBufferStatusChanged",e.ActiveDeviceChanged="activeDeviceChanged",e.ChatMessage="chatMessage",e.LocalTrackSubscribed="localTrackSubscribed",e.MetricsReceived="metricsReceived"}(C||(C={})),function(e){e.TrackPublished="trackPublished",e.TrackSubscribed="trackSubscribed",e.TrackSubscriptionFailed="trackSubscriptionFailed",e.TrackUnpublished="trackUnpublished",e.TrackUnsubscribed="trackUnsubscribed",e.TrackMuted="trackMuted",e.TrackUnmuted="trackUnmuted",e.LocalTrackPublished="localTrackPublished",e.LocalTrackUnpublished="localTrackUnpublished",e.LocalTrackCpuConstrained="localTrackCpuConstrained",e.LocalSenderCreated="localSenderCreated",e.ParticipantMetadataChanged="participantMetadataChanged",e.ParticipantNameChanged="participantNameChanged",e.DataReceived="dataReceived",e.SipDTMFReceived="sipDTMFReceived",e.TranscriptionReceived="transcriptionReceived",e.IsSpeakingChanged="isSpeakingChanged",e.ConnectionQualityChanged="connectionQualityChanged",e.TrackStreamStateChanged="trackStreamStateChanged",e.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",e.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",e.TrackCpuConstrained="trackCpuConstrained",e.MediaDevicesError="mediaDevicesError",e.AudioStreamAcquired="audioStreamAcquired",e.ParticipantPermissionsChanged="participantPermissionsChanged",e.PCTrackAdded="pcTrackAdded",e.AttributesChanged="attributesChanged",e.LocalTrackSubscribed="localTrackSubscribed",e.ChatMessage="chatMessage",e.Active="active"}(S||(S={})),function(e){e.TransportsCreated="transportsCreated",e.Connected="connected",e.Disconnected="disconnected",e.Resuming="resuming",e.Resumed="resumed",e.Restarting="restarting",e.Restarted="restarted",e.SignalResumed="signalResumed",e.SignalRestarted="signalRestarted",e.Closing="closing",e.MediaTrackAdded="mediaTrackAdded",e.ActiveSpeakersUpdate="activeSpeakersUpdate",e.DataPacketReceived="dataPacketReceived",e.RTPVideoMapUpdate="rtpVideoMapUpdate",e.DCBufferStatusChanged="dcBufferStatusChanged",e.ParticipantUpdate="participantUpdate",e.RoomUpdate="roomUpdate",e.SpeakersChanged="speakersChanged",e.StreamStateChanged="streamStateChanged",e.ConnectionQualityUpdate="connectionQualityUpdate",e.SubscriptionError="subscriptionError",e.SubscriptionPermissionUpdate="subscriptionPermissionUpdate",e.RemoteMute="remoteMute",e.SubscribedQualityUpdate="subscribedQualityUpdate",e.LocalTrackUnpublished="localTrackUnpublished",e.LocalTrackSubscribed="localTrackSubscribed",e.Offline="offline",e.SignalRequestResponse="signalRequestResponse",e.SignalConnected="signalConnected",e.RoomMoved="roomMoved"}(w||(w={})),function(e){e.Message="message",e.Muted="muted",e.Unmuted="unmuted",e.Restarted="restarted",e.Ended="ended",e.Subscribed="subscribed",e.Unsubscribed="unsubscribed",e.CpuConstrained="cpuConstrained",e.UpdateSettings="updateSettings",e.UpdateSubscription="updateSubscription",e.AudioPlaybackStarted="audioPlaybackStarted",e.AudioPlaybackFailed="audioPlaybackFailed",e.AudioSilenceDetected="audioSilenceDetected",e.VisibilityChanged="visibilityChanged",e.VideoDimensionsChanged="videoDimensionsChanged",e.VideoPlaybackStarted="videoPlaybackStarted",e.VideoPlaybackFailed="videoPlaybackFailed",e.ElementAttached="elementAttached",e.ElementDetached="elementDetached",e.UpstreamPaused="upstreamPaused",e.UpstreamResumed="upstreamResumed",e.SubscriptionPermissionChanged="subscriptionPermissionChanged",e.SubscriptionStatusChanged="subscriptionStatusChanged",e.SubscriptionFailed="subscriptionFailed",e.TrackProcessorUpdate="trackProcessorUpdate",e.AudioTrackFeatureUpdate="audioTrackFeatureUpdate",e.TranscriptionReceived="transcriptionReceived",e.TimeSyncUpdate="timeSyncUpdate",e.PreConnectBufferFlushed="preConnectBufferFlushed"}(E||(E={}));let n$=/version\/(\d+(\.?_?\d+)+)/i;function nX(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(void 0===e&&"undefined"==typeof navigator)return;let i=(null!=e?e:navigator.userAgent).toLowerCase();if(void 0===n||t){let e=nZ.find(e=>{let{test:t}=e;return t.test(i)});n=null==e?void 0:e.describe(i)}return n}let nZ=[{test:/firefox|iceweasel|fxios/i,describe:e=>({name:"Firefox",version:n0(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("fxios")?"iOS":void 0,osVersion:n1(e)})},{test:/chrom|crios|crmo/i,describe:e=>({name:"Chrome",version:n0(/(?:chrome|chromium|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("crios")?"iOS":void 0,osVersion:n1(e)})},{test:/safari|applewebkit/i,describe:e=>({name:"Safari",version:n0(n$,e),os:e.includes("mobile/")?"iOS":"macOS",osVersion:n1(e)})}];function n0(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=t.match(e);return n&&n.length>=i&&n[i]||""}function n1(e){return e.includes("mac os")?n0(/\(.+?(\d+_\d+(:?_\d+)?)/,e,1).replace(/_/g,"."):void 0}class n5{}n5.setTimeout=function(){return setTimeout(...arguments)},n5.setInterval=function(){return setInterval(...arguments)},n5.clearTimeout=function(){return clearTimeout(...arguments)},n5.clearInterval=function(){return clearInterval(...arguments)};let n2=[];!function(e){e[e.LOW=0]="LOW",e[e.MEDIUM=1]="MEDIUM",e[e.HIGH=2]="HIGH"}(P||(P={}));class n3 extends iJ.EventEmitter{constructor(e,t){var i;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super(),this.attachedElements=[],this.isMuted=!1,this.streamState=n3.StreamState.Active,this.isInBackground=!1,this._currentBitrate=0,this.log=iB,this.appVisibilityChangedListener=()=>{this.backgroundTimeout&&clearTimeout(this.backgroundTimeout),"hidden"===document.visibilityState?this.backgroundTimeout=setTimeout(()=>this.handleAppVisibilityChanged(),5e3):this.handleAppVisibilityChanged()},this.log=iV(null!=(i=n.loggerName)?i:h.Track),this.loggerContextCb=n.loggerContextCb,this.setMaxListeners(100),this.kind=t,this._mediaStreamTrack=e,this._mediaStreamID=e.id,this.source=n3.Source.Unknown}get logContext(){var e;return Object.assign(Object.assign({},null==(e=this.loggerContextCb)?void 0:e.call(this)),rJ(this))}get currentBitrate(){return this._currentBitrate}get mediaStreamTrack(){return this._mediaStreamTrack}get mediaStreamID(){return this._mediaStreamID}attach(e){let t="audio";this.kind===n3.Kind.Video&&(t="video"),0===this.attachedElements.length&&this.kind===n3.Kind.Video&&this.addAppVisibilityListener(),!e&&("audio"===t&&(n2.forEach(t=>{null!==t.parentElement||e||(e=t)}),e&&n2.splice(n2.indexOf(e),1)),e||(e=document.createElement(t))),this.attachedElements.includes(e)||this.attachedElements.push(e),n9(this.mediaStreamTrack,e);let i=e.srcObject.getTracks(),n=i.some(e=>"audio"===e.kind);return e.play().then(()=>{this.emit(n?E.AudioPlaybackStarted:E.VideoPlaybackStarted)}).catch(t=>{"NotAllowedError"===t.name?this.emit(n?E.AudioPlaybackFailed:E.VideoPlaybackFailed,t):"AbortError"===t.name?iB.debug("".concat(n?"audio":"video"," playback aborted, likely due to new play request")):iB.warn("could not playback ".concat(n?"audio":"video"),t),n&&e&&i.some(e=>"video"===e.kind)&&"NotAllowedError"===t.name&&(e.muted=!0,e.play().catch(()=>{}))}),this.emit(E.ElementAttached,e),e}detach(e){try{if(e){n6(this.mediaStreamTrack,e);let t=this.attachedElements.indexOf(e);return t>=0&&(this.attachedElements.splice(t,1),this.recycleElement(e),this.emit(E.ElementDetached,e)),e}let t=[];return this.attachedElements.forEach(e=>{n6(this.mediaStreamTrack,e),t.push(e),this.recycleElement(e),this.emit(E.ElementDetached,e)}),this.attachedElements=[],t}finally{0===this.attachedElements.length&&this.removeAppVisibilityListener()}}stop(){this.stopMonitor(),this._mediaStreamTrack.stop()}enable(){this._mediaStreamTrack.enabled=!0}disable(){this._mediaStreamTrack.enabled=!1}stopMonitor(){this.monitorInterval&&clearInterval(this.monitorInterval),this.timeSyncHandle&&cancelAnimationFrame(this.timeSyncHandle)}updateLoggerOptions(e){e.loggerName&&(this.log=iV(e.loggerName)),e.loggerContextCb&&(this.loggerContextCb=e.loggerContextCb)}recycleElement(e){if(e instanceof HTMLAudioElement){let t=!0;e.pause(),n2.forEach(e=>{e.parentElement||(t=!1)}),t&&n2.push(e)}}handleAppVisibilityChanged(){return iz(this,void 0,void 0,function*(){this.isInBackground="hidden"===document.visibilityState,this.isInBackground||this.kind!==n3.Kind.Video||setTimeout(()=>this.attachedElements.forEach(e=>e.play().catch(()=>{})),0)})}addAppVisibilityListener(){rp()?(this.isInBackground="hidden"===document.visibilityState,document.addEventListener("visibilitychange",this.appVisibilityChangedListener)):this.isInBackground=!1}removeAppVisibilityListener(){rp()&&document.removeEventListener("visibilitychange",this.appVisibilityChangedListener)}}function n9(e,t){let i,n;i=t.srcObject instanceof MediaStream?t.srcObject:new MediaStream,(n="audio"===e.kind?i.getAudioTracks():i.getVideoTracks()).includes(e)||(n.forEach(e=>{i.removeTrack(e)}),i.addTrack(e)),rd()&&t instanceof HTMLVideoElement||(t.autoplay=!0),t.muted=0===i.getAudioTracks().length,t instanceof HTMLVideoElement&&(t.playsInline=!0),t.srcObject!==i&&(t.srcObject=i,(rd()||rl())&&t instanceof HTMLVideoElement&&setTimeout(()=>{t.srcObject=i,t.play().catch(()=>{})},0))}function n6(e,t){if(t.srcObject instanceof MediaStream){let i=t.srcObject;i.removeTrack(e),i.getTracks().length>0?t.srcObject=i:t.srcObject=null}}!function(e){var t,i,n;let r,a,s;(t=r=e.Kind||(e.Kind={})).Audio="audio",t.Video="video",t.Unknown="unknown",(i=a=e.Source||(e.Source={})).Camera="camera",i.Microphone="microphone",i.ScreenShare="screen_share",i.ScreenShareAudio="screen_share_audio",i.Unknown="unknown",(n=s=e.StreamState||(e.StreamState={})).Active="active",n.Paused="paused",n.Unknown="unknown",e.kindToProto=function(e){switch(e){case r.Audio:return ti.AUDIO;case r.Video:return ti.VIDEO;default:return ti.DATA}},e.kindFromProto=function(e){switch(e){case ti.AUDIO:return r.Audio;case ti.VIDEO:return r.Video;default:return r.Unknown}},e.sourceToProto=function(e){switch(e){case a.Camera:return tn.CAMERA;case a.Microphone:return tn.MICROPHONE;case a.ScreenShare:return tn.SCREEN_SHARE;case a.ScreenShareAudio:return tn.SCREEN_SHARE_AUDIO;default:return tn.UNKNOWN}},e.sourceFromProto=function(e){switch(e){case tn.CAMERA:return a.Camera;case tn.MICROPHONE:return a.Microphone;case tn.SCREEN_SHARE:return a.ScreenShare;case tn.SCREEN_SHARE_AUDIO:return a.ScreenShareAudio;default:return a.Unknown}},e.streamStateFromProto=function(e){switch(e){case t$.ACTIVE:return s.Active;case t$.PAUSED:return s.Paused;default:return s.Unknown}}}(n3||(n3={}));class n4{constructor(e,t,i,n,r){if("object"==typeof e)this.width=e.width,this.height=e.height,this.aspectRatio=e.aspectRatio,this.encoding={maxBitrate:e.maxBitrate,maxFramerate:e.maxFramerate,priority:e.priority};else if(void 0!==t&&void 0!==i)this.width=e,this.height=t,this.aspectRatio=e/t,this.encoding={maxBitrate:i,maxFramerate:n,priority:r};else throw TypeError("Unsupported options: provide at least width, height and maxBitrate")}get resolution(){return{width:this.width,height:this.height,frameRate:this.encoding.maxFramerate,aspectRatio:this.aspectRatio}}}let n7=["vp8","h264"],n8=["vp8","h264","vp9","av1","h265"];!function(e){e[e.PREFER_REGRESSION=0]="PREFER_REGRESSION",e[e.SIMULCAST=1]="SIMULCAST",e[e.REGRESSION=2]="REGRESSION"}(R||(R={})),function(e){e.telephone={maxBitrate:12e3},e.speech={maxBitrate:24e3},e.music={maxBitrate:48e3},e.musicStereo={maxBitrate:64e3},e.musicHighQuality={maxBitrate:96e3},e.musicHighQualityStereo={maxBitrate:128e3}}(I||(I={}));let re={h90:new n4(160,90,9e4,20),h180:new n4(320,180,16e4,20),h216:new n4(384,216,18e4,20),h360:new n4(640,360,45e4,20),h540:new n4(960,540,8e5,25),h720:new n4(1280,720,17e5,30),h1080:new n4(1920,1080,3e6,30),h1440:new n4(2560,1440,5e6,30),h2160:new n4(3840,2160,8e6,30)},rt={h120:new n4(160,120,7e4,20),h180:new n4(240,180,125e3,20),h240:new n4(320,240,14e4,20),h360:new n4(480,360,33e4,20),h480:new n4(640,480,5e5,20),h540:new n4(720,540,6e5,25),h720:new n4(960,720,13e5,30),h1080:new n4(1440,1080,23e5,30),h1440:new n4(1920,1440,38e5,30)},ri={h360fps3:new n4(640,360,2e5,3,"medium"),h360fps15:new n4(640,360,4e5,15,"medium"),h720fps5:new n4(1280,720,8e5,5,"medium"),h720fps15:new n4(1280,720,15e5,15,"medium"),h720fps30:new n4(1280,720,2e6,30,"medium"),h1080fps15:new n4(1920,1080,25e5,15,"medium"),h1080fps30:new n4(1920,1080,5e6,30,"medium"),original:new n4(0,0,7e6,30,"medium")},rn="https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension";function rr(e){return iz(this,void 0,void 0,function*(){return new Promise(t=>n5.setTimeout(t,e))})}function ra(){return"addTransceiver"in RTCPeerConnection.prototype}function rs(){return"addTrack"in RTCPeerConnection.prototype}function ro(e){return"av1"===e||"vp9"===e}function rc(e){return!!document&&(e||(e=document.createElement("audio")),"setSinkId"in e)}function rl(){var e;return(null==(e=nX())?void 0:e.name)==="Firefox"}function rd(){var e;return(null==(e=nX())?void 0:e.name)==="Safari"}function ru(){let e=nX();return(null==e?void 0:e.name)==="Safari"||(null==e?void 0:e.os)==="iOS"}function rh(){var e,t;return!!rp()&&(null!=(t=null==(e=navigator.userAgentData)?void 0:e.mobile)?t:/Tablet|iPad|Mobile|Android|BlackBerry/.test(navigator.userAgent))}function rp(){return"undefined"!=typeof document}function rm(){return"ReactNative"==navigator.product}function rf(e){return e.hostname.endsWith(".livekit.cloud")||e.hostname.endsWith(".livekit.run")}function rg(){if(global&&global.LiveKitReactNativeGlobal)return global.LiveKitReactNativeGlobal}function rv(){if(!rm())return;let e=rg();if(e)return e.platform}function rb(){if(rp())return window.devicePixelRatio;if(rm()){let e=rg();if(e)return e.devicePixelRatio}return 1}function ry(e,t){let i=e.split("."),n=t.split("."),r=Math.min(i.length,n.length);for(let e=0;e<r;++e){let t=parseInt(i[e],10),a=parseInt(n[e],10);if(t>a)return 1;if(t<a)return -1;if(e===r-1&&t===a)return 0}return""===e&&""!==t?-1:""===t?1:i.length==n.length?0:i.length<n.length?-1:1}function rk(e){for(let t of e)t.target.handleResize(t)}function rT(e){for(let t of e)t.target.handleVisibilityChanged(t)}let rC=null,rS=()=>(rC||(rC=new ResizeObserver(rk)),rC),rw=null,rE=()=>(rw||(rw=new IntersectionObserver(rT,{root:null,rootMargin:"0px"})),rw);function rP(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:16,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=document.createElement("canvas");r.width=e,r.height=t;let a=r.getContext("2d");null==a||a.fillRect(0,0,r.width,r.height),n&&a&&(a.beginPath(),a.arc(e/2,t/2,50,0,2*Math.PI,!0),a.closePath(),a.fillStyle="grey",a.fill());let[s]=r.captureStream().getTracks();if(!s)throw Error("Could not get empty media stream video track");return s.enabled=i,s}function rR(){if(!r){let e=new AudioContext,t=e.createOscillator(),i=e.createGain();i.gain.setValueAtTime(0,0);let n=e.createMediaStreamDestination();if(t.connect(i),i.connect(n),t.start(),[r]=n.stream.getAudioTracks(),!r)throw Error("Could not get empty media stream audio track");r.enabled=!1}return r.clone()}class rI{constructor(e,t){this.onFinally=t,this.promise=new Promise((t,i)=>iz(this,void 0,void 0,function*(){this.resolve=t,this.reject=i,e&&(yield e(t,i))})).finally(()=>{var e;return null==(e=this.onFinally)?void 0:e.call(this)})}}function rx(e){if("string"==typeof e||"number"==typeof e)return e;if(Array.isArray(e))return e[0];if(e.exact)return Array.isArray(e.exact)?e.exact[0]:e.exact;if(e.ideal)return Array.isArray(e.ideal)?e.ideal[0]:e.ideal;throw Error("could not unwrap constraint")}function rO(e){return e.startsWith("ws")?e.replace(/^(ws)/,"http"):e}function rM(e){switch(e.reason){case y.LeaveRequest:return e.context;case y.Cancelled:return to.CLIENT_INITIATED;case y.NotAllowed:return to.USER_REJECTED;case y.ServerUnreachable:return to.JOIN_FAILURE;default:return to.UNKNOWN_REASON}}function rD(e){return void 0!==e?Number(e):void 0}function rA(e){return void 0!==e?BigInt(e):void 0}function rN(e){return!!e&&!(e instanceof MediaStreamTrack)&&e.isLocal}function r_(e){return!!e&&e.kind==n3.Kind.Audio}function rL(e){return!!e&&e.kind==n3.Kind.Video}function rU(e){return rN(e)&&rL(e)}function rj(e){return rN(e)&&r_(e)}function rF(e){return!!e&&!e.isLocal}function rB(e){return rF(e)&&rL(e)}function rV(e,t,i){var n,r,a,s;let{optionsWithoutProcessor:o,audioProcessor:c,videoProcessor:l}=rY(null!=e?e:{}),d=null==t?void 0:t.processor,u=null==i?void 0:i.processor,h=null!=o?o:{};return!0===h.audio&&(h.audio={}),!0===h.video&&(h.video={}),h.audio&&(rq(h.audio,t),null!=(a=h.audio).deviceId||(a.deviceId={ideal:"default"}),(c||d)&&(h.audio.processor=null!=c?c:d)),h.video&&(rq(h.video,i),null!=(s=h.video).deviceId||(s.deviceId={ideal:"default"}),(l||u)&&(h.video.processor=null!=l?l:u)),h}function rq(e,t){return Object.keys(t).forEach(i=>{void 0===e[i]&&(e[i]=t[i])}),e}function rH(e){var t,i,n,r;let a={};if(e.video)if("object"==typeof e.video){let t={},i=e.video;Object.keys(i).forEach(e=>{"resolution"===e?rq(t,i.resolution):t[e]=i[e]}),a.video=t,null!=(n=a.video).deviceId||(n.deviceId={ideal:"default"})}else a.video=!!e.video&&{deviceId:{ideal:"default"}};else a.video=!1;return e.audio?"object"==typeof e.audio?(a.audio=e.audio,null!=(r=a.audio).deviceId||(r.deviceId={ideal:"default"})):a.audio={deviceId:{ideal:"default"}}:a.audio=!1,a}function rW(e){return iz(this,arguments,void 0,function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return function*(){let i=rz();if(i){let n=i.createAnalyser();n.fftSize=2048;let r=new Uint8Array(n.frequencyBinCount);i.createMediaStreamSource(new MediaStream([e.mediaStreamTrack])).connect(n),yield rr(t),n.getByteTimeDomainData(r);let a=r.some(e=>128!==e&&0!==e);return i.close(),!a}return!1}()})}function rz(){var e;let t="undefined"!=typeof window&&(window.AudioContext||window.webkitAudioContext);if(t){let i=new t({latencyHint:"interactive"});if("suspended"===i.state&&"undefined"!=typeof window&&(null==(e=window.document)?void 0:e.body)){let e=()=>iz(this,void 0,void 0,function*(){var t;try{"suspended"===i.state&&(yield i.resume())}catch(e){console.warn("Error trying to auto-resume audio context",e)}null==(t=window.document.body)||t.removeEventListener("click",e)});window.document.body.addEventListener("click",e)}return i}}function rG(e){return e===n3.Source.Microphone?"audioinput":e===n3.Source.Camera?"videoinput":void 0}function rK(e){return e.split("/")[1].toLowerCase()}function rJ(e){return"mediaStreamTrack"in e?{trackID:e.sid,source:e.source,muted:e.isMuted,enabled:e.mediaStreamTrack.enabled,kind:e.kind,streamID:e.mediaStreamID,streamTrackID:e.mediaStreamTrack.id}:{trackID:e.trackSid,enabled:e.isEnabled,muted:e.isMuted,trackInfo:Object.assign({mimeType:e.mimeType,name:e.trackName,encrypted:e.isEncrypted,kind:e.kind,source:e.source},e.track?rJ(e.track):{})}}function rY(e){let t,i,n=Object.assign({},e);return"object"==typeof n.audio&&n.audio.processor&&(t=n.audio.processor,n.audio=Object.assign(Object.assign({},n.audio),{processor:void 0})),"object"==typeof n.video&&n.video.processor&&(i=n.video.processor,n.video=Object.assign(Object.assign({},n.video),{processor:void 0})),{audioProcessor:t,videoProcessor:i,optionsWithoutProcessor:void 0===n?n:"function"!=typeof structuredClone?JSON.parse(JSON.stringify(n)):"object"==typeof n&&null!==n?structuredClone(Object.assign({},n)):structuredClone(n)}}function rQ(e,t){return e.width*e.height<t.width*t.height}class r$ extends iJ.EventEmitter{constructor(e){super(),this.onWorkerMessage=e=>{var t,i;let{kind:n,data:r}=e.data;switch(n){case"error":iB.error(r.error.message),this.emit(v.EncryptionError,r.error);break;case"initAck":r.enabled&&this.keyProvider.getKeys().forEach(e=>{this.postKey(e)});break;case"enable":if(r.enabled&&this.keyProvider.getKeys().forEach(e=>{this.postKey(e)}),this.encryptionEnabled!==r.enabled&&r.participantIdentity===(null==(t=this.room)?void 0:t.localParticipant.identity))this.emit(v.ParticipantEncryptionStatusChanged,r.enabled,this.room.localParticipant),this.encryptionEnabled=r.enabled;else if(r.participantIdentity){let e=null==(i=this.room)?void 0:i.getParticipantByIdentity(r.participantIdentity);if(!e)throw TypeError("couldn't set encryption status, participant not found".concat(r.participantIdentity));this.emit(v.ParticipantEncryptionStatusChanged,r.enabled,e)}break;case"ratchetKey":this.keyProvider.emit(f.KeyRatcheted,r.ratchetResult,r.participantIdentity,r.keyIndex)}},this.onWorkerError=e=>{iB.error("e2ee worker encountered an error:",{error:e.error}),this.emit(v.EncryptionError,e.error)},this.keyProvider=e.keyProvider,this.worker=e.worker,this.encryptionEnabled=!1}setup(e){if(!(void 0!==window.RTCRtpSender&&void 0!==window.RTCRtpSender.prototype.createEncodedStreams||nV()))throw new nW("tried to setup end-to-end encryption on an unsupported browser");if(iB.info("setting up e2ee"),e!==this.room){this.room=e,this.setupEventListeners(e,this.keyProvider);let t={kind:"init",data:{keyProviderOptions:this.keyProvider.getOptions(),loglevel:iq.getLevel()}};this.worker&&(iB.info("initializing worker",{worker:this.worker}),this.worker.onmessage=this.onWorkerMessage,this.worker.onerror=this.onWorkerError,this.worker.postMessage(t))}}setParticipantCryptorEnabled(e,t){iB.debug("set e2ee to ".concat(e," for participant ").concat(t)),this.postEnable(e,t)}setSifTrailer(e){e&&0!==e.length?this.postSifTrailer(e):iB.warn("ignoring server sent trailer as it's empty")}setupEngine(e){e.on(w.RTPVideoMapUpdate,e=>{this.postRTPMap(e)})}setupEventListeners(e,t){e.on(C.TrackPublished,(e,t)=>this.setParticipantCryptorEnabled(e.trackInfo.encryption!==tb.NONE,t.identity)),e.on(C.ConnectionStateChanged,t=>{t===F.Connected&&e.remoteParticipants.forEach(e=>{e.trackPublications.forEach(t=>{this.setParticipantCryptorEnabled(t.trackInfo.encryption!==tb.NONE,e.identity)})})}).on(C.TrackUnsubscribed,(e,t,i)=>{var n;let r={kind:"removeTransform",data:{participantIdentity:i.identity,trackId:e.mediaStreamID}};null==(n=this.worker)||n.postMessage(r)}).on(C.TrackSubscribed,(e,t,i)=>{this.setupE2EEReceiver(e,i.identity,t.trackInfo)}).on(C.SignalConnected,()=>{if(!this.room)throw TypeError("expected room to be present on signal connect");t.getKeys().forEach(e=>{this.postKey(e)}),this.setParticipantCryptorEnabled(this.room.localParticipant.isE2EEEnabled,this.room.localParticipant.identity)}),e.localParticipant.on(S.LocalSenderCreated,(e,t)=>iz(this,void 0,void 0,function*(){this.setupE2EESender(t,e)})),t.on(f.SetKey,e=>this.postKey(e)).on(f.RatchetRequest,(e,t)=>this.postRatchetRequest(e,t))}postRatchetRequest(e,t){if(!this.worker)throw Error("could not ratchet key, worker is missing");this.worker.postMessage({kind:"ratchetRequest",data:{participantIdentity:e,keyIndex:t}})}postKey(e){var t;let{key:i,participantIdentity:n,keyIndex:r}=e;if(!this.worker)throw Error("could not set key, worker is missing");let a={kind:"setKey",data:{participantIdentity:n,isPublisher:n===(null==(t=this.room)?void 0:t.localParticipant.identity),key:i,keyIndex:r}};this.worker.postMessage(a)}postEnable(e,t){if(this.worker)this.worker.postMessage({kind:"enable",data:{enabled:e,participantIdentity:t}});else throw ReferenceError("failed to enable e2ee, worker is not ready")}postRTPMap(e){var t;if(!this.worker)throw TypeError("could not post rtp map, worker is missing");if(!(null==(t=this.room)?void 0:t.localParticipant.identity))throw TypeError("could not post rtp map, local participant identity is missing");let i={kind:"setRTPMap",data:{map:e,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(i)}postSifTrailer(e){if(!this.worker)throw Error("could not post SIF trailer, worker is missing");this.worker.postMessage({kind:"setSifTrailer",data:{trailer:e}})}setupE2EEReceiver(e,t,i){if(e.receiver){if(!(null==i?void 0:i.mimeType)||""===i.mimeType)throw TypeError("MimeType missing from trackInfo, cannot set up E2EE cryptor");this.handleReceiver(e.receiver,e.mediaStreamID,t,"video"===e.kind?rK(i.mimeType):void 0)}}setupE2EESender(e,t){if(!rN(e)||!t){t||iB.warn("early return because sender is not ready");return}this.handleSender(t,e.mediaStreamID,void 0)}handleReceiver(e,t,i,n){return iz(this,void 0,void 0,function*(){if(this.worker){if(nV())e.transform=new RTCRtpScriptTransform(this.worker,{kind:"decode",participantIdentity:i,trackId:t,codec:n});else{if(nB in e&&n)return void this.worker.postMessage({kind:"updateCodec",data:{trackId:t,codec:n,participantIdentity:i}});let r=e.writableStream,a=e.readableStream;if(!r||!a){let t=e.createEncodedStreams();e.writableStream=t.writable,r=t.writable,e.readableStream=t.readable,a=t.readable}let s={kind:"decode",data:{readableStream:a,writableStream:r,trackId:t,codec:n,participantIdentity:i,isReuse:nB in e}};this.worker.postMessage(s,[a,r])}e[nB]=!0}})}handleSender(e,t,i){var n;if(!(nB in e)&&this.worker){if(!(null==(n=this.room)?void 0:n.localParticipant.identity)||""===this.room.localParticipant.identity)throw TypeError("local identity needs to be known in order to set up encrypted sender");if(nV()){iB.info("initialize script transform");let n={kind:"encode",participantIdentity:this.room.localParticipant.identity,trackId:t,codec:i};e.transform=new RTCRtpScriptTransform(this.worker,n)}else{iB.info("initialize encoded streams");let n=e.createEncodedStreams(),r={kind:"encode",data:{readableStream:n.readable,writableStream:n.writable,codec:i,trackId:t,participantIdentity:this.room.localParticipant.identity,isReuse:!1}};this.worker.postMessage(r,[n.readable,n.writable])}e[nB]=!0}}}let rX="default";class rZ{constructor(){this._previousDevices=[]}static getInstance(){return void 0===this.instance&&(this.instance=new rZ),this.instance}get previousDevices(){return this._previousDevices}getDevices(e){return iz(this,arguments,void 0,function(e){var t=this;let i=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return function*(){var n;if((null==(n=rZ.userMediaPromiseMap)?void 0:n.size)>0){iB.debug("awaiting getUserMedia promise");try{e?yield rZ.userMediaPromiseMap.get(e):yield Promise.all(rZ.userMediaPromiseMap.values())}catch(e){iB.warn("error waiting for media permissons")}}let r=yield navigator.mediaDevices.enumerateDevices();if(i&&!(rd()&&t.hasDeviceInUse(e))&&(0===r.filter(t=>t.kind===e).length||r.some(t=>{let i=""===t.label,n=!e||t.kind===e;return i&&n}))){let t=yield navigator.mediaDevices.getUserMedia({video:"audioinput"!==e&&"audiooutput"!==e,audio:"videoinput"!==e&&{deviceId:{ideal:"default"}}});r=yield navigator.mediaDevices.enumerateDevices(),t.getTracks().forEach(e=>{e.stop()})}return t._previousDevices=r,e&&(r=r.filter(t=>t.kind===e)),r}()})}normalizeDeviceId(e,t,i){return iz(this,void 0,void 0,function*(){if(t!==rX)return t;let n=yield this.getDevices(e),r=n.find(e=>e.deviceId===rX);if(!r)return void iB.warn("could not reliably determine default device");let a=n.find(e=>e.deviceId!==rX&&e.groupId===(null!=i?i:r.groupId));return a?null==a?void 0:a.deviceId:void iB.warn("could not reliably determine default device")})}hasDeviceInUse(e){return e?rZ.userMediaPromiseMap.has(e):rZ.userMediaPromiseMap.size>0}}rZ.mediaDeviceKinds=["audioinput","audiooutput","videoinput"],rZ.userMediaPromiseMap=new Map,function(e){e[e.WAITING=0]="WAITING",e[e.RUNNING=1]="RUNNING",e[e.COMPLETED=2]="COMPLETED"}(x||(x={}));class r0{constructor(){this.pendingTasks=new Map,this.taskMutex=new W,this.nextTaskIndex=0}run(e){return iz(this,void 0,void 0,function*(){let t={id:this.nextTaskIndex++,enqueuedAt:Date.now(),status:x.WAITING};this.pendingTasks.set(t.id,t);let i=yield this.taskMutex.lock();try{return t.executedAt=Date.now(),t.status=x.RUNNING,yield e()}finally{t.status=x.COMPLETED,this.pendingTasks.delete(t.id),i()}})}flush(){return iz(this,void 0,void 0,function*(){return this.run(()=>iz(this,void 0,void 0,function*(){}))})}snapshot(){return Array.from(this.pendingTasks.values())}}function r1(e,t){var i;return e.pathname="".concat((i=e.pathname).endsWith("/")?i:"".concat(i,"/")).concat(t),e.toString()}let r5=["syncState","trickle","offer","answer","simulate","leave"];!function(e){e[e.CONNECTING=0]="CONNECTING",e[e.CONNECTED=1]="CONNECTED",e[e.RECONNECTING=2]="RECONNECTING",e[e.DISCONNECTING=3]="DISCONNECTING",e[e.DISCONNECTED=4]="DISCONNECTED"}(O||(O={}));class r2{get currentState(){return this.state}get isDisconnected(){return this.state===O.DISCONNECTING||this.state===O.DISCONNECTED}get isEstablishingConnection(){return this.state===O.CONNECTING||this.state===O.RECONNECTING}getNextRequestId(){return this._requestId+=1,this._requestId}constructor(){var e;let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.rtt=0,this.state=O.DISCONNECTED,this.log=iB,this._requestId=0,this.resetCallbacks=()=>{this.onAnswer=void 0,this.onLeave=void 0,this.onLocalTrackPublished=void 0,this.onLocalTrackUnpublished=void 0,this.onNegotiateRequested=void 0,this.onOffer=void 0,this.onRemoteMuteChanged=void 0,this.onSubscribedQualityUpdate=void 0,this.onTokenRefresh=void 0,this.onTrickle=void 0,this.onClose=void 0},this.log=iV(null!=(e=i.loggerName)?e:h.Signal),this.loggerContextCb=i.loggerContextCb,this.useJSON=t,this.requestQueue=new r0,this.queuedRequests=[],this.closingLock=new W,this.connectionLock=new W,this.state=O.DISCONNECTED}get logContext(){var e,t;return null!=(t=null==(e=this.loggerContextCb)?void 0:e.call(this))?t:{}}join(e,t,i,n){return iz(this,void 0,void 0,function*(){return this.state=O.CONNECTING,this.options=i,yield this.connect(e,t,i,n)})}reconnect(e,t,i,n){return iz(this,void 0,void 0,function*(){return this.options?(this.state=O.RECONNECTING,this.clearPingInterval(),yield this.connect(e,t,Object.assign(Object.assign({},this.options),{reconnect:!0,sid:i,reconnectReason:n}))):void this.log.warn("attempted to reconnect without signal options being set, ignoring",this.logContext)})}connect(e,t,i,n){this.connectOptions=i;let r=function(e,t){let i=new URL(e.startsWith("http")?e.replace(/^(http)/,"ws"):e);return t.forEach((e,t)=>{i.searchParams.set(t,e)}),r1(i,"rtc")}(e,function(e,t,i){var n;let r=new URLSearchParams;return r.set("access_token",e),i.reconnect&&(r.set("reconnect","1"),i.sid&&r.set("sid",i.sid)),r.set("auto_subscribe",i.autoSubscribe?"1":"0"),r.set("sdk",rm()?"reactnative":"js"),r.set("version",t.version),r.set("protocol",t.protocol.toString()),t.deviceModel&&r.set("device_model",t.deviceModel),t.os&&r.set("os",t.os),t.osVersion&&r.set("os_version",t.osVersion),t.browser&&r.set("browser",t.browser),t.browserVersion&&r.set("browser_version",t.browserVersion),i.adaptiveStream&&r.set("adaptive_stream","1"),i.reconnectReason&&r.set("reconnect_reason",i.reconnectReason.toString()),(null==(n=navigator.connection)?void 0:n.type)&&r.set("network",navigator.connection.type),r}(t,function(){var e;let t=new tj({sdk:tF.JS,protocol:16,version:"2.15.2"});return rm()&&(t.os=null!=(e=rv())?e:""),t}(),i)),a=r1(new URL(rO(r)),"validate");return new Promise((e,t)=>iz(this,void 0,void 0,function*(){let s=yield this.connectionLock.lock();try{let s=()=>iz(this,void 0,void 0,function*(){this.close(),clearTimeout(o),t(new nH("room connection has been cancelled (signal)",y.Cancelled))}),o=setTimeout(()=>{this.close(),t(new nH("room connection has timed out (signal)",y.ServerUnreachable))},i.websocketTimeout);(null==n?void 0:n.aborted)&&s(),null==n||n.addEventListener("abort",s);let c=new URL(r);c.searchParams.has("access_token")&&c.searchParams.set("access_token","<redacted>"),this.log.debug("connecting to ".concat(c),Object.assign({reconnect:i.reconnect,reconnectReason:i.reconnectReason},this.logContext)),this.ws&&(yield this.close(!1)),this.ws=new WebSocket(r),this.ws.binaryType="arraybuffer",this.ws.onopen=()=>{clearTimeout(o)},this.ws.onerror=e=>iz(this,void 0,void 0,function*(){if(this.state!==O.CONNECTED){this.state=O.DISCONNECTED,clearTimeout(o);try{let i=yield fetch(a);if(i.status.toFixed(0).startsWith("4")){let e=yield i.text();t(new nH(e,y.NotAllowed,i.status))}else t(new nH("Encountered unknown websocket error during connection: ".concat(e.toString()),y.InternalError,i.status))}catch(e){t(new nH(e instanceof Error?e.message:"server was not reachable",y.ServerUnreachable))}return}this.handleWSError(e)}),this.ws.onmessage=r=>iz(this,void 0,void 0,function*(){var a,o,c;let l;if("string"==typeof r.data){let e=JSON.parse(r.data);l=t0.fromJson(e,{ignoreUnknownFields:!0})}else{if(!(r.data instanceof ArrayBuffer))return void this.log.error("could not decode websocket message: ".concat(typeof r.data),this.logContext);l=t0.fromBinary(new Uint8Array(r.data))}if(this.state!==O.CONNECTED){let r=!1;if((null==(a=l.message)?void 0:a.case)==="join"?(this.state=O.CONNECTED,null==n||n.removeEventListener("abort",s),this.pingTimeoutDuration=l.message.value.pingTimeout,this.pingIntervalDuration=l.message.value.pingInterval,this.pingTimeoutDuration&&this.pingTimeoutDuration>0&&(this.log.debug("ping config",Object.assign(Object.assign({},this.logContext),{timeout:this.pingTimeoutDuration,interval:this.pingIntervalDuration})),this.startPingInterval()),e(l.message.value)):this.state===O.RECONNECTING&&"leave"!==l.message.case?(this.state=O.CONNECTED,null==n||n.removeEventListener("abort",s),this.startPingInterval(),(null==(o=l.message)?void 0:o.case)==="reconnect"?e(l.message.value):(this.log.debug("declaring signal reconnected without reconnect response received",this.logContext),e(void 0),r=!0)):this.isEstablishingConnection&&"leave"===l.message.case?t(new nH("Received leave request while trying to (re)connect",y.LeaveRequest,void 0,l.message.value.reason)):i.reconnect||t(new nH("did not receive join response, got ".concat(null==(c=l.message)?void 0:c.case," instead"),y.InternalError)),!r)return}this.signalLatency&&(yield rr(this.signalLatency)),this.handleSignalResponse(l)}),this.ws.onclose=e=>{this.isEstablishingConnection&&t(new nH("Websocket got closed during a (re)connection attempt",y.InternalError)),this.log.warn("websocket closed",Object.assign(Object.assign({},this.logContext),{reason:e.reason,code:e.code,wasClean:e.wasClean,state:this.state})),this.handleOnClose(e.reason)}}finally{s()}}))}close(){return iz(this,arguments,void 0,function(){var e=this;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return function*(){let i=yield e.closingLock.lock();try{if(e.clearPingInterval(),t&&(e.state=O.DISCONNECTING),e.ws){e.ws.onmessage=null,e.ws.onopen=null,e.ws.onclose=null;let t=new Promise(t=>{e.ws?e.ws.onclose=()=>{t()}:t()});e.ws.readyState<e.ws.CLOSING&&(e.ws.close(),yield Promise.race([t,rr(250)])),e.ws=void 0}}finally{t&&(e.state=O.DISCONNECTED),i()}}()})}sendOffer(e,t){this.log.debug("sending offer",Object.assign(Object.assign({},this.logContext),{offerSdp:e.sdp})),this.sendRequest({case:"offer",value:r9(e,t)})}sendAnswer(e,t){return this.log.debug("sending answer",Object.assign(Object.assign({},this.logContext),{answerSdp:e.sdp})),this.sendRequest({case:"answer",value:r9(e,t)})}sendIceCandidate(e,t){return this.log.debug("sending ice candidate",Object.assign(Object.assign({},this.logContext),{candidate:e})),this.sendRequest({case:"trickle",value:new t2({candidateInit:JSON.stringify(e),target:t})})}sendMuteTrack(e,t){return this.sendRequest({case:"mute",value:new t3({sid:e,muted:t})})}sendAddTrack(e){return this.sendRequest({case:"addTrack",value:e})}sendUpdateLocalMetadata(e,t){return iz(this,arguments,void 0,function(e,t){var i=this;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function*(){let r=i.getNextRequestId();return yield i.sendRequest({case:"updateMetadata",value:new il({requestId:r,metadata:e,name:t,attributes:n})}),r}()})}sendUpdateTrackSettings(e){this.sendRequest({case:"trackSetting",value:e})}sendUpdateSubscription(e){return this.sendRequest({case:"subscription",value:e})}sendSyncState(e){return this.sendRequest({case:"syncState",value:e})}sendUpdateVideoLayers(e,t){return this.sendRequest({case:"updateLayers",value:new ic({trackSid:e,layers:t})})}sendUpdateSubscriptionPermissions(e,t){return this.sendRequest({case:"subscriptionPermission",value:new iC({allParticipants:e,trackPermissions:t})})}sendSimulateScenario(e){return this.sendRequest({case:"simulate",value:e})}sendPing(){return Promise.all([this.sendRequest({case:"ping",value:el.parse(Date.now())}),this.sendRequest({case:"pingReq",value:new ix({timestamp:el.parse(Date.now()),rtt:el.parse(this.rtt)})})])}sendUpdateLocalAudioTrack(e,t){return this.sendRequest({case:"updateAudioTrack",value:new ir({trackSid:e,features:t})})}sendLeave(){return this.sendRequest({case:"leave",value:new is({reason:to.CLIENT_INITIATED,action:io.DISCONNECT})})}sendRequest(e){return iz(this,arguments,void 0,function(e){var t=this;let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function*(){if(!i&&!function(e){let t=r5.indexOf(e.case)>=0;return iB.trace("request allowed to bypass queue:",{canPass:t,req:e}),t}(e)&&t.state===O.RECONNECTING)return void t.queuedRequests.push(()=>iz(t,void 0,void 0,function*(){yield this.sendRequest(e,!0)}));if(i||(yield t.requestQueue.flush()),t.signalLatency&&(yield rr(t.signalLatency)),!t.ws||t.ws.readyState!==t.ws.OPEN)return void t.log.error("cannot send signal request before connected, type: ".concat(null==e?void 0:e.case),t.logContext);let n=new tZ({message:e});try{t.useJSON?t.ws.send(n.toJsonString()):t.ws.send(n.toBinary())}catch(e){t.log.error("error sending signal message",Object.assign(Object.assign({},t.logContext),{error:e}))}}()})}handleSignalResponse(e){var t,i;let n=e.message;if(void 0==n)return void this.log.debug("received unsupported message",this.logContext);let r=!1;if("answer"===n.case){let e=r3(n.value);this.onAnswer&&this.onAnswer(e,n.value.id)}else if("offer"===n.case){let e=r3(n.value);this.onOffer&&this.onOffer(e,n.value.id)}else if("trickle"===n.case){let e=JSON.parse(n.value.candidateInit);this.onTrickle&&this.onTrickle(e,n.value.target)}else"update"===n.case?this.onParticipantUpdate&&this.onParticipantUpdate(null!=(t=n.value.participants)?t:[]):"trackPublished"===n.case?this.onLocalTrackPublished&&this.onLocalTrackPublished(n.value):"speakersChanged"===n.case?this.onSpeakersChanged&&this.onSpeakersChanged(null!=(i=n.value.speakers)?i:[]):"leave"===n.case?this.onLeave&&this.onLeave(n.value):"mute"===n.case?this.onRemoteMuteChanged&&this.onRemoteMuteChanged(n.value.sid,n.value.muted):"roomUpdate"===n.case?this.onRoomUpdate&&n.value.room&&this.onRoomUpdate(n.value.room):"connectionQuality"===n.case?this.onConnectionQuality&&this.onConnectionQuality(n.value):"streamStateUpdate"===n.case?this.onStreamStateUpdate&&this.onStreamStateUpdate(n.value):"subscribedQualityUpdate"===n.case?this.onSubscribedQualityUpdate&&this.onSubscribedQualityUpdate(n.value):"subscriptionPermissionUpdate"===n.case?this.onSubscriptionPermissionUpdate&&this.onSubscriptionPermissionUpdate(n.value):"refreshToken"===n.case?this.onTokenRefresh&&this.onTokenRefresh(n.value):"trackUnpublished"===n.case?this.onLocalTrackUnpublished&&this.onLocalTrackUnpublished(n.value):"subscriptionResponse"===n.case?this.onSubscriptionError&&this.onSubscriptionError(n.value):"pong"===n.case||("pongResp"===n.case?(this.rtt=Date.now()-Number.parseInt(n.value.lastPingTimestamp.toString()),this.resetPingTimeout(),r=!0):"requestResponse"===n.case?this.onRequestResponse&&this.onRequestResponse(n.value):"trackSubscribed"===n.case?this.onLocalTrackSubscribed&&this.onLocalTrackSubscribed(n.value.trackSid):"roomMoved"===n.case?(this.onTokenRefresh&&this.onTokenRefresh(n.value.token),this.onRoomMoved&&this.onRoomMoved(n.value)):this.log.debug("unsupported message",Object.assign(Object.assign({},this.logContext),{msgCase:n.case})));r||this.resetPingTimeout()}setReconnected(){for(;this.queuedRequests.length>0;){let e=this.queuedRequests.shift();e&&this.requestQueue.run(e)}}handleOnClose(e){return iz(this,void 0,void 0,function*(){if(this.state===O.DISCONNECTED)return;let t=this.onClose;yield this.close(),this.log.debug("websocket connection closed: ".concat(e),Object.assign(Object.assign({},this.logContext),{reason:e})),t&&t(e)})}handleWSError(e){this.log.error("websocket error",Object.assign(Object.assign({},this.logContext),{error:e}))}resetPingTimeout(){if(this.clearPingTimeout(),!this.pingTimeoutDuration)return void this.log.warn("ping timeout duration not set",this.logContext);this.pingTimeout=n5.setTimeout(()=>{this.log.warn("ping timeout triggered. last pong received at: ".concat(new Date(Date.now()-1e3*this.pingTimeoutDuration).toUTCString()),this.logContext),this.handleOnClose("ping timeout")},1e3*this.pingTimeoutDuration)}clearPingTimeout(){this.pingTimeout&&n5.clearTimeout(this.pingTimeout)}startPingInterval(){if(this.clearPingInterval(),this.resetPingTimeout(),!this.pingIntervalDuration)return void this.log.warn("ping interval duration not set",this.logContext);this.log.debug("start ping interval",this.logContext),this.pingInterval=n5.setInterval(()=>{this.sendPing()},1e3*this.pingIntervalDuration)}clearPingInterval(){this.log.debug("clearing ping interval",this.logContext),this.clearPingTimeout(),this.pingInterval&&n5.clearInterval(this.pingInterval)}}function r3(e){let t={type:"offer",sdp:e.sdp};switch(e.type){case"answer":case"offer":case"pranswer":case"rollback":t.type=e.type}return t}function r9(e,t){return new t8({sdp:e.sdp,type:e.type,id:t})}class r6{constructor(){this.buffer=[],this._totalSize=0}push(e){this.buffer.push(e),this._totalSize+=e.data.byteLength}pop(){let e=this.buffer.shift();return e&&(this._totalSize-=e.data.byteLength),e}getAll(){return this.buffer.slice()}popToSequence(e){for(;this.buffer.length>0;)if(this.buffer[0].sequence<=e)this.pop();else break}alignBufferedAmount(e){for(;this.buffer.length>0;){let t=this.buffer[0];if(this._totalSize-t.data.byteLength<=e)break;this.pop()}}get length(){return this.buffer.length}}class r4{constructor(e){this._map=new Map,this._lastCleanup=0,this.ttl=e}set(e,t){let i=Date.now();i-this._lastCleanup>this.ttl/2&&this.cleanup();let n=i+this.ttl;return this._map.set(e,{value:t,expiresAt:n}),this}get(e){let t=this._map.get(e);if(t)return t.expiresAt<Date.now()?void this._map.delete(e):t.value}has(e){let t=this._map.get(e);return!!t&&(!(t.expiresAt<Date.now())||(this._map.delete(e),!1))}delete(e){return this._map.delete(e)}clear(){this._map.clear()}cleanup(){let e=Date.now();for(let[t,i]of this._map.entries())i.expiresAt<e&&this._map.delete(t);this._lastCleanup=e}get size(){return this.cleanup(),this._map.size}forEach(e){for(let[t,i]of(this.cleanup(),this._map.entries()))i.expiresAt>=Date.now()&&e(i.value,t,this.asValueMap())}map(e){this.cleanup();let t=[],i=this.asValueMap();for(let[n,r]of i.entries())t.push(e(r,n,i));return t}asValueMap(){let e=new Map;for(let[t,i]of this._map.entries())i.expiresAt>=Date.now()&&e.set(t,i.value);return e}}var r7={},r8={},ae={exports:{}};function at(){if(M)return ae.exports;M=1;var e=ae.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=(null!=e.raddr?" raddr %s rport %d":"%v%v")+(null!=e.tcptype?" tcptype %s":"%v"),null!=e.generation&&(t+=" generation %d"),t+=(null!=e["network-id"]?" network-id %d":"%v")+(null!=e["network-cost"]?" network-cost %d":"%v")}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+((null!=e.id?"id=%s %s":"%v%s")+(null!=e.mediaClockValue?"=%s":"")+(null!=e.rateNumerator?" rate=%s":"")+(null!=e.rateDenominator?"/%s":""))}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};return Object.keys(e).forEach(function(t){e[t].forEach(function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")})}),ae.exports}var ai=function(){if(_)return r7;_=1;var e=function(){var e,t,i,n,r,a;return D?r8:(D=1,e=function(e){return String(Number(e))===e?Number(e):e},t=function(t,i,n,r){if(r&&!n)i[r]=e(t[1]);else for(var a=0;a<n.length;a+=1)null!=t[a+1]&&(i[n[a]]=e(t[a+1]))},i=function(e,i,n){var r=e.name&&e.names;e.push&&!i[e.push]?i[e.push]=[]:r&&!i[e.name]&&(i[e.name]={});var a=e.push?{}:r?i[e.name]:i;t(n.match(e.reg),a,e.names,e.name),e.push&&i[e.push].push(a)},n=at(),r=RegExp.prototype.test.bind(/^([a-z])=(.*)/),r8.parse=function(e){var t={},a=[],s=t;return e.split(/(\r\n|\r|\n)/).filter(r).forEach(function(e){var t=e[0],r=e.slice(2);"m"===t&&(a.push({rtp:[],fmtp:[]}),s=a[a.length-1]);for(var o=0;o<(n[t]||[]).length;o+=1){var c=n[t][o];if(c.reg.test(r))return i(c,s,r)}}),t.media=a,t},a=function(t,i){var n=i.split(/=(.+)/,2);return 2===n.length?t[n[0]]=e(n[1]):1===n.length&&i.length>1&&(t[n[0]]=void 0),t},r8.parseParams=function(e){return e.split(/;\s?/).reduce(a,{})},r8.parseFmtpConfig=r8.parseParams,r8.parsePayloads=function(e){return e.toString().split(" ").map(Number)},r8.parseRemoteCandidates=function(t){for(var i=[],n=t.split(" ").map(e),r=0;r<n.length;r+=3)i.push({component:n[r],ip:n[r+1],port:n[r+2]});return i},r8.parseImageAttributes=function(e){return e.split(" ").map(function(e){return e.substring(1,e.length-1).split(",").reduce(a,{})})},r8.parseSimulcastStreamList=function(t){return t.split(";").map(function(t){return t.split(",").map(function(t){var i,n=!1;return"~"!==t[0]?i=e(t):(i=e(t.substring(1,t.length)),n=!0),{scid:i,paused:n}})})},r8)}(),t=function(){if(N)return A;N=1;var e=at(),t=/%[sdv%]/g,i=function(e){var i=1,n=arguments,r=n.length;return e.replace(t,function(e){if(i>=r)return e;var t=n[i];switch(i+=1,e){case"%%":return"%";case"%s":return String(t);case"%d":return Number(t);case"%v":return""}})},n=function(e,t,n){var r=[e+"="+(t.format instanceof Function?t.format(t.push?n:n[t.name]):t.format)];if(t.names)for(var a=0;a<t.names.length;a+=1){var s=t.names[a];t.name?r.push(n[t.name][s]):r.push(n[t.names[a]])}else r.push(n[t.name]);return i.apply(null,r)},r=["v","o","s","i","u","e","p","c","b","t","r","z","a"],a=["i","c","b","a"];return A=function(t,i){i=i||{},null==t.version&&(t.version=0),null==t.name&&(t.name=" "),t.media.forEach(function(e){null==e.payloads&&(e.payloads="")});var s=i.outerOrder||r,o=i.innerOrder||a,c=[];return s.forEach(function(i){e[i].forEach(function(e){e.name in t&&null!=t[e.name]?c.push(n(i,e,t)):e.push in t&&null!=t[e.push]&&t[e.push].forEach(function(t){c.push(n(i,e,t))})})}),t.media.forEach(function(t){c.push(n("m",e.m[0],t)),o.forEach(function(i){e[i].forEach(function(e){e.name in t&&null!=t[e.name]?c.push(n(i,e,t)):e.push in t&&null!=t[e.push]&&t[e.push].forEach(function(t){c.push(n(i,e,t))})})})}),c.join("\r\n")+"\r\n"}}();return r7.grammar=at(),r7.write=t,r7.parse=e.parse,r7.parseParams=e.parseParams,r7.parseFmtpConfig=e.parseFmtpConfig,r7.parsePayloads=e.parsePayloads,r7.parseRemoteCandidates=e.parseRemoteCandidates,r7.parseImageAttributes=e.parseImageAttributes,r7.parseSimulcastStreamList=e.parseSimulcastStreamList,r7}();function an(e,t,i){void 0===t&&(t=50),void 0===i&&(i={});var n,r,a,s=null!=(n=i.isImmediate)&&n,o=null!=(r=i.callback)&&r,c=i.maxWait,l=Date.now(),d=[],u=function(){var i=[].slice.call(arguments),n=this;return new Promise(function(r,u){var h=s&&void 0===a;if(void 0!==a&&clearTimeout(a),a=setTimeout(function(){if(a=void 0,l=Date.now(),!s){var t=e.apply(n,i);o&&o(t),d.forEach(function(e){return(0,e.resolve)(t)}),d=[]}},function(){if(void 0!==c){var e=Date.now()-l;if(e+t>=c)return c-e}return t}()),h){var p=e.apply(n,i);return o&&o(p),r(p)}d.push({resolve:r,reject:u})})};return u.cancel=function(e){void 0!==a&&clearTimeout(a),d.forEach(function(t){return(0,t.reject)(e)}),d=[]},u}let ar={NegotiationStarted:"negotiationStarted",NegotiationComplete:"negotiationComplete",RTPVideoPayloadTypes:"rtpVideoPayloadTypes"};class aa extends iJ.EventEmitter{get pc(){return this._pc||(this._pc=this.createPC()),this._pc}constructor(e){var t;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),this.log=iB,this.ddExtID=0,this.latestOfferId=0,this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate=!1,this.trackBitrates=[],this.remoteStereoMids=[],this.remoteNackMids=[],this.negotiate=an(e=>iz(this,void 0,void 0,function*(){this.emit(ar.NegotiationStarted);try{yield this.createAndSendOffer()}catch(t){if(e)e(t);else throw t}}),20),this.close=()=>{this._pc&&(this._pc.close(),this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc.onicegatheringstatechange=null,this._pc.ondatachannel=null,this._pc.onnegotiationneeded=null,this._pc.onsignalingstatechange=null,this._pc.onicecandidate=null,this._pc.ondatachannel=null,this._pc.ontrack=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null)},this.log=iV(null!=(t=i.loggerName)?t:h.PCTransport),this.loggerOptions=i,this.config=e,this._pc=this.createPC(),this.offerLock=new W}createPC(){let e=new RTCPeerConnection(this.config);return e.onicecandidate=e=>{var t;e.candidate&&(null==(t=this.onIceCandidate)||t.call(this,e.candidate))},e.onicecandidateerror=e=>{var t;null==(t=this.onIceCandidateError)||t.call(this,e)},e.oniceconnectionstatechange=()=>{var t;null==(t=this.onIceConnectionStateChange)||t.call(this,e.iceConnectionState)},e.onsignalingstatechange=()=>{var t;null==(t=this.onSignalingStatechange)||t.call(this,e.signalingState)},e.onconnectionstatechange=()=>{var t;null==(t=this.onConnectionStateChange)||t.call(this,e.connectionState)},e.ondatachannel=e=>{var t;null==(t=this.onDataChannel)||t.call(this,e)},e.ontrack=e=>{var t;null==(t=this.onTrack)||t.call(this,e)},e}get logContext(){var e,t;return Object.assign({},null==(t=(e=this.loggerOptions).loggerContextCb)?void 0:t.call(e))}get isICEConnected(){return null!==this._pc&&("connected"===this.pc.iceConnectionState||"completed"===this.pc.iceConnectionState)}addIceCandidate(e){return iz(this,void 0,void 0,function*(){if(this.pc.remoteDescription&&!this.restartingIce)return this.pc.addIceCandidate(e);this.pendingCandidates.push(e)})}setRemoteDescription(e,t){return iz(this,void 0,void 0,function*(){var i;let n;if("answer"===e.type&&this.latestOfferId>0&&t>0&&t!==this.latestOfferId)return this.log.warn("ignoring answer for old offer",Object.assign(Object.assign({},this.logContext),{offerId:t,latestOfferId:this.latestOfferId})),!1;if("offer"===e.type){let{stereoMids:t,nackMids:i}=function(e){var t;let i=[],n=[],r=ai.parse(null!=(t=e.sdp)?t:""),a=0;return r.media.forEach(e=>{var t;"audio"===e.type&&(e.rtp.some(e=>"opus"===e.codec&&(a=e.payload,!0)),(null==(t=e.rtcpFb)?void 0:t.some(e=>e.payload===a&&"nack"===e.type))&&n.push(e.mid),e.fmtp.some(t=>t.payload===a&&(t.config.includes("sprop-stereo=1")&&i.push(e.mid),!0)))}),{stereoMids:i,nackMids:n}}(e);this.remoteStereoMids=t,this.remoteNackMids=i}else if("answer"===e.type){let t=ai.parse(null!=(i=e.sdp)?i:"");t.media.forEach(e=>{"audio"===e.type&&this.trackBitrates.some(t=>{if(!t.transceiver||e.mid!=t.transceiver.mid)return!1;let i=0;if(e.rtp.some(e=>e.codec.toUpperCase()===t.codec.toUpperCase()&&(i=e.payload,!0)),0===i)return!0;let n=!1;for(let r of e.fmtp)if(r.payload===i){r.config=r.config.split(";").filter(e=>!e.includes("maxaveragebitrate")).join(";"),t.maxbr>0&&(r.config+=";maxaveragebitrate=".concat(1e3*t.maxbr)),n=!0;break}return!n&&t.maxbr>0&&e.fmtp.push({payload:i,config:"maxaveragebitrate=".concat(1e3*t.maxbr)}),!0})}),n=ai.write(t)}return yield this.setMungedSDP(e,n,!0),this.pendingCandidates.forEach(e=>{this.pc.addIceCandidate(e)}),this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate?(this.renegotiate=!1,yield this.createAndSendOffer()):"answer"===e.type&&(this.emit(ar.NegotiationComplete),e.sdp&&ai.parse(e.sdp).media.forEach(e=>{"video"===e.type&&this.emit(ar.RTPVideoPayloadTypes,e.rtp)})),!0})}createAndSendOffer(e){return iz(this,void 0,void 0,function*(){var t;let i=yield this.offerLock.lock();try{if(void 0===this.onOffer)return;if((null==e?void 0:e.iceRestart)&&(this.log.debug("restarting ICE",this.logContext),this.restartingIce=!0),this._pc&&"have-local-offer"===this._pc.signalingState){let t=this._pc.remoteDescription;if((null==e?void 0:e.iceRestart)&&t)yield this._pc.setRemoteDescription(t);else{this.renegotiate=!0;return}}else if(!this._pc||"closed"===this._pc.signalingState)return void this.log.warn("could not createOffer with closed peer connection",this.logContext);this.log.debug("starting to negotiate",this.logContext);let i=this.latestOfferId+1;this.latestOfferId=i;let n=yield this.pc.createOffer(e);this.log.debug("original offer",Object.assign({sdp:n.sdp},this.logContext));let r=ai.parse(null!=(t=n.sdp)?t:"");if(r.media.forEach(e=>{ao(e),"audio"===e.type?as(e,[],[]):"video"===e.type&&this.trackBitrates.some(t=>{if(!e.msid||!t.cid||!e.msid.includes(t.cid))return!1;let i=0;if(e.rtp.some(e=>e.codec.toUpperCase()===t.codec.toUpperCase()&&(i=e.payload,!0)),0===i||(ro(t.codec)&&this.ensureVideoDDExtensionForSVC(e,r),"av1"!==t.codec))return!0;let n=Math.round(.7*t.maxbr);for(let t of e.fmtp)if(t.payload===i){t.config.includes("x-google-start-bitrate")||(t.config+=";x-google-start-bitrate=".concat(n));break}return!0})}),this.latestOfferId>i)return void this.log.warn("latestOfferId mismatch",Object.assign(Object.assign({},this.logContext),{latestOfferId:this.latestOfferId,offerId:i}));yield this.setMungedSDP(n,ai.write(r)),this.onOffer(n,this.latestOfferId)}finally{i()}})}createAndSetAnswer(){return iz(this,void 0,void 0,function*(){var e;let t=yield this.pc.createAnswer(),i=ai.parse(null!=(e=t.sdp)?e:"");return i.media.forEach(e=>{ao(e),"audio"===e.type&&as(e,this.remoteStereoMids,this.remoteNackMids)}),yield this.setMungedSDP(t,ai.write(i)),t})}createDataChannel(e,t){return this.pc.createDataChannel(e,t)}addTransceiver(e,t){return this.pc.addTransceiver(e,t)}addTrack(e){if(!this._pc)throw new nK("PC closed, cannot add track");return this._pc.addTrack(e)}setTrackCodecBitrate(e){this.trackBitrates.push(e)}setConfiguration(e){var t;if(!this._pc)throw new nK("PC closed, cannot configure");return null==(t=this._pc)?void 0:t.setConfiguration(e)}canRemoveTrack(){var e;return!!(null==(e=this._pc)?void 0:e.removeTrack)}removeTrack(e){var t;return null==(t=this._pc)?void 0:t.removeTrack(e)}getConnectionState(){var e,t;return null!=(t=null==(e=this._pc)?void 0:e.connectionState)?t:"closed"}getICEConnectionState(){var e,t;return null!=(t=null==(e=this._pc)?void 0:e.iceConnectionState)?t:"closed"}getSignallingState(){var e,t;return null!=(t=null==(e=this._pc)?void 0:e.signalingState)?t:"closed"}getTransceivers(){var e,t;return null!=(t=null==(e=this._pc)?void 0:e.getTransceivers())?t:[]}getSenders(){var e,t;return null!=(t=null==(e=this._pc)?void 0:e.getSenders())?t:[]}getLocalDescription(){var e;return null==(e=this._pc)?void 0:e.localDescription}getRemoteDescription(){var e;return null==(e=this.pc)?void 0:e.remoteDescription}getStats(){return this.pc.getStats()}getConnectedAddress(){return iz(this,void 0,void 0,function*(){var e;if(!this._pc)return;let t="",i=new Map,n=new Map;if((yield this._pc.getStats()).forEach(e=>{switch(e.type){case"transport":t=e.selectedCandidatePairId;break;case"candidate-pair":""===t&&e.selected&&(t=e.id),i.set(e.id,e);break;case"remote-candidate":n.set(e.id,"".concat(e.address,":").concat(e.port))}}),""===t)return;let r=null==(e=i.get(t))?void 0:e.remoteCandidateId;if(void 0!==r)return n.get(r)})}setMungedSDP(e,t,i){return iz(this,void 0,void 0,function*(){if(t){let n=e.sdp;e.sdp=t;try{this.log.debug("setting munged ".concat(i?"remote":"local"," description"),this.logContext),i?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e);return}catch(i){this.log.warn("not able to set ".concat(e.type,", falling back to unmodified sdp"),Object.assign(Object.assign({},this.logContext),{error:i,sdp:t})),e.sdp=n}}try{i?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e)}catch(r){let t="unknown error";r instanceof Error?t=r.message:"string"==typeof r&&(t=r);let n={error:t,sdp:e.sdp};throw!i&&this.pc.remoteDescription&&(n.remoteSdp=this.pc.remoteDescription),this.log.error("unable to set ".concat(e.type),Object.assign(Object.assign({},this.logContext),{fields:n})),new nJ(t)}})}ensureVideoDDExtensionForSVC(e,t){var i,n;if(!(null==(i=e.ext)?void 0:i.some(e=>e.uri===rn))){if(0===this.ddExtID){let e=0;t.media.forEach(t=>{var i;"video"===t.type&&(null==(i=t.ext)||i.forEach(t=>{t.value>e&&(e=t.value)}))}),this.ddExtID=e+1}null==(n=e.ext)||n.push({value:this.ddExtID,uri:rn})}}}function as(e,t,i){let n=0;e.rtp.some(e=>"opus"===e.codec&&(n=e.payload,!0)),n>0&&(e.rtcpFb||(e.rtcpFb=[]),i.includes(e.mid)&&!e.rtcpFb.some(e=>e.payload===n&&"nack"===e.type)&&e.rtcpFb.push({payload:n,type:"nack"}),t.includes(e.mid)&&e.fmtp.some(e=>e.payload===n&&(e.config.includes("stereo=1")||(e.config+=";stereo=1"),!0)))}function ao(e){if(e.connection){let t=e.connection.ip.indexOf(":")>=0;(4===e.connection.version&&t||6===e.connection.version&&!t)&&(e.connection.ip="0.0.0.0",e.connection.version=4)}}let ac={audioPreset:I.music,dtx:!0,red:!0,forceStereo:!1,simulcast:!0,screenShareEncoding:ri.h1080fps15.encoding,stopMicTrackOnMute:!1,videoCodec:"vp8",backupCodec:!0,preConnectBuffer:!1},al={deviceId:{ideal:"default"},autoGainControl:!0,echoCancellation:!0,noiseSuppression:!0,voiceIsolation:!0},ad={deviceId:{ideal:"default"},resolution:re.h720.resolution},au={adaptiveStream:!1,dynacast:!1,stopLocalTrackOnUnpublish:!0,reconnectPolicy:new iW,disconnectOnPageLeave:!0,webAudioMix:!1},ah={autoSubscribe:!0,maxRetries:1,peerConnectionTimeout:15e3,websocketTimeout:15e3};!function(e){e[e.NEW=0]="NEW",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED",e[e.FAILED=3]="FAILED",e[e.CLOSING=4]="CLOSING",e[e.CLOSED=5]="CLOSED"}(L||(L={}));class ap{get needsPublisher(){return this.isPublisherConnectionRequired}get needsSubscriber(){return this.isSubscriberConnectionRequired}get currentState(){return this.state}constructor(e,t,i){var n;this.peerConnectionTimeout=ah.peerConnectionTimeout,this.log=iB,this.updateState=()=>{var e;let t=this.state,i=this.requiredTransports.map(e=>e.getConnectionState());i.every(e=>"connected"===e)?this.state=L.CONNECTED:i.some(e=>"failed"===e)?this.state=L.FAILED:i.some(e=>"connecting"===e)?this.state=L.CONNECTING:i.every(e=>"closed"===e)?this.state=L.CLOSED:i.some(e=>"closed"===e)?this.state=L.CLOSING:i.every(e=>"new"===e)&&(this.state=L.NEW),t!==this.state&&(this.log.debug("pc state change: from ".concat(L[t]," to ").concat(L[this.state]),this.logContext),null==(e=this.onStateChange)||e.call(this,this.state,this.publisher.getConnectionState(),this.subscriber.getConnectionState()))},this.log=iV(null!=(n=i.loggerName)?n:h.PCManager),this.loggerOptions=i,this.isPublisherConnectionRequired=!t,this.isSubscriberConnectionRequired=t,this.publisher=new aa(e,i),this.subscriber=new aa(e,i),this.publisher.onConnectionStateChange=this.updateState,this.subscriber.onConnectionStateChange=this.updateState,this.publisher.onIceConnectionStateChange=this.updateState,this.subscriber.onIceConnectionStateChange=this.updateState,this.publisher.onSignalingStatechange=this.updateState,this.subscriber.onSignalingStatechange=this.updateState,this.publisher.onIceCandidate=e=>{var t;null==(t=this.onIceCandidate)||t.call(this,e,tQ.PUBLISHER)},this.subscriber.onIceCandidate=e=>{var t;null==(t=this.onIceCandidate)||t.call(this,e,tQ.SUBSCRIBER)},this.subscriber.onDataChannel=e=>{var t;null==(t=this.onDataChannel)||t.call(this,e)},this.subscriber.onTrack=e=>{var t;null==(t=this.onTrack)||t.call(this,e)},this.publisher.onOffer=(e,t)=>{var i;null==(i=this.onPublisherOffer)||i.call(this,e,t)},this.state=L.NEW,this.connectionLock=new W,this.remoteOfferLock=new W}get logContext(){var e,t;return Object.assign({},null==(t=(e=this.loggerOptions).loggerContextCb)?void 0:t.call(e))}requirePublisher(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.isPublisherConnectionRequired=e,this.updateState()}requireSubscriber(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.isSubscriberConnectionRequired=e,this.updateState()}createAndSendPublisherOffer(e){return this.publisher.createAndSendOffer(e)}setPublisherAnswer(e,t){return this.publisher.setRemoteDescription(e,t)}removeTrack(e){return this.publisher.removeTrack(e)}close(){return iz(this,void 0,void 0,function*(){if(this.publisher&&"closed"!==this.publisher.getSignallingState()){let e=this.publisher;for(let t of e.getSenders())try{e.canRemoveTrack()&&e.removeTrack(t)}catch(e){this.log.warn("could not removeTrack",Object.assign(Object.assign({},this.logContext),{error:e}))}}yield Promise.all([this.publisher.close(),this.subscriber.close()]),this.updateState()})}triggerIceRestart(){return iz(this,void 0,void 0,function*(){this.subscriber.restartingIce=!0,this.needsPublisher&&(yield this.createAndSendPublisherOffer({iceRestart:!0}))})}addIceCandidate(e,t){return iz(this,void 0,void 0,function*(){t===tQ.PUBLISHER?yield this.publisher.addIceCandidate(e):yield this.subscriber.addIceCandidate(e)})}createSubscriberAnswerFromOffer(e,t){return iz(this,void 0,void 0,function*(){this.log.debug("received server offer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type,sdp:e.sdp,signalingState:this.subscriber.getSignallingState().toString()}));let i=yield this.remoteOfferLock.lock();try{if(!(yield this.subscriber.setRemoteDescription(e,t)))return;return yield this.subscriber.createAndSetAnswer()}finally{i()}})}updateConfiguration(e,t){this.publisher.setConfiguration(e),this.subscriber.setConfiguration(e),t&&this.triggerIceRestart()}ensurePCTransportConnection(e,t){return iz(this,void 0,void 0,function*(){var i;let n=yield this.connectionLock.lock();try{this.isPublisherConnectionRequired&&"connected"!==this.publisher.getConnectionState()&&"connecting"!==this.publisher.getConnectionState()&&(this.log.debug("negotiation required, start negotiating",this.logContext),this.publisher.negotiate()),yield Promise.all(null==(i=this.requiredTransports)?void 0:i.map(i=>this.ensureTransportConnected(i,e,t)))}finally{n()}})}negotiate(e){return iz(this,void 0,void 0,function*(){return new Promise((t,i)=>iz(this,void 0,void 0,function*(){let n=setTimeout(()=>{i("negotiation timed out")},this.peerConnectionTimeout);e.signal.addEventListener("abort",()=>{clearTimeout(n),i("negotiation aborted")}),this.publisher.once(ar.NegotiationStarted,()=>{e.signal.aborted||this.publisher.once(ar.NegotiationComplete,()=>{clearTimeout(n),t()})}),yield this.publisher.negotiate(e=>{clearTimeout(n),i(e)})}))})}addPublisherTransceiver(e,t){return this.publisher.addTransceiver(e,t)}addPublisherTrack(e){return this.publisher.addTrack(e)}createPublisherDataChannel(e,t){return this.publisher.createDataChannel(e,t)}getConnectedAddress(e){return e===tQ.PUBLISHER||e===tQ.SUBSCRIBER?this.publisher.getConnectedAddress():this.requiredTransports[0].getConnectedAddress()}get requiredTransports(){let e=[];return this.isPublisherConnectionRequired&&e.push(this.publisher),this.isSubscriberConnectionRequired&&e.push(this.subscriber),e}ensureTransportConnected(e,t){return iz(this,arguments,void 0,function(e,t){var i=this;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.peerConnectionTimeout;return function*(){if("connected"!==e.getConnectionState())return new Promise((e,r)=>iz(i,void 0,void 0,function*(){let i=()=>{this.log.warn("abort transport connection",this.logContext),n5.clearTimeout(a),r(new nH("room connection has been cancelled",y.Cancelled))};(null==t?void 0:t.signal.aborted)&&i(),null==t||t.signal.addEventListener("abort",i);let a=n5.setTimeout(()=>{null==t||t.signal.removeEventListener("abort",i),r(new nH("could not establish pc connection",y.InternalError))},n);for(;this.state!==L.CONNECTED;)if(yield rr(50),null==t?void 0:t.signal.aborted)return void r(new nH("room connection has been cancelled",y.Cancelled));n5.clearTimeout(a),null==t||t.signal.removeEventListener("abort",i),e()}))}()})}}class am extends Error{constructor(e,t,i){super(t),this.code=e,this.message=ag(t,am.MAX_MESSAGE_BYTES),this.data=i?ag(i,am.MAX_DATA_BYTES):void 0}static fromProto(e){return new am(e.code,e.message,e.data)}toProto(){return new tN({code:this.code,message:this.message,data:this.data})}static builtIn(e,t){return new am(am.ErrorCode[e],am.ErrorMessage[e],t)}}function af(e){return new TextEncoder().encode(e).length}function ag(e,t){if(af(e)<=t)return e;let i=0,n=e.length,r=new TextEncoder;for(;i<n;){let a=Math.floor((i+n+1)/2);r.encode(e.slice(0,a)).length<=t?i=a:n=a-1}return e.slice(0,i)}function av(e,t){let i,n;return t?("bytesReceived"in e?(i=e.bytesReceived,n=t.bytesReceived):"bytesSent"in e&&(i=e.bytesSent,n=t.bytesSent),void 0===i||void 0===n||void 0===e.timestamp||void 0===t.timestamp)?0:(i-n)*8e3/(e.timestamp-t.timestamp):0}am.MAX_MESSAGE_BYTES=256,am.MAX_DATA_BYTES=15360,am.ErrorCode={APPLICATION_ERROR:1500,CONNECTION_TIMEOUT:1501,RESPONSE_TIMEOUT:1502,RECIPIENT_DISCONNECTED:1503,RESPONSE_PAYLOAD_TOO_LARGE:1504,SEND_FAILED:1505,UNSUPPORTED_METHOD:1400,RECIPIENT_NOT_FOUND:1401,REQUEST_PAYLOAD_TOO_LARGE:1402,UNSUPPORTED_SERVER:1403,UNSUPPORTED_VERSION:1404},am.ErrorMessage={APPLICATION_ERROR:"Application error in method handler",CONNECTION_TIMEOUT:"Connection timeout",RESPONSE_TIMEOUT:"Response timeout",RECIPIENT_DISCONNECTED:"Recipient disconnected",RESPONSE_PAYLOAD_TOO_LARGE:"Response payload too large",SEND_FAILED:"Failed to send",UNSUPPORTED_METHOD:"Method not supported at destination",RECIPIENT_NOT_FOUND:"Recipient not found",REQUEST_PAYLOAD_TOO_LARGE:"Request payload too large",UNSUPPORTED_SERVER:"RPC not supported by server",UNSUPPORTED_VERSION:"Unsupported RPC version"};let ab="undefined"!=typeof MediaRecorder;class ay{constructor(){throw Error("MediaRecorder is not available in this environment")}}let ak=ab?MediaRecorder:ay;class aT extends ak{constructor(e,t){let i,n;if(!ab)throw Error("MediaRecorder is not available in this environment");super(new MediaStream([e.mediaStreamTrack]),t);let r=()=>void 0===n,a=()=>{this.removeEventListener("dataavailable",i),this.removeEventListener("stop",a),this.removeEventListener("error",s),null==n||n.close(),n=void 0},s=e=>{null==n||n.error(e),this.removeEventListener("dataavailable",i),this.removeEventListener("stop",a),this.removeEventListener("error",s),n=void 0};this.byteStream=new ReadableStream({start:e=>{n=e,i=t=>iz(this,void 0,void 0,function*(){let i=yield t.data.arrayBuffer();r()||e.enqueue(new Uint8Array(i))}),this.addEventListener("dataavailable",i)},cancel:()=>{a()}}),this.addEventListener("stop",a),this.addEventListener("error",s)}}class aC extends n3{get sender(){return this._sender}set sender(e){this._sender=e}get constraints(){return this._constraints}get hasPreConnectBuffer(){return!!this.localTrackRecorder}constructor(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0;super(e,t,r),this.manuallyStopped=!1,this._isUpstreamPaused=!1,this.handleTrackMuteEvent=()=>this.debouncedTrackMuteHandler().catch(()=>this.log.debug("track mute bounce got cancelled by an unmute event",this.logContext)),this.debouncedTrackMuteHandler=an(()=>iz(this,void 0,void 0,function*(){yield this.pauseUpstream()}),5e3),this.handleTrackUnmuteEvent=()=>iz(this,void 0,void 0,function*(){this.debouncedTrackMuteHandler.cancel("unmute"),yield this.resumeUpstream()}),this.handleEnded=()=>{this.isInBackground&&(this.reacquireTrack=!0),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),this.emit(E.Ended,this)},this.reacquireTrack=!1,this.providedByUser=n,this.muteLock=new W,this.pauseUpstreamLock=new W,this.processorLock=new W,this.restartLock=new W,this.setMediaStreamTrack(e,!0),this._constraints=e.getConstraints(),i&&(this._constraints=i)}get id(){return this._mediaStreamTrack.id}get dimensions(){if(this.kind!==n3.Kind.Video)return;let{width:e,height:t}=this._mediaStreamTrack.getSettings();if(e&&t)return{width:e,height:t}}get isUpstreamPaused(){return this._isUpstreamPaused}get isUserProvided(){return this.providedByUser}get mediaStreamTrack(){var e,t;return null!=(t=null==(e=this.processor)?void 0:e.processedTrack)?t:this._mediaStreamTrack}get isLocal(){return!0}getSourceTrackSettings(){return this._mediaStreamTrack.getSettings()}setMediaStreamTrack(e,t){return iz(this,void 0,void 0,function*(){var i;let n;if(e!==this._mediaStreamTrack||t){if(this._mediaStreamTrack&&(this.attachedElements.forEach(e=>{n6(this._mediaStreamTrack,e)}),this.debouncedTrackMuteHandler.cancel("new-track"),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent)),this.mediaStream=new MediaStream([e]),e&&(e.addEventListener("ended",this.handleEnded),e.addEventListener("mute",this.handleTrackMuteEvent),e.addEventListener("unmute",this.handleTrackUnmuteEvent),this._constraints=e.getConstraints()),this.processor&&e){let t=yield this.processorLock.lock();try{if(this.log.debug("restarting processor",this.logContext),"unknown"===this.kind)throw TypeError("cannot set processor on track of unknown kind");this.processorElement&&(n9(e,this.processorElement),this.processorElement.muted=!0),yield this.processor.restart({track:e,kind:this.kind,element:this.processorElement}),n=this.processor.processedTrack}finally{t()}}this.sender&&(null==(i=this.sender.transport)?void 0:i.state)!=="closed"&&(yield this.sender.replaceTrack(null!=n?n:e)),this.providedByUser||this._mediaStreamTrack===e||this._mediaStreamTrack.stop(),this._mediaStreamTrack=e,e&&(this._mediaStreamTrack.enabled=!this.isMuted,yield this.resumeUpstream(),this.attachedElements.forEach(t=>{n9(null!=n?n:e,t)}))}})}waitForDimensions(){return iz(this,arguments,void 0,function(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3;return function*(){var i;if(e.kind===n3.Kind.Audio)throw Error("cannot get dimensions for audio tracks");(null==(i=nX())?void 0:i.os)==="iOS"&&(yield rr(10));let n=Date.now();for(;Date.now()-n<t;){let t=e.dimensions;if(t)return t;yield rr(50)}throw new nz("unable to get track dimensions after timeout")}()})}setDeviceId(e){return iz(this,void 0,void 0,function*(){return this._constraints.deviceId===e&&this._mediaStreamTrack.getSettings().deviceId===rx(e)||(this._constraints.deviceId=e,!!this.isMuted||(yield this.restartTrack(),rx(e)===this._mediaStreamTrack.getSettings().deviceId))})}getDeviceId(){return iz(this,arguments,void 0,function(){var e=this;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return function*(){if(e.source===n3.Source.ScreenShare)return;let{deviceId:i,groupId:n}=e._mediaStreamTrack.getSettings(),r=e.kind===n3.Kind.Audio?"audioinput":"videoinput";return t?rZ.getInstance().normalizeDeviceId(r,i,n):i}()})}mute(){return iz(this,void 0,void 0,function*(){return this.setTrackMuted(!0),this})}unmute(){return iz(this,void 0,void 0,function*(){return this.setTrackMuted(!1),this})}replaceTrack(e,t){return iz(this,void 0,void 0,function*(){let i,n;if(!this.sender)throw new nz("unable to replace an unpublished track");return"boolean"==typeof t?i=t:void 0!==t&&(i=t.userProvidedTrack,n=t.stopProcessor),this.providedByUser=null==i||i,this.log.debug("replace MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(e),n&&this.processor&&(yield this.stopProcessor()),this})}restart(e){return iz(this,void 0,void 0,function*(){this.manuallyStopped=!1;let t=yield this.restartLock.lock();try{e||(e=this._constraints);let{deviceId:t,facingMode:i}=e,n=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(i[n[r]]=e[n[r]]);return i}(e,["deviceId","facingMode"]);this.log.debug("restarting track with constraints",Object.assign(Object.assign({},this.logContext),{constraints:e}));let r={audio:!1,video:!1};this.kind===n3.Kind.Video?r.video=!t&&!i||{deviceId:t,facingMode:i}:r.audio=!t||{deviceId:t},this.attachedElements.forEach(e=>{n6(this.mediaStreamTrack,e)}),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.stop();let a=(yield navigator.mediaDevices.getUserMedia(r)).getTracks()[0];return yield a.applyConstraints(n),a.addEventListener("ended",this.handleEnded),this.log.debug("re-acquired MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(a),this._constraints=e,this.emit(E.Restarted,this),this.manuallyStopped&&(this.log.warn("track was stopped during a restart, stopping restarted track",this.logContext),this.stop()),this}finally{t()}})}setTrackMuted(e){this.log.debug("setting ".concat(this.kind," track ").concat(e?"muted":"unmuted"),this.logContext),(this.isMuted!==e||this._mediaStreamTrack.enabled===e)&&(this.isMuted=e,this._mediaStreamTrack.enabled=!e,this.emit(e?E.Muted:E.Unmuted,this))}get needsReAcquisition(){return"live"!==this._mediaStreamTrack.readyState||this._mediaStreamTrack.muted||!this._mediaStreamTrack.enabled||this.reacquireTrack}handleAppVisibilityChanged(){let e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return iz(this,void 0,void 0,function*(){yield e.handleAppVisibilityChanged.call(this),rh()&&(this.log.debug("visibility changed, is in Background: ".concat(this.isInBackground),this.logContext),this.isInBackground||!this.needsReAcquisition||this.isUserProvided||this.isMuted||(this.log.debug("track needs to be reacquired, restarting ".concat(this.source),this.logContext),yield this.restart(),this.reacquireTrack=!1))})}stop(){var e;this.manuallyStopped=!0,super.stop(),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),null==(e=this.processor)||e.destroy(),this.processor=void 0}pauseUpstream(){return iz(this,void 0,void 0,function*(){var e;let t=yield this.pauseUpstreamLock.lock();try{if(!0===this._isUpstreamPaused)return;if(!this.sender)return void this.log.warn("unable to pause upstream for an unpublished track",this.logContext);this._isUpstreamPaused=!0,this.emit(E.UpstreamPaused,this);let t=nX();if((null==t?void 0:t.name)==="Safari"&&0>ry(t.version,"12.0"))throw new nW("pauseUpstream is not supported on Safari < 12.");(null==(e=this.sender.transport)?void 0:e.state)!=="closed"&&(yield this.sender.replaceTrack(null))}finally{t()}})}resumeUpstream(){return iz(this,void 0,void 0,function*(){var e;let t=yield this.pauseUpstreamLock.lock();try{if(!1===this._isUpstreamPaused)return;if(!this.sender)return void this.log.warn("unable to resume upstream for an unpublished track",this.logContext);this._isUpstreamPaused=!1,this.emit(E.UpstreamResumed,this),(null==(e=this.sender.transport)?void 0:e.state)!=="closed"&&(yield this.sender.replaceTrack(this.mediaStreamTrack))}finally{t()}})}getRTCStatsReport(){return iz(this,void 0,void 0,function*(){var e;if(null==(e=this.sender)?void 0:e.getStats)return yield this.sender.getStats()})}setProcessor(e){return iz(this,arguments,void 0,function(e){var t=this;let i=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return function*(){var n;let r=yield t.processorLock.lock();try{t.log.debug("setting up processor",t.logContext);let r=document.createElement(t.kind),a={kind:t.kind,track:t._mediaStreamTrack,element:r,audioContext:t.audioContext};if(yield e.init(a),t.log.debug("processor initialized",t.logContext),t.processor&&(yield t.stopProcessor()),"unknown"===t.kind)throw TypeError("cannot set processor on track of unknown kind");if(n9(t._mediaStreamTrack,r),r.muted=!0,r.play().catch(e=>t.log.error("failed to play processor element",Object.assign(Object.assign({},t.logContext),{error:e}))),t.processor=e,t.processorElement=r,t.processor.processedTrack){for(let e of t.attachedElements)e!==t.processorElement&&i&&(n6(t._mediaStreamTrack,e),n9(t.processor.processedTrack,e));yield null==(n=t.sender)?void 0:n.replaceTrack(t.processor.processedTrack)}t.emit(E.TrackProcessorUpdate,t.processor)}finally{r()}}()})}getProcessor(){return this.processor}stopProcessor(){return iz(this,arguments,void 0,function(){var e=this;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return function*(){var i,n;e.processor&&(e.log.debug("stopping processor",e.logContext),null==(i=e.processor.processedTrack)||i.stop(),yield e.processor.destroy(),e.processor=void 0,t||(null==(n=e.processorElement)||n.remove(),e.processorElement=void 0),yield e._mediaStreamTrack.applyConstraints(e._constraints),yield e.setMediaStreamTrack(e._mediaStreamTrack,!0),e.emit(E.TrackProcessorUpdate))}()})}startPreConnectBuffer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return ab?this.localTrackRecorder?void this.log.warn("preconnect buffer already started"):(this.localTrackRecorder=new aT(this,{mimeType:"audio/webm;codecs=opus"}),void(this.localTrackRecorder.start(e),this.autoStopPreConnectBuffer=setTimeout(()=>{this.log.warn("preconnect buffer timed out, stopping recording automatically",this.logContext),this.stopPreConnectBuffer()},1e4))):void this.log.warn("MediaRecorder is not available, cannot start preconnect buffer",this.logContext)}stopPreConnectBuffer(){clearTimeout(this.autoStopPreConnectBuffer),this.localTrackRecorder&&(this.localTrackRecorder.stop(),this.localTrackRecorder=void 0)}getPreConnectBuffer(){var e;return null==(e=this.localTrackRecorder)?void 0:e.byteStream}}class aS extends aC{get enhancedNoiseCancellation(){return this.isKrispNoiseFilterEnabled}constructor(e,t){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],n=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;super(e,n3.Kind.Audio,t,i,r),this.stopOnMute=!1,this.isKrispNoiseFilterEnabled=!1,this.monitorSender=()=>iz(this,void 0,void 0,function*(){let e;if(!this.sender){this._currentBitrate=0;return}try{e=yield this.getSenderStats()}catch(e){this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:e}));return}e&&this.prevStats&&(this._currentBitrate=av(e,this.prevStats)),this.prevStats=e}),this.handleKrispNoiseFilterEnable=()=>{this.isKrispNoiseFilterEnabled=!0,this.log.debug("Krisp noise filter enabled",this.logContext),this.emit(E.AudioTrackFeatureUpdate,this,td.TF_ENHANCED_NOISE_CANCELLATION,!0)},this.handleKrispNoiseFilterDisable=()=>{this.isKrispNoiseFilterEnabled=!1,this.log.debug("Krisp noise filter disabled",this.logContext),this.emit(E.AudioTrackFeatureUpdate,this,td.TF_ENHANCED_NOISE_CANCELLATION,!1)},this.audioContext=n,this.checkForSilence()}mute(){let e=Object.create(null,{mute:{get:()=>super.mute}});return iz(this,void 0,void 0,function*(){let t=yield this.muteLock.lock();try{if(this.isMuted)return this.log.debug("Track already muted",this.logContext),this;return this.source===n3.Source.Microphone&&this.stopOnMute&&!this.isUserProvided&&(this.log.debug("stopping mic track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this}finally{t()}})}unmute(){let e=Object.create(null,{unmute:{get:()=>super.unmute}});return iz(this,void 0,void 0,function*(){let t=yield this.muteLock.lock();try{if(!this.isMuted)return this.log.debug("Track already unmuted",this.logContext),this;let t=this._constraints.deviceId&&this._mediaStreamTrack.getSettings().deviceId!==rx(this._constraints.deviceId);return this.source===n3.Source.Microphone&&(this.stopOnMute||"ended"===this._mediaStreamTrack.readyState||t)&&!this.isUserProvided&&(this.log.debug("reacquiring mic track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this}finally{t()}})}restartTrack(e){return iz(this,void 0,void 0,function*(){let t;if(e){let i=rH({audio:e});"boolean"!=typeof i.audio&&(t=i.audio)}yield this.restart(t)})}restart(e){let t=Object.create(null,{restart:{get:()=>super.restart}});return iz(this,void 0,void 0,function*(){let i=yield t.restart.call(this,e);return this.checkForSilence(),i})}startMonitor(){rp()&&(this.monitorInterval||(this.monitorInterval=setInterval(()=>{this.monitorSender()},2e3)))}setProcessor(e){return iz(this,void 0,void 0,function*(){var t;let i=yield this.processorLock.lock();try{if(!rm()&&!this.audioContext)throw Error("Audio context needs to be set on LocalAudioTrack in order to enable processors");this.processor&&(yield this.stopProcessor());let i={kind:this.kind,track:this._mediaStreamTrack,audioContext:this.audioContext};this.log.debug("setting up audio processor ".concat(e.name),this.logContext),yield e.init(i),this.processor=e,this.processor.processedTrack&&(yield null==(t=this.sender)?void 0:t.replaceTrack(this.processor.processedTrack),this.processor.processedTrack.addEventListener("enable-lk-krisp-noise-filter",this.handleKrispNoiseFilterEnable),this.processor.processedTrack.addEventListener("disable-lk-krisp-noise-filter",this.handleKrispNoiseFilterDisable)),this.emit(E.TrackProcessorUpdate,this.processor)}finally{i()}})}setAudioContext(e){this.audioContext=e}getSenderStats(){return iz(this,void 0,void 0,function*(){var e;let t;if(null==(e=this.sender)?void 0:e.getStats)return(yield this.sender.getStats()).forEach(e=>{"outbound-rtp"===e.type&&(t={type:"audio",streamId:e.id,packetsSent:e.packetsSent,packetsLost:e.packetsLost,bytesSent:e.bytesSent,timestamp:e.timestamp,roundTripTime:e.roundTripTime,jitter:e.jitter})}),t})}checkForSilence(){return iz(this,void 0,void 0,function*(){let e=yield rW(this);return e&&(this.isMuted||this.log.warn("silence detected on local audio track",this.logContext),this.emit(E.AudioSilenceDetected)),e})}}let aw=Object.values(re),aE=Object.values(rt),aP=Object.values(ri),aR=[re.h180,re.h360],aI=[rt.h180,rt.h360],ax=e=>[{scaleResolutionDownBy:2,fps:e.encoding.maxFramerate}].map(t=>{var i,n;return new n4(Math.floor(e.width/t.scaleResolutionDownBy),Math.floor(e.height/t.scaleResolutionDownBy),Math.max(15e4,Math.floor(e.encoding.maxBitrate/(Math.pow(t.scaleResolutionDownBy,2)*((null!=(i=e.encoding.maxFramerate)?i:30)/(null!=(n=t.fps)?n:30))))),t.fps,e.encoding.priority)}),aO=["q","h","f"];function aM(e,t,i,n){var r,a,s;let o,c=null==n?void 0:n.videoEncoding;e&&(c=null==n?void 0:n.screenShareEncoding);let l=null==n?void 0:n.simulcast,d=null==n?void 0:n.scalabilityMode,u=null==n?void 0:n.videoCodec;if(!c&&!l&&!d||!t||!i)return[{}];c||(c=function(e,t,i,n){let r=function(e,t,i){if(e)return aP;let n=t>i?t/i:i/t;return Math.abs(n-16/9)<Math.abs(n-4/3)?aw:aE}(e,t,i),{encoding:a}=r[0],s=Math.max(t,i);for(let e=0;e<r.length;e+=1){let t=r[e];if(a=t.encoding,t.width>=s)break}if(n)switch(n){case"av1":case"h265":(a=Object.assign({},a)).maxBitrate=.7*a.maxBitrate;break;case"vp9":(a=Object.assign({},a)).maxBitrate=.85*a.maxBitrate}return a}(e,t,i,u),iB.debug("using video encoding",c));let h=c.maxFramerate,p=new n4(t,i,c.maxBitrate,c.maxFramerate,c.priority);if(d&&ro(u)){let e=new a_(d),t=[];if(e.spatial>3)throw Error("unsupported scalabilityMode: ".concat(d));let i=nX();if(ru()||rm()||(null==i?void 0:i.name)==="Chrome"&&0>ry(null==i?void 0:i.version,"113")){let n="h"==e.suffix?2:3,r=((s=i)||(s=nX()),(null==s?void 0:s.name)==="Safari"&&ry(s.version,"18.3")>0||(null==s?void 0:s.os)==="iOS"&&!!(null==s?void 0:s.osVersion)&&ry(s.osVersion,"18.3")>0);for(let i=0;i<e.spatial;i+=1)t.push({rid:aO[2-i],maxBitrate:c.maxBitrate/Math.pow(n,i),maxFramerate:p.encoding.maxFramerate,scaleResolutionDownBy:r?Math.pow(2,i):void 0});t[0].scalabilityMode=d}else t.push({maxBitrate:c.maxBitrate,maxFramerate:p.encoding.maxFramerate,scalabilityMode:d});return p.encoding.priority&&(t[0].priority=p.encoding.priority,t[0].networkPriority=p.encoding.priority),iB.debug("using svc encoding",{encodings:t}),t}if(!l)return[c];let m=[];if((m=e?null!=(r=aN(null==n?void 0:n.screenShareSimulcastLayers))?r:aD(e,p):null!=(a=aN(null==n?void 0:n.videoSimulcastLayers))?a:aD(e,p)).length>0){let e=m[0];m.length>1&&([,o]=m);let n=Math.max(t,i);if(n>=960&&o)return aA(t,i,[e,o,p],h);if(n>=480)return aA(t,i,[e,p],h)}return aA(t,i,[p])}function aD(e,t){if(e)return ax(t);let{width:i,height:n}=t,r=i>n?i/n:n/i;return Math.abs(r-16/9)<Math.abs(r-4/3)?aR:aI}function aA(e,t,i,n){let r=[];if(i.forEach((i,a)=>{if(a>=aO.length)return;let s=Math.min(e,t),o={rid:aO[a],scaleResolutionDownBy:Math.max(1,s/Math.min(i.width,i.height)),maxBitrate:i.encoding.maxBitrate},c=n&&i.encoding.maxFramerate?Math.min(n,i.encoding.maxFramerate):i.encoding.maxFramerate;c&&(o.maxFramerate=c);let l=rl()||0===a;i.encoding.priority&&l&&(o.priority=i.encoding.priority,o.networkPriority=i.encoding.priority),r.push(o)}),rm()&&"ios"===rv()){let e;r.forEach(t=>{e?t.maxFramerate&&t.maxFramerate>e&&(e=t.maxFramerate):e=t.maxFramerate});let t=!0;r.forEach(i=>{var n;i.maxFramerate!=e&&(t&&(t=!1,iB.info("Simulcast on iOS React-Native requires all encodings to share the same framerate.")),iB.info('Setting framerate of encoding "'.concat(null!=(n=i.rid)?n:"",'" to ').concat(e)),i.maxFramerate=e)})}return r}function aN(e){if(e)return e.sort((e,t)=>{let{encoding:i}=e,{encoding:n}=t;return i.maxBitrate>n.maxBitrate?1:i.maxBitrate<n.maxBitrate?-1:i.maxBitrate===n.maxBitrate&&i.maxFramerate&&n.maxFramerate?i.maxFramerate>n.maxFramerate?1:-1:0})}class a_{constructor(e){let t=e.match(/^L(\d)T(\d)(h|_KEY|_KEY_SHIFT){0,1}$/);if(!t)throw Error("invalid scalability mode");if(this.spatial=parseInt(t[1]),this.temporal=parseInt(t[2]),t.length>3)switch(t[3]){case"h":case"_KEY":case"_KEY_SHIFT":this.suffix=t[3]}}toString(){var e;return"L".concat(this.spatial,"T").concat(this.temporal).concat(null!=(e=this.suffix)?e:"")}}class aL extends aC{get sender(){return this._sender}set sender(e){this._sender=e,this.degradationPreference&&this.setDegradationPreference(this.degradationPreference)}constructor(e,t){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],n=arguments.length>3?arguments[3]:void 0;super(e,n3.Kind.Video,t,i,n),this.simulcastCodecs=new Map,this.degradationPreference="balanced",this.isCpuConstrained=!1,this.optimizeForPerformance=!1,this.monitorSender=()=>iz(this,void 0,void 0,function*(){let e;if(!this.sender){this._currentBitrate=0;return}try{e=yield this.getSenderStats()}catch(e){this.log.error("could not get video sender stats",Object.assign(Object.assign({},this.logContext),{error:e}));return}let t=new Map(e.map(e=>[e.rid,e])),i=e.some(e=>"cpu"===e.qualityLimitationReason);if(i!==this.isCpuConstrained&&(this.isCpuConstrained=i,this.isCpuConstrained&&this.emit(E.CpuConstrained)),this.prevStats){let e=0;t.forEach((t,i)=>{var n;let r=null==(n=this.prevStats)?void 0:n.get(i);e+=av(t,r)}),this._currentBitrate=e}this.prevStats=t}),this.senderLock=new W}get isSimulcast(){return!!this.sender&&!!(this.sender.getParameters().encodings.length>1)}startMonitor(e){var t;if(this.signalClient=e,!rp())return;let i=null==(t=this.sender)?void 0:t.getParameters();i&&(this.encodings=i.encodings),this.monitorInterval||(this.monitorInterval=setInterval(()=>{this.monitorSender()},2e3))}stop(){this._mediaStreamTrack.getConstraints(),this.simulcastCodecs.forEach(e=>{e.mediaStreamTrack.stop()}),super.stop()}pauseUpstream(){let e=Object.create(null,{pauseUpstream:{get:()=>super.pauseUpstream}});return iz(this,void 0,void 0,function*(){yield e.pauseUpstream.call(this);try{for(var t,i,n,r,a,s,o=!0,c=iG(this.simulcastCodecs.values());!(t=(s=yield c.next()).done);o=!0)r=s.value,o=!1,yield null==(a=r.sender)?void 0:a.replaceTrack(null)}catch(e){i={error:e}}finally{try{!o&&!t&&(n=c.return)&&(yield n.call(c))}finally{if(i)throw i.error}}})}resumeUpstream(){let e=Object.create(null,{resumeUpstream:{get:()=>super.resumeUpstream}});return iz(this,void 0,void 0,function*(){yield e.resumeUpstream.call(this);try{for(var t,i,n,r,a,s,o=!0,c=iG(this.simulcastCodecs.values());!(t=(s=yield c.next()).done);o=!0)r=s.value,o=!1,yield null==(a=r.sender)?void 0:a.replaceTrack(r.mediaStreamTrack)}catch(e){i={error:e}}finally{try{!o&&!t&&(n=c.return)&&(yield n.call(c))}finally{if(i)throw i.error}}})}mute(){let e=Object.create(null,{mute:{get:()=>super.mute}});return iz(this,void 0,void 0,function*(){let t=yield this.muteLock.lock();try{if(this.isMuted)return this.log.debug("Track already muted",this.logContext),this;return this.source!==n3.Source.Camera||this.isUserProvided||(this.log.debug("stopping camera track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this}finally{t()}})}unmute(){let e=Object.create(null,{unmute:{get:()=>super.unmute}});return iz(this,void 0,void 0,function*(){let t=yield this.muteLock.lock();try{if(!this.isMuted)return this.log.debug("Track already unmuted",this.logContext),this;return this.source!==n3.Source.Camera||this.isUserProvided||(this.log.debug("reacquiring camera track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this}finally{t()}})}setTrackMuted(e){for(let t of(super.setTrackMuted(e),this.simulcastCodecs.values()))t.mediaStreamTrack.enabled=!e}getSenderStats(){return iz(this,void 0,void 0,function*(){var e;if(!(null==(e=this.sender)?void 0:e.getStats))return[];let t=[],i=yield this.sender.getStats();return i.forEach(e=>{var n;if("outbound-rtp"===e.type){let r={type:"video",streamId:e.id,frameHeight:e.frameHeight,frameWidth:e.frameWidth,framesPerSecond:e.framesPerSecond,framesSent:e.framesSent,firCount:e.firCount,pliCount:e.pliCount,nackCount:e.nackCount,packetsSent:e.packetsSent,bytesSent:e.bytesSent,qualityLimitationReason:e.qualityLimitationReason,qualityLimitationDurations:e.qualityLimitationDurations,qualityLimitationResolutionChanges:e.qualityLimitationResolutionChanges,rid:null!=(n=e.rid)?n:e.id,retransmittedPacketsSent:e.retransmittedPacketsSent,targetBitrate:e.targetBitrate,timestamp:e.timestamp},a=i.get(e.remoteId);a&&(r.jitter=a.jitter,r.packetsLost=a.packetsLost,r.roundTripTime=a.roundTripTime),t.push(r)}}),t.sort((e,t)=>{var i,n;return(null!=(i=t.frameWidth)?i:0)-(null!=(n=e.frameWidth)?n:0)}),t})}setPublishingQuality(e){let t=[];for(let i=P.LOW;i<=P.HIGH;i+=1)t.push(new ib({quality:i,enabled:i<=e}));this.log.debug("setting publishing quality. max quality ".concat(e),this.logContext),this.setPublishingLayers(ro(this.codec),t)}restartTrack(e){return iz(this,void 0,void 0,function*(){let t;if(e){let i=rH({video:e});"boolean"!=typeof i.video&&(t=i.video)}yield this.restart(t),this.isCpuConstrained=!1;try{for(var i,n,r,a,s,o,c=!0,l=iG(this.simulcastCodecs.values());!(i=(o=yield l.next()).done);c=!0)a=o.value,c=!1,a.sender&&(null==(s=a.sender.transport)?void 0:s.state)!=="closed"&&(a.mediaStreamTrack=this.mediaStreamTrack.clone(),yield a.sender.replaceTrack(a.mediaStreamTrack))}catch(e){n={error:e}}finally{try{!c&&!i&&(r=l.return)&&(yield r.call(l))}finally{if(n)throw n.error}}})}setProcessor(e){let t=Object.create(null,{setProcessor:{get:()=>super.setProcessor}});return iz(this,arguments,void 0,function(e){var i=this;let n=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return function*(){if(yield t.setProcessor.call(i,e,n),null==(c=i.processor)?void 0:c.processedTrack)try{for(var r,a,s,o,c,l,d,u=!0,h=iG(i.simulcastCodecs.values());!(r=(d=yield h.next()).done);u=!0)o=d.value,u=!1,yield null==(l=o.sender)?void 0:l.replaceTrack(i.processor.processedTrack)}catch(e){a={error:e}}finally{try{!u&&!r&&(s=h.return)&&(yield s.call(h))}finally{if(a)throw a.error}}}()})}setDegradationPreference(e){return iz(this,void 0,void 0,function*(){if(this.degradationPreference=e,this.sender)try{this.log.debug("setting degradationPreference to ".concat(e),this.logContext);let t=this.sender.getParameters();t.degradationPreference=e,this.sender.setParameters(t)}catch(e){this.log.warn("failed to set degradationPreference",Object.assign({error:e},this.logContext))}})}addSimulcastTrack(e,t){if(this.simulcastCodecs.has(e))return void this.log.error("".concat(e," already added, skipping adding simulcast codec"),this.logContext);let i={codec:e,mediaStreamTrack:this.mediaStreamTrack.clone(),sender:void 0,encodings:t};return this.simulcastCodecs.set(e,i),i}setSimulcastTrackSender(e,t){let i=this.simulcastCodecs.get(e);i&&(i.sender=t,setTimeout(()=>{this.subscribedCodecs&&this.setPublishingCodecs(this.subscribedCodecs)},5e3))}setPublishingCodecs(e){return iz(this,void 0,void 0,function*(){var t,i,n,r,a,s,o;if(this.log.debug("setting publishing codecs",Object.assign(Object.assign({},this.logContext),{codecs:e,currentCodec:this.codec})),!this.codec&&e.length>0)return yield this.setPublishingLayers(ro(e[0].codec),e[0].qualities),[];this.subscribedCodecs=e;let c=[];try{for(t=!0,i=iG(e);!(r=(n=yield i.next()).done);t=!0)if(o=n.value,t=!1,this.codec&&this.codec!==o.codec){let e=this.simulcastCodecs.get(o.codec);if(this.log.debug("try setPublishingCodec for ".concat(o.codec),Object.assign(Object.assign({},this.logContext),{simulcastCodecInfo:e})),e&&e.sender)e.encodings&&(this.log.debug("try setPublishingLayersForSender ".concat(o.codec),this.logContext),yield aU(e.sender,e.encodings,o.qualities,this.senderLock,ro(o.codec),this.log,this.logContext));else for(let e of o.qualities)if(e.enabled){c.push(o.codec);break}}else yield this.setPublishingLayers(ro(o.codec),o.qualities)}catch(e){a={error:e}}finally{try{!t&&!r&&(s=i.return)&&(yield s.call(i))}finally{if(a)throw a.error}}return c})}setPublishingLayers(e,t){return iz(this,void 0,void 0,function*(){if(this.optimizeForPerformance)return void this.log.info("skipping setPublishingLayers due to optimized publishing performance",Object.assign(Object.assign({},this.logContext),{qualities:t}));this.log.debug("setting publishing layers",Object.assign(Object.assign({},this.logContext),{qualities:t})),this.sender&&this.encodings&&(yield aU(this.sender,this.encodings,t,this.senderLock,e,this.log,this.logContext))})}prioritizePerformance(){return iz(this,void 0,void 0,function*(){if(!this.sender)throw Error("sender not found");let e=yield this.senderLock.lock();try{this.optimizeForPerformance=!0;let e=this.sender.getParameters();e.encodings=e.encodings.map((e,t)=>{var i;return Object.assign(Object.assign({},e),{active:0===t,scaleResolutionDownBy:Math.max(1,Math.ceil((null!=(i=this.mediaStreamTrack.getSettings().height)?i:360)/360)),scalabilityMode:0===t&&ro(this.codec)?"L1T3":void 0,maxFramerate:15*(0===t),maxBitrate:0===t?e.maxBitrate:0})}),this.log.debug("setting performance optimised encodings",Object.assign(Object.assign({},this.logContext),{encodings:e.encodings})),this.encodings=e.encodings,yield this.sender.setParameters(e)}catch(e){this.log.error("failed to set performance optimised encodings",Object.assign(Object.assign({},this.logContext),{error:e})),this.optimizeForPerformance=!1}finally{e()}})}handleAppVisibilityChanged(){let e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return iz(this,void 0,void 0,function*(){yield e.handleAppVisibilityChanged.call(this),rh()&&this.isInBackground&&this.source===n3.Source.Camera&&(this._mediaStreamTrack.enabled=!1)})}}function aU(e,t,i,n,r,a,s){return iz(this,void 0,void 0,function*(){let o=yield n.lock();a.debug("setPublishingLayersForSender",Object.assign(Object.assign({},s),{sender:e,qualities:i,senderEncodings:t}));try{let n=e.getParameters(),{encodings:o}=n;if(!o)return;if(o.length!==t.length)return void a.warn("cannot set publishing layers, encodings mismatch",Object.assign(Object.assign({},s),{encodings:o,senderEncodings:t}));let c=!1;r&&i.some(e=>e.enabled)&&i.forEach(e=>e.enabled=!0),o.forEach((e,n)=>{var r;let o=null!=(r=e.rid)?r:"";""===o&&(o="q");let l=aj(o),d=i.find(e=>e.quality===l);d&&e.active!==d.enabled&&(c=!0,e.active=d.enabled,a.debug("setting layer ".concat(d.quality," to ").concat(e.active?"enabled":"disabled"),s),rl()&&(d.enabled?(e.scaleResolutionDownBy=t[n].scaleResolutionDownBy,e.maxBitrate=t[n].maxBitrate,e.maxFrameRate=t[n].maxFrameRate):(e.scaleResolutionDownBy=4,e.maxBitrate=10,e.maxFrameRate=2)))}),c&&(n.encodings=o,a.debug("setting encodings",Object.assign(Object.assign({},s),{encodings:n.encodings})),yield e.setParameters(n))}finally{o()}})}function aj(e){switch(e){case"f":default:return P.HIGH;case"h":return P.MEDIUM;case"q":return P.LOW}}function aF(e,t,i,n){if(!i)return[new tT({quality:P.HIGH,width:e,height:t,bitrate:0,ssrc:0})];if(n){let n=new a_(i[0].scalabilityMode),r=[],a="h"==n.suffix?1.5:2,s="h"==n.suffix?2:3;for(let o=0;o<n.spatial;o+=1)r.push(new tT({quality:Math.min(P.HIGH,n.spatial-1)-o,width:Math.ceil(e/Math.pow(a,o)),height:Math.ceil(t/Math.pow(a,o)),bitrate:i[0].maxBitrate?Math.ceil(i[0].maxBitrate/Math.pow(s,o)):0,ssrc:0}));return r}return i.map(i=>{var n,r,a;let s=null!=(n=i.scaleResolutionDownBy)?n:1;return new tT({quality:aj(null!=(r=i.rid)?r:""),width:Math.ceil(e/s),height:Math.ceil(t/s),bitrate:null!=(a=i.maxBitrate)?a:0,ssrc:0})})}let aB="_lossy",aV="_reliable",aq="leave-reconnect";!function(e){e[e.New=0]="New",e[e.Connected=1]="Connected",e[e.Disconnected=2]="Disconnected",e[e.Reconnecting=3]="Reconnecting",e[e.Closed=4]="Closed"}(U||(U={}));class aH extends iJ.EventEmitter{get isClosed(){return this._isClosed}get pendingReconnect(){return!!this.reconnectTimeout}constructor(e){var t;super(),this.options=e,this.rtcConfig={},this.peerConnectionTimeout=ah.peerConnectionTimeout,this.fullReconnectOnNext=!1,this.latestRemoteOfferId=0,this.subscriberPrimary=!1,this.pcState=U.New,this._isClosed=!0,this.pendingTrackResolvers={},this.reconnectAttempts=0,this.reconnectStart=0,this.attemptingReconnect=!1,this.joinAttempts=0,this.maxJoinAttempts=1,this.shouldFailNext=!1,this.log=iB,this.reliableDataSequence=1,this.reliableMessageBuffer=new r6,this.reliableReceivedState=new r4(3e4),this.handleDataChannel=e=>iz(this,[e],void 0,function(e){var t=this;let{channel:i}=e;return function*(){if(i){if(i.label===aV)t.reliableDCSub=i;else{if(i.label!==aB)return;t.lossyDCSub=i}t.log.debug("on data channel ".concat(i.id,", ").concat(i.label),t.logContext),i.onmessage=t.handleDataMessage}}()}),this.handleDataMessage=e=>iz(this,void 0,void 0,function*(){var t,i;let n=yield this.dataProcessLock.lock();try{let n;if(e.data instanceof ArrayBuffer)n=e.data;else{if(!(e.data instanceof Blob))return void this.log.error("unsupported data type",Object.assign(Object.assign({},this.logContext),{data:e.data}));n=yield e.data.arrayBuffer()}let r=tC.fromBinary(new Uint8Array(n));if(r.sequence>0&&""!==r.participantSid){let e=this.reliableReceivedState.get(r.participantSid);if(e&&r.sequence<=e)return;this.reliableReceivedState.set(r.participantSid,r.sequence)}(null==(t=r.value)?void 0:t.case)==="speaker"?this.emit(w.ActiveSpeakersUpdate,r.value.value.speakers):((null==(i=r.value)?void 0:i.case)==="user"&&function(e,t){let i=e.participantIdentity?e.participantIdentity:t.participantIdentity;e.participantIdentity=i,t.participantIdentity=i;let n=0!==e.destinationIdentities.length?e.destinationIdentities:t.destinationIdentities;e.destinationIdentities=n,t.destinationIdentities=n}(r,r.value.value),this.emit(w.DataPacketReceived,r))}finally{n()}}),this.handleDataError=e=>{let t=0===e.currentTarget.maxRetransmits?"lossy":"reliable";if(e instanceof ErrorEvent&&e.error){let{error:i}=e.error;this.log.error("DataChannel error on ".concat(t,": ").concat(e.message),Object.assign(Object.assign({},this.logContext),{error:i}))}else this.log.error("Unknown DataChannel error on ".concat(t),Object.assign(Object.assign({},this.logContext),{event:e}))},this.handleBufferedAmountLow=e=>{let t=0===e.currentTarget.maxRetransmits?tS.LOSSY:tS.RELIABLE;this.updateAndEmitDCBufferStatus(t)},this.handleDisconnect=(e,t)=>{if(this._isClosed)return;this.log.warn("".concat(e," disconnected"),this.logContext),0===this.reconnectAttempts&&(this.reconnectStart=Date.now());let i=Date.now()-this.reconnectStart,n=this.getNextRetryDelay({elapsedMs:i,retryCount:this.reconnectAttempts});if(null===n)return void(this.log.warn("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(i,"ms. giving up"),this.logContext),this.emit(w.Disconnected),this.close());e===aq&&(n=0),this.log.debug("reconnecting in ".concat(n,"ms"),this.logContext),this.clearReconnectTimeout(),this.token&&this.regionUrlProvider&&this.regionUrlProvider.updateToken(this.token),this.reconnectTimeout=n5.setTimeout(()=>this.attemptReconnect(t).finally(()=>this.reconnectTimeout=void 0),n)},this.waitForRestarted=()=>new Promise((e,t)=>{this.pcState===U.Connected&&e();let i=()=>{this.off(w.Disconnected,n),e()},n=()=>{this.off(w.Restarted,i),t()};this.once(w.Restarted,i),this.once(w.Disconnected,n)}),this.updateAndEmitDCBufferStatus=e=>{let t=this.isBufferStatusLow(e);void 0!==t&&t!==this.dcBufferStatus.get(e)&&(this.dcBufferStatus.set(e,t),this.emit(w.DCBufferStatusChanged,t,e))},this.isBufferStatusLow=e=>{let t=this.dataChannelForKind(e);if(t)return e===tS.RELIABLE&&this.reliableMessageBuffer.alignBufferedAmount(t.bufferedAmount),t.bufferedAmount<=t.bufferedAmountLowThreshold},this.handleBrowserOnLine=()=>{this.client.currentState===O.RECONNECTING&&(this.clearReconnectTimeout(),this.attemptReconnect(tc.RR_SIGNAL_DISCONNECTED))},this.log=iV(null!=(t=e.loggerName)?t:h.Engine),this.loggerOptions={loggerName:e.loggerName,loggerContextCb:()=>this.logContext},this.client=new r2(void 0,this.loggerOptions),this.client.signalLatency=this.options.expSignalLatency,this.reconnectPolicy=this.options.reconnectPolicy,this.registerOnLineListener(),this.closingLock=new W,this.dataProcessLock=new W,this.dcBufferStatus=new Map([[tS.LOSSY,!0],[tS.RELIABLE,!0]]),this.client.onParticipantUpdate=e=>this.emit(w.ParticipantUpdate,e),this.client.onConnectionQuality=e=>this.emit(w.ConnectionQualityUpdate,e),this.client.onRoomUpdate=e=>this.emit(w.RoomUpdate,e),this.client.onSubscriptionError=e=>this.emit(w.SubscriptionError,e),this.client.onSubscriptionPermissionUpdate=e=>this.emit(w.SubscriptionPermissionUpdate,e),this.client.onSpeakersChanged=e=>this.emit(w.SpeakersChanged,e),this.client.onStreamStateUpdate=e=>this.emit(w.StreamStateChanged,e),this.client.onRequestResponse=e=>this.emit(w.SignalRequestResponse,e)}get logContext(){var e,t,i,n,r,a;return{room:null==(t=null==(e=this.latestJoinResponse)?void 0:e.room)?void 0:t.name,roomID:null==(n=null==(i=this.latestJoinResponse)?void 0:i.room)?void 0:n.sid,participant:null==(a=null==(r=this.latestJoinResponse)?void 0:r.participant)?void 0:a.identity,pID:this.participantSid}}join(e,t,i,n){return iz(this,void 0,void 0,function*(){this.url=e,this.token=t,this.signalOpts=i,this.maxJoinAttempts=i.maxRetries;try{this.joinAttempts+=1,this.setupSignalClientCallbacks();let r=yield this.client.join(e,t,i,n);return this._isClosed=!1,this.latestJoinResponse=r,this.subscriberPrimary=r.subscriberPrimary,this.pcManager||(yield this.configure(r)),(!this.subscriberPrimary||r.fastPublish)&&this.negotiate(),this.clientConfiguration=r.clientConfiguration,this.emit(w.SignalConnected,r),r}catch(r){if(r instanceof nH&&r.reason===y.ServerUnreachable&&(this.log.warn("Couldn't connect to server, attempt ".concat(this.joinAttempts," of ").concat(this.maxJoinAttempts),this.logContext),this.joinAttempts<this.maxJoinAttempts))return this.join(e,t,i,n);throw r}})}close(){return iz(this,void 0,void 0,function*(){let e=yield this.closingLock.lock();if(this.isClosed)return void e();try{this._isClosed=!0,this.joinAttempts=0,this.emit(w.Closing),this.removeAllListeners(),this.deregisterOnLineListener(),this.clearPendingReconnect(),yield this.cleanupPeerConnections(),yield this.cleanupClient()}finally{e()}})}cleanupPeerConnections(){return iz(this,void 0,void 0,function*(){var e;yield null==(e=this.pcManager)?void 0:e.close(),this.pcManager=void 0;let t=e=>{e&&(e.close(),e.onbufferedamountlow=null,e.onclose=null,e.onclosing=null,e.onerror=null,e.onmessage=null,e.onopen=null)};t(this.lossyDC),t(this.lossyDCSub),t(this.reliableDC),t(this.reliableDCSub),this.lossyDC=void 0,this.lossyDCSub=void 0,this.reliableDC=void 0,this.reliableDCSub=void 0,this.reliableMessageBuffer=new r6,this.reliableDataSequence=1,this.reliableReceivedState.clear()})}cleanupClient(){return iz(this,void 0,void 0,function*(){yield this.client.close(),this.client.resetCallbacks()})}addTrack(e){if(this.pendingTrackResolvers[e.cid])throw new nz("a track with the same ID has already been published");return new Promise((t,i)=>{let n=setTimeout(()=>{delete this.pendingTrackResolvers[e.cid],i(new nH("publication of local track timed out, no response from server",y.Timeout))},1e4);this.pendingTrackResolvers[e.cid]={resolve:e=>{clearTimeout(n),t(e)},reject:()=>{clearTimeout(n),i(Error("Cancelled publication by calling unpublish"))}},this.client.sendAddTrack(e)})}removeTrack(e){if(e.track&&this.pendingTrackResolvers[e.track.id]){let{reject:t}=this.pendingTrackResolvers[e.track.id];t&&t(),delete this.pendingTrackResolvers[e.track.id]}try{return this.pcManager.removeTrack(e),!0}catch(e){this.log.warn("failed to remove track",Object.assign(Object.assign({},this.logContext),{error:e}))}return!1}updateMuteStatus(e,t){this.client.sendMuteTrack(e,t)}get dataSubscriberReadyState(){var e;return null==(e=this.reliableDCSub)?void 0:e.readyState}getConnectedServerAddress(){return iz(this,void 0,void 0,function*(){var e;return null==(e=this.pcManager)?void 0:e.getConnectedAddress()})}setRegionUrlProvider(e){this.regionUrlProvider=e}configure(e){return iz(this,void 0,void 0,function*(){var t,i,n;if(this.pcManager&&this.pcManager.currentState!==L.NEW)return;this.participantSid=null==(t=e.participant)?void 0:t.sid;let r=this.makeRTCConfiguration(e);this.pcManager=new ap(r,e.subscriberPrimary,this.loggerOptions),this.emit(w.TransportsCreated,this.pcManager.publisher,this.pcManager.subscriber),this.pcManager.onIceCandidate=(e,t)=>{this.client.sendIceCandidate(e,t)},this.pcManager.onPublisherOffer=(e,t)=>{this.client.sendOffer(e,t)},this.pcManager.onDataChannel=this.handleDataChannel,this.pcManager.onStateChange=(t,i,n)=>iz(this,void 0,void 0,function*(){if(this.log.debug("primary PC state changed ".concat(t),this.logContext),["closed","disconnected","failed"].includes(i)&&(this.publisherConnectionPromise=void 0),t===L.CONNECTED){let t=this.pcState===U.New;this.pcState=U.Connected,t&&this.emit(w.Connected,e)}else t===L.FAILED&&this.pcState===U.Connected&&(this.pcState=U.Disconnected,this.handleDisconnect("peerconnection failed","failed"===n?tc.RR_SUBSCRIBER_FAILED:tc.RR_PUBLISHER_FAILED));let r=this.client.isDisconnected||this.client.currentState===O.RECONNECTING,a=[L.FAILED,L.CLOSING,L.CLOSED].includes(t);r&&a&&!this._isClosed&&this.emit(w.Offline)}),this.pcManager.onTrack=e=>{this.emit(w.MediaTrackAdded,e.track,e.streams[0],e.receiver)},void 0!==(n=null==(i=e.serverInfo)?void 0:i.protocol)&&n>13||this.createDataChannels()})}setupSignalClientCallbacks(){this.client.onAnswer=(e,t)=>iz(this,void 0,void 0,function*(){this.pcManager&&(this.log.debug("received server answer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type})),yield this.pcManager.setPublisherAnswer(e,t))}),this.client.onTrickle=(e,t)=>{this.pcManager&&(this.log.debug("got ICE candidate from peer",Object.assign(Object.assign({},this.logContext),{candidate:e,target:t})),this.pcManager.addIceCandidate(e,t))},this.client.onOffer=(e,t)=>iz(this,void 0,void 0,function*(){if(this.latestRemoteOfferId=t,!this.pcManager)return;let i=yield this.pcManager.createSubscriberAnswerFromOffer(e,t);i&&this.client.sendAnswer(i,t)}),this.client.onLocalTrackPublished=e=>{var t;if(this.log.debug("received trackPublishedResponse",Object.assign(Object.assign({},this.logContext),{cid:e.cid,track:null==(t=e.track)?void 0:t.sid})),!this.pendingTrackResolvers[e.cid])return void this.log.error("missing track resolver for ".concat(e.cid),Object.assign(Object.assign({},this.logContext),{cid:e.cid}));let{resolve:i}=this.pendingTrackResolvers[e.cid];delete this.pendingTrackResolvers[e.cid],i(e.track)},this.client.onLocalTrackUnpublished=e=>{this.emit(w.LocalTrackUnpublished,e)},this.client.onLocalTrackSubscribed=e=>{this.emit(w.LocalTrackSubscribed,e)},this.client.onTokenRefresh=e=>{this.token=e},this.client.onRemoteMuteChanged=(e,t)=>{this.emit(w.RemoteMute,e,t)},this.client.onSubscribedQualityUpdate=e=>{this.emit(w.SubscribedQualityUpdate,e)},this.client.onRoomMoved=e=>{var t;this.participantSid=null==(t=e.participant)?void 0:t.sid,this.latestJoinResponse&&(this.latestJoinResponse.room=e.room),this.emit(w.RoomMoved,e)},this.client.onClose=()=>{this.handleDisconnect("signal",tc.RR_SIGNAL_DISCONNECTED)},this.client.onLeave=e=>{switch(this.log.debug("client leave request",Object.assign(Object.assign({},this.logContext),{reason:null==e?void 0:e.reason})),e.regions&&this.regionUrlProvider&&(this.log.debug("updating regions",this.logContext),this.regionUrlProvider.setServerReportedRegions(e.regions)),e.action){case io.DISCONNECT:this.emit(w.Disconnected,null==e?void 0:e.reason),this.close();break;case io.RECONNECT:this.fullReconnectOnNext=!0,this.handleDisconnect(aq);break;case io.RESUME:this.handleDisconnect(aq)}}}makeRTCConfiguration(e){var t;let i=Object.assign({},this.rtcConfig);if((null==(t=this.signalOpts)?void 0:t.e2eeEnabled)&&(this.log.debug("E2EE - setting up transports with insertable streams",this.logContext),i.encodedInsertableStreams=!0),e.iceServers&&!i.iceServers){let t=[];e.iceServers.forEach(e=>{let i={urls:e.urls};e.username&&(i.username=e.username),e.credential&&(i.credential=e.credential),t.push(i)}),i.iceServers=t}return e.clientConfiguration&&e.clientConfiguration.forceRelay===ts.ENABLED&&(i.iceTransportPolicy="relay"),i.sdpSemantics="unified-plan",i.continualGatheringPolicy="gather_continually",i}createDataChannels(){this.pcManager&&(this.lossyDC&&(this.lossyDC.onmessage=null,this.lossyDC.onerror=null),this.reliableDC&&(this.reliableDC.onmessage=null,this.reliableDC.onerror=null),this.lossyDC=this.pcManager.createPublisherDataChannel(aB,{ordered:!1,maxRetransmits:0}),this.reliableDC=this.pcManager.createPublisherDataChannel(aV,{ordered:!0}),this.lossyDC.onmessage=this.handleDataMessage,this.reliableDC.onmessage=this.handleDataMessage,this.lossyDC.onerror=this.handleDataError,this.reliableDC.onerror=this.handleDataError,this.lossyDC.bufferedAmountLowThreshold=65535,this.reliableDC.bufferedAmountLowThreshold=65535,this.lossyDC.onbufferedamountlow=this.handleBufferedAmountLow,this.reliableDC.onbufferedamountlow=this.handleBufferedAmountLow)}createSender(e,t,i){return iz(this,void 0,void 0,function*(){if(ra())return yield this.createTransceiverRTCRtpSender(e,t,i);if(rs())return this.log.warn("using add-track fallback",this.logContext),yield this.createRTCRtpSender(e.mediaStreamTrack);throw new nK("Required webRTC APIs not supported on this device")})}createSimulcastSender(e,t,i,n){return iz(this,void 0,void 0,function*(){if(ra())return this.createSimulcastTransceiverSender(e,t,i,n);if(rs())return this.log.debug("using add-track fallback",this.logContext),this.createRTCRtpSender(e.mediaStreamTrack);throw new nK("Cannot stream on this device")})}createTransceiverRTCRtpSender(e,t,i){return iz(this,void 0,void 0,function*(){if(!this.pcManager)throw new nK("publisher is closed");let n=[];e.mediaStream&&n.push(e.mediaStream),rL(e)&&(e.codec=t.videoCodec);let r={direction:"sendonly",streams:n};return i&&(r.sendEncodings=i),(yield this.pcManager.addPublisherTransceiver(e.mediaStreamTrack,r)).sender})}createSimulcastTransceiverSender(e,t,i,n){return iz(this,void 0,void 0,function*(){if(!this.pcManager)throw new nK("publisher is closed");let r={direction:"sendonly"};n&&(r.sendEncodings=n);let a=yield this.pcManager.addPublisherTransceiver(t.mediaStreamTrack,r);if(i.videoCodec)return e.setSimulcastTrackSender(i.videoCodec,a.sender),a.sender})}createRTCRtpSender(e){return iz(this,void 0,void 0,function*(){if(!this.pcManager)throw new nK("publisher is closed");return this.pcManager.addPublisherTrack(e)})}attemptReconnect(e){return iz(this,void 0,void 0,function*(){var t,i,n;if(!this._isClosed){if(this.attemptingReconnect)return void iB.warn("already attempting reconnect, returning early",this.logContext);((null==(t=this.clientConfiguration)?void 0:t.resumeConnection)===ts.DISABLED||(null!=(n=null==(i=this.pcManager)?void 0:i.currentState)?n:L.NEW)===L.NEW)&&(this.fullReconnectOnNext=!0);try{this.attemptingReconnect=!0,this.fullReconnectOnNext?yield this.restartConnection():yield this.resumeConnection(e),this.clearPendingReconnect(),this.fullReconnectOnNext=!1}catch(t){this.reconnectAttempts+=1;let e=!0;t instanceof nK?(this.log.debug("received unrecoverable error",Object.assign(Object.assign({},this.logContext),{error:t})),e=!1):t instanceof aW||(this.fullReconnectOnNext=!0),e?this.handleDisconnect("reconnect",tc.RR_UNKNOWN):(this.log.info("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(Date.now()-this.reconnectStart,"ms. giving up"),this.logContext),this.emit(w.Disconnected),yield this.close())}finally{this.attemptingReconnect=!1}}})}getNextRetryDelay(e){try{return this.reconnectPolicy.nextRetryDelayInMs(e)}catch(e){this.log.warn("encountered error in reconnect policy",Object.assign(Object.assign({},this.logContext),{error:e}))}return null}restartConnection(e){return iz(this,void 0,void 0,function*(){var t,i,n;try{let i;if(!this.url||!this.token)throw new nK("could not reconnect, url or token not saved");this.log.info("reconnecting, attempt: ".concat(this.reconnectAttempts),this.logContext),this.emit(w.Restarting),this.client.isDisconnected||(yield this.client.sendLeave()),yield this.cleanupPeerConnections(),yield this.cleanupClient();try{if(!this.signalOpts)throw this.log.warn("attempted connection restart, without signal options present",this.logContext),new aW;i=yield this.join(null!=e?e:this.url,this.token,this.signalOpts)}catch(e){if(e instanceof nH&&e.reason===y.NotAllowed)throw new nK("could not reconnect, token might be expired");throw new aW}if(this.shouldFailNext)throw this.shouldFailNext=!1,Error("simulated failure");if(this.client.setReconnected(),this.emit(w.SignalRestarted,i),yield this.waitForPCReconnected(),this.client.currentState!==O.CONNECTED)throw new aW("Signal connection got severed during reconnect");null==(t=this.regionUrlProvider)||t.resetAttempts(),this.emit(w.Restarted)}catch(t){let e=yield null==(i=this.regionUrlProvider)?void 0:i.getNextBestRegionUrl();if(e)return void(yield this.restartConnection(e));throw null==(n=this.regionUrlProvider)||n.resetAttempts(),t}})}resumeConnection(e){return iz(this,void 0,void 0,function*(){var t;let i;if(!this.url||!this.token)throw new nK("could not reconnect, url or token not saved");if(!this.pcManager)throw new nK("publisher and subscriber connections unset");this.log.info("resuming signal connection, attempt ".concat(this.reconnectAttempts),this.logContext),this.emit(w.Resuming);try{this.setupSignalClientCallbacks(),i=yield this.client.reconnect(this.url,this.token,this.participantSid,e)}catch(t){let e="";if(t instanceof Error&&(e=t.message,this.log.error(t.message,Object.assign(Object.assign({},this.logContext),{error:t}))),t instanceof nH&&t.reason===y.NotAllowed)throw new nK("could not reconnect, token might be expired");if(t instanceof nH&&t.reason===y.LeaveRequest)throw t;throw new aW(e)}if(this.emit(w.SignalResumed),i){let e=this.makeRTCConfiguration(i);this.pcManager.updateConfiguration(e),this.latestJoinResponse&&(this.latestJoinResponse.serverInfo=i.serverInfo)}else this.log.warn("Did not receive reconnect response",this.logContext);if(this.shouldFailNext)throw this.shouldFailNext=!1,Error("simulated failure");if(yield this.pcManager.triggerIceRestart(),yield this.waitForPCReconnected(),this.client.currentState!==O.CONNECTED)throw new aW("Signal connection got severed during reconnect");this.client.setReconnected(),(null==(t=this.reliableDC)?void 0:t.readyState)==="open"&&null===this.reliableDC.id&&this.createDataChannels(),(null==i?void 0:i.lastMessageSeq)&&this.resendReliableMessagesForResume(i.lastMessageSeq),this.emit(w.Resumed)})}waitForPCInitialConnection(e,t){return iz(this,void 0,void 0,function*(){if(!this.pcManager)throw new nK("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(t,e)})}waitForPCReconnected(){return iz(this,void 0,void 0,function*(){this.pcState=U.Reconnecting,this.log.debug("waiting for peer connection to reconnect",this.logContext);try{if(yield rr(2e3),!this.pcManager)throw new nK("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(void 0,this.peerConnectionTimeout),this.pcState=U.Connected}catch(e){throw this.pcState=U.Disconnected,new nH("could not establish PC connection, ".concat(e.message),y.InternalError)}})}publishRpcResponse(e,t,i,n){return iz(this,void 0,void 0,function*(){let r=new tC({destinationIdentities:[e],kind:tS.RELIABLE,value:{case:"rpcResponse",value:new tA({requestId:t,value:n?{case:"error",value:n.toProto()}:{case:"payload",value:null!=i?i:""}})}});yield this.sendDataPacket(r,tS.RELIABLE)})}publishRpcAck(e,t){return iz(this,void 0,void 0,function*(){let i=new tC({destinationIdentities:[e],kind:tS.RELIABLE,value:{case:"rpcAck",value:new tD({requestId:t})}});yield this.sendDataPacket(i,tS.RELIABLE)})}sendDataPacket(e,t){return iz(this,void 0,void 0,function*(){yield this.ensurePublisherConnected(t),t===tS.RELIABLE&&(e.sequence=this.reliableDataSequence,this.reliableDataSequence+=1);let i=e.toBinary(),n=this.dataChannelForKind(t);if(n){if(t===tS.RELIABLE&&this.reliableMessageBuffer.push({data:i,sequence:e.sequence}),this.attemptingReconnect)return;n.send(i)}this.updateAndEmitDCBufferStatus(t)})}resendReliableMessagesForResume(e){return iz(this,void 0,void 0,function*(){yield this.ensurePublisherConnected(tS.RELIABLE);let t=this.dataChannelForKind(tS.RELIABLE);t&&(this.reliableMessageBuffer.popToSequence(e),this.reliableMessageBuffer.getAll().forEach(e=>{t.send(e.data)})),this.updateAndEmitDCBufferStatus(tS.RELIABLE)})}waitForBufferStatusLow(e){return new Promise((t,i)=>iz(this,void 0,void 0,function*(){if(this.isBufferStatusLow(e))t();else{let n=()=>i("Engine closed");for(this.once(w.Closing,n);!this.dcBufferStatus.get(e);)yield rr(10);this.off(w.Closing,n),t()}}))}ensureDataTransportConnected(e){return iz(this,arguments,void 0,function(e){var t=this;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.subscriberPrimary;return function*(){var n;if(!t.pcManager)throw new nK("PC manager is closed");let r=i?t.pcManager.subscriber:t.pcManager.publisher,a=i?"Subscriber":"Publisher";if(!r)throw new nH("".concat(a," connection not set"),y.InternalError);let s=!1;i||t.dataChannelForKind(e,i)||(t.createDataChannels(),s=!0),s||i||t.pcManager.publisher.isICEConnected||"checking"===t.pcManager.publisher.getICEConnectionState()||(s=!0),s&&t.negotiate();let o=t.dataChannelForKind(e,i);if((null==o?void 0:o.readyState)==="open")return;let c=new Date().getTime()+t.peerConnectionTimeout;for(;new Date().getTime()<c;){if(r.isICEConnected&&(null==(n=t.dataChannelForKind(e,i))?void 0:n.readyState)==="open")return;yield rr(50)}throw new nH("could not establish ".concat(a," connection, state: ").concat(r.getICEConnectionState()),y.InternalError)}()})}ensurePublisherConnected(e){return iz(this,void 0,void 0,function*(){this.publisherConnectionPromise||(this.publisherConnectionPromise=this.ensureDataTransportConnected(e,!1)),yield this.publisherConnectionPromise})}verifyTransport(){return!!this.pcManager&&this.pcManager.currentState===L.CONNECTED&&!!this.client.ws&&this.client.ws.readyState!==WebSocket.CLOSED}negotiate(){return iz(this,void 0,void 0,function*(){return new Promise((e,t)=>iz(this,void 0,void 0,function*(){if(!this.pcManager)return void t(new nJ("PC manager is closed"));this.pcManager.requirePublisher(),0!=this.pcManager.publisher.getTransceivers().length||this.lossyDC||this.reliableDC||this.createDataChannels();let i=new AbortController,n=()=>{i.abort(),this.log.debug("engine disconnected while negotiation was ongoing",this.logContext),e()};this.isClosed&&t("cannot negotiate on closed engine"),this.on(w.Closing,n),this.pcManager.publisher.once(ar.RTPVideoPayloadTypes,e=>{let t=new Map;e.forEach(e=>{let i=e.codec.toLowerCase();n8.includes(i)&&t.set(e.payload,i)}),this.emit(w.RTPVideoMapUpdate,t)});try{yield this.pcManager.negotiate(i),e()}catch(e){e instanceof nJ&&(this.fullReconnectOnNext=!0),this.handleDisconnect("negotiation",tc.RR_UNKNOWN),t(e)}finally{this.off(w.Closing,n)}}))})}dataChannelForKind(e,t){if(t){if(e===tS.LOSSY)return this.lossyDCSub;if(e===tS.RELIABLE)return this.reliableDCSub}else{if(e===tS.LOSSY)return this.lossyDC;if(e===tS.RELIABLE)return this.reliableDC}}sendSyncState(e,t){var i,n;if(!this.pcManager)return void this.log.warn("sync state cannot be sent without peer connection setup",this.logContext);let r=this.pcManager.subscriber.getLocalDescription(),a=this.pcManager.subscriber.getRemoteDescription(),s=null==(n=null==(i=this.signalOpts)?void 0:i.autoSubscribe)||n,o=[],c=[];e.forEach(e=>{e.isDesired!==s&&o.push(e.trackSid),e.isEnabled||c.push(e.trackSid)}),this.client.sendSyncState(new iE({answer:r?r9({sdp:r.sdp,type:r.type}):void 0,offer:a?r9({sdp:a.sdp,type:a.type}):void 0,subscription:new it({trackSids:o,subscribe:!s,participantTracks:[]}),publishTracks:function(e){let t=[];return e.forEach(e=>{void 0!==e.track&&t.push(new t4({cid:e.track.mediaStreamID,track:e.trackInfo}))}),t}(t),dataChannels:this.dataChannelsInfo(),trackSidsDisabled:c,datachannelReceiveStates:this.reliableReceivedState.map((e,t)=>new iP({publisherSid:t,lastSeq:e}))}))}failNext(){this.shouldFailNext=!0}dataChannelsInfo(){let e=[],t=(t,i)=>{(null==t?void 0:t.id)!==void 0&&null!==t.id&&e.push(new iR({label:t.label,id:t.id,target:i}))};return t(this.dataChannelForKind(tS.LOSSY),tQ.PUBLISHER),t(this.dataChannelForKind(tS.RELIABLE),tQ.PUBLISHER),t(this.dataChannelForKind(tS.LOSSY,!0),tQ.SUBSCRIBER),t(this.dataChannelForKind(tS.RELIABLE,!0),tQ.SUBSCRIBER),e}clearReconnectTimeout(){this.reconnectTimeout&&n5.clearTimeout(this.reconnectTimeout)}clearPendingReconnect(){this.clearReconnectTimeout(),this.reconnectAttempts=0}registerOnLineListener(){rp()&&window.addEventListener("online",this.handleBrowserOnLine)}deregisterOnLineListener(){rp()&&window.removeEventListener("online",this.handleBrowserOnLine)}}class aW extends Error{}class az{constructor(e,t){this.lastUpdateAt=0,this.settingsCacheTime=3e3,this.attemptedRegions=[],this.serverUrl=new URL(e),this.token=t}updateToken(e){this.token=e}isCloud(){return rf(this.serverUrl)}getServerUrl(){return this.serverUrl}getNextBestRegionUrl(e){return iz(this,void 0,void 0,function*(){if(!this.isCloud())throw Error("region availability is only supported for LiveKit Cloud domains");(!this.regionSettings||Date.now()-this.lastUpdateAt>this.settingsCacheTime)&&(this.regionSettings=yield this.fetchRegionSettings(e));let t=this.regionSettings.regions.filter(e=>!this.attemptedRegions.find(t=>t.url===e.url));if(!(t.length>0))return null;{let e=t[0];return this.attemptedRegions.push(e),iB.debug("next region: ".concat(e.region)),e.url}})}resetAttempts(){this.attemptedRegions=[]}fetchRegionSettings(e){return iz(this,void 0,void 0,function*(){var t;let i=yield fetch("".concat((t=this.serverUrl,"".concat(t.protocol.replace("ws","http"),"//").concat(t.host,"/settings")),"/regions"),{headers:{authorization:"Bearer ".concat(this.token)},signal:e});if(i.ok){let e=yield i.json();return this.lastUpdateAt=Date.now(),e}throw new nH("Could not fetch region settings: ".concat(i.statusText),401===i.status?y.NotAllowed:y.InternalError,i.status)})}setServerReportedRegions(e){this.regionSettings=e,this.lastUpdateAt=Date.now()}}class aG{get info(){return this._info}constructor(e,t,i){this.reader=t,this.totalByteSize=i,this._info=e,this.bytesReceived=0}}class aK extends aG{handleChunkReceived(e){var t;this.bytesReceived+=e.content.byteLength;let i=this.totalByteSize?this.bytesReceived/this.totalByteSize:void 0;null==(t=this.onProgress)||t.call(this,i)}[Symbol.asyncIterator](){let e=this.reader.getReader();return{next:()=>iz(this,void 0,void 0,function*(){try{let{done:t,value:i}=yield e.read();if(t)return{done:!0,value:void 0};return this.handleChunkReceived(i),{done:!1,value:i.content}}catch(e){return{done:!0,value:void 0}}}),return(){return iz(this,void 0,void 0,function*(){return e.releaseLock(),{done:!0,value:void 0}})}}}readAll(){return iz(this,void 0,void 0,function*(){var e,t,i,n;let r=new Set;try{for(var a,s=!0,o=iG(this);!(e=(a=yield o.next()).done);s=!0)n=a.value,s=!1,r.add(n)}catch(e){t={error:e}}finally{try{!s&&!e&&(i=o.return)&&(yield i.call(o))}finally{if(t)throw t.error}}return Array.from(r)})}}class aJ extends aG{constructor(e,t,i){super(e,t,i),this.receivedChunks=new Map}handleChunkReceived(e){var t;let i=rD(e.chunkIndex),n=this.receivedChunks.get(i);if(n&&n.version>e.version)return;this.receivedChunks.set(i,e),this.bytesReceived+=e.content.byteLength;let r=this.totalByteSize?this.bytesReceived/this.totalByteSize:void 0;null==(t=this.onProgress)||t.call(this,r)}[Symbol.asyncIterator](){let e=this.reader.getReader(),t=new TextDecoder;return{next:()=>iz(this,void 0,void 0,function*(){try{let{done:i,value:n}=yield e.read();if(i)return{done:!0,value:void 0};return this.handleChunkReceived(n),{done:!1,value:t.decode(n.content)}}catch(e){return{done:!0,value:void 0}}}),return(){return iz(this,void 0,void 0,function*(){return e.releaseLock(),{done:!0,value:void 0}})}}}readAll(){return iz(this,void 0,void 0,function*(){var e,t,i,n;let r="";try{for(var a,s=!0,o=iG(this);!(e=(a=yield o.next()).done);s=!0)n=a.value,s=!1,r+=n}catch(e){t={error:e}}finally{try{!s&&!e&&(i=o.return)&&(yield i.call(o))}finally{if(t)throw t.error}}return r})}}class aY{constructor(e,t,i){this.writableStream=e,this.defaultWriter=e.getWriter(),this.onClose=i,this.info=t}write(e){return this.defaultWriter.write(e)}close(){return iz(this,void 0,void 0,function*(){var e;yield this.defaultWriter.close(),this.defaultWriter.releaseLock(),null==(e=this.onClose)||e.call(this)})}}class aQ extends aY{}class a$ extends aY{}class aX extends n3{constructor(e,t,i,n,r){super(e,i,r),this.sid=t,this.receiver=n}get isLocal(){return!1}setMuted(e){this.isMuted!==e&&(this.isMuted=e,this._mediaStreamTrack.enabled=!e,this.emit(e?E.Muted:E.Unmuted,this))}setMediaStream(e){this.mediaStream=e;let t=i=>{i.track===this._mediaStreamTrack&&(e.removeEventListener("removetrack",t),this.receiver&&"playoutDelayHint"in this.receiver&&(this.receiver.playoutDelayHint=void 0),this.receiver=void 0,this._currentBitrate=0,this.emit(E.Ended,this))};e.addEventListener("removetrack",t)}start(){this.startMonitor(),super.enable()}stop(){this.stopMonitor(),super.disable()}getRTCStatsReport(){return iz(this,void 0,void 0,function*(){var e;if(null==(e=this.receiver)?void 0:e.getStats)return yield this.receiver.getStats()})}setPlayoutDelay(e){this.receiver?"playoutDelayHint"in this.receiver?this.receiver.playoutDelayHint=e:this.log.warn("Playout delay not supported in this browser"):this.log.warn("Cannot set playout delay, track already ended")}getPlayoutDelay(){if(this.receiver)if("playoutDelayHint"in this.receiver)return this.receiver.playoutDelayHint;else this.log.warn("Playout delay not supported in this browser");else this.log.warn("Cannot get playout delay, track already ended");return 0}startMonitor(){this.monitorInterval||(this.monitorInterval=setInterval(()=>this.monitorReceiver(),2e3)),"undefined"!=typeof RTCRtpReceiver&&"getSynchronizationSources"in RTCRtpReceiver&&this.registerTimeSyncUpdate()}registerTimeSyncUpdate(){let e=()=>{var t;this.timeSyncHandle=requestAnimationFrame(()=>e());let i=null==(t=this.receiver)?void 0:t.getSynchronizationSources()[0];if(i){let{timestamp:e,rtpTimestamp:t}=i;t&&this.rtpTimestamp!==t&&(this.emit(E.TimeSyncUpdate,{timestamp:e,rtpTimestamp:t}),this.rtpTimestamp=t)}};e()}}class aZ extends aX{constructor(e,t,i,n,r,a){super(e,t,n3.Kind.Audio,i,a),this.monitorReceiver=()=>iz(this,void 0,void 0,function*(){if(!this.receiver){this._currentBitrate=0;return}let e=yield this.getReceiverStats();e&&this.prevStats&&this.receiver&&(this._currentBitrate=av(e,this.prevStats)),this.prevStats=e}),this.audioContext=n,this.webAudioPluginNodes=[],r&&(this.sinkId=r.deviceId)}setVolume(e){var t;for(let i of this.attachedElements)this.audioContext?null==(t=this.gainNode)||t.gain.setTargetAtTime(e,0,.1):i.volume=e;rm()&&this._mediaStreamTrack._setVolume(e),this.elementVolume=e}getVolume(){if(this.elementVolume)return this.elementVolume;if(rm())return 1;let e=0;return this.attachedElements.forEach(t=>{t.volume>e&&(e=t.volume)}),e}setSinkId(e){return iz(this,void 0,void 0,function*(){this.sinkId=e,yield Promise.all(this.attachedElements.map(t=>{if(rc(t))return t.setSinkId(e)}))})}attach(e){let t=0===this.attachedElements.length;return e?super.attach(e):e=super.attach(),this.sinkId&&rc(e)&&e.setSinkId(this.sinkId).catch(e=>{this.log.error("Failed to set sink id on remote audio track",e,this.logContext)}),this.audioContext&&t&&(this.log.debug("using audio context mapping",this.logContext),this.connectWebAudio(this.audioContext,e),e.volume=0,e.muted=!0),this.elementVolume&&this.setVolume(this.elementVolume),e}detach(e){let t;return e?(t=super.detach(e),this.audioContext&&(this.attachedElements.length>0?this.connectWebAudio(this.audioContext,this.attachedElements[0]):this.disconnectWebAudio())):(t=super.detach(),this.disconnectWebAudio()),t}setAudioContext(e){this.audioContext=e,e&&this.attachedElements.length>0?this.connectWebAudio(e,this.attachedElements[0]):e||this.disconnectWebAudio()}setWebAudioPlugins(e){this.webAudioPluginNodes=e,this.attachedElements.length>0&&this.audioContext&&this.connectWebAudio(this.audioContext,this.attachedElements[0])}connectWebAudio(e,t){this.disconnectWebAudio(),this.sourceNode=e.createMediaStreamSource(t.srcObject);let i=this.sourceNode;this.webAudioPluginNodes.forEach(e=>{i.connect(e),i=e}),this.gainNode=e.createGain(),i.connect(this.gainNode),this.gainNode.connect(e.destination),this.elementVolume&&this.gainNode.gain.setTargetAtTime(this.elementVolume,0,.1),"running"!==e.state&&e.resume().then(()=>{"running"!==e.state&&this.emit(E.AudioPlaybackFailed,Error("Audio Context couldn't be started automatically"))}).catch(e=>{this.emit(E.AudioPlaybackFailed,e)})}disconnectWebAudio(){var e,t;null==(e=this.gainNode)||e.disconnect(),null==(t=this.sourceNode)||t.disconnect(),this.gainNode=void 0,this.sourceNode=void 0}getReceiverStats(){return iz(this,void 0,void 0,function*(){let e;if(this.receiver&&this.receiver.getStats)return(yield this.receiver.getStats()).forEach(t=>{"inbound-rtp"===t.type&&(e={type:"audio",streamId:t.id,timestamp:t.timestamp,jitter:t.jitter,bytesReceived:t.bytesReceived,concealedSamples:t.concealedSamples,concealmentEvents:t.concealmentEvents,silentConcealedSamples:t.silentConcealedSamples,silentConcealmentEvents:t.silentConcealmentEvents,totalAudioEnergy:t.totalAudioEnergy,totalSamplesDuration:t.totalSamplesDuration})}),e})}}class a0 extends aX{constructor(e,t,i,n,r){super(e,t,n3.Kind.Video,i,r),this.elementInfos=[],this.monitorReceiver=()=>iz(this,void 0,void 0,function*(){if(!this.receiver){this._currentBitrate=0;return}let e=yield this.getReceiverStats();e&&this.prevStats&&this.receiver&&(this._currentBitrate=av(e,this.prevStats)),this.prevStats=e}),this.debouncedHandleResize=an(()=>{this.updateDimensions()},100),this.adaptiveStreamSettings=n}get isAdaptiveStream(){return void 0!==this.adaptiveStreamSettings}get mediaStreamTrack(){return this._mediaStreamTrack}setMuted(e){super.setMuted(e),this.attachedElements.forEach(t=>{e?n6(this._mediaStreamTrack,t):n9(this._mediaStreamTrack,t)})}attach(e){if(e?super.attach(e):e=super.attach(),this.adaptiveStreamSettings&&void 0===this.elementInfos.find(t=>t.element===e)){let t=new a1(e);this.observeElementInfo(t)}return e}observeElementInfo(e){this.adaptiveStreamSettings&&void 0===this.elementInfos.find(t=>t===e)?(e.handleResize=()=>{this.debouncedHandleResize()},e.handleVisibilityChanged=()=>{this.updateVisibility()},this.elementInfos.push(e),e.observe(),this.debouncedHandleResize(),this.updateVisibility()):this.log.warn("visibility resize observer not triggered",this.logContext)}stopObservingElementInfo(e){if(!this.isAdaptiveStream)return void this.log.warn("stopObservingElementInfo ignored",this.logContext);for(let t of this.elementInfos.filter(t=>t===e))t.stopObserving();this.elementInfos=this.elementInfos.filter(t=>t!==e),this.updateVisibility(),this.debouncedHandleResize()}detach(e){let t=[];if(e)return this.stopObservingElement(e),super.detach(e);for(let e of t=super.detach())this.stopObservingElement(e);return t}getDecoderImplementation(){var e;return null==(e=this.prevStats)?void 0:e.decoderImplementation}getReceiverStats(){return iz(this,void 0,void 0,function*(){let e;if(!this.receiver||!this.receiver.getStats)return;let t=yield this.receiver.getStats(),i="",n=new Map;return t.forEach(t=>{"inbound-rtp"===t.type?(i=t.codecId,e={type:"video",streamId:t.id,framesDecoded:t.framesDecoded,framesDropped:t.framesDropped,framesReceived:t.framesReceived,packetsReceived:t.packetsReceived,packetsLost:t.packetsLost,frameWidth:t.frameWidth,frameHeight:t.frameHeight,pliCount:t.pliCount,firCount:t.firCount,nackCount:t.nackCount,jitter:t.jitter,timestamp:t.timestamp,bytesReceived:t.bytesReceived,decoderImplementation:t.decoderImplementation}):"codec"===t.type&&n.set(t.id,t)}),e&&""!==i&&n.get(i)&&(e.mimeType=n.get(i).mimeType),e})}stopObservingElement(e){for(let t of this.elementInfos.filter(t=>t.element===e))this.stopObservingElementInfo(t)}handleAppVisibilityChanged(){let e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return iz(this,void 0,void 0,function*(){yield e.handleAppVisibilityChanged.call(this),this.isAdaptiveStream&&this.updateVisibility()})}updateVisibility(){var e,t;let i=this.elementInfos.reduce((e,t)=>Math.max(e,t.visibilityChangedAt||0),0),n=(null==(t=null==(e=this.adaptiveStreamSettings)?void 0:e.pauseVideoInBackground)||!!t)&&this.isInBackground,r=this.elementInfos.some(e=>e.pictureInPicture),a=this.elementInfos.some(e=>e.visible)&&!n||r;if(this.lastVisible!==a){if(!a&&Date.now()-i<100)return void n5.setTimeout(()=>{this.updateVisibility()},100);this.lastVisible=a,this.emit(E.VisibilityChanged,a,this)}}updateDimensions(){var e,t;let i=0,n=0,r=this.getPixelDensity();for(let e of this.elementInfos){let t=e.width()*r,a=e.height()*r;t+a>i+n&&(i=t,n=a)}((null==(e=this.lastDimensions)?void 0:e.width)!==i||(null==(t=this.lastDimensions)?void 0:t.height)!==n)&&(this.lastDimensions={width:i,height:n},this.emit(E.VideoDimensionsChanged,this.lastDimensions,this))}getPixelDensity(){var e;let t=null==(e=this.adaptiveStreamSettings)?void 0:e.pixelDensity;return"screen"===t?rb():t?t:rb()>2?2:1}}class a1{get visible(){return this.isPiP||this.isIntersecting}get pictureInPicture(){return this.isPiP}constructor(e,t){this.onVisibilityChanged=e=>{var t;let{target:i,isIntersecting:n}=e;i===this.element&&(this.isIntersecting=n,this.isPiP=a5(this.element),this.visibilityChangedAt=Date.now(),null==(t=this.handleVisibilityChanged)||t.call(this))},this.onEnterPiP=()=>{var e,t,i;null==(t=null==(e=window.documentPictureInPicture)?void 0:e.window)||t.addEventListener("pagehide",this.onLeavePiP),this.isPiP=a5(this.element),null==(i=this.handleVisibilityChanged)||i.call(this)},this.onLeavePiP=()=>{var e;this.isPiP=a5(this.element),null==(e=this.handleVisibilityChanged)||e.call(this)},this.element=e,this.isIntersecting=null!=t?t:a2(e),this.isPiP=rp()&&a5(e),this.visibilityChangedAt=0}width(){return this.element.clientWidth}height(){return this.element.clientHeight}observe(){var e,t,i;this.isIntersecting=a2(this.element),this.isPiP=a5(this.element),this.element.handleResize=()=>{var e;null==(e=this.handleResize)||e.call(this)},this.element.handleVisibilityChanged=this.onVisibilityChanged,rE().observe(this.element),rS().observe(this.element),this.element.addEventListener("enterpictureinpicture",this.onEnterPiP),this.element.addEventListener("leavepictureinpicture",this.onLeavePiP),null==(e=window.documentPictureInPicture)||e.addEventListener("enter",this.onEnterPiP),null==(i=null==(t=window.documentPictureInPicture)?void 0:t.window)||i.addEventListener("pagehide",this.onLeavePiP)}stopObserving(){var e,t,i,n,r;null==(e=rE())||e.unobserve(this.element),null==(t=rS())||t.unobserve(this.element),this.element.removeEventListener("enterpictureinpicture",this.onEnterPiP),this.element.removeEventListener("leavepictureinpicture",this.onLeavePiP),null==(i=window.documentPictureInPicture)||i.removeEventListener("enter",this.onEnterPiP),null==(r=null==(n=window.documentPictureInPicture)?void 0:n.window)||r.removeEventListener("pagehide",this.onLeavePiP)}}function a5(e){var t,i;return document.pictureInPictureElement===e||null!=(t=window.documentPictureInPicture)&&!!t.window&&a2(e,null==(i=window.documentPictureInPicture)?void 0:i.window)}function a2(e,t){let i=t||window,n=e.offsetTop,r=e.offsetLeft,a=e.offsetWidth,s=e.offsetHeight,{hidden:o}=e,{display:c}=getComputedStyle(e);for(;e.offsetParent;)n+=(e=e.offsetParent).offsetTop,r+=e.offsetLeft;return n<i.pageYOffset+i.innerHeight&&r<i.pageXOffset+i.innerWidth&&n+s>i.pageYOffset&&r+a>i.pageXOffset&&!o&&"none"!==c}class a3 extends iJ.EventEmitter{constructor(e,t,i,n){var r;super(),this.metadataMuted=!1,this.encryption=tb.NONE,this.log=iB,this.handleMuted=()=>{this.emit(E.Muted)},this.handleUnmuted=()=>{this.emit(E.Unmuted)},this.log=iV(null!=(r=null==n?void 0:n.loggerName)?r:h.Publication),this.loggerContextCb=this.loggerContextCb,this.setMaxListeners(100),this.kind=e,this.trackSid=t,this.trackName=i,this.source=n3.Source.Unknown}setTrack(e){this.track&&(this.track.off(E.Muted,this.handleMuted),this.track.off(E.Unmuted,this.handleUnmuted)),this.track=e,e&&(e.on(E.Muted,this.handleMuted),e.on(E.Unmuted,this.handleUnmuted))}get logContext(){var e;return Object.assign(Object.assign({},null==(e=this.loggerContextCb)?void 0:e.call(this)),rJ(this))}get isMuted(){return this.metadataMuted}get isEnabled(){return!0}get isSubscribed(){return void 0!==this.track}get isEncrypted(){return this.encryption!==tb.NONE}get audioTrack(){if(r_(this.track))return this.track}get videoTrack(){if(rL(this.track))return this.track}updateInfo(e){this.trackSid=e.sid,this.trackName=e.name,this.source=n3.sourceFromProto(e.source),this.mimeType=e.mimeType,this.kind===n3.Kind.Video&&e.width>0&&(this.dimensions={width:e.width,height:e.height},this.simulcasted=e.simulcast),this.encryption=e.encryption,this.trackInfo=e,this.log.debug("update publication info",Object.assign(Object.assign({},this.logContext),{info:e}))}}!function(e){var t,i;(t=e.SubscriptionStatus||(e.SubscriptionStatus={})).Desired="desired",t.Subscribed="subscribed",t.Unsubscribed="unsubscribed",(i=e.PermissionStatus||(e.PermissionStatus={})).Allowed="allowed",i.NotAllowed="not_allowed"}(a3||(a3={}));class a9 extends a3{get isUpstreamPaused(){var e;return null==(e=this.track)?void 0:e.isUpstreamPaused}constructor(e,t,i,n){super(e,t.sid,t.name,n),this.track=void 0,this.handleTrackEnded=()=>{this.emit(E.Ended)},this.handleCpuConstrained=()=>{this.track&&rL(this.track)&&this.emit(E.CpuConstrained,this.track)},this.updateInfo(t),this.setTrack(i)}setTrack(e){this.track&&(this.track.off(E.Ended,this.handleTrackEnded),this.track.off(E.CpuConstrained,this.handleCpuConstrained)),super.setTrack(e),e&&(e.on(E.Ended,this.handleTrackEnded),e.on(E.CpuConstrained,this.handleCpuConstrained))}get isMuted(){return this.track?this.track.isMuted:super.isMuted}get audioTrack(){return super.audioTrack}get videoTrack(){return super.videoTrack}get isLocal(){return!0}mute(){return iz(this,void 0,void 0,function*(){var e;return null==(e=this.track)?void 0:e.mute()})}unmute(){return iz(this,void 0,void 0,function*(){var e;return null==(e=this.track)?void 0:e.unmute()})}pauseUpstream(){return iz(this,void 0,void 0,function*(){var e;yield null==(e=this.track)?void 0:e.pauseUpstream()})}resumeUpstream(){return iz(this,void 0,void 0,function*(){var e;yield null==(e=this.track)?void 0:e.resumeUpstream()})}getTrackFeatures(){var e;if(!r_(this.track))return[];{let t=this.track.getSourceTrackSettings(),i=new Set;return t.autoGainControl&&i.add(td.TF_AUTO_GAIN_CONTROL),t.echoCancellation&&i.add(td.TF_ECHO_CANCELLATION),t.noiseSuppression&&i.add(td.TF_NOISE_SUPPRESSION),t.channelCount&&t.channelCount>1&&i.add(td.TF_STEREO),(null==(e=this.options)?void 0:e.dtx)||i.add(td.TF_NO_DTX),this.track.enhancedNoiseCancellation&&i.add(td.TF_ENHANCED_NOISE_CANCELLATION),Array.from(i.values())}}}function a6(e,t){return iz(this,void 0,void 0,function*(){null!=e||(e={});let i=!1,{audioProcessor:n,videoProcessor:r,optionsWithoutProcessor:a}=rY(e),s=a.audio,o=a.video;if(n&&"object"==typeof a.audio&&(a.audio.processor=n),r&&"object"==typeof a.video&&(a.video.processor=r),e.audio&&"object"==typeof a.audio&&"string"==typeof a.audio.deviceId){let e=a.audio.deviceId;a.audio.deviceId={exact:e},i=!0,s=Object.assign(Object.assign({},a.audio),{deviceId:{ideal:e}})}if(a.video&&"object"==typeof a.video&&"string"==typeof a.video.deviceId){let e=a.video.deviceId;a.video.deviceId={exact:e},i=!0,o=Object.assign(Object.assign({},a.video),{deviceId:{ideal:e}})}!0!==a.audio&&("object"!=typeof a.audio||a.audio.deviceId)||(a.audio={deviceId:"default"}),!0===a.video?a.video={deviceId:"default"}:"object"!=typeof a.video||a.video.deviceId||(a.video.deviceId="default");let c=rV(a,al,ad),l=rH(c),d=navigator.mediaDevices.getUserMedia(l);a.audio&&(rZ.userMediaPromiseMap.set("audioinput",d),d.catch(()=>rZ.userMediaPromiseMap.delete("audioinput"))),a.video&&(rZ.userMediaPromiseMap.set("videoinput",d),d.catch(()=>rZ.userMediaPromiseMap.delete("videoinput")));try{let e=yield d;return yield Promise.all(e.getTracks().map(i=>iz(this,void 0,void 0,function*(){let a,s="audio"===i.kind,o=s?c.audio:c.video;"boolean"!=typeof o&&o||(o={});let d=s?l.audio:l.video;"boolean"!=typeof d&&(a=d);let u=i.getSettings().deviceId;(null==a?void 0:a.deviceId)&&rx(a.deviceId)!==u?a.deviceId=u:a||(a={deviceId:u});let h=function(e,t,i){switch(e.kind){case"audio":return new aS(e,t,!1,void 0,i);case"video":return new aL(e,t,!1,i);default:throw new nz("unsupported track type: ".concat(e.kind))}}(i,a,t);return h.kind===n3.Kind.Video?h.source=n3.Source.Camera:h.kind===n3.Kind.Audio&&(h.source=n3.Source.Microphone),h.mediaStream=e,r_(h)&&n?yield h.setProcessor(n):rL(h)&&r&&(yield h.setProcessor(r)),h})))}catch(n){if(!i)throw n;return a6(Object.assign(Object.assign({},e),{audio:s,video:o}),t)}})}!function(e){e.Excellent="excellent",e.Good="good",e.Poor="poor",e.Lost="lost",e.Unknown="unknown"}(j||(j={}));class a4 extends iJ.EventEmitter{get logContext(){var e,t;return Object.assign({},null==(t=null==(e=this.loggerOptions)?void 0:e.loggerContextCb)?void 0:t.call(e))}get isEncrypted(){return this.trackPublications.size>0&&Array.from(this.trackPublications.values()).every(e=>e.isEncrypted)}get isAgent(){var e;return(null==(e=this.permissions)?void 0:e.agent)||this.kind===tg.AGENT}get isActive(){var e;return(null==(e=this.participantInfo)?void 0:e.state)===tf.ACTIVE}get kind(){return this._kind}get attributes(){return Object.freeze(Object.assign({},this._attributes))}constructor(e,t,i,n,r,a){var s;let o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:tg.STANDARD;super(),this.audioLevel=0,this.isSpeaking=!1,this._connectionQuality=j.Unknown,this.log=iB,this.log=iV(null!=(s=null==a?void 0:a.loggerName)?s:h.Participant),this.loggerOptions=a,this.setMaxListeners(100),this.sid=e,this.identity=t,this.name=i,this.metadata=n,this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.trackPublications=new Map,this._kind=o,this._attributes=null!=r?r:{}}getTrackPublications(){return Array.from(this.trackPublications.values())}getTrackPublication(e){for(let[,t]of this.trackPublications)if(t.source===e)return t}getTrackPublicationByName(e){for(let[,t]of this.trackPublications)if(t.trackName===e)return t}waitUntilActive(){return this.isActive?Promise.resolve():(this.activeFuture||(this.activeFuture=new rI,this.once(S.Active,()=>{var e,t;null==(t=null==(e=this.activeFuture)?void 0:e.resolve)||t.call(e),this.activeFuture=void 0})),this.activeFuture.promise)}get connectionQuality(){return this._connectionQuality}get isCameraEnabled(){var e;let t=this.getTrackPublication(n3.Source.Camera);return!(null==(e=null==t?void 0:t.isMuted)||e)}get isMicrophoneEnabled(){var e;let t=this.getTrackPublication(n3.Source.Microphone);return!(null==(e=null==t?void 0:t.isMuted)||e)}get isScreenShareEnabled(){return!!this.getTrackPublication(n3.Source.ScreenShare)}get isLocal(){return!1}get joinedAt(){return this.participantInfo?new Date(1e3*Number.parseInt(this.participantInfo.joinedAt.toString())):new Date}updateInfo(e){var t;return(!this.participantInfo||this.participantInfo.sid!==e.sid||!(this.participantInfo.version>e.version))&&(this.identity=e.identity,this.sid=e.sid,this._setName(e.name),this._setMetadata(e.metadata),this._setAttributes(e.attributes),e.state===tf.ACTIVE&&(null==(t=this.participantInfo)?void 0:t.state)!==tf.ACTIVE&&this.emit(S.Active),e.permission&&this.setPermissions(e.permission),this.participantInfo=e,!0)}_setMetadata(e){let t=this.metadata!==e,i=this.metadata;this.metadata=e,t&&this.emit(S.ParticipantMetadataChanged,i)}_setName(e){let t=this.name!==e;this.name=e,t&&this.emit(S.ParticipantNameChanged,e)}_setAttributes(e){let t=function(e,t){var i;void 0===e&&(e={}),void 0===t&&(t={});let n=[...Object.keys(t),...Object.keys(e)],r={};for(let a of n)e[a]!==t[a]&&(r[a]=null!=(i=t[a])?i:"");return r}(this.attributes,e);this._attributes=e,Object.keys(t).length>0&&this.emit(S.AttributesChanged,t)}setPermissions(e){var t,i,n,r,a,s;let o=this.permissions,c=e.canPublish!==(null==(t=this.permissions)?void 0:t.canPublish)||e.canSubscribe!==(null==(i=this.permissions)?void 0:i.canSubscribe)||e.canPublishData!==(null==(n=this.permissions)?void 0:n.canPublishData)||e.hidden!==(null==(r=this.permissions)?void 0:r.hidden)||e.recorder!==(null==(a=this.permissions)?void 0:a.recorder)||e.canPublishSources.length!==this.permissions.canPublishSources.length||e.canPublishSources.some((e,t)=>{var i;return e!==(null==(i=this.permissions)?void 0:i.canPublishSources[t])})||e.canSubscribeMetrics!==(null==(s=this.permissions)?void 0:s.canSubscribeMetrics);return this.permissions=e,c&&this.emit(S.ParticipantPermissionsChanged,o),c}setIsSpeaking(e){e!==this.isSpeaking&&(this.isSpeaking=e,e&&(this.lastSpokeAt=new Date),this.emit(S.IsSpeakingChanged,e))}setConnectionQuality(e){let t=this._connectionQuality;this._connectionQuality=function(e){switch(e){case ta.EXCELLENT:return j.Excellent;case ta.GOOD:return j.Good;case ta.POOR:return j.Poor;case ta.LOST:return j.Lost;default:return j.Unknown}}(e),t!==this._connectionQuality&&this.emit(S.ConnectionQualityChanged,this._connectionQuality)}setDisconnected(){var e,t;this.activeFuture&&(null==(t=(e=this.activeFuture).reject)||t.call(e,Error("Participant disconnected")),this.activeFuture=void 0)}setAudioContext(e){this.audioContext=e,this.audioTrackPublications.forEach(t=>r_(t.track)&&t.track.setAudioContext(e))}addTrackPublication(e){switch(e.on(E.Muted,()=>{this.emit(S.TrackMuted,e)}),e.on(E.Unmuted,()=>{this.emit(S.TrackUnmuted,e)}),e.track&&(e.track.sid=e.trackSid),this.trackPublications.set(e.trackSid,e),e.kind){case n3.Kind.Audio:this.audioTrackPublications.set(e.trackSid,e);break;case n3.Kind.Video:this.videoTrackPublications.set(e.trackSid,e)}}}class a7 extends a4{constructor(e,t,i,n,r){super(e,t,void 0,void 0,void 0,{loggerName:n.loggerName,loggerContextCb:()=>this.engine.logContext}),this.pendingPublishing=new Set,this.pendingPublishPromises=new Map,this.participantTrackPermissions=[],this.allParticipantsAllowedToSubscribe=!0,this.encryptionType=tb.NONE,this.enabledPublishVideoCodecs=[],this.pendingAcks=new Map,this.pendingResponses=new Map,this.handleReconnecting=()=>{this.reconnectFuture||(this.reconnectFuture=new rI)},this.handleReconnected=()=>{var e,t;null==(t=null==(e=this.reconnectFuture)?void 0:e.resolve)||t.call(e),this.reconnectFuture=void 0,this.updateTrackSubscriptionPermissions()},this.handleDisconnected=()=>{var e,t,i,n,r,a;this.reconnectFuture&&(this.reconnectFuture.promise.catch(e=>this.log.warn(e.message,this.logContext)),null==(t=null==(e=this.reconnectFuture)?void 0:e.reject)||t.call(e,"Got disconnected during reconnection attempt"),this.reconnectFuture=void 0),this.signalConnectedFuture&&(null==(n=(i=this.signalConnectedFuture).reject)||n.call(i,"Got disconnected without signal connected"),this.signalConnectedFuture=void 0),null==(a=null==(r=this.activeAgentFuture)?void 0:r.reject)||a.call(r,"Got disconnected without active agent present"),this.activeAgentFuture=void 0,this.firstActiveAgent=void 0},this.handleSignalConnected=e=>{var t,i;e.participant&&this.updateInfo(e.participant),this.signalConnectedFuture||(this.signalConnectedFuture=new rI),null==(i=(t=this.signalConnectedFuture).resolve)||i.call(t)},this.handleSignalRequestResponse=e=>{let{requestId:t,reason:i,message:n}=e,r=this.pendingSignalRequests.get(t);r&&(i!==i_.OK&&r.reject(new nQ(n,i)),this.pendingSignalRequests.delete(t))},this.handleDataPacket=e=>{switch(e.value.case){case"rpcResponse":let t=e.value.value,i=null,n=null;"payload"===t.value.case?i=t.value.value:"error"===t.value.case&&(n=am.fromProto(t.value.value)),this.handleIncomingRpcResponse(t.requestId,i,n);break;case"rpcAck":let r=e.value.value;this.handleIncomingRpcAck(r.requestId)}},this.updateTrackSubscriptionPermissions=()=>{this.log.debug("updating track subscription permissions",Object.assign(Object.assign({},this.logContext),{allParticipantsAllowed:this.allParticipantsAllowedToSubscribe,participantTrackPermissions:this.participantTrackPermissions})),this.engine.client.sendUpdateSubscriptionPermissions(this.allParticipantsAllowedToSubscribe,this.participantTrackPermissions.map(e=>(function(e){var t,i,n;if(!e.participantSid&&!e.participantIdentity)throw Error("Invalid track permission, must provide at least one of participantIdentity and participantSid");return new iT({participantIdentity:null!=(t=e.participantIdentity)?t:"",participantSid:null!=(i=e.participantSid)?i:"",allTracks:null!=(n=e.allowAll)&&n,trackSids:e.allowedTrackSids||[]})})(e)))},this.onTrackUnmuted=e=>{this.onTrackMuted(e,e.isUpstreamPaused)},this.onTrackMuted=(e,t)=>{if(void 0===t&&(t=!0),!e.sid)return void this.log.error("could not update mute status for unpublished track",Object.assign(Object.assign({},this.logContext),rJ(e)));this.engine.updateMuteStatus(e.sid,t)},this.onTrackUpstreamPaused=e=>{this.log.debug("upstream paused",Object.assign(Object.assign({},this.logContext),rJ(e))),this.onTrackMuted(e,!0)},this.onTrackUpstreamResumed=e=>{this.log.debug("upstream resumed",Object.assign(Object.assign({},this.logContext),rJ(e))),this.onTrackMuted(e,e.isMuted)},this.onTrackFeatureUpdate=e=>{let t=this.audioTrackPublications.get(e.sid);if(!t)return void this.log.warn("Could not update local audio track settings, missing publication for track ".concat(e.sid),this.logContext);this.engine.client.sendUpdateLocalAudioTrack(t.trackSid,t.getTrackFeatures())},this.onTrackCpuConstrained=(e,t)=>{this.log.debug("track cpu constrained",Object.assign(Object.assign({},this.logContext),rJ(t))),this.emit(S.LocalTrackCpuConstrained,e,t)},this.handleSubscribedQualityUpdate=e=>iz(this,void 0,void 0,function*(){if(!(null==(o=this.roomOptions)?void 0:o.dynacast))return;let t=this.videoTrackPublications.get(e.trackSid);if(!t)return void this.log.warn("received subscribed quality update for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:e.trackSid}));if(!t.videoTrack)return;let i=yield t.videoTrack.setPublishingCodecs(e.subscribedCodecs);try{for(var n,r,a,s,o,c,l=!0,d=iG(i);!(n=(c=yield d.next()).done);l=!0)s=c.value,l=!1,function(e){return!!n7.find(t=>t===e)}(s)&&(this.log.debug("publish ".concat(s," for ").concat(t.videoTrack.sid),Object.assign(Object.assign({},this.logContext),rJ(t))),yield this.publishAdditionalCodecForTrack(t.videoTrack,s,t.options))}catch(e){r={error:e}}finally{try{!l&&!n&&(a=d.return)&&(yield a.call(d))}finally{if(r)throw r.error}}}),this.handleLocalTrackUnpublished=e=>{let t=this.trackPublications.get(e.trackSid);if(!t)return void this.log.warn("received unpublished event for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:e.trackSid}));this.unpublishTrack(t.track)},this.handleTrackEnded=e=>iz(this,void 0,void 0,function*(){if(e.source===n3.Source.ScreenShare||e.source===n3.Source.ScreenShareAudio)this.log.debug("unpublishing local track due to TrackEnded",Object.assign(Object.assign({},this.logContext),rJ(e))),this.unpublishTrack(e);else if(e.isUserProvided)yield e.mute();else if(rj(e)||rU(e))try{if(rp())try{let t=yield null==navigator?void 0:navigator.permissions.query({name:e.source===n3.Source.Camera?"camera":"microphone"});if(t&&"denied"===t.state)throw this.log.warn("user has revoked access to ".concat(e.source),Object.assign(Object.assign({},this.logContext),rJ(e))),t.onchange=()=>{"denied"!==t.state&&(e.isMuted||e.restartTrack(),t.onchange=null)},Error("GetUserMedia Permission denied")}catch(e){}e.isMuted||(this.log.debug("track ended, attempting to use a different device",Object.assign(Object.assign({},this.logContext),rJ(e))),rj(e)?yield e.restartTrack({deviceId:"default"}):yield e.restartTrack())}catch(t){this.log.warn("could not restart track, muting instead",Object.assign(Object.assign({},this.logContext),rJ(e))),yield e.mute()}}),this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.trackPublications=new Map,this.engine=i,this.roomOptions=n,this.setupEngine(i),this.activeDeviceMap=new Map([["audioinput","default"],["videoinput","default"],["audiooutput","default"]]),this.pendingSignalRequests=new Map,this.rpcHandlers=r}get lastCameraError(){return this.cameraError}get lastMicrophoneError(){return this.microphoneError}get isE2EEEnabled(){return this.encryptionType!==tb.NONE}getTrackPublication(e){let t=super.getTrackPublication(e);if(t)return t}getTrackPublicationByName(e){let t=super.getTrackPublicationByName(e);if(t)return t}setupEngine(e){this.engine=e,this.engine.on(w.RemoteMute,(e,t)=>{let i=this.trackPublications.get(e);i&&i.track&&(t?i.mute():i.unmute())}),this.engine.on(w.Connected,this.handleReconnected).on(w.SignalConnected,this.handleSignalConnected).on(w.SignalRestarted,this.handleReconnected).on(w.SignalResumed,this.handleReconnected).on(w.Restarting,this.handleReconnecting).on(w.Resuming,this.handleReconnecting).on(w.LocalTrackUnpublished,this.handleLocalTrackUnpublished).on(w.SubscribedQualityUpdate,this.handleSubscribedQualityUpdate).on(w.Disconnected,this.handleDisconnected).on(w.SignalRequestResponse,this.handleSignalRequestResponse).on(w.DataPacketReceived,this.handleDataPacket)}setMetadata(e){return iz(this,void 0,void 0,function*(){yield this.requestMetadataUpdate({metadata:e})})}setName(e){return iz(this,void 0,void 0,function*(){yield this.requestMetadataUpdate({name:e})})}setAttributes(e){return iz(this,void 0,void 0,function*(){yield this.requestMetadataUpdate({attributes:e})})}requestMetadataUpdate(e){return iz(this,arguments,void 0,function(e){var t=this;let{metadata:i,name:n,attributes:r}=e;return function*(){return new Promise((e,a)=>iz(t,void 0,void 0,function*(){var t,s;try{let o=!1,c=yield this.engine.client.sendUpdateLocalMetadata(null!=(t=null!=i?i:this.metadata)?t:"",null!=(s=null!=n?n:this.name)?s:"",r),l=performance.now();for(this.pendingSignalRequests.set(c,{resolve:e,reject:e=>{a(e),o=!0},values:{name:n,metadata:i,attributes:r}});performance.now()-l<5e3&&!o;){if((!n||this.name===n)&&(!i||this.metadata===i)&&(!r||Object.entries(r).every(e=>{let[t,i]=e;return this.attributes[t]===i||""===i&&!this.attributes[t]}))){this.pendingSignalRequests.delete(c),e();return}yield rr(50)}a(new nQ("Request to update local metadata timed out","TimeoutError"))}catch(e){e instanceof Error&&a(e)}}))}()})}setCameraEnabled(e,t,i){return this.setTrackEnabled(n3.Source.Camera,e,t,i)}setMicrophoneEnabled(e,t,i){return this.setTrackEnabled(n3.Source.Microphone,e,t,i)}setScreenShareEnabled(e,t,i){return this.setTrackEnabled(n3.Source.ScreenShare,e,t,i)}setPermissions(e){let t=this.permissions,i=super.setPermissions(e);return i&&t&&this.emit(S.ParticipantPermissionsChanged,t),i}setE2EEEnabled(e){return iz(this,void 0,void 0,function*(){this.encryptionType=e?tb.GCM:tb.NONE,yield this.republishAllTracks(void 0,!1)})}setTrackEnabled(e,t,i,n){return iz(this,void 0,void 0,function*(){this.log.debug("setTrackEnabled",Object.assign(Object.assign({},this.logContext),{source:e,enabled:t})),this.republishPromise&&(yield this.republishPromise);let r=this.getTrackPublication(e);if(t)if(r)yield r.unmute();else{let t;if(this.pendingPublishing.has(e)){let t=yield this.waitForPendingPublicationOfSource(e);return t||this.log.info("waiting for pending publication promise timed out",Object.assign(Object.assign({},this.logContext),{source:e})),yield null==t?void 0:t.unmute(),t}this.pendingPublishing.add(e);try{switch(e){case n3.Source.Camera:t=yield this.createTracks({video:null==i||i});break;case n3.Source.Microphone:t=yield this.createTracks({audio:null==i||i});break;case n3.Source.ScreenShare:t=yield this.createScreenTracks(Object.assign({},i));break;default:throw new nz(e)}}catch(i){throw null==t||t.forEach(e=>{e.stop()}),i instanceof Error&&this.emit(S.MediaDevicesError,i,rG(e)),this.pendingPublishing.delete(e),i}for(let i of t)e===n3.Source.Microphone&&r_(i)&&(null==n?void 0:n.preConnectBuffer)&&(this.log.info("starting preconnect buffer for microphone",Object.assign({},this.logContext)),i.startPreConnectBuffer());try{let e=[];for(let i of t)this.log.info("publishing track",Object.assign(Object.assign({},this.logContext),rJ(i))),e.push(this.publishTrack(i,n));let i=yield Promise.all(e);[r]=i}catch(e){throw null==t||t.forEach(e=>{e.stop()}),e}finally{this.pendingPublishing.delete(e)}}else if(!(null==r?void 0:r.track)&&this.pendingPublishing.has(e)&&((r=yield this.waitForPendingPublicationOfSource(e))||this.log.info("waiting for pending publication promise timed out",Object.assign(Object.assign({},this.logContext),{source:e}))),r&&r.track)if(e===n3.Source.ScreenShare){r=yield this.unpublishTrack(r.track);let e=this.getTrackPublication(n3.Source.ScreenShareAudio);e&&e.track&&this.unpublishTrack(e.track)}else yield r.mute();return r})}enableCameraAndMicrophone(){return iz(this,void 0,void 0,function*(){if(!(this.pendingPublishing.has(n3.Source.Camera)||this.pendingPublishing.has(n3.Source.Microphone))){this.pendingPublishing.add(n3.Source.Camera),this.pendingPublishing.add(n3.Source.Microphone);try{let e=yield this.createTracks({audio:!0,video:!0});yield Promise.all(e.map(e=>this.publishTrack(e)))}finally{this.pendingPublishing.delete(n3.Source.Camera),this.pendingPublishing.delete(n3.Source.Microphone)}}})}createTracks(e){return iz(this,void 0,void 0,function*(){var t,i;null!=e||(e={});let n=rV(e,null==(t=this.roomOptions)?void 0:t.audioCaptureDefaults,null==(i=this.roomOptions)?void 0:i.videoCaptureDefaults);try{return(yield a6(n,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext})).map(e=>(r_(e)&&(this.microphoneError=void 0,e.setAudioContext(this.audioContext),e.source=n3.Source.Microphone,this.emit(S.AudioStreamAcquired)),rL(e)&&(this.cameraError=void 0,e.source=n3.Source.Camera),e))}catch(t){throw t instanceof Error&&(e.audio&&(this.microphoneError=t),e.video&&(this.cameraError=t)),t}})}createScreenTracks(e){return iz(this,void 0,void 0,function*(){var t,i,n;let r;if(void 0===e&&(e={}),void 0===navigator.mediaDevices.getDisplayMedia)throw new nW("getDisplayMedia not supported");void 0!==e.resolution||function(){let e=nX();return(null==e?void 0:e.name)==="Safari"&&e.version.startsWith("17.")||(null==e?void 0:e.os)==="iOS"&&!!(null==e?void 0:e.osVersion)&&ry(e.osVersion,"17")>=0}()||(e.resolution=ri.h1080fps30.resolution);let a=(r=null==(i=(t=e).video)||i,t.resolution&&t.resolution.width>0&&t.resolution.height>0&&(r="boolean"==typeof r?{}:r,r=rd()?Object.assign(Object.assign({},r),{width:{max:t.resolution.width},height:{max:t.resolution.height},frameRate:t.resolution.frameRate}):Object.assign(Object.assign({},r),{width:{ideal:t.resolution.width},height:{ideal:t.resolution.height},frameRate:t.resolution.frameRate})),{audio:null!=(n=t.audio)&&n,video:r,controller:t.controller,selfBrowserSurface:t.selfBrowserSurface,surfaceSwitching:t.surfaceSwitching,systemAudio:t.systemAudio,preferCurrentTab:t.preferCurrentTab}),s=yield navigator.mediaDevices.getDisplayMedia(a),o=s.getVideoTracks();if(0===o.length)throw new nz("no video track found");let c=new aL(o[0],void 0,!1,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});c.source=n3.Source.ScreenShare,e.contentHint&&(c.mediaStreamTrack.contentHint=e.contentHint);let l=[c];if(s.getAudioTracks().length>0){this.emit(S.AudioStreamAcquired);let e=new aS(s.getAudioTracks()[0],void 0,!1,this.audioContext,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});e.source=n3.Source.ScreenShareAudio,l.push(e)}return l})}publishTrack(e,t){return iz(this,void 0,void 0,function*(){return this.publishOrRepublishTrack(e,t)})}publishOrRepublishTrack(e,t){return iz(this,arguments,void 0,function(e,t){var i=this;let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function*(){var r,a,s,o;let c,l;if(rj(e)&&e.setAudioContext(i.audioContext),yield null==(r=i.reconnectFuture)?void 0:r.promise,i.republishPromise&&!n&&(yield i.republishPromise),rN(e)&&i.pendingPublishPromises.has(e)&&(yield i.pendingPublishPromises.get(e)),e instanceof MediaStreamTrack)c=e.getConstraints();else{let t;switch(c=e.constraints,e.source){case n3.Source.Microphone:t="audioinput";break;case n3.Source.Camera:t="videoinput"}t&&i.activeDeviceMap.has(t)&&(c=Object.assign(Object.assign({},c),{deviceId:i.activeDeviceMap.get(t)}))}if(e instanceof MediaStreamTrack)switch(e.kind){case"audio":e=new aS(e,c,!0,i.audioContext,{loggerName:i.roomOptions.loggerName,loggerContextCb:()=>i.logContext});break;case"video":e=new aL(e,c,!0,{loggerName:i.roomOptions.loggerName,loggerContextCb:()=>i.logContext});break;default:throw new nz("unsupported MediaStreamTrack kind ".concat(e.kind))}else e.updateLoggerOptions({loggerName:i.roomOptions.loggerName,loggerContextCb:()=>i.logContext});if(i.trackPublications.forEach(t=>{t.track&&t.track===e&&(l=t)}),l)return i.log.warn("track has already been published, skipping",Object.assign(Object.assign({},i.logContext),rJ(l))),l;let d="channelCount"in e.mediaStreamTrack.getSettings()&&2===e.mediaStreamTrack.getSettings().channelCount||2===e.mediaStreamTrack.getConstraints().channelCount,u=null!=(a=null==t?void 0:t.forceStereo)?a:d;u&&(t||(t={}),void 0===t.dtx&&i.log.info("Opus DTX will be disabled for stereo tracks by default. Enable them explicitly to make it work.",Object.assign(Object.assign({},i.logContext),rJ(e))),void 0===t.red&&i.log.info("Opus RED will be disabled for stereo tracks by default. Enable them explicitly to make it work."),null!=t.dtx||(t.dtx=!1),null!=t.red||(t.red=!1));let h=Object.assign(Object.assign({},i.roomOptions.publishDefaults),t);!function(){let e=nX(),t="17.2";if(e)if("Safari"!==e.name&&"iOS"!==e.os)return!0;else if("iOS"===e.os&&e.osVersion&&ry(t,e.osVersion)>=0)return!0;else if("Safari"===e.name&&ry(t,e.version)>=0)return!0;else return!1}()&&i.roomOptions.e2ee&&(i.log.info("End-to-end encryption is set up, simulcast publishing will be disabled on Safari versions and iOS browsers running iOS < v17.2",Object.assign({},i.logContext)),h.simulcast=!1),h.source&&(e.source=h.source);let p=new Promise((t,n)=>iz(i,void 0,void 0,function*(){try{if(this.engine.client.currentState!==O.CONNECTED){this.log.debug("deferring track publication until signal is connected",Object.assign(Object.assign({},this.logContext),{track:rJ(e)}));let i=setTimeout(()=>{n(new nY("publishing rejected as engine not connected within timeout",408))},15e3);yield this.waitUntilEngineConnected(),clearTimeout(i);let r=yield this.publish(e,h,u);t(r)}else try{let i=yield this.publish(e,h,u);t(i)}catch(e){n(e)}}catch(e){n(e)}}));i.pendingPublishPromises.set(e,p);try{return yield p}catch(e){throw e}finally{i.pendingPublishPromises.delete(e)}}()})}waitUntilEngineConnected(){return this.signalConnectedFuture||(this.signalConnectedFuture=new rI),this.signalConnectedFuture.promise}hasPermissionsToPublish(e){if(!this.permissions)return this.log.warn("no permissions present for publishing track",Object.assign(Object.assign({},this.logContext),rJ(e))),!1;let{canPublish:t,canPublishSources:i}=this.permissions;return!!(t&&(0===i.length||i.map(e=>(function(e){switch(e){case tn.CAMERA:return n3.Source.Camera;case tn.MICROPHONE:return n3.Source.Microphone;case tn.SCREEN_SHARE:return n3.Source.ScreenShare;case tn.SCREEN_SHARE_AUDIO:return n3.Source.ScreenShareAudio;default:return n3.Source.Unknown}})(e)).includes(e.source)))||(this.log.warn("insufficient permissions to publish",Object.assign(Object.assign({},this.logContext),rJ(e))),!1)}publish(e,t,i){return iz(this,void 0,void 0,function*(){var n,r,a,s,o,c,l,d,u,h;let p,m;if(!this.hasPermissionsToPublish(e))throw new nY("failed to publish track, insufficient permissions",403);Array.from(this.trackPublications.values()).find(t=>rN(e)&&t.source===e.source)&&e.source!==n3.Source.Unknown&&this.log.info("publishing a second track with the same source: ".concat(e.source),Object.assign(Object.assign({},this.logContext),rJ(e))),t.stopMicTrackOnMute&&r_(e)&&(e.stopOnMute=!0),e.source===n3.Source.ScreenShare&&rl()&&(t.simulcast=!1),"av1"===t.videoCodec&&!function(){if(!("getCapabilities"in RTCRtpSender)||rd())return!1;let e=RTCRtpSender.getCapabilities("video"),t=!1;if(e){for(let i of e.codecs)if("video/AV1"===i.mimeType){t=!0;break}}return t}()&&(t.videoCodec=void 0),"vp9"===t.videoCodec&&!function(){if(!("getCapabilities"in RTCRtpSender)||rl())return!1;if(rd()){let e=nX();if((null==e?void 0:e.version)&&0>ry(e.version,"16")||(null==e?void 0:e.os)==="iOS"&&(null==e?void 0:e.osVersion)&&0>ry(e.osVersion,"16"))return!1}let e=RTCRtpSender.getCapabilities("video"),t=!1;if(e){for(let i of e.codecs)if("video/VP9"===i.mimeType){t=!0;break}}return t}()&&(t.videoCodec=void 0),void 0===t.videoCodec&&(t.videoCodec="vp8"),this.enabledPublishVideoCodecs.length>0&&!this.enabledPublishVideoCodecs.some(e=>t.videoCodec===rK(e.mime))&&(t.videoCodec=rK(this.enabledPublishVideoCodecs[0].mime));let f=t.videoCodec;e.on(E.Muted,this.onTrackMuted),e.on(E.Unmuted,this.onTrackUnmuted),e.on(E.Ended,this.handleTrackEnded),e.on(E.UpstreamPaused,this.onTrackUpstreamPaused),e.on(E.UpstreamResumed,this.onTrackUpstreamResumed),e.on(E.AudioTrackFeatureUpdate,this.onTrackFeatureUpdate);let g=[],v=!(null==(n=t.dtx)||n),b=e.getSourceTrackSettings();b.autoGainControl&&g.push(td.TF_AUTO_GAIN_CONTROL),b.echoCancellation&&g.push(td.TF_ECHO_CANCELLATION),b.noiseSuppression&&g.push(td.TF_NOISE_SUPPRESSION),b.channelCount&&b.channelCount>1&&g.push(td.TF_STEREO),v&&g.push(td.TF_NO_DTX),rj(e)&&e.hasPreConnectBuffer&&g.push(td.TF_PRECONNECT_BUFFER);let y=new t5({cid:e.mediaStreamTrack.id,name:t.name,type:n3.kindToProto(e.kind),muted:e.isMuted,source:n3.sourceToProto(e.source),disableDtx:v,encryption:this.encryptionType,stereo:i,disableRed:this.isE2EEEnabled||!(null==(r=t.red)||r),stream:null==t?void 0:t.stream,backupCodecPolicy:null==t?void 0:t.backupCodecPolicy,audioFeatures:g});if(e.kind===n3.Kind.Video){let i={width:0,height:0};try{i=yield e.waitForDimensions()}catch(n){let t=null!=(s=null==(a=this.roomOptions.videoCaptureDefaults)?void 0:a.resolution)?s:re.h720.resolution;i={width:t.width,height:t.height},this.log.error("could not determine track dimensions, using defaults",Object.assign(Object.assign(Object.assign({},this.logContext),rJ(e)),{dims:i}))}y.width=i.width,y.height=i.height,rU(e)&&(ro(f)&&(e.source===n3.Source.ScreenShare&&(t.scalabilityMode="L1T3","contentHint"in e.mediaStreamTrack&&(e.mediaStreamTrack.contentHint="motion",this.log.info("forcing contentHint to motion for screenshare with SVC codecs",Object.assign(Object.assign({},this.logContext),rJ(e))))),t.scalabilityMode=null!=(o=t.scalabilityMode)?o:"L3T3_KEY"),y.simulcastCodecs=[new t1({codec:f,cid:e.mediaStreamTrack.id})],!0===t.backupCodec&&(t.backupCodec={codec:"vp8"}),t.backupCodec&&f!==t.backupCodec.codec&&y.encryption===tb.NONE&&(this.roomOptions.dynacast||(this.roomOptions.dynacast=!0),y.simulcastCodecs.push(new t1({codec:t.backupCodec.codec,cid:""})))),p=aM(e.source===n3.Source.ScreenShare,y.width,y.height,t),y.layers=aF(y.width,y.height,p,ro(t.videoCodec))}else e.kind===n3.Kind.Audio&&(p=[{maxBitrate:null==(c=t.audioPreset)?void 0:c.maxBitrate,priority:null!=(d=null==(l=t.audioPreset)?void 0:l.priority)?d:"high",networkPriority:null!=(h=null==(u=t.audioPreset)?void 0:u.priority)?h:"high"}]);if(!this.engine||this.engine.isClosed)throw new nK("cannot publish track when not connected");let k=()=>iz(this,void 0,void 0,function*(){var i,n,r;if(!this.engine.pcManager)throw new nK("pcManager is not ready");if(e.sender=yield this.engine.createSender(e,t,p),this.emit(S.LocalSenderCreated,e.sender,e),rU(e)&&(null!=t.degradationPreference||(t.degradationPreference=e.source===n3.Source.ScreenShare||e.constraints.height&&rx(e.constraints.height)>=1080?"maintain-resolution":"balanced"),e.setDegradationPreference(t.degradationPreference)),p)if(rl()&&e.kind===n3.Kind.Audio){let t;for(let i of this.engine.pcManager.publisher.getTransceivers())if(i.sender===e.sender){t=i;break}t&&this.engine.pcManager.publisher.setTrackCodecBitrate({transceiver:t,codec:"opus",maxbr:(null==(n=p[0])?void 0:n.maxBitrate)?p[0].maxBitrate/1e3:0})}else e.codec&&ro(e.codec)&&(null==(r=p[0])?void 0:r.maxBitrate)&&this.engine.pcManager.publisher.setTrackCodecBitrate({cid:y.cid,codec:e.codec,maxbr:p[0].maxBitrate/1e3});yield this.engine.negotiate()}),T=new Promise((t,i)=>iz(this,void 0,void 0,function*(){var n;try{m=yield this.engine.addTrack(y),t(m)}catch(t){e.sender&&(null==(n=this.engine.pcManager)?void 0:n.publisher)&&(this.engine.pcManager.publisher.removeTrack(e.sender),yield this.engine.negotiate().catch(t=>{this.log.error("failed to negotiate after removing track due to failed add track request",Object.assign(Object.assign(Object.assign({},this.logContext),rJ(e)),{error:t}))})),i(t)}}));if(this.enabledPublishVideoCodecs.length>0)m=(yield Promise.all([T,k()]))[0];else{let i;if((m=yield T).codecs.forEach(e=>{void 0===i&&(i=e.mimeType)}),i&&e.kind===n3.Kind.Video){let n=rK(i);n!==f&&(this.log.debug("falling back to server selected codec",Object.assign(Object.assign(Object.assign({},this.logContext),rJ(e)),{codec:n})),t.videoCodec=n,p=aM(e.source===n3.Source.ScreenShare,y.width,y.height,t))}yield k()}let C=new a9(e.kind,m,e,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});if(C.on(E.CpuConstrained,e=>this.onTrackCpuConstrained(e,C)),C.options=t,e.sid=m.sid,this.log.debug("publishing ".concat(e.kind," with encodings"),Object.assign(Object.assign({},this.logContext),{encodings:p,trackInfo:m})),rU(e)?e.startMonitor(this.engine.client):rj(e)&&e.startMonitor(),this.addTrackPublication(C),this.emit(S.LocalTrackPublished,C),rj(e)&&m.audioFeatures.includes(td.TF_PRECONNECT_BUFFER)){let t=e.getPreConnectBuffer();this.on(S.LocalTrackSubscribed,t=>{if(t.trackSid===m.sid){if(!e.hasPreConnectBuffer)return void this.log.warn("subscribe event came to late, buffer already closed",this.logContext);this.log.debug("finished recording preconnect buffer",Object.assign(Object.assign({},this.logContext),rJ(e))),e.stopPreConnectBuffer()}}),t&&new Promise((i,n)=>iz(this,void 0,void 0,function*(){try{this.log.debug("waiting for agent",Object.assign(Object.assign({},this.logContext),rJ(e)));let p=setTimeout(()=>{n(Error("agent not active within 10 seconds"))},1e4),m=yield this.waitUntilActiveAgentPresent();clearTimeout(p),this.log.debug("sending preconnect buffer",Object.assign(Object.assign({},this.logContext),rJ(e)));let f=yield this.streamBytes({name:"preconnect-buffer",mimeType:"audio/opus",topic:"lk.agent.pre-connect-audio-buffer",destinationIdentities:[m.identity],attributes:{trackId:C.trackSid,sampleRate:String(null!=(c=b.sampleRate)?c:"48000"),channels:String(null!=(l=b.channelCount)?l:"1")}});try{for(var r,a,s,o,c,l,d,u=!0,h=iG(t);!(r=(d=yield h.next()).done);u=!0)o=d.value,u=!1,yield f.write(o)}catch(e){a={error:e}}finally{try{!u&&!r&&(s=h.return)&&(yield s.call(h))}finally{if(a)throw a.error}}yield f.close(),i()}catch(e){n(e)}})).then(()=>{this.log.debug("preconnect buffer sent successfully",Object.assign(Object.assign({},this.logContext),rJ(e)))}).catch(t=>{this.log.error("error sending preconnect buffer",Object.assign(Object.assign(Object.assign({},this.logContext),rJ(e)),{error:t}))})}return C})}get isLocal(){return!0}publishAdditionalCodecForTrack(e,t,i){return iz(this,void 0,void 0,function*(){var n;let r;if(this.encryptionType!==tb.NONE)return;if(this.trackPublications.forEach(t=>{t.track&&t.track===e&&(r=t)}),!r)throw new nz("track is not published");if(!rU(e))throw new nz("track is not a video track");let a=Object.assign(Object.assign({},null==(n=this.roomOptions)?void 0:n.publishDefaults),i),s=function(e,t,i){var n,r,a,s;if(!i.backupCodec||!0===i.backupCodec||i.backupCodec.codec===i.videoCodec)return;t!==i.backupCodec.codec&&iB.warn("requested a different codec than specified as backup",{serverRequested:t,backup:i.backupCodec.codec}),i.videoCodec=t,i.videoEncoding=i.backupCodec.encoding;let o=e.mediaStreamTrack.getSettings(),c=null!=(n=o.width)?n:null==(r=e.dimensions)?void 0:r.width,l=null!=(a=o.height)?a:null==(s=e.dimensions)?void 0:s.height;return e.source===n3.Source.ScreenShare&&i.simulcast&&(i.simulcast=!1),aM(e.source===n3.Source.ScreenShare,c,l,i)}(e,t,a);if(!s)return void this.log.info("backup codec has been disabled, ignoring request to add additional codec for track",Object.assign(Object.assign({},this.logContext),rJ(e)));let o=e.addSimulcastTrack(t,s);if(!o)return;let c=new t5({cid:o.mediaStreamTrack.id,type:n3.kindToProto(e.kind),muted:e.isMuted,source:n3.sourceToProto(e.source),sid:e.sid,simulcastCodecs:[{codec:a.videoCodec,cid:o.mediaStreamTrack.id}]});if(c.layers=aF(c.width,c.height,s),!this.engine||this.engine.isClosed)throw new nK("cannot publish track when not connected");let l=(yield Promise.all([this.engine.addTrack(c),iz(this,void 0,void 0,function*(){yield this.engine.createSimulcastSender(e,o,a,s),yield this.engine.negotiate()})]))[0];this.log.debug("published ".concat(t," for track ").concat(e.sid),Object.assign(Object.assign({},this.logContext),{encodings:s,trackInfo:l}))})}unpublishTrack(e,t){return iz(this,void 0,void 0,function*(){var i,n;if(rN(e)){let t=this.pendingPublishPromises.get(e);t&&(this.log.info("awaiting publish promise before attempting to unpublish",Object.assign(Object.assign({},this.logContext),rJ(e))),yield t)}let r=this.getPublicationForTrack(e),a=r?rJ(r):void 0;if(this.log.debug("unpublishing track",Object.assign(Object.assign({},this.logContext),a)),!r||!r.track)return void this.log.warn("track was not unpublished because no publication was found",Object.assign(Object.assign({},this.logContext),a));(e=r.track).off(E.Muted,this.onTrackMuted),e.off(E.Unmuted,this.onTrackUnmuted),e.off(E.Ended,this.handleTrackEnded),e.off(E.UpstreamPaused,this.onTrackUpstreamPaused),e.off(E.UpstreamResumed,this.onTrackUpstreamResumed),e.off(E.AudioTrackFeatureUpdate,this.onTrackFeatureUpdate),void 0===t&&(t=null==(n=null==(i=this.roomOptions)?void 0:i.stopLocalTrackOnUnpublish)||n),t?e.stop():e.stopMonitor();let s=!1,o=e.sender;if(e.sender=void 0,this.engine.pcManager&&this.engine.pcManager.currentState<L.FAILED&&o)try{for(let e of this.engine.pcManager.publisher.getTransceivers())e.sender===o&&(e.direction="inactive",s=!0);if(this.engine.removeTrack(o)&&(s=!0),rU(e)){for(let[,t]of e.simulcastCodecs)t.sender&&(this.engine.removeTrack(t.sender)&&(s=!0),t.sender=void 0);e.simulcastCodecs.clear()}}catch(e){this.log.warn("failed to unpublish track",Object.assign(Object.assign(Object.assign({},this.logContext),a),{error:e}))}switch(this.trackPublications.delete(r.trackSid),r.kind){case n3.Kind.Audio:this.audioTrackPublications.delete(r.trackSid);break;case n3.Kind.Video:this.videoTrackPublications.delete(r.trackSid)}return this.emit(S.LocalTrackUnpublished,r),r.setTrack(void 0),s&&(yield this.engine.negotiate()),r})}unpublishTracks(e){return iz(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>this.unpublishTrack(e)))).filter(e=>!!e)})}republishAllTracks(e){return iz(this,arguments,void 0,function(e){var t=this;let i=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return function*(){t.republishPromise&&(yield t.republishPromise),t.republishPromise=new Promise((n,r)=>iz(t,void 0,void 0,function*(){try{let t=[];this.trackPublications.forEach(i=>{i.track&&(e&&(i.options=Object.assign(Object.assign({},i.options),e)),t.push(i))}),yield Promise.all(t.map(e=>iz(this,void 0,void 0,function*(){let t=e.track;yield this.unpublishTrack(t,!1),i&&!t.isMuted&&t.source!==n3.Source.ScreenShare&&t.source!==n3.Source.ScreenShareAudio&&(rj(t)||rU(t))&&!t.isUserProvided&&(this.log.debug("restarting existing track",Object.assign(Object.assign({},this.logContext),{track:e.trackSid})),yield t.restartTrack()),yield this.publishOrRepublishTrack(t,e.options,!0)}))),n()}catch(e){r(e)}finally{this.republishPromise=void 0}})),yield t.republishPromise}()})}publishData(e){return iz(this,arguments,void 0,function(e){var t=this;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){let n=i.reliable?tS.RELIABLE:tS.LOSSY,r=i.destinationIdentities,a=i.topic,s=new tC({kind:n,value:{case:"user",value:new tP({participantIdentity:t.identity,payload:e,destinationIdentities:r,topic:a})}});yield t.engine.sendDataPacket(s,n)}()})}publishDtmf(e,t){return iz(this,void 0,void 0,function*(){let i=new tC({kind:tS.RELIABLE,value:{case:"sipDtmf",value:new tR({code:e,digit:t})}});yield this.engine.sendDataPacket(i,tS.RELIABLE)})}sendChatMessage(e,t){return iz(this,void 0,void 0,function*(){let i={id:crypto.randomUUID(),message:e,timestamp:Date.now(),attachedFiles:null==t?void 0:t.attachments},n=new tC({value:{case:"chatMessage",value:new tO(Object.assign(Object.assign({},i),{timestamp:el.parse(i.timestamp)}))}});return yield this.engine.sendDataPacket(n,tS.RELIABLE),this.emit(S.ChatMessage,i),i})}editChatMessage(e,t){return iz(this,void 0,void 0,function*(){let i=Object.assign(Object.assign({},t),{message:e,editTimestamp:Date.now()}),n=new tC({value:{case:"chatMessage",value:new tO(Object.assign(Object.assign({},i),{timestamp:el.parse(i.timestamp),editTimestamp:el.parse(i.editTimestamp)}))}});return yield this.engine.sendDataPacket(n,tS.RELIABLE),this.emit(S.ChatMessage,i),i})}sendText(e,t){return iz(this,void 0,void 0,function*(){var i;let n=crypto.randomUUID(),r=new TextEncoder().encode(e).byteLength,a=null==(i=null==t?void 0:t.attachments)?void 0:i.map(()=>crypto.randomUUID()),s=Array(a?a.length+1:1).fill(0),o=(e,i)=>{var n;s[i]=e;let r=s.reduce((e,t)=>e+t,0);null==(n=null==t?void 0:t.onProgress)||n.call(t,r)},c=yield this.streamText({streamId:n,totalSize:r,destinationIdentities:null==t?void 0:t.destinationIdentities,topic:null==t?void 0:t.topic,attachedStreamIds:a,attributes:null==t?void 0:t.attributes});return yield c.write(e),o(1,0),yield c.close(),(null==t?void 0:t.attachments)&&a&&(yield Promise.all(t.attachments.map((e,i)=>iz(this,void 0,void 0,function*(){return this._sendFile(a[i],e,{topic:t.topic,mimeType:e.type,onProgress:e=>{o(e,i+1)}})})))),c.info})}streamText(e){return iz(this,void 0,void 0,function*(){var t,i;let n=null!=(t=null==e?void 0:e.streamId)?t:crypto.randomUUID(),r={id:n,mimeType:"text/plain",timestamp:Date.now(),topic:null!=(i=null==e?void 0:e.topic)?i:"",size:null==e?void 0:e.totalSize,attributes:null==e?void 0:e.attributes},a=new tK({streamId:n,mimeType:r.mimeType,topic:r.topic,timestamp:rA(r.timestamp),totalLength:rA(null==e?void 0:e.totalSize),attributes:r.attributes,contentHeader:{case:"textHeader",value:new tz({version:null==e?void 0:e.version,attachedStreamIds:null==e?void 0:e.attachedStreamIds,replyToStreamId:null==e?void 0:e.replyToStreamId,operationType:(null==e?void 0:e.type)==="update"?tW.UPDATE:tW.CREATE})}}),s=null==e?void 0:e.destinationIdentities,o=new tC({destinationIdentities:s,value:{case:"streamHeader",value:a}});yield this.engine.sendDataPacket(o,tS.RELIABLE);let c=0,l=this,d=new WritableStream({write(e){return iz(this,void 0,void 0,function*(){for(let t of function(e,t){let i=[],n=new TextEncoder().encode(e);for(;n.length>15e3;){let e=15e3;for(;e>0;){let t=n[e];if(void 0!==t&&(192&t)!=128)break;e--}i.push(n.slice(0,e)),n=n.slice(e)}return n.length>0&&i.push(n),i}(e,15e3)){yield l.engine.waitForBufferStatusLow(tS.RELIABLE);let e=new tC({destinationIdentities:s,value:{case:"streamChunk",value:new tJ({content:t,streamId:n,chunkIndex:rA(c)})}});yield l.engine.sendDataPacket(e,tS.RELIABLE),c+=1}})},close(){return iz(this,void 0,void 0,function*(){let e=new tC({destinationIdentities:s,value:{case:"streamTrailer",value:new tY({streamId:n})}});yield l.engine.sendDataPacket(e,tS.RELIABLE)})},abort(e){console.log("Sink error:",e)}}),u=()=>iz(this,void 0,void 0,function*(){yield h.close()});l.engine.once(w.Closing,u);let h=new aQ(d,r,()=>this.engine.off(w.Closing,u));return h})}sendFile(e,t){return iz(this,void 0,void 0,function*(){let i=crypto.randomUUID();return yield this._sendFile(i,e,t),{id:i}})}_sendFile(e,t,i){return iz(this,void 0,void 0,function*(){var n;let r=yield this.streamBytes({streamId:e,totalSize:t.size,name:t.name,mimeType:null!=(n=null==i?void 0:i.mimeType)?n:t.type,topic:null==i?void 0:i.topic,destinationIdentities:null==i?void 0:i.destinationIdentities}),a=t.stream().getReader();for(;;){let{done:e,value:t}=yield a.read();if(e)break;yield r.write(t)}return yield r.close(),r.info})}streamBytes(e){return iz(this,void 0,void 0,function*(){var t,i,n,r,a;let s=null!=(t=null==e?void 0:e.streamId)?t:crypto.randomUUID(),o=null==e?void 0:e.destinationIdentities,c={id:s,mimeType:null!=(i=null==e?void 0:e.mimeType)?i:"application/octet-stream",topic:null!=(n=null==e?void 0:e.topic)?n:"",timestamp:Date.now(),attributes:null==e?void 0:e.attributes,size:null==e?void 0:e.totalSize,name:null!=(r=null==e?void 0:e.name)?r:"unknown"},l=new tC({destinationIdentities:o,value:{case:"streamHeader",value:new tK({totalLength:rA(null!=(a=c.size)?a:0),mimeType:c.mimeType,streamId:s,topic:c.topic,timestamp:rA(Date.now()),attributes:c.attributes,contentHeader:{case:"byteHeader",value:new tG({name:c.name})}})}});yield this.engine.sendDataPacket(l,tS.RELIABLE);let d=0,u=new W,h=this.engine,p=this.log;return new a$(new WritableStream({write(e){return iz(this,void 0,void 0,function*(){let t=yield u.lock(),i=0;try{for(;i<e.byteLength;){let t=e.slice(i,i+15e3);yield h.waitForBufferStatusLow(tS.RELIABLE);let n=new tC({destinationIdentities:o,value:{case:"streamChunk",value:new tJ({content:t,streamId:s,chunkIndex:rA(d)})}});yield h.sendDataPacket(n,tS.RELIABLE),d+=1,i+=t.byteLength}}finally{t()}})},close(){return iz(this,void 0,void 0,function*(){let e=new tC({destinationIdentities:o,value:{case:"streamTrailer",value:new tY({streamId:s})}});yield h.sendDataPacket(e,tS.RELIABLE)})},abort(e){p.error("Sink error:",e)}}),c)})}performRpc(e){return iz(this,arguments,void 0,function(e){var t=this;let{destinationIdentity:i,method:n,payload:r,responseTimeout:a=1e4}=e;return function*(){return new Promise((e,s)=>iz(t,void 0,void 0,function*(){var t,o,c,l;if(af(r)>15360)return void s(am.builtIn("REQUEST_PAYLOAD_TOO_LARGE"));if((null==(o=null==(t=this.engine.latestJoinResponse)?void 0:t.serverInfo)?void 0:o.version)&&0>ry(null==(l=null==(c=this.engine.latestJoinResponse)?void 0:c.serverInfo)?void 0:l.version,"1.8.0"))return void s(am.builtIn("UNSUPPORTED_SERVER"));let d=crypto.randomUUID();yield this.publishRpcRequest(i,d,n,r,a-2e3);let u=setTimeout(()=>{this.pendingAcks.delete(d),s(am.builtIn("CONNECTION_TIMEOUT")),this.pendingResponses.delete(d),clearTimeout(h)},2e3);this.pendingAcks.set(d,{resolve:()=>{clearTimeout(u)},participantIdentity:i});let h=setTimeout(()=>{this.pendingResponses.delete(d),s(am.builtIn("RESPONSE_TIMEOUT"))},a);this.pendingResponses.set(d,{resolve:(t,i)=>{clearTimeout(h),this.pendingAcks.has(d)&&(console.warn("RPC response received before ack",d),this.pendingAcks.delete(d),clearTimeout(u)),i?s(i):e(null!=t?t:"")},participantIdentity:i})}))}()})}registerRpcMethod(e,t){this.rpcHandlers.has(e)&&this.log.warn("you're overriding the RPC handler for method ".concat(e,", in the future this will throw an error")),this.rpcHandlers.set(e,t)}unregisterRpcMethod(e){this.rpcHandlers.delete(e)}setTrackSubscriptionPermissions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.participantTrackPermissions=t,this.allParticipantsAllowedToSubscribe=e,this.engine.client.isDisconnected||this.updateTrackSubscriptionPermissions()}handleIncomingRpcAck(e){let t=this.pendingAcks.get(e);t?(t.resolve(),this.pendingAcks.delete(e)):console.error("Ack received for unexpected RPC request",e)}handleIncomingRpcResponse(e,t,i){let n=this.pendingResponses.get(e);n?(n.resolve(t,i),this.pendingResponses.delete(e)):console.error("Response received for unexpected RPC request",e)}publishRpcRequest(e,t,i,n,r){return iz(this,void 0,void 0,function*(){let a=new tC({destinationIdentities:[e],kind:tS.RELIABLE,value:{case:"rpcRequest",value:new tM({id:t,method:i,payload:n,responseTimeoutMs:r,version:1})}});yield this.engine.sendDataPacket(a,tS.RELIABLE)})}handleParticipantDisconnected(e){for(let[t,{participantIdentity:i}]of this.pendingAcks)i===e&&this.pendingAcks.delete(t);for(let[t,{participantIdentity:i,resolve:n}]of this.pendingResponses)i===e&&(n(null,am.builtIn("RECIPIENT_DISCONNECTED")),this.pendingResponses.delete(t))}setEnabledPublishCodecs(e){this.enabledPublishVideoCodecs=e.filter(e=>"video"===e.mime.split("/")[0].toLowerCase())}updateInfo(e){return!!super.updateInfo(e)&&(e.tracks.forEach(e=>{var t,i;let n=this.trackPublications.get(e.sid);if(n){let r=n.isMuted||null!=(i=null==(t=n.track)?void 0:t.isUpstreamPaused)&&i;r!==e.muted&&(this.log.debug("updating server mute state after reconcile",Object.assign(Object.assign(Object.assign({},this.logContext),rJ(n)),{mutedOnServer:r})),this.engine.client.sendMuteTrack(e.sid,r))}}),!0)}setActiveAgent(e){var t,i,n,r;this.firstActiveAgent=e,e&&!this.firstActiveAgent&&(this.firstActiveAgent=e),e?null==(i=null==(t=this.activeAgentFuture)?void 0:t.resolve)||i.call(t,e):null==(r=null==(n=this.activeAgentFuture)?void 0:n.reject)||r.call(n,"Agent disconnected"),this.activeAgentFuture=void 0}waitUntilActiveAgentPresent(){return this.firstActiveAgent?Promise.resolve(this.firstActiveAgent):(this.activeAgentFuture||(this.activeAgentFuture=new rI),this.activeAgentFuture.promise)}getPublicationForTrack(e){let t;return this.trackPublications.forEach(i=>{let n=i.track;n&&(e instanceof MediaStreamTrack?(rj(n)||rU(n))&&n.mediaStreamTrack===e&&(t=i):e===n&&(t=i))}),t}waitForPendingPublicationOfSource(e){return iz(this,void 0,void 0,function*(){let t=Date.now();for(;Date.now()<t+1e4;){let t=Array.from(this.pendingPublishPromises.entries()).find(t=>{let[i]=t;return i.source===e});if(t)return t[1];yield rr(20)}})}}class a8 extends a3{constructor(e,t,i,n){super(e,t.sid,t.name,n),this.track=void 0,this.allowed=!0,this.requestedDisabled=void 0,this.visible=!0,this.handleEnded=e=>{this.setTrack(void 0),this.emit(E.Ended,e)},this.handleVisibilityChange=e=>{this.log.debug("adaptivestream video visibility ".concat(this.trackSid,", visible=").concat(e),this.logContext),this.visible=e,this.emitTrackUpdate()},this.handleVideoDimensionsChange=e=>{this.log.debug("adaptivestream video dimensions ".concat(e.width,"x").concat(e.height),this.logContext),this.videoDimensionsAdaptiveStream=e,this.emitTrackUpdate()},this.subscribed=i,this.updateInfo(t)}setSubscribed(e){let t=this.subscriptionStatus,i=this.permissionStatus;this.subscribed=e,e&&(this.allowed=!0);let n=new it({trackSids:[this.trackSid],subscribe:this.subscribed,participantTracks:[new t_({participantSid:"",trackSids:[this.trackSid]})]});this.emit(E.UpdateSubscription,n),this.emitSubscriptionUpdateIfChanged(t),this.emitPermissionUpdateIfChanged(i)}get subscriptionStatus(){return!1===this.subscribed?a3.SubscriptionStatus.Unsubscribed:super.isSubscribed?a3.SubscriptionStatus.Subscribed:a3.SubscriptionStatus.Desired}get permissionStatus(){return this.allowed?a3.PermissionStatus.Allowed:a3.PermissionStatus.NotAllowed}get isSubscribed(){return!1!==this.subscribed&&super.isSubscribed}get isDesired(){return!1!==this.subscribed}get isEnabled(){return void 0!==this.requestedDisabled?!this.requestedDisabled:!this.isAdaptiveStream||this.visible}get isLocal(){return!1}setEnabled(e){this.isManualOperationAllowed()&&!e!==this.requestedDisabled&&(this.requestedDisabled=!e,this.emitTrackUpdate())}setVideoQuality(e){this.isManualOperationAllowed()&&this.requestedMaxQuality!==e&&(this.requestedMaxQuality=e,this.requestedVideoDimensions=void 0,this.emitTrackUpdate())}setVideoDimensions(e){var t,i;this.isManualOperationAllowed()&&((null==(t=this.requestedVideoDimensions)?void 0:t.width)!==e.width||(null==(i=this.requestedVideoDimensions)?void 0:i.height)!==e.height)&&(rB(this.track)&&(this.requestedVideoDimensions=e),this.requestedMaxQuality=void 0,this.emitTrackUpdate())}setVideoFPS(e){this.isManualOperationAllowed()&&rB(this.track)&&this.fps!==e&&(this.fps=e,this.emitTrackUpdate())}get videoQuality(){var e;return null!=(e=this.requestedMaxQuality)?e:P.HIGH}setTrack(e){let t=this.subscriptionStatus,i=this.permissionStatus,n=this.track;n!==e&&(n&&(n.off(E.VideoDimensionsChanged,this.handleVideoDimensionsChange),n.off(E.VisibilityChanged,this.handleVisibilityChange),n.off(E.Ended,this.handleEnded),n.detach(),n.stopMonitor(),this.emit(E.Unsubscribed,n)),super.setTrack(e),e&&(e.sid=this.trackSid,e.on(E.VideoDimensionsChanged,this.handleVideoDimensionsChange),e.on(E.VisibilityChanged,this.handleVisibilityChange),e.on(E.Ended,this.handleEnded),this.emit(E.Subscribed,e)),this.emitPermissionUpdateIfChanged(i),this.emitSubscriptionUpdateIfChanged(t))}setAllowed(e){let t=this.subscriptionStatus,i=this.permissionStatus;this.allowed=e,this.emitPermissionUpdateIfChanged(i),this.emitSubscriptionUpdateIfChanged(t)}setSubscriptionError(e){this.emit(E.SubscriptionFailed,e)}updateInfo(e){super.updateInfo(e);let t=this.metadataMuted;this.metadataMuted=e.muted,this.track?this.track.setMuted(e.muted):t!==e.muted&&this.emit(e.muted?E.Muted:E.Unmuted)}emitSubscriptionUpdateIfChanged(e){let t=this.subscriptionStatus;e!==t&&this.emit(E.SubscriptionStatusChanged,t,e)}emitPermissionUpdateIfChanged(e){this.permissionStatus!==e&&this.emit(E.SubscriptionPermissionChanged,this.permissionStatus,e)}isManualOperationAllowed(){return!!this.isDesired||(this.log.warn("cannot update track settings when not subscribed",this.logContext),!1)}get isAdaptiveStream(){return rB(this.track)&&this.track.isAdaptiveStream}emitTrackUpdate(){let e=new ii({trackSids:[this.trackSid],disabled:!this.isEnabled,fps:this.fps});if(this.kind===n3.Kind.Video){let r=this.requestedVideoDimensions;if(void 0!==this.videoDimensionsAdaptiveStream)if(r)rQ(this.videoDimensionsAdaptiveStream,r)&&(this.log.debug("using adaptive stream dimensions instead of requested",Object.assign(Object.assign({},this.logContext),this.videoDimensionsAdaptiveStream)),r=this.videoDimensionsAdaptiveStream);else if(void 0!==this.requestedMaxQuality&&this.trackInfo){var t,i,n;let e=(t=this.trackInfo,i=this.requestedMaxQuality,null==(n=t.layers)?void 0:n.find(e=>e.quality===i));e&&rQ(this.videoDimensionsAdaptiveStream,e)&&(this.log.debug("using adaptive stream dimensions instead of max quality layer",Object.assign(Object.assign({},this.logContext),this.videoDimensionsAdaptiveStream)),r=this.videoDimensionsAdaptiveStream)}else this.log.debug("using adaptive stream dimensions",Object.assign(Object.assign({},this.logContext),this.videoDimensionsAdaptiveStream)),r=this.videoDimensionsAdaptiveStream;r?(e.width=Math.ceil(r.width),e.height=Math.ceil(r.height)):void 0!==this.requestedMaxQuality?(this.log.debug("using requested max quality",Object.assign(Object.assign({},this.logContext),{quality:this.requestedMaxQuality})),e.quality=this.requestedMaxQuality):(this.log.debug("using default quality",Object.assign(Object.assign({},this.logContext),{quality:P.HIGH})),e.quality=P.HIGH)}this.emit(E.UpdateSettings,e)}}class se extends a4{static fromParticipantInfo(e,t,i){return new se(e,t.sid,t.identity,t.name,t.metadata,t.attributes,i,t.kind)}get logContext(){return Object.assign(Object.assign({},super.logContext),{rpID:this.sid,remoteParticipant:this.identity})}constructor(e,t,i,n,r,a,s){let o=arguments.length>7&&void 0!==arguments[7]?arguments[7]:tg.STANDARD;super(t,i||"",n,r,a,s,o),this.signalClient=e,this.trackPublications=new Map,this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.volumeMap=new Map}addTrackPublication(e){super.addTrackPublication(e),e.on(E.UpdateSettings,t=>{this.log.debug("send update settings",Object.assign(Object.assign(Object.assign({},this.logContext),rJ(e)),{settings:t})),this.signalClient.sendUpdateTrackSettings(t)}),e.on(E.UpdateSubscription,e=>{e.participantTracks.forEach(e=>{e.participantSid=this.sid}),this.signalClient.sendUpdateSubscription(e)}),e.on(E.SubscriptionPermissionChanged,t=>{this.emit(S.TrackSubscriptionPermissionChanged,e,t)}),e.on(E.SubscriptionStatusChanged,t=>{this.emit(S.TrackSubscriptionStatusChanged,e,t)}),e.on(E.Subscribed,t=>{this.emit(S.TrackSubscribed,t,e)}),e.on(E.Unsubscribed,t=>{this.emit(S.TrackUnsubscribed,t,e)}),e.on(E.SubscriptionFailed,t=>{this.emit(S.TrackSubscriptionFailed,e.trackSid,t)})}getTrackPublication(e){let t=super.getTrackPublication(e);if(t)return t}getTrackPublicationByName(e){let t=super.getTrackPublicationByName(e);if(t)return t}setVolume(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n3.Source.Microphone;this.volumeMap.set(t,e);let i=this.getTrackPublication(t);i&&i.track&&i.track.setVolume(e)}getVolume(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n3.Source.Microphone,t=this.getTrackPublication(e);return t&&t.track?t.track.getVolume():this.volumeMap.get(e)}addSubscribedMediaTrack(e,t,i,n,r,a){let s,o=this.getTrackPublicationBySid(t);if(o||t.startsWith("TR")||this.trackPublications.forEach(t=>{o||e.kind!==t.kind.toString()||(o=t)}),!o){if(0===a){this.log.error("could not find published track",Object.assign(Object.assign({},this.logContext),{trackSid:t})),this.emit(S.TrackSubscriptionFailed,t);return}void 0===a&&(a=20),setTimeout(()=>{this.addSubscribedMediaTrack(e,t,i,n,r,a-1)},150);return}if("ended"===e.readyState){this.log.error("unable to subscribe because MediaStreamTrack is ended. Do not call MediaStreamTrack.stop()",Object.assign(Object.assign({},this.logContext),rJ(o))),this.emit(S.TrackSubscriptionFailed,t);return}return(s="video"===e.kind?new a0(e,t,n,r):new aZ(e,t,n,this.audioContext,this.audioOutput)).source=o.source,s.isMuted=o.isMuted,s.setMediaStream(i),s.start(),o.setTrack(s),this.volumeMap.has(o.source)&&rF(s)&&r_(s)&&s.setVolume(this.volumeMap.get(o.source)),o}get hasMetadata(){return!!this.participantInfo}getTrackPublicationBySid(e){return this.trackPublications.get(e)}updateInfo(e){if(!super.updateInfo(e))return!1;let t=new Map,i=new Map;return e.tracks.forEach(e=>{var n,r;let a=this.getTrackPublicationBySid(e.sid);if(a)a.updateInfo(e);else{let t=n3.kindFromProto(e.type);if(!t)return;(a=new a8(t,e,null==(n=this.signalClient.connectOptions)?void 0:n.autoSubscribe,{loggerContextCb:()=>this.logContext,loggerName:null==(r=this.loggerOptions)?void 0:r.loggerName})).updateInfo(e),i.set(e.sid,a);let s=Array.from(this.trackPublications.values()).find(e=>e.source===(null==a?void 0:a.source));s&&a.source!==n3.Source.Unknown&&this.log.debug("received a second track publication for ".concat(this.identity," with the same source: ").concat(a.source),Object.assign(Object.assign({},this.logContext),{oldTrack:rJ(s),newTrack:rJ(a)})),this.addTrackPublication(a)}t.set(e.sid,a)}),this.trackPublications.forEach(e=>{t.has(e.trackSid)||(this.log.trace("detected removed track on remote participant, unpublishing",Object.assign(Object.assign({},this.logContext),rJ(e))),this.unpublishTrack(e.trackSid,!0))}),i.forEach(e=>{this.emit(S.TrackPublished,e)}),!0}unpublishTrack(e,t){let i=this.trackPublications.get(e);if(!i)return;let{track:n}=i;switch(n&&(n.stop(),i.setTrack(void 0)),this.trackPublications.delete(e),i.kind){case n3.Kind.Audio:this.audioTrackPublications.delete(e);break;case n3.Kind.Video:this.videoTrackPublications.delete(e)}t&&this.emit(S.TrackUnpublished,i)}setAudioOutput(e){return iz(this,void 0,void 0,function*(){this.audioOutput=e;let t=[];this.audioTrackPublications.forEach(i=>{var n;r_(i.track)&&rF(i.track)&&t.push(i.track.setSinkId(null!=(n=e.deviceId)?n:"default"))}),yield Promise.all(t)})}emit(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return this.log.trace("participant event",Object.assign(Object.assign({},this.logContext),{event:e,args:i})),super.emit(e,...i)}}!function(e){e.Disconnected="disconnected",e.Connecting="connecting",e.Connected="connected",e.Reconnecting="reconnecting",e.SignalReconnecting="signalReconnecting"}(F||(F={}));class st extends iJ.EventEmitter{constructor(e){var t,i,n,r;if(super(),t=this,this.state=F.Disconnected,this.activeSpeakers=[],this.isE2EEEnabled=!1,this.audioEnabled=!0,this.isVideoPlaybackBlocked=!1,this.log=iB,this.bufferedEvents=[],this.isResuming=!1,this.byteStreamControllers=new Map,this.textStreamControllers=new Map,this.byteStreamHandlers=new Map,this.textStreamHandlers=new Map,this.rpcHandlers=new Map,this.connect=(e,t,i)=>iz(this,void 0,void 0,function*(){var n;if(!("undefined"!=typeof RTCPeerConnection&&(ra()||rs())))if(rm())throw Error("WebRTC isn't detected, have you called registerGlobals?");else throw Error("LiveKit doesn't seem to be supported on this browser. Try to update your browser and make sure no browser extensions are disabling webRTC.");let r=yield this.disconnectLock.lock();if(this.state===F.Connected)return this.log.info("already connected to room ".concat(this.name),this.logContext),r(),Promise.resolve();if(this.connectFuture)return r(),this.connectFuture.promise;this.setAndEmitConnectionState(F.Connecting),(null==(n=this.regionUrlProvider)?void 0:n.getServerUrl().toString())!==e&&(this.regionUrl=void 0,this.regionUrlProvider=void 0),rf(new URL(e))&&(void 0===this.regionUrlProvider?this.regionUrlProvider=new az(e,t):this.regionUrlProvider.updateToken(t),this.regionUrlProvider.fetchRegionSettings().then(e=>{var t;null==(t=this.regionUrlProvider)||t.setServerReportedRegions(e)}).catch(e=>{this.log.warn("could not fetch region settings",Object.assign(Object.assign({},this.logContext),{error:e}))}));let a=(n,s,o)=>iz(this,void 0,void 0,function*(){var c,l;this.abortController&&this.abortController.abort();let d=new AbortController;this.abortController=d,null==r||r();try{yield this.attemptConnection(null!=o?o:e,t,i,d),this.abortController=void 0,n()}catch(e){if(this.regionUrlProvider&&e instanceof nH&&e.reason!==y.Cancelled&&e.reason!==y.NotAllowed){let t=null;try{t=yield this.regionUrlProvider.getNextBestRegionUrl(null==(c=this.abortController)?void 0:c.signal)}catch(e){if(e instanceof nH&&(401===e.status||e.reason===y.Cancelled)){this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),s(e);return}}!t||(null==(l=this.abortController)?void 0:l.signal.aborted)?(this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,rM(e)),s(e)):(this.log.info("Initial connection failed with ConnectionError: ".concat(e.message,". Retrying with another region: ").concat(t),this.logContext),this.recreateEngine(),yield a(n,s,t))}else{let t=to.UNKNOWN_REASON;e instanceof nH&&(t=rM(e)),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,t),s(e)}}}),s=this.regionUrl;return this.regionUrl=void 0,this.connectFuture=new rI((e,t)=>{a(e,t,s)},()=>{this.clearConnectionFutures()}),this.connectFuture.promise}),this.connectSignal=(e,t,i,n,r,a)=>iz(this,void 0,void 0,function*(){var s,o,c;let l=yield i.join(e,t,{autoSubscribe:n.autoSubscribe,adaptiveStream:"object"==typeof r.adaptiveStream||r.adaptiveStream,maxRetries:n.maxRetries,e2eeEnabled:!!this.e2eeManager,websocketTimeout:n.websocketTimeout},a.signal),d=l.serverInfo;if(d||(d={version:l.serverVersion,region:l.serverRegion}),this.serverInfo=d,this.log.debug("connected to Livekit Server ".concat(Object.entries(d).map(e=>{let[t,i]=e;return"".concat(t,": ").concat(i)}).join(", ")),{room:null==(s=l.room)?void 0:s.name,roomSid:null==(o=l.room)?void 0:o.sid,identity:null==(c=l.participant)?void 0:c.identity}),!d.version)throw new nG("unknown server version");return"0.15.1"===d.version&&this.options.dynacast&&(this.log.debug("disabling dynacast due to server version",this.logContext),r.dynacast=!1),l}),this.applyJoinResponse=e=>{let t=e.participant;if(this.localParticipant.sid=t.sid,this.localParticipant.identity=t.identity,this.localParticipant.setEnabledPublishCodecs(e.enabledPublishCodecs),this.options.e2ee&&this.e2eeManager)try{this.e2eeManager.setSifTrailer(e.sifTrailer)}catch(e){this.log.error(e instanceof Error?e.message:"Could not set SifTrailer",Object.assign(Object.assign({},this.logContext),{error:e}))}this.handleParticipantUpdates([t,...e.otherParticipants]),e.room&&this.handleRoomUpdate(e.room)},this.attemptConnection=(e,t,i,n)=>iz(this,void 0,void 0,function*(){var r,a;this.state===F.Reconnecting||this.isResuming||(null==(r=this.engine)?void 0:r.pendingReconnect)?(this.log.info("Reconnection attempt replaced by new connection attempt",this.logContext),this.recreateEngine()):this.maybeCreateEngine(),(null==(a=this.regionUrlProvider)?void 0:a.isCloud())&&this.engine.setRegionUrlProvider(this.regionUrlProvider),this.acquireAudioContext(),this.connOptions=Object.assign(Object.assign({},ah),i),this.connOptions.rtcConfig&&(this.engine.rtcConfig=this.connOptions.rtcConfig),this.connOptions.peerConnectionTimeout&&(this.engine.peerConnectionTimeout=this.connOptions.peerConnectionTimeout);try{let i=yield this.connectSignal(e,t,this.engine,this.connOptions,this.options,n);this.applyJoinResponse(i),this.setupLocalParticipantEvents(),this.emit(C.SignalConnected)}catch(t){yield this.engine.close(),this.recreateEngine();let e=new nH("could not establish signal connection",y.ServerUnreachable);throw t instanceof Error&&(e.message="".concat(e.message,": ").concat(t.message)),t instanceof nH&&(e.reason=t.reason,e.status=t.status),this.log.debug("error trying to establish signal connection",Object.assign(Object.assign({},this.logContext),{error:t})),e}if(n.signal.aborted)throw yield this.engine.close(),this.recreateEngine(),new nH("Connection attempt aborted",y.Cancelled);try{yield this.engine.waitForPCInitialConnection(this.connOptions.peerConnectionTimeout,n)}catch(e){throw yield this.engine.close(),this.recreateEngine(),e}rp()&&this.options.disconnectOnPageLeave&&(window.addEventListener("pagehide",this.onPageLeave),window.addEventListener("beforeunload",this.onPageLeave)),rp()&&document.addEventListener("freeze",this.onPageLeave),this.setAndEmitConnectionState(F.Connected),this.emit(C.Connected),this.registerConnectionReconcile()}),this.disconnect=function(){for(var e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];return iz(t,[...i],void 0,function(){var e=this;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return function*(){var i,n,r,a;let s=yield e.disconnectLock.lock();try{if(e.state===F.Disconnected)return void e.log.debug("already disconnected",e.logContext);e.log.info("disconnect from room",Object.assign({},e.logContext)),(e.state===F.Connecting||e.state===F.Reconnecting||e.isResuming)&&(e.log.warn("abort connection attempt",e.logContext),null==(i=e.abortController)||i.abort(),null==(r=null==(n=e.connectFuture)?void 0:n.reject)||r.call(n,new nH("Client initiated disconnect",y.Cancelled)),e.connectFuture=void 0),(null==(a=e.engine)?void 0:a.client.isDisconnected)||(yield e.engine.client.sendLeave()),e.engine&&(yield e.engine.close()),e.handleDisconnect(t,to.CLIENT_INITIATED),e.engine=void 0}finally{s()}}()})},this.onPageLeave=()=>iz(this,void 0,void 0,function*(){this.log.info("Page leave detected, disconnecting",this.logContext),yield this.disconnect()}),this.startAudio=()=>iz(this,void 0,void 0,function*(){let e=[],t=nX();if(t&&"iOS"===t.os){let t="livekit-dummy-audio-el",i=document.getElementById(t);if(!i){(i=document.createElement("audio")).id=t,i.autoplay=!0,i.hidden=!0;let e=rR();e.enabled=!0;let n=new MediaStream([e]);i.srcObject=n,document.addEventListener("visibilitychange",()=>{i&&(i.srcObject=document.hidden?null:n,document.hidden||(this.log.debug("page visible again, triggering startAudio to resume playback and update playback status",this.logContext),this.startAudio()))}),document.body.append(i),this.once(C.Disconnected,()=>{null==i||i.remove(),i=null})}e.push(i)}this.remoteParticipants.forEach(t=>{t.audioTrackPublications.forEach(t=>{t.track&&t.track.attachedElements.forEach(t=>{e.push(t)})})});try{yield Promise.all([this.acquireAudioContext(),...e.map(e=>(e.muted=!1,e.play()))]),this.handleAudioPlaybackStarted()}catch(e){throw this.handleAudioPlaybackFailed(e),e}}),this.startVideo=()=>iz(this,void 0,void 0,function*(){let e=[];for(let t of this.remoteParticipants.values())t.videoTrackPublications.forEach(t=>{var i;null==(i=t.track)||i.attachedElements.forEach(t=>{e.includes(t)||e.push(t)})});yield Promise.all(e.map(e=>e.play())).then(()=>{this.handleVideoPlaybackStarted()}).catch(e=>{"NotAllowedError"===e.name?this.handleVideoPlaybackFailed():this.log.warn("Resuming video playback failed, make sure you call `startVideo` directly in a user gesture handler",this.logContext)})}),this.handleRestarting=()=>{for(let e of(this.clearConnectionReconcile(),this.isResuming=!1,this.remoteParticipants.values()))this.handleParticipantDisconnected(e.identity,e);this.setAndEmitConnectionState(F.Reconnecting)&&this.emit(C.Reconnecting)},this.handleSignalRestarted=e=>iz(this,void 0,void 0,function*(){this.log.debug("signal reconnected to server, region ".concat(e.serverRegion),Object.assign(Object.assign({},this.logContext),{region:e.serverRegion})),this.bufferedEvents=[],this.applyJoinResponse(e);try{yield this.localParticipant.republishAllTracks(void 0,!0)}catch(e){this.log.error("error trying to re-publish tracks after reconnection",Object.assign(Object.assign({},this.logContext),{error:e}))}try{yield this.engine.waitForRestarted(),this.log.debug("fully reconnected to server",Object.assign(Object.assign({},this.logContext),{region:e.serverRegion}))}catch(e){return}this.setAndEmitConnectionState(F.Connected),this.emit(C.Reconnected),this.registerConnectionReconcile(),this.emitBufferedEvents()}),this.handleParticipantUpdates=e=>{e.forEach(e=>{var t;if(e.identity===this.localParticipant.identity)return void this.localParticipant.updateInfo(e);""===e.identity&&(e.identity=null!=(t=this.sidToIdentity.get(e.sid))?t:"");let i=this.remoteParticipants.get(e.identity);e.state===tf.DISCONNECTED?this.handleParticipantDisconnected(e.identity,i):i=this.getOrCreateParticipant(e.identity,e)})},this.handleActiveSpeakersUpdate=e=>{let t=[],i={};e.forEach(e=>{if(i[e.sid]=!0,e.sid===this.localParticipant.sid)this.localParticipant.audioLevel=e.level,this.localParticipant.setIsSpeaking(!0),t.push(this.localParticipant);else{let i=this.getRemoteParticipantBySid(e.sid);i&&(i.audioLevel=e.level,i.setIsSpeaking(!0),t.push(i))}}),i[this.localParticipant.sid]||(this.localParticipant.audioLevel=0,this.localParticipant.setIsSpeaking(!1)),this.remoteParticipants.forEach(e=>{i[e.sid]||(e.audioLevel=0,e.setIsSpeaking(!1))}),this.activeSpeakers=t,this.emitWhenConnected(C.ActiveSpeakersChanged,t)},this.handleSpeakersChanged=e=>{let t=new Map;this.activeSpeakers.forEach(e=>{let i=this.remoteParticipants.get(e.identity);i&&i.sid!==e.sid||t.set(e.sid,e)}),e.forEach(e=>{let i=this.getRemoteParticipantBySid(e.sid);e.sid===this.localParticipant.sid&&(i=this.localParticipant),i&&(i.audioLevel=e.level,i.setIsSpeaking(e.active),e.active?t.set(e.sid,i):t.delete(e.sid))});let i=Array.from(t.values());i.sort((e,t)=>t.audioLevel-e.audioLevel),this.activeSpeakers=i,this.emitWhenConnected(C.ActiveSpeakersChanged,i)},this.handleStreamStateUpdate=e=>{e.streamStates.forEach(e=>{let t=this.getRemoteParticipantBySid(e.participantSid);if(!t)return;let i=t.getTrackPublicationBySid(e.trackSid);if(!i||!i.track)return;let n=n3.streamStateFromProto(e.state);n!==i.track.streamState&&(i.track.streamState=n,t.emit(S.TrackStreamStateChanged,i,i.track.streamState),this.emitWhenConnected(C.TrackStreamStateChanged,i,i.track.streamState,t))})},this.handleSubscriptionPermissionUpdate=e=>{let t=this.getRemoteParticipantBySid(e.participantSid);if(!t)return;let i=t.getTrackPublicationBySid(e.trackSid);i&&i.setAllowed(e.allowed)},this.handleSubscriptionError=e=>{let t=Array.from(this.remoteParticipants.values()).find(t=>t.trackPublications.has(e.trackSid));if(!t)return;let i=t.getTrackPublicationBySid(e.trackSid);i&&i.setSubscriptionError(e.err)},this.handleDataPacket=e=>{let t=this.remoteParticipants.get(e.participantIdentity);if("user"===e.value.case)this.handleUserPacket(t,e.value.value,e.kind);else if("transcription"===e.value.case)this.handleTranscription(t,e.value.value);else if("sipDtmf"===e.value.case)this.handleSipDtmf(t,e.value.value);else if("chatMessage"===e.value.case)this.handleChatMessage(t,e.value.value);else if("metrics"===e.value.case)this.handleMetrics(e.value.value,t);else if("streamHeader"===e.value.case)this.handleStreamHeader(e.value.value,e.participantIdentity);else if("streamChunk"===e.value.case)this.handleStreamChunk(e.value.value);else if("streamTrailer"===e.value.case)this.handleStreamTrailer(e.value.value);else if("rpcRequest"===e.value.case){let t=e.value.value;this.handleIncomingRpcRequest(e.participantIdentity,t.id,t.method,t.payload,t.responseTimeoutMs,t.version)}},this.handleUserPacket=(e,t,i)=>{this.emit(C.DataReceived,t.payload,e,i,t.topic),null==e||e.emit(S.DataReceived,t.payload,i)},this.handleSipDtmf=(e,t)=>{this.emit(C.SipDTMFReceived,t,e),null==e||e.emit(S.SipDTMFReceived,t)},this.bufferedSegments=new Map,this.handleTranscription=(e,t)=>{let i=t.transcribedParticipantIdentity===this.localParticipant.identity?this.localParticipant:this.getParticipantByIdentity(t.transcribedParticipantIdentity),n=null==i?void 0:i.trackPublications.get(t.trackId),r=function(e,t){return e.segments.map(e=>{var i;let{id:n,text:r,language:a,startTime:s,endTime:o,final:c}=e,l=null!=(i=t.get(n))?i:Date.now(),d=Date.now();return c?t.delete(n):t.set(n,l),{id:n,text:r,startTime:Number.parseInt(s.toString()),endTime:Number.parseInt(o.toString()),final:c,language:a,firstReceivedTime:l,lastReceivedTime:d}})}(t,this.transcriptionReceivedTimes);null==n||n.emit(E.TranscriptionReceived,r),null==i||i.emit(S.TranscriptionReceived,r,n),this.emit(C.TranscriptionReceived,r,i,n)},this.handleChatMessage=(e,t)=>{let i=function(e){let{id:t,timestamp:i,message:n,editTimestamp:r}=e;return{id:t,timestamp:Number.parseInt(i.toString()),editTimestamp:r?Number.parseInt(r.toString()):void 0,message:n}}(t);this.emit(C.ChatMessage,i,e)},this.handleMetrics=(e,t)=>{this.emit(C.MetricsReceived,e,t)},this.handleAudioPlaybackStarted=()=>{this.canPlaybackAudio||(this.audioEnabled=!0,this.emit(C.AudioPlaybackStatusChanged,!0))},this.handleAudioPlaybackFailed=e=>{this.log.warn("could not playback audio",Object.assign(Object.assign({},this.logContext),{error:e})),this.canPlaybackAudio&&(this.audioEnabled=!1,this.emit(C.AudioPlaybackStatusChanged,!1))},this.handleVideoPlaybackStarted=()=>{this.isVideoPlaybackBlocked&&(this.isVideoPlaybackBlocked=!1,this.emit(C.VideoPlaybackStatusChanged,!0))},this.handleVideoPlaybackFailed=()=>{this.isVideoPlaybackBlocked||(this.isVideoPlaybackBlocked=!0,this.emit(C.VideoPlaybackStatusChanged,!1))},this.handleDeviceChange=()=>iz(this,void 0,void 0,function*(){var e;(null==(e=nX())?void 0:e.os)!=="iOS"&&(yield this.selectDefaultDevices()),this.emit(C.MediaDevicesChanged)}),this.handleRoomUpdate=e=>{let t=this.roomInfo;this.roomInfo=e,t&&t.metadata!==e.metadata&&this.emitWhenConnected(C.RoomMetadataChanged,e.metadata),(null==t?void 0:t.activeRecording)!==e.activeRecording&&this.emitWhenConnected(C.RecordingStatusChanged,e.activeRecording)},this.handleConnectionQualityUpdate=e=>{e.updates.forEach(e=>{if(e.participantSid===this.localParticipant.sid)return void this.localParticipant.setConnectionQuality(e.quality);let t=this.getRemoteParticipantBySid(e.participantSid);t&&t.setConnectionQuality(e.quality)})},this.onLocalParticipantMetadataChanged=e=>{this.emit(C.ParticipantMetadataChanged,e,this.localParticipant)},this.onLocalParticipantNameChanged=e=>{this.emit(C.ParticipantNameChanged,e,this.localParticipant)},this.onLocalAttributesChanged=e=>{this.emit(C.ParticipantAttributesChanged,e,this.localParticipant)},this.onLocalTrackMuted=e=>{this.emit(C.TrackMuted,e,this.localParticipant)},this.onLocalTrackUnmuted=e=>{this.emit(C.TrackUnmuted,e,this.localParticipant)},this.onTrackProcessorUpdate=e=>{var t;null==(t=null==e?void 0:e.onPublish)||t.call(e,this)},this.onLocalTrackPublished=e=>iz(this,void 0,void 0,function*(){var t,i,n,r,a,s;null==(t=e.track)||t.on(E.TrackProcessorUpdate,this.onTrackProcessorUpdate),null==(i=e.track)||i.on(E.Restarted,this.onLocalTrackRestarted),null==(a=null==(r=null==(n=e.track)?void 0:n.getProcessor())?void 0:r.onPublish)||a.call(r,this),this.emit(C.LocalTrackPublished,e,this.localParticipant),rj(e.track)&&(yield e.track.checkForSilence())&&this.emit(C.LocalAudioSilenceDetected,e);let o=yield null==(s=e.track)?void 0:s.getDeviceId(!1),c=rG(e.source);c&&o&&o!==this.localParticipant.activeDeviceMap.get(c)&&(this.localParticipant.activeDeviceMap.set(c,o),this.emit(C.ActiveDeviceChanged,c,o))}),this.onLocalTrackUnpublished=e=>{var t,i;null==(t=e.track)||t.off(E.TrackProcessorUpdate,this.onTrackProcessorUpdate),null==(i=e.track)||i.off(E.Restarted,this.onLocalTrackRestarted),this.emit(C.LocalTrackUnpublished,e,this.localParticipant)},this.onLocalTrackRestarted=e=>iz(this,void 0,void 0,function*(){let t=yield e.getDeviceId(!1),i=rG(e.source);i&&t&&t!==this.localParticipant.activeDeviceMap.get(i)&&(this.log.debug("local track restarted, setting ".concat(i," ").concat(t," active"),this.logContext),this.localParticipant.activeDeviceMap.set(i,t),this.emit(C.ActiveDeviceChanged,i,t))}),this.onLocalConnectionQualityChanged=e=>{this.emit(C.ConnectionQualityChanged,e,this.localParticipant)},this.onMediaDevicesError=(e,t)=>{this.emit(C.MediaDevicesError,e,t)},this.onLocalParticipantPermissionsChanged=e=>{this.emit(C.ParticipantPermissionsChanged,e,this.localParticipant)},this.onLocalChatMessageSent=e=>{this.emit(C.ChatMessage,e,this.localParticipant)},this.setMaxListeners(100),this.remoteParticipants=new Map,this.sidToIdentity=new Map,this.options=Object.assign(Object.assign({},au),e),this.log=iV(null!=(i=this.options.loggerName)?i:h.Room),this.transcriptionReceivedTimes=new Map,this.options.audioCaptureDefaults=Object.assign(Object.assign({},al),null==e?void 0:e.audioCaptureDefaults),this.options.videoCaptureDefaults=Object.assign(Object.assign({},ad),null==e?void 0:e.videoCaptureDefaults),this.options.publishDefaults=Object.assign(Object.assign({},ac),null==e?void 0:e.publishDefaults),this.maybeCreateEngine(),this.disconnectLock=new W,this.localParticipant=new a7("","",this.engine,this.options,this.rpcHandlers),this.options.videoCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("videoinput",rx(this.options.videoCaptureDefaults.deviceId)),this.options.audioCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("audioinput",rx(this.options.audioCaptureDefaults.deviceId)),(null==(n=this.options.audioOutput)?void 0:n.deviceId)&&this.switchActiveDevice("audiooutput",rx(this.options.audioOutput.deviceId)).catch(e=>this.log.warn("Could not set audio output: ".concat(e.message),this.logContext)),this.options.e2ee&&this.setupE2EE(),rp()){let e=new AbortController;null==(r=navigator.mediaDevices)||r.addEventListener("devicechange",this.handleDeviceChange,{signal:e.signal}),st.cleanupRegistry&&st.cleanupRegistry.register(this,()=>{e.abort()})}}registerTextStreamHandler(e,t){if(this.textStreamHandlers.has(e))throw TypeError('A text stream handler for topic "'.concat(e,'" has already been set.'));this.textStreamHandlers.set(e,t)}unregisterTextStreamHandler(e){this.textStreamHandlers.delete(e)}registerByteStreamHandler(e,t){if(this.byteStreamHandlers.has(e))throw TypeError('A byte stream handler for topic "'.concat(e,'" has already been set.'));this.byteStreamHandlers.set(e,t)}unregisterByteStreamHandler(e){this.byteStreamHandlers.delete(e)}registerRpcMethod(e,t){if(this.rpcHandlers.has(e))throw Error("RPC handler already registered for method ".concat(e,", unregisterRpcMethod before trying to register again"));this.rpcHandlers.set(e,t)}unregisterRpcMethod(e){this.rpcHandlers.delete(e)}handleIncomingRpcRequest(e,t,i,n,r,a){return iz(this,void 0,void 0,function*(){if(yield this.engine.publishRpcAck(e,t),1!==a)return void(yield this.engine.publishRpcResponse(e,t,null,am.builtIn("UNSUPPORTED_VERSION")));let s=this.rpcHandlers.get(i);if(!s)return void(yield this.engine.publishRpcResponse(e,t,null,am.builtIn("UNSUPPORTED_METHOD")));let o=null,c=null;try{let a=yield s({requestId:t,callerIdentity:e,payload:n,responseTimeout:r});af(a)>15360?(o=am.builtIn("RESPONSE_PAYLOAD_TOO_LARGE"),console.warn("RPC Response payload too large for ".concat(i))):c=a}catch(e){e instanceof am?o=e:(console.warn("Uncaught error returned by RPC handler for ".concat(i,". Returning APPLICATION_ERROR instead."),e),o=am.builtIn("APPLICATION_ERROR"))}yield this.engine.publishRpcResponse(e,t,c,o)})}setE2EEEnabled(e){return iz(this,void 0,void 0,function*(){if(this.e2eeManager)yield Promise.all([this.localParticipant.setE2EEEnabled(e)]),""!==this.localParticipant.identity&&this.e2eeManager.setParticipantCryptorEnabled(e,this.localParticipant.identity);else throw Error("e2ee not configured, please set e2ee settings within the room options")})}setupE2EE(){var e;this.options.e2ee&&("e2eeManager"in this.options.e2ee?this.e2eeManager=this.options.e2ee.e2eeManager:this.e2eeManager=new r$(this.options.e2ee),this.e2eeManager.on(v.ParticipantEncryptionStatusChanged,(e,t)=>{t.isLocal&&(this.isE2EEEnabled=e),this.emit(C.ParticipantEncryptionStatusChanged,e,t)}),this.e2eeManager.on(v.EncryptionError,e=>this.emit(C.EncryptionError,e)),null==(e=this.e2eeManager)||e.setup(this))}get logContext(){var e;return{room:this.name,roomID:null==(e=this.roomInfo)?void 0:e.sid,participant:this.localParticipant.identity,pID:this.localParticipant.sid}}get isRecording(){var e,t;return null!=(t=null==(e=this.roomInfo)?void 0:e.activeRecording)&&t}getSid(){return iz(this,void 0,void 0,function*(){return this.state===F.Disconnected?"":this.roomInfo&&""!==this.roomInfo.sid?this.roomInfo.sid:new Promise((e,t)=>{let i=t=>{""!==t.sid&&(this.engine.off(w.RoomUpdate,i),e(t.sid))};this.engine.on(w.RoomUpdate,i),this.once(C.Disconnected,()=>{this.engine.off(w.RoomUpdate,i),t("Room disconnected before room server id was available")})})})}get name(){var e,t;return null!=(t=null==(e=this.roomInfo)?void 0:e.name)?t:""}get metadata(){var e;return null==(e=this.roomInfo)?void 0:e.metadata}get numParticipants(){var e,t;return null!=(t=null==(e=this.roomInfo)?void 0:e.numParticipants)?t:0}get numPublishers(){var e,t;return null!=(t=null==(e=this.roomInfo)?void 0:e.numPublishers)?t:0}maybeCreateEngine(){(!this.engine||this.engine.isClosed)&&(this.engine=new aH(this.options),this.engine.on(w.ParticipantUpdate,this.handleParticipantUpdates).on(w.RoomUpdate,this.handleRoomUpdate).on(w.SpeakersChanged,this.handleSpeakersChanged).on(w.StreamStateChanged,this.handleStreamStateUpdate).on(w.ConnectionQualityUpdate,this.handleConnectionQualityUpdate).on(w.SubscriptionError,this.handleSubscriptionError).on(w.SubscriptionPermissionUpdate,this.handleSubscriptionPermissionUpdate).on(w.MediaTrackAdded,(e,t,i)=>{this.onTrackAdded(e,t,i)}).on(w.Disconnected,e=>{this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,e)}).on(w.ActiveSpeakersUpdate,this.handleActiveSpeakersUpdate).on(w.DataPacketReceived,this.handleDataPacket).on(w.Resuming,()=>{this.clearConnectionReconcile(),this.isResuming=!0,this.log.info("Resuming signal connection",this.logContext),this.setAndEmitConnectionState(F.SignalReconnecting)&&this.emit(C.SignalReconnecting)}).on(w.Resumed,()=>{this.registerConnectionReconcile(),this.isResuming=!1,this.log.info("Resumed signal connection",this.logContext),this.updateSubscriptions(),this.emitBufferedEvents(),this.setAndEmitConnectionState(F.Connected)&&this.emit(C.Reconnected)}).on(w.SignalResumed,()=>{this.bufferedEvents=[],(this.state===F.Reconnecting||this.isResuming)&&this.sendSyncState()}).on(w.Restarting,this.handleRestarting).on(w.SignalRestarted,this.handleSignalRestarted).on(w.Offline,()=>{this.setAndEmitConnectionState(F.Reconnecting)&&this.emit(C.Reconnecting)}).on(w.DCBufferStatusChanged,(e,t)=>{this.emit(C.DCBufferStatusChanged,e,t)}).on(w.LocalTrackSubscribed,e=>{let t=this.localParticipant.getTrackPublications().find(t=>{let{trackSid:i}=t;return i===e});if(!t)return void this.log.warn("could not find local track subscription for subscribed event",this.logContext);this.localParticipant.emit(S.LocalTrackSubscribed,t),this.emitWhenConnected(C.LocalTrackSubscribed,t,this.localParticipant)}).on(w.RoomMoved,e=>{this.log.debug("room moved",e),e.room&&this.handleRoomUpdate(e.room),this.remoteParticipants.forEach((e,t)=>{this.handleParticipantDisconnected(t,e)}),this.emit(C.Moved,e.room.name),e.participant?this.handleParticipantUpdates([e.participant,...e.otherParticipants]):this.handleParticipantUpdates(e.otherParticipants)}),this.localParticipant&&this.localParticipant.setupEngine(this.engine),this.e2eeManager&&this.e2eeManager.setupEngine(this.engine))}static getLocalDevices(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return rZ.getInstance().getDevices(e,t)}prepareConnection(e,t){return iz(this,void 0,void 0,function*(){if(this.state===F.Disconnected){this.log.debug("prepareConnection to ".concat(e),this.logContext);try{if(rf(new URL(e))&&t){this.regionUrlProvider=new az(e,t);let i=yield this.regionUrlProvider.getNextBestRegionUrl();i&&this.state===F.Disconnected&&(this.regionUrl=i,yield fetch(rO(i),{method:"HEAD"}),this.log.debug("prepared connection to ".concat(i),this.logContext))}else yield fetch(rO(e),{method:"HEAD"})}catch(e){this.log.warn("could not prepare connection",Object.assign(Object.assign({},this.logContext),{error:e}))}}})}getParticipantByIdentity(e){return this.localParticipant.identity===e?this.localParticipant:this.remoteParticipants.get(e)}clearConnectionFutures(){this.connectFuture=void 0}simulateScenario(e,t){return iz(this,void 0,void 0,function*(){let i,n=()=>{};switch(e){case"signal-reconnect":yield this.engine.client.handleOnClose("simulate disconnect");break;case"speaker":i=new iI({scenario:{case:"speakerUpdate",value:3}});break;case"node-failure":i=new iI({scenario:{case:"nodeFailure",value:!0}});break;case"server-leave":i=new iI({scenario:{case:"serverLeave",value:!0}});break;case"migration":i=new iI({scenario:{case:"migration",value:!0}});break;case"resume-reconnect":this.engine.failNext(),yield this.engine.client.handleOnClose("simulate resume-disconnect");break;case"disconnect-signal-on-resume":n=()=>iz(this,void 0,void 0,function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")}),i=new iI({scenario:{case:"disconnectSignalOnResume",value:!0}});break;case"disconnect-signal-on-resume-no-messages":n=()=>iz(this,void 0,void 0,function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")}),i=new iI({scenario:{case:"disconnectSignalOnResumeNoMessages",value:!0}});break;case"full-reconnect":this.engine.fullReconnectOnNext=!0,yield this.engine.client.handleOnClose("simulate full-reconnect");break;case"force-tcp":case"force-tls":i=new iI({scenario:{case:"switchCandidateProtocol",value:"force-tls"===e?2:1}}),n=()=>iz(this,void 0,void 0,function*(){let e=this.engine.client.onLeave;e&&e(new is({reason:to.CLIENT_INITIATED,action:io.RECONNECT}))});break;case"subscriber-bandwidth":if(void 0===t||"number"!=typeof t)throw Error("subscriber-bandwidth requires a number as argument");i=new iI({scenario:{case:"subscriberBandwidth",value:rA(t)}});break;case"leave-full-reconnect":i=new iI({scenario:{case:"leaveRequestFullReconnect",value:!0}})}i&&(yield this.engine.client.sendSimulateScenario(i),yield n())})}get canPlaybackAudio(){return this.audioEnabled}get canPlaybackVideo(){return!this.isVideoPlaybackBlocked}getActiveDevice(e){return this.localParticipant.activeDeviceMap.get(e)}switchActiveDevice(e,t){return iz(this,arguments,void 0,function(e,t){var i=this;let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return function*(){var r,a,s,o,c,l,d;let u=!0,h=!1,p=n?{exact:t}:t;if("audioinput"===e){h=0===i.localParticipant.audioTrackPublications.size;let t=null!=(r=i.getActiveDevice(e))?r:i.options.audioCaptureDefaults.deviceId;i.options.audioCaptureDefaults.deviceId=p;let n=Array.from(i.localParticipant.audioTrackPublications.values()).filter(e=>e.source===n3.Source.Microphone);try{u=(yield Promise.all(n.map(e=>{var t;return null==(t=e.audioTrack)?void 0:t.setDeviceId(p)}))).every(e=>!0===e)}catch(e){throw i.options.audioCaptureDefaults.deviceId=t,e}let a=n.some(e=>{var t,i;return null!=(i=null==(t=e.track)?void 0:t.isMuted)&&i});u&&a&&(h=!0)}else if("videoinput"===e){h=0===i.localParticipant.videoTrackPublications.size;let t=null!=(a=i.getActiveDevice(e))?a:i.options.videoCaptureDefaults.deviceId;i.options.videoCaptureDefaults.deviceId=p;let n=Array.from(i.localParticipant.videoTrackPublications.values()).filter(e=>e.source===n3.Source.Camera);try{u=(yield Promise.all(n.map(e=>{var t;return null==(t=e.videoTrack)?void 0:t.setDeviceId(p)}))).every(e=>!0===e)}catch(e){throw i.options.videoCaptureDefaults.deviceId=t,e}let r=n.some(e=>{var t,i;return null!=(i=null==(t=e.track)?void 0:t.isMuted)&&i});u&&r&&(h=!0)}else if("audiooutput"===e){if(h=!0,!rc()&&!i.options.webAudioMix||i.options.webAudioMix&&i.audioContext&&!("setSinkId"in i.audioContext))throw Error("cannot switch audio output, setSinkId not supported");i.options.webAudioMix&&(t=null!=(s=yield rZ.getInstance().normalizeDeviceId("audiooutput",t))?s:""),null!=(d=i.options).audioOutput||(d.audioOutput={});let n=null!=(c=i.getActiveDevice(e))?c:i.options.audioOutput.deviceId;i.options.audioOutput.deviceId=t;try{i.options.webAudioMix&&(null==(l=i.audioContext)||l.setSinkId(t)),yield Promise.all(Array.from(i.remoteParticipants.values()).map(e=>e.setAudioOutput({deviceId:t})))}catch(e){throw i.options.audioOutput.deviceId=n,e}}return h&&(i.localParticipant.activeDeviceMap.set(e,t),i.emit(C.ActiveDeviceChanged,e,t)),u}()})}setupLocalParticipantEvents(){this.localParticipant.on(S.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).on(S.ParticipantNameChanged,this.onLocalParticipantNameChanged).on(S.AttributesChanged,this.onLocalAttributesChanged).on(S.TrackMuted,this.onLocalTrackMuted).on(S.TrackUnmuted,this.onLocalTrackUnmuted).on(S.LocalTrackPublished,this.onLocalTrackPublished).on(S.LocalTrackUnpublished,this.onLocalTrackUnpublished).on(S.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).on(S.MediaDevicesError,this.onMediaDevicesError).on(S.AudioStreamAcquired,this.startAudio).on(S.ChatMessage,this.onLocalChatMessageSent).on(S.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged)}recreateEngine(){var e;null==(e=this.engine)||e.close(),this.engine=void 0,this.isResuming=!1,this.remoteParticipants.clear(),this.sidToIdentity.clear(),this.bufferedEvents=[],this.maybeCreateEngine()}onTrackAdded(e,t,i){let n;if(this.state===F.Connecting||this.state===F.Reconnecting){let n=()=>{this.onTrackAdded(e,t,i),r()},r=()=>{this.off(C.Reconnected,n),this.off(C.Connected,n),this.off(C.Disconnected,r)};this.once(C.Reconnected,n),this.once(C.Connected,n),this.once(C.Disconnected,r);return}if(this.state===F.Disconnected)return void this.log.warn("skipping incoming track after Room disconnected",this.logContext);if("ended"===e.readyState)return void this.log.info("skipping incoming track as it already ended",this.logContext);let r=function(e){let t=e.split("|");return t.length>1?[t[0],e.substr(t[0].length+1)]:[e,""]}(t.id),a=r[0],s=r[1],o=e.id;if(s&&s.startsWith("TR")&&(o=s),a===this.localParticipant.sid)return void this.log.warn("tried to create RemoteParticipant for local participant",this.logContext);let c=Array.from(this.remoteParticipants.values()).find(e=>e.sid===a);if(!c)return void this.log.error("Tried to add a track for a participant, that's not present. Sid: ".concat(a),this.logContext);this.options.adaptiveStream&&(n="object"==typeof this.options.adaptiveStream?this.options.adaptiveStream:{}),c.addSubscribedMediaTrack(e,o,t,i,n)}handleDisconnect(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=arguments.length>1?arguments[1]:void 0;if(this.clearConnectionReconcile(),this.isResuming=!1,this.bufferedEvents=[],this.transcriptionReceivedTimes.clear(),this.state!==F.Disconnected){this.regionUrl=void 0;try{this.remoteParticipants.forEach(e=>{e.trackPublications.forEach(t=>{e.unpublishTrack(t.trackSid)})}),this.localParticipant.trackPublications.forEach(e=>{var i,n,r;e.track&&this.localParticipant.unpublishTrack(e.track,t),t?(null==(i=e.track)||i.detach(),null==(n=e.track)||n.stop()):null==(r=e.track)||r.stopMonitor()}),this.localParticipant.off(S.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).off(S.ParticipantNameChanged,this.onLocalParticipantNameChanged).off(S.AttributesChanged,this.onLocalAttributesChanged).off(S.TrackMuted,this.onLocalTrackMuted).off(S.TrackUnmuted,this.onLocalTrackUnmuted).off(S.LocalTrackPublished,this.onLocalTrackPublished).off(S.LocalTrackUnpublished,this.onLocalTrackUnpublished).off(S.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).off(S.MediaDevicesError,this.onMediaDevicesError).off(S.AudioStreamAcquired,this.startAudio).off(S.ChatMessage,this.onLocalChatMessageSent).off(S.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged),this.localParticipant.trackPublications.clear(),this.localParticipant.videoTrackPublications.clear(),this.localParticipant.audioTrackPublications.clear(),this.remoteParticipants.clear(),this.sidToIdentity.clear(),this.activeSpeakers=[],this.audioContext&&"boolean"==typeof this.options.webAudioMix&&(this.audioContext.close(),this.audioContext=void 0),rp()&&(window.removeEventListener("beforeunload",this.onPageLeave),window.removeEventListener("pagehide",this.onPageLeave),window.removeEventListener("freeze",this.onPageLeave),null==(e=navigator.mediaDevices)||e.removeEventListener("devicechange",this.handleDeviceChange))}finally{this.setAndEmitConnectionState(F.Disconnected),this.emit(C.Disconnected,i)}}}handleParticipantDisconnected(e,t){var i;this.remoteParticipants.delete(e),t&&(t.trackPublications.forEach(e=>{t.unpublishTrack(e.trackSid,!0)}),this.emit(C.ParticipantDisconnected,t),t.setDisconnected(),null==(i=this.localParticipant)||i.handleParticipantDisconnected(t.identity))}handleStreamHeader(e,t){return iz(this,void 0,void 0,function*(){var i;if("byteHeader"===e.contentHeader.case){let n,r=this.byteStreamHandlers.get(e.topic);if(!r)return void this.log.debug("ignoring incoming byte stream due to no handler for topic",e.topic);let a={id:e.streamId,name:null!=(i=e.contentHeader.value.name)?i:"unknown",mimeType:e.mimeType,size:e.totalLength?Number(e.totalLength):void 0,topic:e.topic,timestamp:rD(e.timestamp),attributes:e.attributes},s=new ReadableStream({start:t=>{n=t,this.byteStreamControllers.set(e.streamId,{info:a,controller:n,startTime:Date.now()})}});r(new aK(a,s,rD(e.totalLength)),{identity:t})}else if("textHeader"===e.contentHeader.case){let i,n=this.textStreamHandlers.get(e.topic);if(!n)return void this.log.debug("ignoring incoming text stream due to no handler for topic",e.topic);let r={id:e.streamId,mimeType:e.mimeType,size:e.totalLength?Number(e.totalLength):void 0,topic:e.topic,timestamp:Number(e.timestamp),attributes:e.attributes},a=new ReadableStream({start:t=>{i=t,this.textStreamControllers.set(e.streamId,{info:r,controller:i,startTime:Date.now()})}});n(new aJ(r,a,rD(e.totalLength)),{identity:t})}})}handleStreamChunk(e){let t=this.byteStreamControllers.get(e.streamId);t&&e.content.length>0&&t.controller.enqueue(e);let i=this.textStreamControllers.get(e.streamId);i&&e.content.length>0&&i.controller.enqueue(e)}handleStreamTrailer(e){let t=this.textStreamControllers.get(e.streamId);t&&(t.info.attributes=Object.assign(Object.assign({},t.info.attributes),e.attributes),t.controller.close(),this.textStreamControllers.delete(e.streamId));let i=this.byteStreamControllers.get(e.streamId);i&&(i.info.attributes=Object.assign(Object.assign({},i.info.attributes),e.attributes),i.controller.close(),this.byteStreamControllers.delete(e.streamId))}selectDefaultDevices(){return iz(this,void 0,void 0,function*(){var e,t,i;let n=rZ.getInstance().previousDevices,r=yield rZ.getInstance().getDevices(void 0,!1),a=nX();if((null==a?void 0:a.name)==="Chrome"&&"iOS"!==a.os)for(let e of r){let t=n.find(t=>t.deviceId===e.deviceId);t&&""!==t.label&&t.kind===e.kind&&t.label!==e.label&&"default"===this.getActiveDevice(e.kind)&&this.emit(C.ActiveDeviceChanged,e.kind,e.deviceId)}for(let a of["audiooutput","audioinput","videoinput"]){let s="audioinput"===a?n3.Source.Microphone:"videoinput"===a?n3.Source.Camera:n3.Source.Unknown,o=this.localParticipant.getTrackPublication(s);if(o&&(null==(e=o.track)?void 0:e.isUserProvided))continue;let c=r.filter(e=>e.kind===a),l=this.getActiveDevice(a);if(l===(null==(t=n.filter(e=>e.kind===a)[0])?void 0:t.deviceId)&&c.length>0&&(null==(i=c[0])?void 0:i.deviceId)!==l){yield this.switchActiveDevice(a,c[0].deviceId);continue}("audioinput"!==a||ru())&&"videoinput"!==a&&(!(c.length>0)||c.find(e=>e.deviceId===this.getActiveDevice(a))||"audiooutput"===a&&ru()||(yield this.switchActiveDevice(a,c[0].deviceId)))}})}acquireAudioContext(){return iz(this,void 0,void 0,function*(){var e,t;if("boolean"!=typeof this.options.webAudioMix&&this.options.webAudioMix.audioContext?this.audioContext=this.options.webAudioMix.audioContext:this.audioContext&&"closed"!==this.audioContext.state||(this.audioContext=null!=(e=rz())?e:void 0),this.options.webAudioMix&&this.remoteParticipants.forEach(e=>e.setAudioContext(this.audioContext)),this.localParticipant.setAudioContext(this.audioContext),this.audioContext&&"suspended"===this.audioContext.state)try{yield Promise.race([this.audioContext.resume(),rr(200)])}catch(e){this.log.warn("Could not resume audio context",Object.assign(Object.assign({},this.logContext),{error:e}))}let i=(null==(t=this.audioContext)?void 0:t.state)==="running";i!==this.canPlaybackAudio&&(this.audioEnabled=i,this.emit(C.AudioPlaybackStatusChanged,i))})}createParticipant(e,t){var i;let n;return n=t?se.fromParticipantInfo(this.engine.client,t,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}):new se(this.engine.client,"",e,void 0,void 0,void 0,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}),this.options.webAudioMix&&n.setAudioContext(this.audioContext),(null==(i=this.options.audioOutput)?void 0:i.deviceId)&&n.setAudioOutput(this.options.audioOutput).catch(e=>this.log.warn("Could not set audio output: ".concat(e.message),this.logContext)),n}getOrCreateParticipant(e,t){if(this.remoteParticipants.has(e)){let i=this.remoteParticipants.get(e);return t&&i.updateInfo(t)&&this.sidToIdentity.set(t.sid,t.identity),i}let i=this.createParticipant(e,t);return this.remoteParticipants.set(e,i),this.sidToIdentity.set(t.sid,t.identity),this.emitWhenConnected(C.ParticipantConnected,i),i.on(S.TrackPublished,e=>{this.emitWhenConnected(C.TrackPublished,e,i)}).on(S.TrackSubscribed,(e,t)=>{e.kind===n3.Kind.Audio?(e.on(E.AudioPlaybackStarted,this.handleAudioPlaybackStarted),e.on(E.AudioPlaybackFailed,this.handleAudioPlaybackFailed)):e.kind===n3.Kind.Video&&(e.on(E.VideoPlaybackFailed,this.handleVideoPlaybackFailed),e.on(E.VideoPlaybackStarted,this.handleVideoPlaybackStarted)),this.emit(C.TrackSubscribed,e,t,i)}).on(S.TrackUnpublished,e=>{this.emit(C.TrackUnpublished,e,i)}).on(S.TrackUnsubscribed,(e,t)=>{this.emit(C.TrackUnsubscribed,e,t,i)}).on(S.TrackMuted,e=>{this.emitWhenConnected(C.TrackMuted,e,i)}).on(S.TrackUnmuted,e=>{this.emitWhenConnected(C.TrackUnmuted,e,i)}).on(S.ParticipantMetadataChanged,e=>{this.emitWhenConnected(C.ParticipantMetadataChanged,e,i)}).on(S.ParticipantNameChanged,e=>{this.emitWhenConnected(C.ParticipantNameChanged,e,i)}).on(S.AttributesChanged,e=>{this.emitWhenConnected(C.ParticipantAttributesChanged,e,i)}).on(S.ConnectionQualityChanged,e=>{this.emitWhenConnected(C.ConnectionQualityChanged,e,i)}).on(S.ParticipantPermissionsChanged,e=>{this.emitWhenConnected(C.ParticipantPermissionsChanged,e,i)}).on(S.TrackSubscriptionStatusChanged,(e,t)=>{this.emitWhenConnected(C.TrackSubscriptionStatusChanged,e,t,i)}).on(S.TrackSubscriptionFailed,(e,t)=>{this.emit(C.TrackSubscriptionFailed,e,i,t)}).on(S.TrackSubscriptionPermissionChanged,(e,t)=>{this.emitWhenConnected(C.TrackSubscriptionPermissionChanged,e,t,i)}).on(S.Active,()=>{this.emitWhenConnected(C.ParticipantActive,i),i.kind===tg.AGENT&&this.localParticipant.setActiveAgent(i)}),t&&i.updateInfo(t),i}sendSyncState(){let e=Array.from(this.remoteParticipants.values()).reduce((e,t)=>(e.push(...t.getTrackPublications()),e),[]),t=this.localParticipant.getTrackPublications();this.engine.sendSyncState(e,t)}updateSubscriptions(){for(let t of this.remoteParticipants.values())for(let i of t.videoTrackPublications.values()){var e;i.isSubscribed&&(e=i)&&!e.isLocal&&i.emitTrackUpdate()}}getRemoteParticipantBySid(e){let t=this.sidToIdentity.get(e);if(t)return this.remoteParticipants.get(t)}registerConnectionReconcile(){this.clearConnectionReconcile();let e=0;this.connectionReconcileInterval=n5.setInterval(()=>{this.engine&&!this.engine.isClosed&&this.engine.verifyTransport()?e=0:(e++,this.log.warn("detected connection state mismatch",Object.assign(Object.assign({},this.logContext),{numFailures:e,engine:this.engine?{closed:this.engine.isClosed,transportsConnected:this.engine.verifyTransport()}:void 0})),e>=3&&(this.recreateEngine(),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,to.STATE_MISMATCH)))},4e3)}clearConnectionReconcile(){this.connectionReconcileInterval&&n5.clearInterval(this.connectionReconcileInterval)}setAndEmitConnectionState(e){return e!==this.state&&(this.state=e,this.emit(C.ConnectionStateChanged,this.state),!0)}emitBufferedEvents(){this.bufferedEvents.forEach(e=>{let[t,i]=e;this.emit(t,...i)}),this.bufferedEvents=[]}emitWhenConnected(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];if(this.state===F.Reconnecting||this.isResuming||!this.engine||this.engine.pendingReconnect)this.bufferedEvents.push([e,i]);else if(this.state===F.Connected)return this.emit(e,...i);return!1}simulateParticipants(e){return iz(this,void 0,void 0,function*(){var t,i;let n=Object.assign({audio:!0,video:!0,useRealTracks:!1},e.publish),r=Object.assign({count:9,audio:!1,video:!0,aspectRatios:[1.66,1.7,1.3]},e.participants);if(this.handleDisconnect(),this.roomInfo=new tu({sid:"RM_SIMULATED",name:"simulated-room",emptyTimeout:0,maxParticipants:0,creationTime:el.parse(new Date().getTime()),metadata:"",numParticipants:1,numPublishers:1,turnPassword:"",enabledCodecs:[],activeRecording:!1}),this.localParticipant.updateInfo(new tm({identity:"simulated-local",name:"local-name"})),this.setupLocalParticipantEvents(),this.emit(C.SignalConnected),this.emit(C.Connected),this.setAndEmitConnectionState(F.Connected),n.video){let e=new a9(n3.Kind.Video,new tk({source:tn.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:ti.AUDIO,name:"video-dummy"}),new aL(n.useRealTracks?(yield window.navigator.mediaDevices.getUserMedia({video:!0})).getVideoTracks()[0]:rP(160*(null!=(t=r.aspectRatios[0])?t:1),160,!0,!0),void 0,!1,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(e),this.localParticipant.emit(S.LocalTrackPublished,e)}if(n.audio){let e=new a9(n3.Kind.Audio,new tk({source:tn.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:ti.AUDIO}),new aS(n.useRealTracks?(yield navigator.mediaDevices.getUserMedia({audio:!0})).getAudioTracks()[0]:rR(),void 0,!1,this.audioContext,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(e),this.localParticipant.emit(S.LocalTrackPublished,e)}for(let e=0;e<r.count-1;e+=1){let t=new tm({sid:Math.floor(1e4*Math.random()).toString(),identity:"simulated-".concat(e),state:tf.ACTIVE,tracks:[],joinedAt:el.parse(Date.now())}),n=this.getOrCreateParticipant(t.identity,t);if(r.video){let a=rP(160*(null!=(i=r.aspectRatios[e%r.aspectRatios.length])?i:1),160,!1,!0),s=new tk({source:tn.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:ti.AUDIO});n.addSubscribedMediaTrack(a,s.sid,new MediaStream([a]),new RTCRtpReceiver),t.tracks=[...t.tracks,s]}if(r.audio){let e=rR(),i=new tk({source:tn.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:ti.AUDIO});n.addSubscribedMediaTrack(e,i.sid,new MediaStream([e]),new RTCRtpReceiver),t.tracks=[...t.tracks,i]}n.updateInfo(t)}})}emit(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];if(e!==C.ActiveSpeakersChanged&&e!==C.TranscriptionReceived){let t=(function e(t){return t.map(t=>{if(t)return Array.isArray(t)?e(t):"object"==typeof t?"logContext"in t?t.logContext:void 0:t})})(i).filter(e=>void 0!==e);this.log.debug("room event ".concat(e),Object.assign(Object.assign({},this.logContext),{event:e,args:t}))}return super.emit(e,...i)}}st.cleanupRegistry="undefined"!=typeof FinalizationRegistry&&new FinalizationRegistry(e=>{e()});!function(e){e[e.IDLE=0]="IDLE",e[e.RUNNING=1]="RUNNING",e[e.SKIPPED=2]="SKIPPED",e[e.SUCCESS=3]="SUCCESS",e[e.FAILED=4]="FAILED"}(B||(B={}));class si extends iJ.EventEmitter{constructor(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super(),this.status=B.IDLE,this.logs=[],this.options={},this.url=e,this.token=t,this.name=this.constructor.name,this.room=new st(i.roomOptions),this.connectOptions=i.connectOptions,this.options=i}run(e){return iz(this,void 0,void 0,function*(){if(this.status!==B.IDLE)throw Error("check is running already");this.setStatus(B.RUNNING);try{yield this.perform()}catch(e){e instanceof Error&&(this.options.errorsAsWarnings?this.appendWarning(e.message):this.appendError(e.message))}return yield this.disconnect(),yield new Promise(e=>setTimeout(e,500)),this.status!==B.SKIPPED&&this.setStatus(this.isSuccess()?B.SUCCESS:B.FAILED),e&&e(),this.getInfo()})}isSuccess(){return!this.logs.some(e=>"error"===e.level)}connect(e){return iz(this,void 0,void 0,function*(){return this.room.state===F.Connected||(e||(e=this.url),yield this.room.connect(e,this.token,this.connectOptions)),this.room})}disconnect(){return iz(this,void 0,void 0,function*(){this.room&&this.room.state!==F.Disconnected&&(yield this.room.disconnect(),yield new Promise(e=>setTimeout(e,500)))})}skip(){this.setStatus(B.SKIPPED)}switchProtocol(e){return iz(this,void 0,void 0,function*(){let t=!1,i=!1;if(this.room.on(C.Reconnecting,()=>{t=!0}),this.room.once(C.Reconnected,()=>{i=!0}),this.room.simulateScenario("force-".concat(e)),yield new Promise(e=>setTimeout(e,1e3)),!t)return;let n=Date.now()+1e4;for(;Date.now()<n;){if(i)return;yield rr(100)}throw Error("Could not reconnect using ".concat(e," protocol after 10 seconds"))})}appendMessage(e){this.logs.push({level:"info",message:e}),this.emit("update",this.getInfo())}appendWarning(e){this.logs.push({level:"warning",message:e}),this.emit("update",this.getInfo())}appendError(e){this.logs.push({level:"error",message:e}),this.emit("update",this.getInfo())}setStatus(e){this.status=e,this.emit("update",this.getInfo())}get engine(){var e;return null==(e=this.room)?void 0:e.engine}getInfo(){return{logs:this.logs,name:this.name,status:this.status,description:this.description}}}function sn(e){var t,i;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=rN(e)?e.mediaStreamTrack:e,a=r.getSettings(),s={facingMode:null!=(t=n.defaultFacingMode)?t:"user",confidence:"low"};if("facingMode"in a){let e=a.facingMode;iB.trace("rawFacingMode",{rawFacingMode:e}),e&&"string"==typeof e&&(void 0===(i=e)||["user","environment","left","right"].includes(i))&&(s={facingMode:e,confidence:"high"})}if(["low","medium"].includes(s.confidence)){iB.trace("Try to get facing mode from device label: (".concat(r.label,")"));let e=function(e){var t;let i=e.trim().toLowerCase();if(""!==i)return sr.has(i)?sr.get(i):null==(t=Array.from(sa.entries()).find(e=>{let[t]=e;return i.includes(t)}))?void 0:t[1]}(r.label);void 0!==e&&(s=e)}return s}iJ.EventEmitter;let sr=new Map([["obs virtual camera",{facingMode:"environment",confidence:"medium"}]]),sa=new Map([["iphone",{facingMode:"environment",confidence:"medium"}],["ipad",{facingMode:"environment",confidence:"medium"}]])},96093:(e,t,i)=>{let n;i.d(t,{A:()=>k,C:()=>y,K:()=>E,b:()=>x,c:()=>f,d:()=>g,e:()=>T,f:()=>P,g:()=>R,h:()=>M,i:()=>p,j:()=>u,k:()=>O,l:()=>S,m:()=>_,n:()=>v,o:()=>C,p:()=>I,t:()=>D,w:()=>A,x:()=>N,z:()=>h});var r=i(43210),a=i(11921),s=i(54274),o=i(89967);let c=e=>{let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),t},l=()=>n||(n=function(){let e=!1,t=[],i=new Map;if(typeof window>"u")return;let n=new ResizeObserver((n,r)=>{t=t.concat(n),e||window.requestAnimationFrame(()=>{let n=new Set;for(let e=0;e<t.length;e++){if(n.has(t[e].target))continue;n.add(t[e].target);let a=i.get(t[e].target);null==a||a.forEach(i=>i(t[e],r))}t=[],e=!1}),e=!0});return{observer:n,subscribe(e,t){n.observe(e);let r=i.get(e)??[];r.push(t),i.set(e,r)},unsubscribe(e,t){let r=i.get(e)??[];if(1===r.length){n.unobserve(e),i.delete(e);return}let a=r.indexOf(t);-1!==a&&r.splice(a,1),i.set(e,r)}}}()),d=e=>{let[t,i]=r.useState({width:0,height:0});return r.useLayoutEffect(()=>{if(e.current){let{width:t,height:n}=e.current.getBoundingClientRect();i({width:t,height:n})}},[e.current]),function(e,t){let i=l(),n=c(t);r.useLayoutEffect(()=>{let t=!1,r=e.current;if(r)return null==i||i.subscribe(r,a),()=>{t=!0,null==i||i.unsubscribe(r,a)};function a(e,i){t||n.current(e,i)}},[e.current,i,n]),null==i||i.observer}(e,r.useCallback(e=>i(e.contentRect),[])),t};function u(e,t,i=!0){let[n,a]=r.useState(t);return r.useEffect(()=>{if(i&&a(t),typeof window>"u"||!e)return;let n=e.subscribe(a);return()=>n.unsubscribe()},[e,i]),n}function h(e){let t=e=>"u">typeof window&&window.matchMedia(e).matches,[i,n]=r.useState(t(e));function a(){n(t(e))}return r.useEffect(()=>{let t=window.matchMedia(e);return a(),t.addListener?t.addListener(a):t.addEventListener("change",a),()=>{t.removeListener?t.removeListener(a):t.removeEventListener("change",a)}},[e]),i}function p(e={}){let t=(0,a.c)(e.participant),{className:i,connectionQualityObserver:n}=r.useMemo(()=>(0,a.d)(t),[t]);return{className:i,quality:u(n,t.connectionQuality)}}function m(e){let t=(0,a.u)(e);return u(r.useMemo(()=>(0,a.e)(t),[t]),t.state)}function f(e){let t=(0,a.f)(),i=m(t);return{buttonProps:r.useMemo(()=>{let{className:n,disconnect:r}=(0,a.h)(t);return(0,s.m)(e,{className:n,onClick:()=>r(e.stopTracks??!0),disabled:i===o.KN.Disconnected})},[t,e,i])}}function g({trackRef:e,props:t}){let i=(0,a.i)(e),n=(0,a.j)(),{className:o}=r.useMemo(()=>(0,a.k)(),[]),c=r.useMemo(()=>(0,a.m)(i,null==n?void 0:n.pin.state),[i,null==n?void 0:n.pin.state]);return{mergedProps:r.useMemo(()=>(0,s.m)(t,{className:o,onClick:e=>{var r,a,s,o,l;null==(r=t.onClick)||r.call(t,e),c?null==(s=null==n?void 0:(a=n.pin).dispatch)||s.call(a,{msg:"clear_pin"}):null==(l=null==n?void 0:(o=n.pin).dispatch)||l.call(o,{msg:"set_pin",trackReference:i})}}),[t,o,i,c,null==n?void 0:n.pin]),inFocus:c}}function v(e,t,i={}){let n=i.gridLayouts??a.G,{width:s,height:o}=d(e),c=(0,a.n)(n,t,s,o);return r.useEffect(()=>{e.current&&c&&(e.current.style.setProperty("--lk-col-count",null==c?void 0:c.columns.toString()),e.current.style.setProperty("--lk-row-count",null==c?void 0:c.rows.toString()))},[e,c]),{layout:c,containerWidth:s,containerHeight:o}}function b(e,t={}){var i,n;let s="string"==typeof e?t.participant:e.participant,o=(0,a.c)(s),c="string"==typeof e?{participant:o,source:e}:e,[l,d]=r.useState(!!(null!=(i=c.publication)&&i.isMuted||null!=(n=o.getTrackPublication(c.source))&&n.isMuted));return r.useEffect(()=>{let e=(0,a.o)(c).subscribe(d);return()=>e.unsubscribe()},[(0,a.p)(c)]),l}function y(e={}){let t=(0,a.u)(e.room),[i,n]=r.useState(t.localParticipant),[s,o]=r.useState(i.isMicrophoneEnabled),[c,l]=r.useState(i.isCameraEnabled),[d,u]=r.useState(i.isScreenShareEnabled),[h,p]=r.useState(i.lastMicrophoneError),[m,f]=r.useState(i.lastCameraError),[g,v]=r.useState(void 0),[b,k]=r.useState(void 0),T=e=>{l(e.isCameraEnabled),o(e.isMicrophoneEnabled),u(e.isScreenShareEnabled),k(e.cameraTrack),v(e.microphoneTrack),p(e.participant.lastMicrophoneError),f(e.participant.lastCameraError),n(e.participant)};return r.useEffect(()=>{let e=(0,a.t)(t.localParticipant).subscribe(T);return()=>e.unsubscribe()},[t]),{isMicrophoneEnabled:s,isScreenShareEnabled:d,isCameraEnabled:c,microphoneTrack:g,cameraTrack:b,lastMicrophoneError:h,lastCameraError:m,localParticipant:i}}function k(){let e=(0,a.f)();return u(r.useMemo(()=>(0,a.v)(e.localParticipant),[e]),e.localParticipant.permissions)}function T({kind:e,room:t,track:i,requestPermissions:n,onError:s}){let c=(0,a.w)(),l=r.useMemo(()=>t??c??new o.Wv,[t,c]),d=u(r.useMemo(()=>(0,a.x)(e,s,n),[e,n,s]),[]),[h,p]=r.useState((null==l?void 0:l.getActiveDevice(e))??"default"),{className:m,activeDeviceObservable:f,setActiveMediaDevice:g}=r.useMemo(()=>(0,a.y)(e,l),[e,l,i]);return r.useEffect(()=>{let e=f.subscribe(e=>{e&&(a.l.info("setCurrentDeviceId",e),p(e))});return()=>{null==e||e.unsubscribe()}},[f]),{devices:d,className:m,activeDeviceId:h,setActiveMediaDevice:g}}function C(e,t){let[i,n]=r.useState(1),s=Math.max(Math.ceil(t.length/e),1);i>s&&n(s);let o=i*e,c=o-e,l=e=>{n(t=>"next"===e?t===s?t:t+1:1===t?t:t-1)},d=(function(e,t,i={}){let n=r.useRef([]),s=r.useRef(-1),o=t!==s.current,c="function"==typeof i.customSortFunction?i.customSortFunction(e):(0,a.z)(e),l=[...c];if(!1===o)try{l=(0,a.A)(n.current,c,t)}catch(e){a.l.error("Error while running updatePages(): ",e)}return o?n.current=c:n.current=l,s.current=t,l})(t,e).slice(c,o);return{totalPageCount:s,nextPage:()=>l("next"),prevPage:()=>l("previous"),setPage:e=>{e>s?n(s):e<1?n(1):n(e)},firstItemIndex:c,lastItemIndex:o,tracks:d,currentPage:i}}function S({trackRef:e,onParticipantClick:t,disableSpeakingIndicator:i,htmlProps:n}){let c=(0,a.i)(e),l=r.useMemo(()=>{let{className:e}=(0,a.D)();return(0,s.m)(n,{className:e,onClick:e=>{var i;if(null==(i=n.onClick)||i.call(n,e),"function"==typeof t){let e=c.publication??c.participant.getTrackPublication(c.source);t({participant:c.participant,track:e})}}})},[n,t,c.publication,c.source,c.participant]),d=c.participant.getTrackPublication(o.CC.Source.Microphone),h=r.useMemo(()=>({participant:c.participant,source:o.CC.Source.Microphone,publication:d}),[d,c.participant]),p=b(c),m=b(h),f=function(e){let t=(0,a.c)(e);return u(r.useMemo(()=>(0,a.q)(t),[t]),t.isSpeaking)}(c.participant),g=function(e){if(e.publication instanceof o.HO){let t=e.publication.track;if(t){let{facingMode:e}=(0,o.EC)(t);return e}}return"undefined"}(c);return{elementProps:{"data-lk-audio-muted":m,"data-lk-video-muted":p,"data-lk-speaking":!0!==i&&f,"data-lk-local-participant":c.participant.isLocal,"data-lk-source":c.source,"data-lk-facing-mode":g,...l}}}function w(e={}){let t=(0,a.u)(e.room),[i,n]=r.useState([]);return r.useEffect(()=>{let i=(0,a.E)(t,{additionalRoomEvents:e.updateOnlyOn}).subscribe(n);return()=>i.unsubscribe()},[t,JSON.stringify(e.updateOnlyOn)]),i}function E(e={}){let t=w(e),{localParticipant:i}=y(e);return r.useMemo(()=>[i,...t],[i,t])}function P({room:e,props:t}){let i=(0,a.u)(e),{className:n,roomAudioPlaybackAllowedObservable:o,handleStartAudioPlayback:c}=r.useMemo(()=>(0,a.N)(),[]),{canPlayAudio:l}=u(r.useMemo(()=>o(i),[i,o]),{canPlayAudio:i.canPlaybackAudio});return{mergedProps:r.useMemo(()=>(0,s.m)(t,{className:n,onClick:()=>{c(i)},style:{display:l?"none":"block"}}),[t,n,l,c,i]),canPlayAudio:l}}function R({room:e,props:t}){let i=(0,a.u)(e),{className:n,roomVideoPlaybackAllowedObservable:o,handleStartVideoPlayback:c}=r.useMemo(()=>(0,a.O)(),[]),{canPlayVideo:l}=u(r.useMemo(()=>o(i),[i,o]),{canPlayVideo:i.canPlaybackVideo});return{mergedProps:r.useMemo(()=>(0,s.m)(t,{className:n,onClick:()=>{c(i)},style:{display:l?"none":"block"}}),[t,n,l,c,i]),canPlayVideo:l}}function I(e,t={}){let i=r.useRef(null),n=r.useRef(null),a=t.minSwipeDistance??50,s=e=>{n.current=null,i.current=e.targetTouches[0].clientX},o=e=>{n.current=e.targetTouches[0].clientX},c=r.useCallback(()=>{if(!i.current||!n.current)return;let e=i.current-n.current,r=e>a,s=e<-a;r&&t.onLeftSwipe&&t.onLeftSwipe(),s&&t.onRightSwipe&&t.onRightSwipe()},[a,t]);r.useEffect(()=>{let t=e.current;return t&&(t.addEventListener("touchstart",s,{passive:!0}),t.addEventListener("touchmove",o,{passive:!0}),t.addEventListener("touchend",c,{passive:!0})),()=>{t&&(t.removeEventListener("touchstart",s),t.removeEventListener("touchmove",o),t.removeEventListener("touchend",c))}},[e,c])}function x({props:e}){let{dispatch:t,state:i}=(0,a.a)().widget,{className:n}=r.useMemo(()=>(0,a.P)(),[]);return{mergedProps:r.useMemo(()=>(0,s.m)(e,{className:n,onClick:()=>{t&&t({msg:"toggle_chat"})},"aria-pressed":null!=i&&i.showChat?"true":"false","data-lk-unread-msgs":i?i.unreadMessages<10?i.unreadMessages.toFixed(0):"9+":"0"}),[e,n,t,i])}}function O(e){var t,i;let n=(0,a.i)(e),{className:s,mediaMutedObserver:o}=r.useMemo(()=>(0,a.Q)(n),[(0,a.p)(n)]);return{isMuted:u(o,!!(null!=(t=n.publication)&&t.isMuted||null!=(i=n.participant.getTrackPublication(n.source))&&i.isMuted)),className:s}}function M({source:e,onChange:t,initialState:i,captureOptions:n,publishOptions:o,onDeviceError:c,...l}){var d;let h=(0,a.w)(),p=null==(d=null==h?void 0:h.localParticipant)?void 0:d.getTrackPublication(e),m=r.useRef(!1),{toggle:f,className:g,pendingObserver:v,enabledObserver:b}=r.useMemo(()=>h?(0,a.S)(e,h,n,o,c):(0,a.T)(),[h,e,JSON.stringify(n),o]),y=u(v,!1),k=u(b,i??!!(null!=p&&p.isEnabled));r.useEffect(()=>{null==t||t(k,m.current),m.current=!1},[k,t]),r.useEffect(()=>{void 0!==i&&(a.l.debug("forcing initial toggle state",e,i),f(i))},[]);let T=r.useMemo(()=>(0,s.m)(l,{className:g}),[l,g]),C=r.useCallback(e=>{var t;m.current=!0,f().catch(()=>m.current=!1),null==(t=l.onClick)||t.call(l,e)},[l,f]);return{toggle:f,enabled:k,pending:y,track:p,buttonProps:{...T,"aria-pressed":k,"data-lk-source":e,"data-lk-enabled":k,disabled:y,onClick:C}}}function D(e=[o.CC.Source.Camera,o.CC.Source.Microphone,o.CC.Source.ScreenShare,o.CC.Source.ScreenShareAudio,o.CC.Source.Unknown],t={}){let i=(0,a.u)(t.room),[n,s]=r.useState([]),[c,l]=r.useState([]),d=r.useMemo(()=>e.map(e=>(0,a.U)(e)?e.source:e),[JSON.stringify(e)]);return r.useEffect(()=>{let e=(0,a.V)(i,d,{additionalRoomEvents:t.updateOnlyOn,onlySubscribed:t.onlySubscribed}).subscribe(({trackReferences:e,participants:t})=>{a.l.debug("setting track bundles",e,t),s(e),l(t)});return()=>e.unsubscribe()},[i,JSON.stringify(t.onlySubscribed),JSON.stringify(t.updateOnlyOn),JSON.stringify(e)]),r.useMemo(()=>{if(!(0,a.W)(e))return n;{let t=function(e,t){let i=new Map;if((0,a.W)(e)){let n=e.filter(e=>e.withPlaceholder).map(e=>e.source);t.forEach(e=>{let t=e.getTrackPublications().map(e=>{var t;return null==(t=e.track)?void 0:t.source}).filter(e=>void 0!==e),r=Array.from(function(e,t){let i=new Set(e);for(let e of t)i.delete(e);return i}(new Set(n),new Set(t)));r.length>0&&i.set(e.identity,r)})}return i}(e,c),i=Array.from(n);return c.forEach(e=>{t.has(e.identity)&&(t.get(e.identity)??[]).forEach(t=>{n.find(({participant:i,publication:n})=>e.identity===i.identity&&n.source===t)||(a.l.debug(`Add ${t} placeholder for participant ${e.identity}.`),i.push({participant:e,source:t}))})}),i}},[n,c,e])}function A(e){let t=(0,a.f)(),i=m(t),n=r.useMemo(()=>i===o.KN.Disconnected,[i]),s=r.useMemo(()=>(0,a.Z)(t,e),[t,e,n]),c=u(s.isSendingObservable,!1),l=u(s.messageObservable,[]);return{send:s.send,chatMessages:l,isSending:c}}function N(e={}){let[t,i]=r.useState((0,a._)(e.defaults,e.preventLoad??!1)),n=r.useCallback(e=>{i(t=>({...t,audioEnabled:e}))},[]),s=r.useCallback(e=>{i(t=>({...t,videoEnabled:e}))},[]),o=r.useCallback(e=>{i(t=>({...t,audioDeviceId:e}))},[]),c=r.useCallback(e=>{i(t=>({...t,videoDeviceId:e}))},[]),l=r.useCallback(e=>{i(t=>({...t,username:e}))},[]);return r.useEffect(()=>{(0,a.$)(t,e.preventSave??!1)},[t,e.preventSave]),{userChoices:t,saveAudioInputEnabled:n,saveVideoInputEnabled:s,saveAudioInputDeviceId:o,saveVideoInputDeviceId:c,saveUsername:l}}function _(e,t={}){let i=(0,a.c)(e),n=(0,a.u)(t.room);return u(r.useMemo(()=>(0,a.a0)(n,i),[n,i]),i.isLocal?i.isE2EEEnabled:!!(null!=i&&i.isEncrypted))}}};