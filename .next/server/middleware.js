(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{16:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>s});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},35:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,s=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator,g=Object.prototype.hasOwnProperty,m=Object.assign;function y(e,t,r,n,i,a){return{$$typeof:s,type:e,key:t,ref:void 0!==(r=a.ref)?r:null,props:a}}function b(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var _=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function w(){}function k(e,t,r){if(null==e)return e;var o=[],l=0;return!function e(t,r,o,l,c){var u,d,h,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var m=!1;if(null===t)m=!0;else switch(g){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case s:case a:m=!0;break;case p:return e((m=t._init)(t._payload),r,o,l,c)}}if(m)return c=c(t),m=""===l?"."+v(t,0):l,i(c)?(o="",null!=m&&(o=m.replace(_,"$&/")+"/"),e(c,r,o,"",function(e){return e})):null!=c&&(b(c)&&(u=c,d=o+(null==c.key||t&&t.key===c.key?"":(""+c.key).replace(_,"$&/")+"/")+m,c=y(u.type,d,void 0,void 0,void 0,u.props)),r.push(c)),1;m=0;var k=""===l?".":l+":";if(i(t))for(var S=0;S<t.length;S++)g=k+v(l=t[S],S),m+=e(l,r,o,g,c);else if("function"==typeof(S=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=f&&h[f]||h["@@iterator"])?h:null))for(t=S.call(t),S=0;!(l=t.next()).done;)g=k+v(l=l.value,S++),m+=e(l,r,o,g,c);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(w,w):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,o,l,c);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,o,"","",function(e){return t.call(r,e,l++)}),o}function S(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function E(){return new WeakMap}function T(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:k,forEach:function(e,t,r){k(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return k(e,function(){t++}),t},toArray:function(e){return k(e,function(e){return e})||[]},only:function(e){if(!b(e))throw Error(n(143));return e}},t.Fragment=o,t.Profiler=c,t.StrictMode=l,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(E);void 0===(t=n.get(e))&&(t=T(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var s=arguments[n];if("function"==typeof s||"object"==typeof s&&null!==s){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(s))&&(t=T(),a.set(s,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(s))&&(t=T(),a.set(s,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var o=e.apply(null,arguments);return(n=t).s=1,n.v=o}catch(e){throw(o=t).s=2,o.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=m({},e.props),s=e.key,a=void 0;if(null!=t)for(o in void 0!==t.ref&&(a=void 0),void 0!==t.key&&(s=""+t.key),t)g.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(i[o]=t[o]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var l=Array(o),c=0;c<o;c++)l[c]=arguments[c+2];i.children=l}return y(e.type,s,void 0,void 0,a,i)},t.createElement=function(e,t,r){var n,i={},s=null;if(null!=t)for(n in void 0!==t.key&&(s=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var a=arguments.length-2;if(1===a)i.children=r;else if(1<a){for(var o=Array(a),l=0;l<a;l++)o[l]=arguments[l+2];i.children=o}if(e&&e.defaultProps)for(n in a=e.defaultProps)void 0===i[n]&&(i[n]=a[n]);return y(e,s,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=b,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:S}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},58:(e,t,r)=>{"use strict";r.d(t,{xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}},74:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function i(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var s=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,c=t.delimiter,u=o(o(e,void 0===r?s:r,"$1\0$2"),void 0===n?a:n,"\0"),d=0,h=u.length;"\0"===u.charAt(d);)d++;for(;"\0"===u.charAt(h-1);)h--;return u.slice(d,h).split("\0").map(void 0===l?i:l).join(void 0===c?" ":c)}(e,n({delimiter:"."},r))}},115:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(58).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},159:(e,t,r)=>{"use strict";r.d(t,{RM:()=>s,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},167:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>i});var n=r(821);function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,s=t.slice(2,-2).join(";"),a=Number(t.at(-2));return"NEXT_REDIRECT"===r&&("replace"===i||"push"===i)&&"string"==typeof s&&!isNaN(a)&&a in n.Q}},199:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(159),i=r(167);function s(e){return(0,i.nJ)(e)||(0,n.RM)(e)}},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=i(e,t);return s?n.run(s,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},221:(e,t,r)=>{"use strict";r.d(t,{headers:()=>_}),r(818),r(725);var n=r(535),i=r(115),s=r(557),a=r(602),o=r(801),l=r(815);let c={current:null},u="function"==typeof l.cache?l.cache:e=>e,d=console.warn;function h(e){return function(...t){d(e(...t))}}u(e=>{try{d(c.current)}finally{c.current=null}});var p=r(335);let f=new WeakMap,g=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});function m(){return this.getAll().map(e=>[e.name,e]).values()}function y(e){for(let e of this.getAll())this.delete(e.name);return e}var b=r(381);function _(){let e=n.J.getStore(),t=i.FP.getStore();if(e){if(t&&"after"===t.phase&&!(0,p.iC)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return w(b.o.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=v.get(l);if(n)return n;let i=(0,o.W)(l.renderSignal,"`headers()`");return v.set(l,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${k(arguments[0])}, ...)\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${k(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${k(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${k(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${k(arguments[0])}, ...)\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=E(r,e);(0,s.t3)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=E(r,e);(0,s.t3)(r,e,t,l)}}}),i}else"prerender-ppr"===t.type?(0,s.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.xI)("headers",e,t);(0,s.Pk)(e,t)}return w((0,i.XN)("headers").headers)}let v=new WeakMap;function w(e){let t=v.get(e);if(t)return t;let r=Promise.resolve(e);return v.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function k(e){return"string"==typeof e?`'${e}'`:"..."}let S=h(E);function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function T(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return x(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return x(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return C(null);default:return t}}function x(e,t){let r,n=O.get(T);return n||(r=C(e),O.set(e,r),r)}r(16);let O=new WeakMap;function C(e){let t=new R(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class R{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){I("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){I("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let P=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function I(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},280:(e,t,r)=>{var n;(()=>{var i={226:function(i,s){!function(a,o){"use strict";var l="function",c="undefined",u="object",d="string",h="major",p="model",f="name",g="type",m="vendor",y="version",b="architecture",_="console",v="mobile",w="tablet",k="smarttv",S="wearable",E="embedded",T="Amazon",x="Apple",O="ASUS",C="BlackBerry",R="Browser",P="Chrome",I="Firefox",A="Google",N="Huawei",U="Microsoft",M="Motorola",j="Opera",D="Samsung",L="Sharp",q="Sony",H="Xiaomi",B="Zebra",$="Facebook",z="Chromium OS",K="Mac OS",J=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},W=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},F=function(e,t){return typeof e===d&&-1!==V(t).indexOf(V(e))},V=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,s,a,c,d=0;d<t.length&&!a;){var h=t[d],p=t[d+1];for(r=n=0;r<h.length&&!a&&h[r];)if(a=h[r++].exec(e))for(i=0;i<p.length;i++)c=a[++n],typeof(s=p[i])===u&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):void 0:this[s[0]]=c?s[1].call(this,c,s[2]):void 0:4===s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):o):this[s]=c||o;d+=2}},Q=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(F(t[r][n],e))return"?"===r?o:r}else if(F(t[r],e))return"?"===r?o:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,y],[/opios[\/ ]+([\w\.]+)/i],[y,[f,j+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[f,j]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[f,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+R],y],[/\bfocus\/([\w\.]+)/i],[y,[f,I+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[f,j+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[f,j+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[f,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[y,[f,I]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+R],y],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,$],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[f,P+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,P+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[f,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[y,Q,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[f,I+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,y],[/(cobalt)\/([\w\.]+)/i],[f,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,V]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,D],[g,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,D],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,x],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,x],[g,w]],[/(macintosh);/i],[p,[m,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,L],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,N],[g,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,N],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,H],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,H],[g,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,M],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,M],[g,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,v]],[/(pixel c)\b/i],[p,[m,A],[g,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,A],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,q],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,q],[g,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,T],[g,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,T],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,C],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,O],[g,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,O],[g,v]],[/(nexus 9)/i],[p,[m,"HTC"],[g,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,w]],[/(surface duo)/i],[p,[m,U],[g,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,v]],[/(u304aa)/i],[p,[m,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,w]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,w]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,w]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,w]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,w]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,w]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,w]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,v]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,w]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,w]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,w]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,w]],[/(sprint) (\w+)/i],[m,p,[g,v]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,U],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,B],[g,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,B],[g,v]],[/smart-tv.+(samsung)/i],[m,[g,k]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,D],[g,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,k]],[/(apple) ?tv/i],[m,[p,x+" TV"],[g,k]],[/crkey/i],[[p,P+"cast"],[m,A],[g,k]],[/droid.+aft(\w)( bui|\))/i],[p,[m,T],[g,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,L],[g,k]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,q],[g,k]],[/(mitv-\w{5}) bui/i],[p,[m,H],[g,k]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,G],[p,G],[g,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,_]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,_]],[/(playstation [345portablevi]+)/i],[p,[m,q],[g,_]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,U],[g,_]],[/((pebble))app/i],[m,p,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,x],[g,S]],[/droid.+; (glass) \d/i],[p,[m,A],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,B],[g,S]],[/(quest( 2| pro)?)/i],[p,[m,$],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,E]],[/(aeobc)\b/i],[p,[m,T],[g,E]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[y,Q,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[y,Q,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,K],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,y],[/\(bb(10);/i],[y,[f,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[f,I+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[f,P+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,z],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,y],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,y]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,s=t?J(Z,t):Z,_=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=o,t[y]=o,X.call(t,n,s.browser),t[h]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,_&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[b]=o,X.call(e,n,s.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,X.call(e,n,s.device),_&&!e[g]&&i&&i.mobile&&(e[g]=v),_&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=w),e},this.getEngine=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,s.engine),e},this.getOS=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,s.os),_&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,z).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=W([f,y,h]),ee.CPU=W([b]),ee.DEVICE=W([p,m,g,_,v,k,w,S,E]),ee.ENGINE=ee.OS=W([f,y]),typeof s!==c?(i.exports&&(s=i.exports=ee),s.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete s[e]}return r.exports}a.ab="//",e.exports=a(226)})()},335:(e,t,r)=>{"use strict";r.d(t,{iC:()=>i}),r(602);var n=r(427);function i(){let e=n.Z.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},356:e=>{"use strict";e.exports=require("node:buffer")},360:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>oN});var i={};async function s(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(i),r.d(i,{config:()=>oR,default:()=>oC});let a=null;async function o(){if("phase-production-build"===process.env.NEXT_PHASE)return;a||(a=s());let e=await a;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function l(...e){let t=await s();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let c=null;function u(){return c||(c=o()),c}function d(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),u();class h extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class p extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class f extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let g="_N_T_",m={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function y(e){var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function b(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...y(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function _(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...m,GROUP:{builtinReact:[m.reactServerComponents,m.actionBrowser],serverOnly:[m.reactServerComponents,m.actionBrowser,m.instrument,m.middleware],neutralTarget:[m.apiNode,m.apiEdge],clientOnly:[m.serverSideRendering,m.appPagesBrowser],bundled:[m.reactServerComponents,m.actionBrowser,m.serverSideRendering,m.appPagesBrowser,m.shared,m.instrument,m.middleware],appPages:[m.reactServerComponents,m.serverSideRendering,m.appPagesBrowser,m.actionBrowser]}});let v=Symbol("response"),w=Symbol("passThrough"),k=Symbol("waitUntil");class S{constructor(e,t){this[w]=!1,this[k]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[v]||(this[v]=Promise.resolve(e))}passThroughOnException(){this[w]=!0}waitUntil(e){if("external"===this[k].kind)return(0,this[k].function)(e);this[k].promises.push(e)}}class E extends S{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function T(e){return e.replace(/\/$/,"")||"/"}function x(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function O(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=x(e);return""+t+r+n+i}function C(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=x(e);return""+r+t+n+i}function R(e,t){if("string"!=typeof e)return!1;let{pathname:r}=x(e);return r===t||r.startsWith(t+"/")}let P=new WeakMap;function I(e,t){let r;if(!t)return{pathname:e};let n=P.get(t);n||(n=t.map(e=>e.toLowerCase()),P.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let s=i[1].toLowerCase(),a=n.indexOf(s);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let A=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function N(e,t){return new URL(String(e).replace(A,"localhost"),t&&String(t).replace(A,"localhost"))}let U=Symbol("NextURLInternal");class M{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[U]={url:N(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let s=function(e,t){var r,n;let{basePath:i,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&R(o.pathname,i)&&(o.pathname=function(e,t){if(!R(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,i),o.basePath=i);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):I(o.pathname,s.locales);o.locale=e.detectedLocale,o.pathname=null!=(n=e.pathname)?n:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):I(l,s.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[U].url.pathname,{nextConfig:this[U].options.nextConfig,parseData:!0,i18nProvider:this[U].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[U].url,this[U].options.headers);this[U].domainLocale=this[U].options.i18nProvider?this[U].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(i=s.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[U].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[U].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[U].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[U].url.pathname=s.pathname,this[U].defaultLocale=o,this[U].basePath=s.basePath??"",this[U].buildId=s.buildId,this[U].locale=s.locale??o,this[U].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(R(i,"/api")||R(i,"/"+t.toLowerCase()))?e:O(e,"/"+t)}((e={basePath:this[U].basePath,buildId:this[U].buildId,defaultLocale:this[U].options.forceLocale?void 0:this[U].defaultLocale,locale:this[U].locale,pathname:this[U].url.pathname,trailingSlash:this[U].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=T(t)),e.buildId&&(t=C(O(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=O(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:C(t,"/"):T(t)}formatSearch(){return this[U].url.search}get buildId(){return this[U].buildId}set buildId(e){this[U].buildId=e}get locale(){return this[U].locale??""}set locale(e){var t,r;if(!this[U].locale||!(null==(r=this[U].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[U].locale=e}get defaultLocale(){return this[U].defaultLocale}get domainLocale(){return this[U].domainLocale}get searchParams(){return this[U].url.searchParams}get host(){return this[U].url.host}set host(e){this[U].url.host=e}get hostname(){return this[U].url.hostname}set hostname(e){this[U].url.hostname=e}get port(){return this[U].url.port}set port(e){this[U].url.port=e}get protocol(){return this[U].url.protocol}set protocol(e){this[U].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[U].url=N(e),this.analyze()}get origin(){return this[U].url.origin}get pathname(){return this[U].url.pathname}set pathname(e){this[U].url.pathname=e}get hash(){return this[U].url.hash}set hash(e){this[U].url.hash=e}get search(){return this[U].url.search}set search(e){this[U].url.search=e}get password(){return this[U].url.password}set password(e){this[U].url.password=e}get username(){return this[U].url.username}set username(e){this[U].url.username=e}get basePath(){return this[U].basePath}set basePath(e){this[U].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new M(String(this),this[U].options)}}var j=r(725);let D=Symbol("internal request");class L extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);_(r),e instanceof Request?super(e,t):super(r,t);let n=new M(r,{headers:b(this.headers),nextConfig:t.nextConfig});this[D]={cookies:new j.tm(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[D].cookies}get nextUrl(){return this[D].nextUrl}get page(){throw new p}get ua(){throw new f}get url(){return this[D].url}}var q=r(716);let H=Symbol("internal response"),B=new Set([301,302,303,307,308]);function $(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class z extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new j.VO(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let s=Reflect.apply(e[n],e,i),a=new Headers(r);return s instanceof j.VO&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,j.Ud)(e)).join(",")),$(t,a),s};default:return q.l.get(e,n,i)}}});this[H]={cookies:n,url:t.url?new M(t.url,{headers:b(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[H].cookies}static json(e,t){let r=Response.json(e,t);return new z(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!B.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",_(e)),new z(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",_(e)),$(t,r),new z(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),$(e,t),new z(null,{...e,headers:t})}}function K(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let J="Next-Router-Prefetch",W=["RSC","Next-Router-State-Tree",J,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],F="_rsc";var V=r(381),G=r(818),X=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(X||{}),Q=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(Q||{}),Y=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(Y||{}),Z=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(Z||{}),ee=function(e){return e.startServer="startServer.startServer",e}(ee||{}),et=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(et||{}),er=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(er||{}),en=function(e){return e.executeRoute="Router.executeRoute",e}(en||{}),ei=function(e){return e.runHandler="Node.runHandler",e}(ei||{}),es=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(es||{}),ea=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(ea||{}),eo=function(e){return e.execute="Middleware.execute",e}(eo||{});let el=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ec=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eu(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:ed,propagation:eh,trace:ep,SpanStatusCode:ef,SpanKind:eg,ROOT_CONTEXT:em}=n=r(956);class ey extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eb=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof ey})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:ef.ERROR,message:null==t?void 0:t.message})),e.end()},e_=new Map,ev=n.createContextKey("next.rootSpanId"),ew=0,ek=()=>ew++,eS={set(e,t,r){e.push({key:t,value:r})}};class eE{getTracerInstance(){return ep.getTracer("next.js","0.0.1")}getContext(){return ed}getTracePropagationData(){let e=ed.active(),t=[];return eh.inject(e,t,eS),t}getActiveScopeSpan(){return ep.getSpan(null==ed?void 0:ed.active())}withPropagatedContext(e,t,r){let n=ed.active();if(ep.getSpanContext(n))return t();let i=eh.extract(n,e,r);return ed.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:s,options:a}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},o=a.spanName??r;if(!el.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return s();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=ep.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==ed?void 0:ed.active())??em,c=!0);let u=ek();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},ed.with(l.setValue(ev,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{e_.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ec.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&e_.set(u,new Map(Object.entries(a.attributes??{})));try{if(s.length>1)return s(e,t=>eb(e,t));let t=s(e);if(eu(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eb(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eb(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return el.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let s=arguments.length-1,a=arguments[s];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(ed.active(),a);return t.trace(r,e,(e,t)=>(arguments[s]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?ep.setSpan(ed.active(),e):void 0}getRootSpanAttributes(){let e=ed.active().getValue(ev);return e_.get(e)}setRootSpanAttribute(e,t){let r=ed.active().getValue(ev),n=e_.get(r);n&&n.set(e,t)}}let eT=(()=>{let e=new eE;return()=>e})(),ex="__prerender_bypass";Symbol("__next_preview_data"),Symbol(ex);class eO{constructor(e,t,r,n){var i;let s=e&&function(e,t){let r=V.o.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=r.get(ex))?void 0:i.value;this._isEnabled=!!(!s&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:ex,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:ex,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eC(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of y(r))n.append("set-cookie",e);for(let e of new j.VO(n).getAll())t.set(e)}}var eR=r(115),eP=r(802),eI=r.n(eP);class eA extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}var eN=r(535);class eU{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eU(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let eM=Symbol.for("@next/cache-handlers-map"),ej=Symbol.for("@next/cache-handlers-set"),eD=globalThis;function eL(){if(eD[eM])return eD[eM].entries()}async function eq(e,t){if(!e)return t();let r=eH(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eH(e));await e$(e,t)}}function eH(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eB(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eD[ej])return eD[ej].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function e$(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([eB(r,e.incrementalCache),...Object.values(n),...i])}var ez=r(620),eK=r(427);class eJ{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eI()),this.callbackQueue.pause()}after(e){if(eu(e))this.waitUntil||eW(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||eW();let t=eR.FP.getStore();t&&this.workUnitStores.add(t);let r=eK.Z.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let i=(0,ez.cg)(async()=>{try{await eK.Z.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(i)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=eN.J.getStore();if(!e)throw Object.defineProperty(new eA("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eq(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eA("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function eW(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function eF(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class eV{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function eG(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let eX=Symbol.for("@next/request-context"),eQ=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function eY(e,t,r){let n=[],i=r&&r.size>0;for(let t of eQ(e))t=`${g}${t}`,n.push(t);if(t.pathname&&!i){let e=`${g}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eL();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,eF(async()=>i.getExpiration(...e)));return t}(n)}}class eZ extends L{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let e0={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},e1=(e,t)=>eT().withPropagatedContext(e.headers,t,e0),e2=!1;async function e5(e){var t;let n,i;if(!e2&&(e2=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),e1=t(e1)}await u();let s=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new M(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),d="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let h=new Map;if(!s)for(let e of W){let t=e.toLowerCase(),r=l.get(t);null!==r&&(h.set(t,r),l.delete(t))}let p=new eZ({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(F),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eG()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[eX];return null==e?void 0:e.get()}())?void 0:t.waitUntil),g=new E({request:p,page:e.page,context:f?{waitUntil:f}:void 0});if((n=await e1(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=g.waitUntil.bind(g),r=new eV;return eT().trace(eo.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,s,a,l,c,u;let d=eG(),h=await eY("/",p.nextUrl,null),f=(c=p.nextUrl,u=e=>{i=e},function(e,t,r,n,i,s,a,o,l,c,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let h={};return{type:"request",phase:e,implicitTags:s,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return h.headers||(h.headers=function(e){let t=V.o.from(e);for(let e of W)t.delete(e.toLowerCase());return V.o.seal(t)}(t.headers)),h.headers},get cookies(){if(!h.cookies){let e=new j.tm(V.o.from(t.headers));eC(t,e),h.cookies=G.Ck.seal(e)}return h.cookies},set cookies(value){h.cookies=value},get mutableCookies(){if(!h.mutableCookies){let e=function(e,t){let r=new j.tm(V.o.from(e));return G.K8.wrap(r,t)}(t.headers,a||(r?d:void 0));eC(t,e),h.mutableCookies=e}return h.mutableCookies},get userspaceMutableCookies(){return h.userspaceMutableCookies||(h.userspaceMutableCookies=(0,G.hm)(this.mutableCookies)),h.userspaceMutableCookies},get draftMode(){return h.draftMode||(h.draftMode=new eO(l,t,this.cookies,this.mutableCookies)),h.draftMode},renderResumeDataCache:o??null,isHmrRefresh:c,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",p,void 0,c,{},h,u,void 0,d,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:s,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:s,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new eJ({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=eL();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,eF(async()=>n.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(s=e.request.nextConfig)||null==(n=s.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(J),buildId:o??"",previouslyRevalidatedTags:[]});return await eN.J.run(m,()=>eR.FP.run(f,e.handler,p,g))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,g)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let m=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&m&&(d||!s)){let t=new M(m,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});s||t.host!==p.nextUrl.host||(t.buildId=o||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=K(t.toString(),a.toString());!s&&c&&n.headers.set("x-nextjs-rewrite",r),d&&i&&(a.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let y=null==n?void 0:n.headers.get("Location");if(n&&y&&!s){let t=new M(y,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=o||t.buildId,n.headers.set("Location",t.toString())),c&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",K(t.toString(),a.toString()).url))}let b=n||z.next(),_=b.headers.get("x-middleware-override-headers"),v=[];if(_){for(let[e,t]of h)b.headers.set(`x-middleware-request-${e}`,t),v.push(e);v.length>0&&b.headers.set("x-middleware-override-headers",_+","+v.join(","))}return{response:b,waitUntil:("internal"===g[k].kind?Promise.all(g[k].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}var e4=Object.defineProperty,e3=Object.getOwnPropertyDescriptor,e6=Object.getOwnPropertyNames,e8=Object.prototype.hasOwnProperty,e9=e=>{throw TypeError(e)},e7=(e,t,r)=>t.has(e)||e9("Cannot "+r),te=(e,t,r)=>(e7(e,t,"read from private field"),r?r.call(e):t.get(e)),tt=(e,t,r)=>t.has(e)?e9("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),tr=(e,t,r,n)=>(e7(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),tn=(e,t,r)=>(e7(e,t,"access private method"),r),ti={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},ts=async e=>new Promise(t=>setTimeout(t,e)),ta=(e,t)=>t?e*(1+Math.random()):e,to=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=ta(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await ts(r()),t++}},tl=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:s,factor:a,retryImmediately:o,jitter:l}={...ti,...t},c=to({initialDelay:i,maxDelayBetweenRetries:s,factor:a,jitter:l});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;o&&1===r?await ts(ta(100,l)):await c()}},tc=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,tu=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,td=[".lcl.dev",".lclstage.dev",".lclclerk.com"],th=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],tp=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],tf=[".accountsstage.dev"],tg="https://api.clerk.com",tm="pk_live_";function ty(e){if(!e.endsWith("$"))return!1;let t=e.slice(0,-1);return!t.includes("$")&&t.includes(".")}function tb(e,t={}){let r;if(!(e=e||"")||!t_(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!t_(e))throw Error("Publishable key not valid.");return null}let n=e.startsWith(tm)?"production":"development";try{r=tc(e.split("_")[2])}catch{if(t.fatal)throw Error("Publishable key not valid: Failed to decode key.");return null}if(!ty(r)){if(t.fatal)throw Error("Publishable key not valid: Decoded key has invalid format.");return null}let i=r.slice(0,-1);return t.proxyUrl?i=t.proxyUrl:"development"!==n&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:n,frontendApi:i}}function t_(e=""){try{if(!(e.startsWith(tm)||e.startsWith("pk_test_")))return!1;let t=e.split("_");if(3!==t.length)return!1;let r=t[2];if(!r)return!1;let n=tc(r);return ty(n)}catch{return!1}}function tv(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function tw(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return tu(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var tk=(e,t)=>`${e}_${t}`,tS=()=>!1,tE=()=>{try{return!0}catch{}return!1},tT=new Set,tx=(e,t,r)=>{let n=tS()||tE(),i=r??e;tT.has(i)||n||(tT.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};function tO(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn,plan:e?.meta?.plan}}}var tC=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i,retryAfter:s}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.retryAfter=s,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(tO):[]}(r)}},tR=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function tP({packageName:e,customMessages:t}){let r=e,n={...tR,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}var tI=tP({packageName:"@clerk/backend"}),{isDevOrStagingUrl:tA}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=th.some(e=>r.endsWith(e)),e.set(r,n)),n}}}(),tN={InvalidSecretKey:"clerk_key_invalid"},tU={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},tM={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable."},tj=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},tD={TokenInvalid:"token-invalid",InvalidSecretKey:"secret-key-invalid",UnexpectedError:"unexpected-error"},tL=class e extends Error{constructor({message:t,code:r,status:n}){super(t),Object.setPrototypeOf(this,e.prototype),this.code=r,this.status=n}getFullMessage(){return`${this.message} (code=${this.code}, status=${this.status})`}};let tq=crypto;var tH=fetch.bind(globalThis),tB={crypto:tq,get fetch(){return tH},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},t$={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),s=0,a=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|n,(s+=t.bits)>=8&&(s-=8,i[o++]=255&a>>s)}if(s>=t.bits||255&a<<8-s)throw SyntaxError("Unexpected end of data");return i})(e,tz,t)},tz={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},tK={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},tJ="RSASSA-PKCS1-v1_5",tW={RS256:tJ,RS384:tJ,RS512:tJ},tF=Object.keys(tK),tV=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),tG=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(tV(e)&&!e.some(e=>r.includes(e)))throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},tX=e=>{if(void 0!==e&&"JWT"!==e)throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},tQ=e=>{if(!tF.includes(e))throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${tF}.`})},tY=e=>{if("string"!=typeof e)throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},tZ=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new tj({reason:tU.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},t0=(e,t)=>{if("number"!=typeof e)throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new tj({reason:tU.TokenExpired,message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},t1=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tj({reason:tU.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},t2=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tj({reason:tU.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};async function t5(e,t){let{header:r,signature:n,raw:i}=e,s=new TextEncoder().encode([i.header,i.payload].join(".")),a=function(e){let t=tK[e],r=tW[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${tF.join(",")}.`);return{hash:{name:tK[e]},name:tW[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return tB.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=tc(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new Uint8Array(new ArrayBuffer(t.length));for(let e=0,n=t.length;e<n;e++)r[e]=t.charCodeAt(e);return r}(e),i="sign"===r?"pkcs8":"spki";return tB.crypto.subtle.importKey(i,n,t,!1,[r])}(t,a,"verify");return{data:await tB.crypto.subtle.verify(a.name,e,n,s)}}catch(e){return{errors:[new tj({reason:tU.TokenInvalidSignature,message:e?.message})]}}}function t4(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new tj({reason:tU.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,n,i]=t,s=new TextDecoder,a=JSON.parse(s.decode(t$.parse(r,{loose:!0}))),o=JSON.parse(s.decode(t$.parse(n,{loose:!0})));return{data:{header:a,payload:o,signature:t$.parse(i,{loose:!0}),raw:{header:r,payload:n,signature:i,text:e}}}}async function t3(e,t){let{audience:r,authorizedParties:n,clockSkewInMs:i,key:s}=t,a=i||5e3,{data:o,errors:l}=t4(e);if(l)return{errors:l};let{header:c,payload:u}=o;try{let{typ:e,alg:t}=c;tX(e),tQ(t);let{azp:i,sub:s,aud:o,iat:l,exp:d,nbf:h}=u;tY(s),tG([o],[r]),tZ(i,n),t0(d,a),t1(h,a),t2(l,a)}catch(e){return{errors:[e]}}let{data:d,errors:h}=await t5(o,s);return h?{errors:[new tj({action:tM.EnsureClerkJWT,reason:tU.TokenVerificationFailed,message:`Error verifying JWT signature. ${h[0]}`})]}:d?{data:u}:{errors:[new tj({reason:tU.TokenInvalidSignature,message:"JWT signature is invalid."})]}}var t6={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},t8=new Set(["first_factor","second_factor","multi_factor"]),t9=new Set(["strict_mfa","strict","moderate","lax"]),t7=e=>"number"==typeof e&&e>0,re=e=>t8.has(e),rt=e=>t9.has(e),rr=e=>e.replace(/^(org:)*/,"org:"),rn=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(rr(e.permission)):e.role?rr(n)===rr(e.role):null:null},ri=(e,t)=>{let{org:r,user:n}=ra(e),[i,s]=t.split(":"),a=s||i;return"org"===i?r.includes(a):"user"===i?n.includes(a):[...r,...n].includes(a)},rs=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?ri(r,e.feature):e.plan&&n?ri(n,e.plan):null},ra=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},ro=e=>{if(!e)return!1;let t="string"==typeof e&&rt(e),r="object"==typeof e&&re(e.level)&&t7(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?t6[e]:e).bind(null,e)},rl=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=ro(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[s,a]=t,o=-1!==s?i>s:null,l=-1!==a?i>a:null;switch(n){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},rc=e=>t=>{if(!e.userId)return!1;let r=rs(t,e),n=rn(t,e),i=rl(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},ru=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),n=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:n}},rd=e=>{let t,r,n,i,s=e.fva??null,a=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,n=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:s}=ra(e.fea),{permissions:a,featurePermissionMap:o}=ru({per:e.o?.per,fpm:e.o?.fpm});i=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let n=[];for(let i=0;i<e.length;i++){let s=e[i];if(i>=r.length)continue;let a=r[i];if(a)for(let e=0;e<a.length;e++)1===a[e]&&n.push(`org:${s}:${t[e]}`)}return n}({features:s,featurePermissionMap:o,permissions:a})}}else t=e.org_id,r=e.org_role,n=e.org_slug,i=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:a,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:n,orgPermissions:i,factorVerificationAge:s}},rh=r(412),rp=r(554);function rf(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function rg(e){return e&&e.sensitive?"":"i"}var rm="https://api.clerk.com",ry="@clerk/backend@2.4.1",rb="2025-04-10",r_={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},rv={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:r_.DevBrowser,Handshake:r_.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:r_.HandshakeNonce,HandshakeFormat:"format"},rw={Cookies:r_,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",SecFetchSite:"sec-fetch-site",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:rv},rk=(e,t,r,n)=>{if(""===e)return rS(t.toString(),r?.toString());let i=new URL(e),s=r?new URL(r,i):void 0,a=new URL(t,i),o=`${i.hostname}:${i.port}`!=`${a.hostname}:${a.port}`;return s&&(o&&s.searchParams.delete(rw.QueryParameters.ClerkSynced),a.searchParams.set("redirect_url",s.toString())),o&&n&&a.searchParams.set(rw.QueryParameters.DevBrowser,n),a.toString()},rS=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},rE=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:n,signUpUrl:i,baseUrl:s,sessionStatus:a}=e,o=tb(t),l=o?.frontendApi,c=o?.instanceType==="development",u=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(l),d="pending"===a,h=(t,{returnBackUrl:n})=>r(rk(s,`${t}/tasks`,n,c?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{i||u||tI.throwMissingPublishableKeyError();let a=`${u}/sign-up`,o=i||function(e){if(!e)return;let t=new URL(e,s);return t.pathname=`${t.pathname}/create`,t.toString()}(n)||a;return d?h(o,{returnBackUrl:t}):r(rk(s,o,t,c?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{n||u||tI.throwMissingPublishableKeyError();let i=`${u}/sign-in`,a=n||i;return d?h(a,{returnBackUrl:t}):r(rk(s,a,t,c?e.devBrowserToken:null))}}};function rT(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}function rx(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var rO=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.originalFrontendApi="",this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.tokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(rw.Cookies.ClientUat),t=this.getCookie(rw.Cookies.ClientUat),r=this.getSuffixedCookie(rw.Cookies.Session)||"",n=this.getCookie(rw.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:i}=t4(n),s=i?.payload.iat||0,{data:a}=t4(r),o=a?.payload.iat||0;if("0"!==e&&"0"!==t&&s>o||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(a);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}isCrossOriginReferrer(){if(!this.referrer||!this.origin)return!1;try{if("cross-site"===this.getHeader(rw.Headers.SecFetchSite))return!0;return new URL(this.referrer).origin!==this.origin}catch{return!1}}initPublishableKeyValues(e){tb(e.publishableKey,{fatal:!0}),this.publishableKey=e.publishableKey;let t=tb(this.publishableKey,{fatal:!0,domain:e.domain,isSatellite:e.isSatellite});this.originalFrontendApi=t.frontendApi;let r=tb(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.tokenInHeader=this.parseAuthorizationHeader(this.getHeader(rw.Headers.Authorization)),this.origin=this.getHeader(rw.Headers.Origin),this.host=this.getHeader(rw.Headers.Host),this.forwardedHost=this.getHeader(rw.Headers.ForwardedHost),this.forwardedProto=this.getHeader(rw.Headers.CloudFrontForwardedProto)||this.getHeader(rw.Headers.ForwardedProto),this.referrer=this.getHeader(rw.Headers.Referrer),this.userAgent=this.getHeader(rw.Headers.UserAgent),this.secFetchDest=this.getHeader(rw.Headers.SecFetchDest),this.accept=this.getHeader(rw.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(rw.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(rw.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(rw.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(rw.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(rw.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(rw.QueryParameters.Handshake)||this.getCookie(rw.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(rw.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(rw.QueryParameters.HandshakeNonce)||this.getCookie(rw.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie(tk(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=t4(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=t4(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.originalFrontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},rC=async(e,t)=>new rO(t.publishableKey?await tw(t.publishableKey,tB.crypto.subtle):"",e,t),rR=RegExp("(?<!:)/{1,}","g");function rP(...e){return e.filter(e=>e).join("/").replace(rR,"/")}var rI=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},rA="/actor_tokens",rN=class extends rI{async create(e){return this.request({method:"POST",path:rA,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:rP(rA,e,"revoke")})}},rU="/accountless_applications",rM=class extends rI{async createAccountlessApplication(){return this.request({method:"POST",path:rU})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:rP(rU,"complete")})}},rj="/allowlist_identifiers",rD=class extends rI{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:rj,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:rj,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:rP(rj,e)})}},rL="/api_keys",rq=class extends rI{async create(e){return this.request({method:"POST",path:rL,bodyParams:e})}async revoke(e){let{apiKeyId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rP(rL,t,"revoke"),bodyParams:r})}async getSecret(e){return this.requireId(e),this.request({method:"GET",path:rP(rL,e,"secret")})}async verifySecret(e){return this.request({method:"POST",path:rP(rL,"verify"),bodyParams:{secret:e}})}},rH=class extends rI{async changeDomain(e){return this.request({method:"POST",path:rP("/beta_features","change_domain"),bodyParams:e})}},rB="/blocklist_identifiers",r$=class extends rI{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:rB,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:rB,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:rP(rB,e)})}},rz="/clients",rK=class extends rI{async getClientList(e={}){return this.request({method:"GET",path:rz,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:rP(rz,e)})}verifyClient(e){return this.request({method:"POST",path:rP(rz,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:rP(rz,"handshake_payload"),queryParams:e})}},rJ="/domains",rW=class extends rI{async list(){return this.request({method:"GET",path:rJ})}async add(e){return this.request({method:"POST",path:rJ,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rP(rJ,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:rP(rJ,e)})}},rF="/email_addresses",rV=class extends rI{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:rP(rF,e)})}async createEmailAddress(e){return this.request({method:"POST",path:rF,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rP(rF,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:rP(rF,e)})}},rG=class extends rI{async verifyAccessToken(e){return this.request({method:"POST",path:rP("/oauth_applications/access_tokens","verify"),bodyParams:{access_token:e}})}},rX="/instance",rQ=class extends rI{async get(){return this.request({method:"GET",path:rX})}async update(e){return this.request({method:"PATCH",path:rX,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:rP(rX,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:rP(rX,"organization_settings"),bodyParams:e})}},rY="/invitations",rZ=class extends rI{async getInvitationList(e={}){return this.request({method:"GET",path:rY,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:rY,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:rP(rY,e,"revoke")})}},r0=class extends rI{async verifySecret(e){return this.request({method:"POST",path:rP("/m2m_tokens","verify"),bodyParams:{secret:e}})}},r1=class extends rI{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},r2="/jwt_templates",r5=class extends rI{async list(e={}){return this.request({method:"GET",path:r2,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:rP(r2,e)})}async create(e){return this.request({method:"POST",path:r2,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rP(r2,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rP(r2,e)})}},r4="/organizations",r3=class extends rI{async getOrganizationList(e){return this.request({method:"GET",path:r4,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:r4,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:rP(r4,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:rP(r4,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new tB.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:rP(r4,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:rP(r4,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:rP(r4,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:rP(r4,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rP(r4,t,"memberships"),queryParams:r})}async getInstanceOrganizationMembershipList(e){return this.request({method:"GET",path:"/organization_memberships",queryParams:e})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rP(r4,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:rP(r4,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:rP(r4,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:rP(r4,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rP(r4,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rP(r4,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:rP(r4,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:rP(r4,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:rP(r4,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rP(r4,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rP(r4,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:rP(r4,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:rP(r4,t,"domains",r)})}},r6="/oauth_applications",r8=class extends rI{async list(e={}){return this.request({method:"GET",path:r6,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:rP(r6,e)})}async create(e){return this.request({method:"POST",path:r6,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rP(r6,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rP(r6,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:rP(r6,e,"rotate_secret")})}},r9="/phone_numbers",r7=class extends rI{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:rP(r9,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:r9,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rP(r9,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:rP(r9,e)})}},ne=class extends rI{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},nt="/redirect_urls",nr=class extends rI{async getRedirectUrlList(){return this.request({method:"GET",path:nt,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:rP(nt,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:nt,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nt,e)})}},nn="/saml_connections",ni=class extends rI{async getSamlConnectionList(e={}){return this.request({method:"GET",path:nn,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:nn,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:rP(nn,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rP(nn,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nn,e)})}},ns="/sessions",na=class extends rI{async getSessionList(e={}){return this.request({method:"GET",path:ns,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:rP(ns,e)})}async createSession(e){return this.request({method:"POST",path:ns,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:rP(ns,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:rP(ns,e,"verify"),bodyParams:{token:t}})}async getToken(e,t,r){this.requireId(e);let n={method:"POST",path:t?rP(ns,e,"tokens",t):rP(ns,e,"tokens")};return void 0!==r&&(n.bodyParams={expires_in_seconds:r}),this.request(n)}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:rP(ns,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},no="/sign_in_tokens",nl=class extends rI{async createSignInToken(e){return this.request({method:"POST",path:no,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:rP(no,e,"revoke")})}},nc="/sign_ups",nu=class extends rI{async get(e){return this.requireId(e),this.request({method:"GET",path:rP(nc,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:rP(nc,t),bodyParams:r})}},nd=class extends rI{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},nh="/users",np=class extends rI{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[s,a]=await Promise.all([this.request({method:"GET",path:nh,queryParams:e}),this.getCount(i)]);return{data:s,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:rP(nh,e)})}async createUser(e){return this.request({method:"POST",path:nh,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rP(nh,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new tB.FormData;return r.append("file",t?.file),this.request({method:"POST",path:rP(nh,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:rP(nh,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nh,e)})}async getCount(e={}){return this.request({method:"GET",path:rP(nh,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),n=r?t:`oauth_${t}`;return r&&tx("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:rP(nh,e,"oauth_access_tokens",n),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nh,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:rP(nh,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rP(nh,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:rP(nh,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:rP(nh,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:rP(nh,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:rP(nh,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:rP(nh,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:rP(nh,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nh,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:rP(nh,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:rP(nh,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:rP(nh,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nh,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:rP(nh,e,"totp")})}},nf="/waitlist_entries",ng=class extends rI{async list(e={}){return this.request({method:"GET",path:nf,queryParams:e})}async create(e){return this.request({method:"POST",path:nf,bodyParams:e})}},nm="/webhooks",ny=class extends rI{async createSvixApp(){return this.request({method:"POST",path:rP(nm,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:rP(nm,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:rP(nm,"svix")})}},nb=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},n_=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.status=t,this.userId=r,this.actor=n,this.token=i,this.url=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},nv=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=i,this.instanceId=s,this.invitationId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},nw=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g){this.id=e,this.type=t,this.name=r,this.subject=n,this.scopes=i,this.claims=s,this.revoked=a,this.revocationReason=o,this.expired=l,this.expiration=c,this.createdBy=u,this.description=d,this.lastUsedAt=h,this.createdAt=p,this.updatedAt=f,this.secret=g}static fromJSON(t){return new e(t.id,t.type,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.description,t.last_used_at,t.created_at,t.updated_at,t.secret)}},nk=class e{constructor(e,t,r,n,i,s){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=i,this.instanceId=s}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},nS=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=i,this.browserVersion=s,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},nE=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=s,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=u,this.actor=d}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&nS.fromJSON(t.latest_activity),t.actor)}},nT=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>nE.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},nx=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},nO=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},nC=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},nR=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=n,this.developmentOrigin=i,this.cnameTargets=s,this.accountsPortalUrl=a,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>nx.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},nP=class e{constructor(e,t,r,n,i,s,a,o,l,c,u){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=s,this.bodyPlain=a,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=u}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},nI=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},nA=class e{constructor(e,t,r=null,n=null,i=null,s=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=s,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},nN=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&nA.fromJSON(t.verification),t.linked_to.map(e=>nI.fromJSON(e)))}},nU=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=s,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=c,this.phoneNumber=u,this.publicMetadata=d,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&nA.fromJSON(t.verification))}},nM=class e{constructor(e,t,r,n,i,s,a,o,l,c,u){this.id=e,this.clientId=t,this.type=r,this.subject=n,this.scopes=i,this.revoked=s,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdAt=c,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.client_id,t.type,t.subject,t.scopes,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_at,t.updated_at)}},nj=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},nD=class e{constructor(e,t,r,n,i){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=n,this.ignoreDotsForGmailAddresses=i}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},nL=class e{constructor(e,t,r,n,i){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=n,this.enhancedEmailDeliverability=i}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},nq=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=s,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},nH={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",ApiKey:"api_key",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",MachineToken:"machine_to_machine_token",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",IdpOAuthAccessToken:"clerk_idp_oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",SamlConnection:"saml_connection",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},nB=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h){this.id=e,this.name=t,this.subject=r,this.scopes=n,this.claims=i,this.revoked=s,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdBy=c,this.creationReason=u,this.createdAt=d,this.updatedAt=h}static fromJSON(t){return new e(t.id,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.creation_reason,t.created_at,t.updated_at)}},n$=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=n,this.allowedClockSkew=i,this.customSigningKey=s,this.signingAlgorithm=a,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},nz=class e{constructor(e,t,r,n={},i,s,a,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=s,this.tokenSecret=a,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},nK=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=n,this.isPublic=i,this.scopes=s,this.redirectUris=a,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=u,this.tokenIntrospectionUrl=d,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},nJ=class e{constructor(e,t,r,n,i,s,a,o={},l={},c,u,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdAt=s,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=u,this.membersCount=d,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},nW=class e{constructor(e,t,r,n,i,s,a,o,l,c,u={},d={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=n,this.organizationId=i,this.createdAt=s,this.updatedAt=a,this.expiresAt=o,this.url=l,this.status=c,this.publicMetadata=u,this.privateMetadata=d,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},nF=class e{constructor(e,t,r,n={},i={},s,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=s,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,nJ.fromJSON(t.organization),nV.fromJSON(t.public_user_data));return r._raw=t,r}},nV=class e{constructor(e,t,r,n,i,s){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=s}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},nG=class e{constructor(e,t,r,n,i,s,a,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=n,this.creatorRole=i,this.adminDeleteEnabled=s,this.domainsEnabled=a,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},nX=class e{constructor(e,t,r,n,i,s){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&nA.fromJSON(t.verification),t.linked_to.map(e=>nI.fromJSON(e)))}},nQ=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=n,this.successful=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},nY=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},nZ=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g,m,y,b,_,v){this.id=e,this.name=t,this.domain=r,this.organizationId=n,this.idpEntityId=i,this.idpSsoUrl=s,this.idpCertificate=a,this.idpMetadataUrl=o,this.idpMetadata=l,this.acsUrl=c,this.spEntityId=u,this.spMetadataUrl=d,this.active=h,this.provider=p,this.userCount=f,this.syncUserAttributes=g,this.allowSubdomains=m,this.allowIdpInitiated=y,this.createdAt=b,this.updatedAt=_,this.attributeMapping=v}static fromJSON(t){return new e(t.id,t.name,t.domain,t.organization_id,t.idp_entity_id,t.idp_sso_url,t.idp_certificate,t.idp_metadata_url,t.idp_metadata,t.acs_url,t.sp_entity_id,t.sp_metadata_url,t.active,t.provider,t.user_count,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at,t.attribute_mapping&&n1.fromJSON(t.attribute_mapping))}},n0=class e{constructor(e,t,r,n,i,s,a,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=s,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},n1=class e{constructor(e,t,r,n){this.userId=e,this.emailAddress=t,this.firstName=r,this.lastName=n}static fromJSON(t){return new e(t.user_id,t.email_address,t.first_name,t.last_name)}},n2=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=s,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&nA.fromJSON(t.verification),t.saml_connection&&n0.fromJSON(t.saml_connection))}},n5=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},n4=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},n3=class e{constructor(e,t,r,n){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=n}static fromJSON(t){return new e(t.email_address&&n4.fromJSON(t.email_address),t.phone_number&&n4.fromJSON(t.phone_number),t.web3_wallet&&n4.fromJSON(t.web3_wallet),t.external_account)}},n6=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g,m,y,b,_,v,w){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=n,this.missingFields=i,this.unverifiedFields=s,this.verifications=a,this.username=o,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=u,this.passwordEnabled=d,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=g,this.createdSessionId=m,this.createdUserId=y,this.abandonAt=b,this.legalAcceptedAt=_,this.publicMetadata=v,this.unsafeMetadata=w}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?n3.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},n8=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=s,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},n9=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},n7=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&nA.fromJSON(t.verification))}},ie=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g,m,y,b,_={},v={},w={},k=[],S=[],E=[],T=[],x=[],O,C,R=null,P,I){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=s,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=u,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=g,this.username=m,this.firstName=y,this.lastName=b,this.publicMetadata=_,this.privateMetadata=v,this.unsafeMetadata=w,this.emailAddresses=k,this.phoneNumbers=S,this.web3Wallets=E,this.externalAccounts=T,this.samlAccounts=x,this.lastActiveAt=O,this.createOrganizationEnabled=C,this.createOrganizationsLimit=R,this.deleteSelfEnabled=P,this.legalAcceptedAt=I,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>nN.fromJSON(e)),(t.phone_numbers||[]).map(e=>nX.fromJSON(e)),(t.web3_wallets||[]).map(e=>n7.fromJSON(e)),(t.external_accounts||[]).map(e=>nU.fromJSON(e)),(t.saml_accounts||[]).map(e=>n2.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},it=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.emailAddress=t,this.status=r,this.invitation=n,this.createdAt=i,this.updatedAt=s,this.isLocked=a}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&nq.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function ir(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return nC.fromJSON(e);switch(e.object){case nH.AccountlessApplication:return nb.fromJSON(e);case nH.ActorToken:return n_.fromJSON(e);case nH.AllowlistIdentifier:return nv.fromJSON(e);case nH.ApiKey:return nw.fromJSON(e);case nH.BlocklistIdentifier:return nk.fromJSON(e);case nH.Client:return nT.fromJSON(e);case nH.Cookies:return nO.fromJSON(e);case nH.Domain:return nR.fromJSON(e);case nH.EmailAddress:return nN.fromJSON(e);case nH.Email:return nP.fromJSON(e);case nH.IdpOAuthAccessToken:return nM.fromJSON(e);case nH.Instance:return nj.fromJSON(e);case nH.InstanceRestrictions:return nD.fromJSON(e);case nH.InstanceSettings:return nL.fromJSON(e);case nH.Invitation:return nq.fromJSON(e);case nH.JwtTemplate:return n$.fromJSON(e);case nH.MachineToken:return nB.fromJSON(e);case nH.OauthAccessToken:return nz.fromJSON(e);case nH.OAuthApplication:return nK.fromJSON(e);case nH.Organization:return nJ.fromJSON(e);case nH.OrganizationInvitation:return nW.fromJSON(e);case nH.OrganizationMembership:return nF.fromJSON(e);case nH.OrganizationSettings:return nG.fromJSON(e);case nH.PhoneNumber:return nX.fromJSON(e);case nH.ProxyCheck:return nQ.fromJSON(e);case nH.RedirectUrl:return nY.fromJSON(e);case nH.SamlConnection:return nZ.fromJSON(e);case nH.SignInToken:return n5.fromJSON(e);case nH.SignUpAttempt:return n6.fromJSON(e);case nH.Session:return nE.fromJSON(e);case nH.SmsMessage:return n8.fromJSON(e);case nH.Token:return n9.fromJSON(e);case nH.TotalCount:return e.total_count;case nH.User:return ie.fromJSON(e);case nH.WaitlistEntry:return it.fromJSON(e);default:return e}}function ii(e){var t;return t=async t=>{let r,{secretKey:n,requireSecretKey:i=!0,apiUrl:s=rm,apiVersion:a="v1",userAgent:o=ry,skipApiVersionInUrl:l=!1}=e,{path:c,method:u,queryParams:d,headerParams:h,bodyParams:p,formData:f}=t;i&&rx(n);let g=new URL(l?rP(s,c):rP(s,a,c));if(d)for(let[e,t]of Object.entries(rh({...d})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let m=new Headers({"Clerk-API-Version":rb,"User-Agent":o,...h});n&&m.set("Authorization",`Bearer ${n}`);try{var y;f?r=await tB.fetch(g.href,{method:u,headers:m,body:f}):(m.set("Content-Type","application/json"),r=await tB.fetch(g.href,{method:u,headers:m,...(()=>{if(!("GET"!==u&&p&&Object.keys(p).length>0))return null;let e=e=>rh(e,{deep:!1});return{body:JSON.stringify(Array.isArray(p)?p.map(e):e(p))}})()}));let e=r?.headers&&r.headers?.get(rw.Headers.ContentType)===rw.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:io(t),status:r?.status,statusText:r?.statusText,clerkTraceId:is(t,r?.headers),retryAfter:ia(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>ir(e))}:(y=t)&&"object"==typeof y&&"data"in y&&Array.isArray(y.data)&&void 0!==y.data?{data:t.data.map(e=>ir(e)),totalCount:t.total_count}:{data:ir(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:is(e,r?.headers)};return{data:null,errors:io(e),status:r?.status,statusText:r?.statusText,clerkTraceId:is(e,r?.headers),retryAfter:ia(r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:s,statusText:a,clerkTraceId:o,retryAfter:l}=await t(...e);if(n){let e=new tC(a||"",{data:[],status:s,clerkTraceId:o,retryAfter:l});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r}}function is(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function ia(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function io(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(tO):[]}return[]}function il(e){let t=ii(e);return{__experimental_accountlessApplications:new rM(ii({...e,requireSecretKey:!1})),actorTokens:new rN(t),allowlistIdentifiers:new rD(t),apiKeys:new rq(ii({...e,skipApiVersionInUrl:!0})),betaFeatures:new rH(t),blocklistIdentifiers:new r$(t),clients:new rK(t),domains:new rW(t),emailAddresses:new rV(t),idPOAuthAccessToken:new rG(ii({...e,skipApiVersionInUrl:!0})),instance:new rQ(t),invitations:new rZ(t),jwks:new r1(t),jwtTemplates:new r5(t),machineTokens:new r0(ii({...e,skipApiVersionInUrl:!0})),oauthApplications:new r8(t),organizations:new r3(t),phoneNumbers:new r7(t),proxyChecks:new ne(t),redirectUrls:new nr(t),samlConnections:new ni(t),sessions:new na(t),signInTokens:new nl(t),signUps:new nu(t),testingTokens:new nd(t),users:new np(t),waitlistEntries:new ng(t),webhooks:new ny(t)}}var ic={SessionToken:"session_token",ApiKey:"api_key",MachineToken:"machine_token",OAuthToken:"oauth_token"},iu="oat_",id=["mt_",iu,"ak_"];function ih(e){return id.some(t=>e.startsWith(t))}function ip(e){if(e.startsWith("mt_"))return ic.MachineToken;if(e.startsWith(iu))return ic.OAuthToken;if(e.startsWith("ak_"))return ic.ApiKey;throw Error("Unknown machine token type")}var ig=(e,t)=>!!e&&("any"===t||(Array.isArray(t)?t:[t]).includes(e)),im=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function iy(e,t){return{tokenType:ic.SessionToken,sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:im(e),isAuthenticated:!1}}function ib(e,t){let r={id:null,subject:null,scopes:null,has:()=>!1,getToken:()=>Promise.resolve(null),debug:im(t),isAuthenticated:!1};switch(e){case ic.ApiKey:return{...r,tokenType:e,name:null,claims:null,scopes:null,userId:null,orgId:null};case ic.MachineToken:return{...r,tokenType:e,name:null,claims:null,scopes:null,machineId:null};case ic.OAuthToken:return{...r,tokenType:e,scopes:null,userId:null,clientId:null};default:throw Error(`Invalid token type: ${e}`)}}function i_(){return{isAuthenticated:!1,tokenType:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:()=>({})}}var iv=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template||void 0!==e.expiresInSeconds?t(n,e.template,e.expiresInSeconds):r:null},iw=({authObject:e,acceptsToken:t=ic.SessionToken})=>"any"===t?e:Array.isArray(t)?ig(e.tokenType,t)?e:i_():ig(e.tokenType,t)?e:!function(e){return e===ic.ApiKey||e===ic.MachineToken||e===ic.OAuthToken}(t)?iy(e.debug):ib(t,e.debug),ik={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},iS={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",PrimaryDomainCrossOriginSync:"primary-domain-cross-origin-sync",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",TokenTypeMismatch:"token-type-mismatch",UnexpectedError:"unexpected-error"};function iE(e){let{authenticateContext:t,headers:r=new Headers,token:n}=e;return{status:ik.SignedIn,reason:null,message:null,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!0,isAuthenticated:!0,tokenType:e.tokenType,toAuth:({treatPendingAsSignedOut:r=!0}={})=>{if(e.tokenType===ic.SessionToken){let{sessionClaims:i}=e,s=function(e,t,r){let{actor:n,sessionId:i,sessionStatus:s,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d}=rd(r),h=il(e),p=iv({sessionId:i,sessionToken:t,fetcher:async(e,t,r)=>(await h.sessions.getToken(e,t||"",r)).jwt});return{tokenType:ic.SessionToken,actor:n,sessionClaims:r,sessionId:i,sessionStatus:s,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d,getToken:p,has:rc({orgId:o,orgRole:l,orgPermissions:u,userId:a,factorVerificationAge:d,features:r.fea||"",plans:r.pla||""}),debug:im({...e,sessionToken:t}),isAuthenticated:!0}}(t,n,i);return r&&"pending"===s.sessionStatus?iy(void 0,s.sessionStatus):s}let{machineData:i}=e;var s=e.tokenType;let a={id:i.id,subject:i.subject,getToken:()=>Promise.resolve(n),has:()=>!1,debug:im(t),isAuthenticated:!0};switch(s){case ic.ApiKey:return{...a,tokenType:s,name:i.name,claims:i.claims,scopes:i.scopes,userId:i.subject.startsWith("user_")?i.subject:null,orgId:i.subject.startsWith("org_")?i.subject:null};case ic.MachineToken:return{...a,tokenType:s,name:i.name,claims:i.claims,scopes:i.scopes,machineId:i.subject};case ic.OAuthToken:return{...a,tokenType:s,scopes:i.scopes,userId:i.subject,clientId:i.clientId};default:throw Error(`Invalid token type: ${s}`)}},headers:r,token:n}}function iT(e){let{authenticateContext:t,headers:r=new Headers,reason:n,message:i="",tokenType:s}=e;return ix({status:ik.SignedOut,reason:n,message:i,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:s,toAuth:()=>s===ic.SessionToken?iy({...t,status:ik.SignedOut,reason:n,message:i}):ib(s,{reason:n,message:i,headers:r}),headers:r,token:null})}var ix=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(rw.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(rw.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(rw.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},iO=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},iC=(...e)=>new iO(...e),iR=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(rw.Headers.ForwardedProto),n=e.headers.get(rw.Headers.ForwardedHost),i=e.headers.get(rw.Headers.Host),s=t.protocol,a=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??s?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?iC(t):iC(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,rp.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},iP=(...e)=>e[0]instanceof iR?e[0]:new iR(...e),iI=e=>e.split(";")[0]?.split("=")[0],iA=e=>e.split(";")[0]?.split("=")[1],iN={},iU=0;function iM(e,t=!0){iN[e.kid]=e,iU=t?Date.now():-1}var ij="local";function iD(e){if(!iN[ij]){if(!e)throw new tj({action:tM.SetClerkJWTKey,message:"Missing local JWK.",reason:tU.LocalJWKMissing});iM({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return iN[ij]}async function iL({secretKey:e,apiUrl:t=rm,apiVersion:r="v1",kid:n,skipJwksCache:i}){if(i||function(){if(-1===iU)return!1;let e=Date.now()-iU>=3e5;return e&&(iN={}),e}()||!iN[n]){if(!e)throw new tj({action:tM.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:tU.RemoteJWKFailedToLoad});let{keys:n}=await tl(()=>iq(t,e,r));if(!n||!n.length)throw new tj({action:tM.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:tU.RemoteJWKFailedToLoad});n.forEach(e=>iM(e))}let s=iN[n];if(!s){let e=Object.values(iN).map(e=>e.kid).sort().join(", ");throw new tj({action:`Go to your Dashboard and validate your secret and public keys are correct. ${tM.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:tU.JWKKidMismatch})}return s}async function iq(e,t,r){if(!t)throw new tj({action:tM.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:tU.RemoteJWKFailedToLoad});let n=new URL(e);n.pathname=rP(n.pathname,r,"/jwks");let i=await tB.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":rb,"Content-Type":"application/json","User-Agent":ry}});if(!i.ok){let e=await i.json(),t=iH(e?.errors,tN.InvalidSecretKey);if(t){let e=tU.InvalidSecretKey;throw new tj({action:tM.ContactSupport,message:t.message,reason:e})}throw new tj({action:tM.ContactSupport,message:`Error loading Clerk JWKS from ${n.href} with code=${i.status}`,reason:tU.RemoteJWKFailedToLoad})}return i.json()}var iH=(e,t)=>e?e.find(e=>e.code===t):null;async function iB(e,t){let{data:r,errors:n}=t4(e);if(n)return{errors:n};let{header:i}=r,{kid:s}=i;try{let r;if(t.jwtKey)r=iD(t.jwtKey);else{if(!t.secretKey)return{errors:[new tj({action:tM.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:tU.JWKFailedToResolve})]};r=await iL({...t,kid:s})}return await t3(e,{...t,key:r})}catch(e){return{errors:[e]}}}function i$(e,t,r){if("clerkError"in t){let n,i;switch(t.status){case 401:n=tD.InvalidSecretKey,i=t.errors[0]?.message||"Invalid secret key";break;case 404:n=tD.TokenInvalid,i=r;break;default:n=tD.UnexpectedError,i="Unexpected error"}return{data:void 0,tokenType:e,errors:[new tL({message:i,code:n,status:t.status})]}}return{data:void 0,tokenType:e,errors:[new tL({message:"Unexpected error",code:tD.UnexpectedError,status:t.status})]}}async function iz(e,t){try{let r=il(t);return{data:await r.machineTokens.verifySecret(e),tokenType:ic.MachineToken,errors:void 0}}catch(e){return i$(ic.MachineToken,e,"Machine token not found")}}async function iK(e,t){try{let r=il(t);return{data:await r.idPOAuthAccessToken.verifyAccessToken(e),tokenType:ic.OAuthToken,errors:void 0}}catch(e){return i$(ic.OAuthToken,e,"OAuth token not found")}}async function iJ(e,t){try{let r=il(t);return{data:await r.apiKeys.verifySecret(e),tokenType:ic.ApiKey,errors:void 0}}catch(e){return i$(ic.ApiKey,e,"API key not found")}}async function iW(e,t){if(e.startsWith("mt_"))return iz(e,t);if(e.startsWith(iu))return iK(e,t);if(e.startsWith("ak_"))return iJ(e,t);throw Error("Unknown machine token type")}async function iF(e,{key:t}){let{data:r,errors:n}=t4(e);if(n)throw n[0];let{header:i,payload:s}=r,{typ:a,alg:o}=i;tX(a),tQ(o);let{data:l,errors:c}=await t5(r,t);if(c)throw new tj({reason:tU.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!l)throw new tj({reason:tU.TokenInvalidSignature,message:"Handshake signature is invalid."});return s}async function iV(e,t){let r,{secretKey:n,apiUrl:i,apiVersion:s,jwksCacheTtlInMs:a,jwtKey:o,skipJwksCache:l}=t,{data:c,errors:u}=t4(e);if(u)throw u[0];let{kid:d}=c.header;if(o)r=iD(o);else if(n)r=await iL({secretKey:n,apiUrl:i,apiVersion:s,kid:d,jwksCacheTtlInMs:a,skipJwksCache:l});else throw new tj({action:tM.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:tU.JWKFailedToResolve});return await iF(e,{key:r})}var iG=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.startsWith("http")?this.authenticateContext.frontendApi:`https://${this.authenticateContext.frontendApi}`,n=new URL("v1/client/handshake",r=r.replace(/\/+$/,"")+"/");n.searchParams.append("redirect_url",t?.href||""),n.searchParams.append("__clerk_api_version",rb),n.searchParams.append(rw.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),n.searchParams.append(rw.QueryParameters.HandshakeReason,e),n.searchParams.append(rw.QueryParameters.HandshakeFormat,"nonce"),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&n.searchParams.append(rw.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let i=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return i&&this.getOrganizationSyncQueryParams(i).forEach((e,t)=>{n.searchParams.append(t,e)}),new Headers({[rw.Headers.Location]:n.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await iV(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),iI(t).startsWith(rw.Cookies.Session)&&(r=iA(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(rw.QueryParameters.Handshake),t.searchParams.delete(rw.QueryParameters.HandshakeHelp),e.append(rw.Headers.Location,t.toString()),e.set(rw.Headers.CacheControl,"no-store")}if(""===r)return iT({tokenType:ic.SessionToken,authenticateContext:this.authenticateContext,reason:iS.SessionTokenMissing,message:"",headers:e});let{data:n,errors:[i]=[]}=await iB(r,this.authenticateContext);if(n)return iE({tokenType:ic.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:n,headers:e,token:r});if("development"===this.authenticateContext.instanceType&&(i?.reason===tU.TokenExpired||i?.reason===tU.TokenNotActiveYet||i?.reason===tU.TokenIatInTheFuture)){let t=new tj({action:i.action,message:i.message,reason:i.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:n,errors:[s]=[]}=await iB(r,{...this.authenticateContext,clockSkewInMs:864e5});if(n)return iE({tokenType:ic.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:n,headers:e,token:r});throw Error(s?.message||"Clerk: Handshake retry failed.")}throw Error(i?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===tU.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=rw.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(rw.QueryParameters.DevBrowser),t.searchParams.delete(rw.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},iX=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,n,i,s,a,o,l;return r=void 0,n=[],i=function e(t,r,n){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(i.join("|"),")"),rg(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,c=r.delimiter,u=r.endsWith,d="[".concat(rf(void 0===u?"":u),"]|$"),h="[".concat(rf(void 0===c?"/#?":c),"]"),p=void 0===s||s?"^":"",f=0;f<e.length;f++){var g=e[f];if("string"==typeof g)p+=rf(l(g));else{var m=rf(l(g.prefix)),y=rf(l(g.suffix));if(g.pattern)if(t&&t.push(g),m||y)if("+"===g.modifier||"*"===g.modifier){var b="*"===g.modifier?"?":"";p+="(?:".concat(m,"((?:").concat(g.pattern,")(?:").concat(y).concat(m,"(?:").concat(g.pattern,"))*)").concat(y,")").concat(b)}else p+="(?:".concat(m,"(").concat(g.pattern,")").concat(y,")").concat(g.modifier);else{if("+"===g.modifier||"*"===g.modifier)throw TypeError('Can not repeat "'.concat(g.name,'" without a prefix and suffix'));p+="(".concat(g.pattern,")").concat(g.modifier)}else p+="(?:".concat(m).concat(y,")").concat(g.modifier)}}if(void 0===a||a)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(d,")"):"$";else{var _=e[e.length-1],v="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;i||(p+="(?:".concat(h,"(?=").concat(d,"))?")),v||(p+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(p,rg(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at ".concat(s));l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s=t.delimiter,a=void 0===s?"/#?":s,o=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c],i=n.type,s=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(s,", expected ").concat(e))},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},g=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(rf(a),"]+?"):"(?:(?!".concat(rf(r),")[^").concat(rf(a),"])+?")};c<r.length;){var m=d("CHAR"),y=d("NAME"),b=d("PATTERN");if(y||b){var _=m||"";-1===i.indexOf(_)&&(u+=_,_=""),u&&(o.push(u),u=""),o.push({name:y||l++,prefix:_,suffix:"",pattern:b||g(_),modifier:d("MODIFIER")||""});continue}var v=m||d("ESCAPED_CHAR");if(v){u+=v;continue}if(u&&(o.push(u),u=""),d("OPEN")){var _=p(),w=d("NAME")||"",k=d("PATTERN")||"",S=p();h("CLOSE"),o.push({name:w||(k?l++:""),pattern:w&&!k?g(_):k,prefix:_,suffix:S,modifier:d("MODIFIER")||""});continue}h("END")}return o}(t,n),r,n)}(e,n,r),s=n,a=r,void 0===a&&(a={}),o=a.decode,l=void 0===o?function(e){return e}:o,function(e){var t=i.exec(e);if(!t)return!1;for(var r=t[0],n=t.index,a=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=s[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):a[r.name]=l(t[e],r)}}(o);return{path:r,index:n,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},iQ={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};function iY(e,t,r){return ig(e,t)?null:iT({tokenType:e,authenticateContext:r,reason:iS.TokenTypeMismatch})}var iZ=async(e,t)=>{let r=await rC(iP(e),t);rx(r.secretKey);let n=t.acceptsToken??ic.SessionToken;if(r.isSatellite){var i=r.signInUrl,s=r.secretKey;if(!i&&tv(s))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let a=new iX(t.organizationSyncOptions),o=new iG(r,{organizationSyncOptions:t.organizationSyncOptions},a);async function l(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:iQ.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:i}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:iQ.MissingSessionToken}}};if(!i)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:iQ.MissingRefreshToken}}};let{data:s,errors:a}=t4(n);if(!s||a)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:iQ.ExpiredSessionTokenDecodeFailed,errors:a}}};if(!s?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:iQ.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(s.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:n||"",refresh_token:i||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:iQ.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:iQ.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function c(e){let{data:t,error:r}=await l(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,i="";t.forEach(e=>{n.append("Set-Cookie",e),iI(e).startsWith(rw.Cookies.Session)&&(i=iA(e))});let{data:s,errors:a}=await iB(i,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:iQ.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:s,sessionToken:i,headers:n},error:null}}function u(e,t,r,n){if(!o.isRequestEligibleForHandshake())return iT({tokenType:ic.SessionToken,authenticateContext:e,reason:t,message:r});let i=n??o.buildRedirectToHandshake(t);return(i.get(rw.Headers.Location)&&i.set(rw.Headers.CacheControl,"no-store"),o.checkAndTrackRedirectLoop(i))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),iT({tokenType:ic.SessionToken,authenticateContext:e,reason:t,message:r})):function(e,t,r="",n){return ix({status:ik.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:ic.SessionToken,toAuth:()=>null,headers:n,token:null})}(e,t,r,i)}async function d(){let{tokenInHeader:e}=r;try{let{data:t,errors:n}=await iB(e,r);if(n)throw n[0];return iE({tokenType:ic.SessionToken,authenticateContext:r,sessionClaims:t,headers:new Headers,token:e})}catch(e){return p(e,"header")}}async function h(){let e=r.clientUat,t=!!r.sessionTokenInCookie,n=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await o.resolveHandshake()}catch(e){e instanceof tj&&"development"===r.instanceType?o.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(rw.QueryParameters.DevBrowser))return u(r,iS.DevBrowserSync,"");let i=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&i)return u(r,iS.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&i&&!r.clerkUrl.searchParams.has(rw.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(rw.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[rw.Headers.Location]:e.toString()});return u(r,iS.SatelliteCookieNeedsSyncing,"",t)}let s=new URL(r.clerkUrl).searchParams.get(rw.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&s){let e=new URL(s);r.devBrowserToken&&e.searchParams.append(rw.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(rw.QueryParameters.ClerkSynced,"true");let t=new Headers({[rw.Headers.Location]:e.toString()});return u(r,iS.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!n)return u(r,iS.DevBrowserMissing,"");if(!e&&!t)return iT({tokenType:ic.SessionToken,authenticateContext:r,reason:iS.SessionTokenAndUATMissing});if(!e&&t)return u(r,iS.SessionTokenWithoutClientUAT,"");if(e&&!t)return u(r,iS.ClientUATWithoutSessionToken,"");let{data:l,errors:c}=t4(r.sessionTokenInCookie);if(c)return p(c[0],"cookie");if(l.payload.iat<r.clientUat)return u(r,iS.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await iB(r.sessionTokenInCookie,r);if(t)throw t[0];let n=iE({tokenType:ic.SessionToken,authenticateContext:r,sessionClaims:e,headers:new Headers,token:r.sessionTokenInCookie});if(!r.isSatellite&&"document"===r.secFetchDest&&r.isCrossOriginReferrer())return u(r,iS.PrimaryDomainCrossOriginSync,"Cross-origin request from satellite domain requires handshake");let i=n.toAuth();if(i.userId){let e=function(e,t){let r=a.findTarget(e.clerkUrl);if(!r)return null;let n=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(n=!0),r.organizationId&&r.organizationId!==t.orgId&&(n=!0)),"personalAccount"===r.type&&t.orgId&&(n=!0),!n)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let i=u(e,iS.ActiveOrganizationMismatch,"");return"handshake"!==i.status?null:i}(r,i);if(e)return e}return n}catch(e){return p(e,"cookie")}}async function p(t,n){let i;if(!(t instanceof tj))return iT({tokenType:ic.SessionToken,authenticateContext:r,reason:iS.UnexpectedError});if(t.reason===tU.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await c(r);if(e)return iE({tokenType:ic.SessionToken,authenticateContext:r,sessionClaims:e.jwtPayload,headers:e.headers,token:e.sessionToken});i=t?.cause?.reason?t.cause.reason:iQ.UnexpectedSDKError}else i="GET"!==e.method?iQ.NonEligibleNonGet:r.refreshTokenInCookie?null:iQ.NonEligibleNoCookie;return(t.tokenCarrier=n,[tU.TokenExpired,tU.TokenNotActiveYet,tU.TokenIatInTheFuture].includes(t.reason))?u(r,i1({tokenError:t.reason,refreshError:i}),t.getFullMessage()):iT({tokenType:ic.SessionToken,authenticateContext:r,reason:t.reason,message:t.getFullMessage()})}function f(e,t){return t instanceof tL?iT({tokenType:e,authenticateContext:r,reason:t.code,message:t.getFullMessage()}):iT({tokenType:e,authenticateContext:r,reason:iS.UnexpectedError})}async function g(){let{tokenInHeader:e}=r;if(!e)return p(Error("Missing token in header"),"header");if(!ih(e))return iT({tokenType:n,authenticateContext:r,reason:iS.TokenTypeMismatch,message:""});let t=iY(ip(e),n,r);if(t)return t;let{data:i,tokenType:s,errors:a}=await iW(e,r);return a?f(s,a[0]):iE({tokenType:s,authenticateContext:r,machineData:i,token:e})}async function m(){let{tokenInHeader:e}=r;if(!e)return p(Error("Missing token in header"),"header");if(ih(e)){let t=iY(ip(e),n,r);if(t)return t;let{data:i,tokenType:s,errors:a}=await iW(e,r);return a?f(s,a[0]):iE({tokenType:s,authenticateContext:r,machineData:i,token:e})}let{data:t,errors:i}=await iB(e,r);return i?p(i[0],"header"):iE({tokenType:ic.SessionToken,authenticateContext:r,sessionClaims:t,token:e})}return Array.isArray(n)&&!function(e,t){let r=null,{tokenInHeader:n}=t;return n&&(r=ih(n)?ip(n):ic.SessionToken),ig(r??ic.SessionToken,e)}(n,r)?function(){let e=i_();return ix({status:ik.SignedOut,reason:iS.TokenTypeMismatch,message:"",proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",signInUrl:"",signUpUrl:"",afterSignInUrl:"",afterSignUpUrl:"",isSignedIn:!1,isAuthenticated:!1,tokenType:null,toAuth:()=>e,headers:new Headers,token:null})}():r.tokenInHeader?"any"===n?m():n===ic.SessionToken?d():g():n===ic.OAuthToken||n===ic.ApiKey||n===ic.MachineToken?iT({tokenType:n,authenticateContext:r,reason:"No token in header"}):h()},i0=e=>{let{isSignedIn:t,isAuthenticated:r,proxyUrl:n,reason:i,message:s,publishableKey:a,isSatellite:o,domain:l}=e;return{isSignedIn:t,isAuthenticated:r,proxyUrl:n,reason:i,message:s,publishableKey:a,isSatellite:o,domain:l}},i1=({tokenError:e,refreshError:t})=>{switch(e){case tU.TokenExpired:return`${iS.SessionTokenExpired}-refresh-${t}`;case tU.TokenNotActiveYet:return iS.SessionTokenNBF;case tU.TokenIatInTheFuture:return iS.SessionTokenIatInTheFuture;default:return iS.UnexpectedError}},i2={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};r(821),r(167),r(830).s;var i5=r(159);let i4=""+i5.s8+";404";i5.s8,i5.s8,r(792).X,r(280),"undefined"==typeof URLPattern||URLPattern,r(557),r(602),r(801),r(335),new WeakMap;let i3={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},i6=e=>e.headers.get(i3.Headers.NextRedirect),i8=(e,t,r)=>(e.headers.set(t,r),e);var i9="__clerk_db_jwt",i7=e=>{let t=new URL(e);return t.searchParams.delete(i9),t},se=e=>{let t=new URL(e);return t.searchParams.delete("__dev_session"),t.hash=decodeURI(t.hash).replace(/__clerk_db_jwt\[(.*)\]/,""),t.href.endsWith("#")&&(t.hash=""),t};let st=(e,t,r)=>{let n=t.headers.get("location");if("true"===t.headers.get(rw.Headers.ClerkRedirectTo)&&n&&tv(r.secretKey)&&e.clerkUrl.isCrossOrigin(n)){let r=e.cookies.get(i9)||"",i=function(e,t){let r=new URL(e),n=r.searchParams.get(i9);r.searchParams.delete(i9);let i=n||t;return i&&r.searchParams.set(i9,i),r}(new URL(n),r);return z.redirect(i.href,t)}return t},sr={rE:"15.3.5"},sn=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},si=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?sn(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,sn(t)])),null,2)).join(", "),ss=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){var i,s;for(let n of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((s=e,`[clerk debug end: ${s}] (@clerk/nextjs=6.24.0,next=${sr.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},sa=(e,t)=>(...r)=>{let n=("string"==typeof e?ss(e,si):e)(),i=t(n);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};function so(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}var sl=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function sc(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}sl(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),sl(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let su=process.env.CLERK_API_VERSION||"v1",sd=process.env.CLERK_SECRET_KEY||"",sh="pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ",sp=process.env.CLERK_ENCRYPTION_KEY||"",sf=process.env.CLERK_API_URL||(e=>{let t=tb(e)?.frontendApi;return t?.startsWith("clerk.")&&td.some(e=>t?.endsWith(e))?tg:tp.some(e=>t?.endsWith(e))?"https://api.lclclerk.com":tf.some(e=>t?.endsWith(e))?"https://api.clerkstage.dev":tg})(sh),sg=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",sm=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",sy=sc(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,sb=process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL||"",s_=process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL||"",sv=sc(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),sw=sc(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),sk=sc(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1,sS=!(sr.rE.startsWith("13.")||sr.rE.startsWith("14.0"))&&!1,sE=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),n=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||n||i};async function sT(){try{let{headers:e}=await Promise.resolve().then(r.bind(r,221)),t=await e();return new L("https://placeholder.com",{headers:t})}catch(e){if(e&&sE(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}var sx=class{constructor(){tt(this,sH),tt(this,sL,"clerk_telemetry_throttler"),tt(this,sq,864e5)}isEventThrottled(e){if(!te(this,sH,sz))return!1;let t=Date.now(),r=tn(this,sH,sB).call(this,e),n=te(this,sH,s$)?.[r];if(!n){let e={...te(this,sH,s$),[r]:t};localStorage.setItem(te(this,sL),JSON.stringify(e))}if(n&&t-n>te(this,sq)){let e=te(this,sH,s$);delete e[r],localStorage.setItem(te(this,sL),JSON.stringify(e))}return!!n}};sL=new WeakMap,sq=new WeakMap,sH=new WeakSet,sB=function(e){let{sk:t,pk:r,payload:n,...i}=e,s={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>s[e]))},s$=function(){let e=localStorage.getItem(te(this,sL));return e?JSON.parse(e):{}},sz=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem(te(this,sL)),!1}};var sO={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},sC=class{constructor(e){tt(this,sG),tt(this,sK),tt(this,sJ),tt(this,sW,{}),tt(this,sF,[]),tt(this,sV),tr(this,sK,{maxBufferSize:e.maxBufferSize??sO.maxBufferSize,samplingRate:e.samplingRate??sO.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:sO.endpoint}),e.clerkVersion||"undefined"!=typeof window?te(this,sW).clerkVersion=e.clerkVersion??"":te(this,sW).clerkVersion="",te(this,sW).sdk=e.sdk,te(this,sW).sdkVersion=e.sdkVersion,te(this,sW).publishableKey=e.publishableKey??"";let t=tb(e.publishableKey);t&&(te(this,sW).instanceType=t.instanceType),e.secretKey&&(te(this,sW).secretKey=e.secretKey.substring(0,16)),tr(this,sJ,new sx)}get isEnabled(){return!("development"!==te(this,sW).instanceType||te(this,sK).disabled||"undefined"!=typeof process&&process.env&&sc(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return te(this,sK).debug||"undefined"!=typeof process&&process.env&&sc(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=tn(this,sG,s2).call(this,e.event,e.payload);tn(this,sG,s0).call(this,t.event,t),tn(this,sG,sX).call(this,t,e.eventSamplingRate)&&(te(this,sF).push(t),tn(this,sG,sY).call(this))}};sK=new WeakMap,sJ=new WeakMap,sW=new WeakMap,sF=new WeakMap,sV=new WeakMap,sG=new WeakSet,sX=function(e,t){return this.isEnabled&&!this.isDebug&&tn(this,sG,sQ).call(this,e,t)},sQ=function(e,t){let r=Math.random();return!!(r<=te(this,sK).samplingRate&&(void 0===t||r<=t))&&!te(this,sJ).isEventThrottled(e)},sY=function(){if("undefined"==typeof window)return void tn(this,sG,sZ).call(this);if(te(this,sF).length>=te(this,sK).maxBufferSize){te(this,sV)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)(te(this,sV)),tn(this,sG,sZ).call(this);return}te(this,sV)||("requestIdleCallback"in window?tr(this,sV,requestIdleCallback(()=>{tn(this,sG,sZ).call(this)})):tr(this,sV,setTimeout(()=>{tn(this,sG,sZ).call(this)},0)))},sZ=function(){fetch(new URL("/v1/event",te(this,sK).endpoint),{method:"POST",body:JSON.stringify({events:te(this,sF)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{tr(this,sF,[])}).catch(()=>void 0)},s0=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},s1=function(){let e={name:te(this,sW).sdk,version:te(this,sW).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},s2=function(e,t){let r=tn(this,sG,s1).call(this);return{event:e,cv:te(this,sW).clerkVersion??"",it:te(this,sW).instanceType??"",sdk:r.name,sdkv:r.version,...te(this,sW).publishableKey?{pk:te(this,sW).publishableKey}:{},...te(this,sW).secretKey?{sk:te(this,sW).secretKey}:{},payload:t}};let sR={secretKey:sd,publishableKey:sh,apiUrl:sf,apiVersion:su,userAgent:"@clerk/nextjs@6.24.0",proxyUrl:sm,domain:sg,isSatellite:sy,sdkMetadata:{name:"@clerk/nextjs",version:"6.24.0",environment:"production"},telemetry:{disabled:sv,debug:sw}},sP=e=>(function(e){let t={...e},r=il(t),n=function(e){let t=rT(i2,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:s}=t,a=rT(t,n);return iZ(e,{...n,...a,apiUrl:i,apiVersion:s,apiClient:r})},debugRequestState:i0}}({options:t,apiClient:r}),i=new sC({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...n,telemetry:i}})({...sR,...e});function sI(e,t){var r,n;return function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(n=null==(r=e.socket)?void 0:r._httpMessage)?void 0:n.getHeader(t))}var sA=r(521);let sN=new Map,sU=new sA.AsyncLocalStorage;var sM=new Set,sj={warnOnce:e=>{sM.has(e)||(sM.add(e),console.warn(e))}};function sD(e){return/^http(s)?:\/\//.test(e||"")}var sL,sq,sH,sB,s$,sz,sK,sJ,sW,sF,sV,sG,sX,sQ,sY,sZ,s0,s1,s2,s5,s4,s3,s6,s8,s9,s7,ae=Object.defineProperty,at=(e,t,r)=>t in e?ae(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ar=(null==(s5="undefined"!=typeof globalThis?globalThis:void 0)?void 0:s5.crypto)||(null==(s4=void 0!==r.g?r.g:void 0)?void 0:s4.crypto)||(null==(s3="undefined"!=typeof window?window:void 0)?void 0:s3.crypto)||(null==(s6="undefined"!=typeof self?self:void 0)?void 0:s6.crypto)||(null==(s9=null==(s8="undefined"!=typeof frames?frames:void 0)?void 0:s8[0])?void 0:s9.crypto);s7=ar?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(ar.getRandomValues(new Uint32Array(1))[0]);return new ai(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let n=0,i;n<e;n+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new ai(t,e)};var an=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},ai=class extends an{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let n=0;n<e;n+=1)t[n>>>2]|=r[n]<<24-n%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=as){return e.stringify(this)}concat(e){let t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[n+e>>>2]|=i<<24-(n+e)%4*8}else for(let e=0;e<i;e+=4)t[n+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>at(e,"symbol"!=typeof t?t+"":t,r))(ai,"random",s7);var as={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new ai(r,t/2)}},aa={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push(String.fromCharCode(r))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=1)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new ai(r,t)}},ao={stringify(e){try{return decodeURIComponent(escape(aa.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>aa.parse(unescape(encodeURIComponent(e)))},al=class extends an{constructor(){super(),this._minBufferSize=0}reset(){this._data=new ai,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=ao.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:n}=this,i=r.words,s=r.sigBytes,a=s/(4*n),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,l=Math.min(4*o,s);if(o){for(let e=0;e<o;e+=n)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new ai(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},ac=class extends al{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new an,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new au(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},au=class extends an{constructor(e,t){super();let r=new e;this._hasher=r;let n=t;"string"==typeof n&&(n=ao.parse(n));let i=r.blockSize,s=4*i;n.sigBytes>s&&(n=r.finalize(t)),n.clamp();let a=n.clone();this._oKey=a;let o=n.clone();this._iKey=o;let l=a.words,c=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;a.sigBytes=s,o.sigBytes=s,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},ad=(e,t,r)=>{let n=[],i=0;for(let s=0;s<t;s+=1)if(s%4){let t=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;n[i>>>2]|=t<<24-i%4*8,i+=1}return ai.create(n,i)},ah={stringify(e){let{words:t,sigBytes:r}=e,n=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let s=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(n.charAt(s>>>6*(3-t)&63))}let s=n.charAt(64);if(s)for(;i.length%4;)i.push(s);return i.join("")},parse(e){let t=e.length,r=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(let e=0;e<r.length;e+=1)n[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return ad(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},ap=[];for(let e=0;e<64;e+=1)ap[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var af=(e,t,r,n,i,s,a)=>{let o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t},ag=(e,t,r,n,i,s,a)=>{let o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t},am=(e,t,r,n,i,s,a)=>{let o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t},ay=(e,t,r,n,i,s,a)=>{let o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t},ab=class extends ac{_doReset(){this._hash=new ai([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let n=t+r,i=e[n];e[n]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,n=e[t+0],i=e[t+1],s=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],u=e[t+7],d=e[t+8],h=e[t+9],p=e[t+10],f=e[t+11],g=e[t+12],m=e[t+13],y=e[t+14],b=e[t+15],_=r[0],v=r[1],w=r[2],k=r[3];_=af(_,v,w,k,n,7,ap[0]),k=af(k,_,v,w,i,12,ap[1]),w=af(w,k,_,v,s,17,ap[2]),v=af(v,w,k,_,a,22,ap[3]),_=af(_,v,w,k,o,7,ap[4]),k=af(k,_,v,w,l,12,ap[5]),w=af(w,k,_,v,c,17,ap[6]),v=af(v,w,k,_,u,22,ap[7]),_=af(_,v,w,k,d,7,ap[8]),k=af(k,_,v,w,h,12,ap[9]),w=af(w,k,_,v,p,17,ap[10]),v=af(v,w,k,_,f,22,ap[11]),_=af(_,v,w,k,g,7,ap[12]),k=af(k,_,v,w,m,12,ap[13]),w=af(w,k,_,v,y,17,ap[14]),v=af(v,w,k,_,b,22,ap[15]),_=ag(_,v,w,k,i,5,ap[16]),k=ag(k,_,v,w,c,9,ap[17]),w=ag(w,k,_,v,f,14,ap[18]),v=ag(v,w,k,_,n,20,ap[19]),_=ag(_,v,w,k,l,5,ap[20]),k=ag(k,_,v,w,p,9,ap[21]),w=ag(w,k,_,v,b,14,ap[22]),v=ag(v,w,k,_,o,20,ap[23]),_=ag(_,v,w,k,h,5,ap[24]),k=ag(k,_,v,w,y,9,ap[25]),w=ag(w,k,_,v,a,14,ap[26]),v=ag(v,w,k,_,d,20,ap[27]),_=ag(_,v,w,k,m,5,ap[28]),k=ag(k,_,v,w,s,9,ap[29]),w=ag(w,k,_,v,u,14,ap[30]),v=ag(v,w,k,_,g,20,ap[31]),_=am(_,v,w,k,l,4,ap[32]),k=am(k,_,v,w,d,11,ap[33]),w=am(w,k,_,v,f,16,ap[34]),v=am(v,w,k,_,y,23,ap[35]),_=am(_,v,w,k,i,4,ap[36]),k=am(k,_,v,w,o,11,ap[37]),w=am(w,k,_,v,u,16,ap[38]),v=am(v,w,k,_,p,23,ap[39]),_=am(_,v,w,k,m,4,ap[40]),k=am(k,_,v,w,n,11,ap[41]),w=am(w,k,_,v,a,16,ap[42]),v=am(v,w,k,_,c,23,ap[43]),_=am(_,v,w,k,h,4,ap[44]),k=am(k,_,v,w,g,11,ap[45]),w=am(w,k,_,v,b,16,ap[46]),v=am(v,w,k,_,s,23,ap[47]),_=ay(_,v,w,k,n,6,ap[48]),k=ay(k,_,v,w,u,10,ap[49]),w=ay(w,k,_,v,y,15,ap[50]),v=ay(v,w,k,_,l,21,ap[51]),_=ay(_,v,w,k,g,6,ap[52]),k=ay(k,_,v,w,a,10,ap[53]),w=ay(w,k,_,v,p,15,ap[54]),v=ay(v,w,k,_,i,21,ap[55]),_=ay(_,v,w,k,d,6,ap[56]),k=ay(k,_,v,w,b,10,ap[57]),w=ay(w,k,_,v,c,15,ap[58]),v=ay(v,w,k,_,m,21,ap[59]),_=ay(_,v,w,k,o,6,ap[60]),k=ay(k,_,v,w,f,10,ap[61]),w=ay(w,k,_,v,s,15,ap[62]),v=ay(v,w,k,_,h,21,ap[63]),r[0]=r[0]+_|0,r[1]=r[1]+v|0,r[2]=r[2]+w|0,r[3]=r[3]+k|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;let i=Math.floor(r/0x100000000);t[(n+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(n+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let s=this._hash,a=s.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return s}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};ac._createHelper(ab),ac._createHmacHelper(ab);var a_=class extends an{constructor(e){super(),this.cfg=Object.assign(new an,{keySize:4,hasher:ab,iterations:1},e)}compute(e,t){let r,{cfg:n}=this,i=n.hasher.create(),s=ai.create(),a=s.words,{keySize:o,iterations:l}=n;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*o,s}},av=class extends al{constructor(e,t,r){super(),this.cfg=Object.assign(new an,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?aC:aO;return{encrypt:(r,n,i)=>t(n).encrypt(e,r,n,i),decrypt:(r,n,i)=>t(n).decrypt(e,r,n,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};av._ENC_XFORM_MODE=1,av._DEC_XFORM_MODE=2,av.keySize=4,av.ivSize=4;var aw=class extends an{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function ak(e,t,r){let n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=n[i]}var aS=class extends aw{};aS.Encryptor=class extends aS{processBlock(e,t){let r=this._cipher,{blockSize:n}=r;ak.call(this,e,t,n),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}},aS.Decryptor=class extends aS{processBlock(e,t){let r=this._cipher,{blockSize:n}=r,i=e.slice(t,t+n);r.decryptBlock(e,t),ak.call(this,e,t,n),this._prevBlock=i}};var aE={pad(e,t){let r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[];for(let e=0;e<n;e+=4)s.push(i);let a=ai.create(s,n);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},aT=class extends av{constructor(e,t,r){super(e,t,Object.assign({mode:aS,padding:aE},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:n}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},ax=class extends an{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},aO=class extends an{static encrypt(e,t,r,n){let i=Object.assign(new an,this.cfg,n),s=e.createEncryptor(r,i),a=s.finalize(t),o=s.cfg;return ax.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:s.blockSize,formatter:i.format})}static decrypt(e,t,r,n){let i=t,s=Object.assign(new an,this.cfg,n);return i=this._parse(i,s.format),e.createDecryptor(r,s).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};aO.cfg=Object.assign(new an,{format:{stringify(e){let t,{ciphertext:r,salt:n}=e;return(n?ai.create([0x53616c74,0x65645f5f]).concat(n).concat(r):r).toString(ah)},parse(e){let t,r=ah.parse(e),n=r.words;return 0x53616c74===n[0]&&0x65645f5f===n[1]&&(t=ai.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),ax.create({ciphertext:r,salt:t})}}});var aC=class extends aO{static encrypt(e,t,r,n){let i=Object.assign(new an,this.cfg,n),s=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=s.iv;let a=aO.encrypt.call(this,e,t,s.key,i);return a.mixIn(s),a}static decrypt(e,t,r,n){let i=t,s=Object.assign(new an,this.cfg,n);i=this._parse(i,s.format);let a=s.kdf.execute(r,e.keySize,e.ivSize,i.salt,s.hasher);return s.iv=a.iv,aO.decrypt.call(this,e,i,a.key,s)}};aC.cfg=Object.assign(aO.cfg,{kdf:{execute(e,t,r,n,i){let s,a=n;a||(a=ai.random(8)),s=i?a_.create({keySize:t+r,hasher:i}).compute(e,a):a_.create({keySize:t+r}).compute(e,a);let o=ai.create(s.words.slice(t),4*r);return s.sigBytes=4*t,ax.create({key:s,iv:o,salt:a})}}});var aR=[],aP=[],aI=[],aA=[],aN=[],aU=[],aM=[],aj=[],aD=[],aL=[],aq=[];for(let e=0;e<256;e+=1)e<128?aq[e]=e<<1:aq[e]=e<<1^283;var aH=0,aB=0;for(let e=0;e<256;e+=1){let e=aB^aB<<1^aB<<2^aB<<3^aB<<4;e=e>>>8^255&e^99,aR[aH]=e,aP[e]=aH;let t=aq[aH],r=aq[t],n=aq[r],i=257*aq[e]^0x1010100*e;aI[aH]=i<<24|i>>>8,aA[aH]=i<<16|i>>>16,aN[aH]=i<<8|i>>>24,aU[aH]=i,i=0x1010101*n^65537*r^257*t^0x1010100*aH,aM[e]=i<<24|i>>>8,aj[e]=i<<16|i>>>16,aD[e]=i<<8|i>>>24,aL[e]=i,aH?(aH=t^aq[aq[aq[n^t]]],aB^=aq[aq[aB]]):aH=aB=1}var a$=[0,1,2,4,8,16,32,64,128,27,54],az=class extends aT{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,n=t.sigBytes/4;this._nRounds=n+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let s=this._keySchedule;for(let t=0;t<i;t+=1)t<n?s[t]=r[t]:(e=s[t-1],t%n?n>6&&t%n==4&&(e=aR[e>>>24]<<24|aR[e>>>16&255]<<16|aR[e>>>8&255]<<8|aR[255&e]):e=(aR[(e=e<<8|e>>>24)>>>24]<<24|aR[e>>>16&255]<<16|aR[e>>>8&255]<<8|aR[255&e])^a$[t/n|0]<<24,s[t]=s[t-n]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?s[r]:s[r-4],t<4||r<=4?a[t]=e:a[t]=aM[aR[e>>>24]]^aj[aR[e>>>16&255]]^aD[aR[e>>>8&255]]^aL[aR[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,aI,aA,aN,aU,aR)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,aM,aj,aD,aL,aP),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,n,i,s,a,o){let l=this._nRounds,c=e[t]^r[0],u=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],p=4;for(let e=1;e<l;e+=1){let e=n[c>>>24]^i[u>>>16&255]^s[d>>>8&255]^a[255&h]^r[p];p+=1;let t=n[u>>>24]^i[d>>>16&255]^s[h>>>8&255]^a[255&c]^r[p];p+=1;let o=n[d>>>24]^i[h>>>16&255]^s[c>>>8&255]^a[255&u]^r[p];p+=1;let l=n[h>>>24]^i[c>>>16&255]^s[u>>>8&255]^a[255&d]^r[p];p+=1,c=e,u=t,d=o,h=l}let f=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1;let g=(o[u>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[p];p+=1;let m=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^r[p];p+=1;let y=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&d])^r[p];p+=1,e[t]=f,e[t+1]=g,e[t+2]=m,e[t+3]=y}};az.keySize=8;var aK=aT._createHelper(az),aJ=[],aW=class extends ac{_doReset(){this._hash=new ai([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)aJ[r]=0|e[t+r];else{let e=aJ[r-3]^aJ[r-8]^aJ[r-14]^aJ[r-16];aJ[r]=e<<1|e>>>31}let l=(n<<5|n>>>27)+o+aJ[r];r<20?l+=(i&s|~i&a)+0x5a827999:r<40?l+=(i^s^a)+0x6ed9eba1:r<60?l+=(i&s|i&a|s&a)-0x70e44324:l+=(i^s^a)-0x359d3e2a,o=a,a=s,s=i<<30|i>>>2,i=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[(n+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(n+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},aF=(ac._createHelper(aW),ac._createHmacHelper(aW));let aV=`
Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.

1) With middleware
   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'
   `,aG=`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL

1) With middleware
   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`,aX=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`,aQ=tP({packageName:"@clerk/nextjs"}),aY="x-middleware-override-headers",aZ="x-middleware-request",a0=(e,t,r)=>{e.headers.get(aY)||(e.headers.set(aY,[...t.headers.keys()]),t.headers.forEach((t,r)=>{e.headers.set(`${aZ}-${r}`,t)})),Object.entries(r).forEach(([t,r])=>{e.headers.set(aY,`${e.headers.get(aY)},${t}`),e.headers.set(`${aZ}-${t}`,r)})},a1=(e,t)=>{let r,n=so(null==t?void 0:t.proxyUrl,e.clerkUrl,sm);r=n&&!sD(n)?new URL(n,e.clerkUrl).toString():n;let i=so(t.isSatellite,new URL(e.url),sy),s=so(t.domain,new URL(e.url),sg),a=(null==t?void 0:t.signInUrl)||sb;if(i&&!r&&!s)throw Error(aV);if(i&&!sD(a)&&tv(t.secretKey||sd))throw Error(aG);return{proxyUrl:r,isSatellite:i,domain:s,signInUrl:a}},a2=e=>z.redirect(e,{headers:{[rw.Headers.ClerkRedirectTo]:"true"}}),a5="clerk_keyless_dummy_key";function a4(){if(tE())throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)");throw Error(aX)}function a3(e,t){return JSON.parse(aK.decrypt(e,t).toString(ao))}let a6=async()=>{var e,t;let r;try{let e=await sT(),t=sI(e,rw.Headers.ClerkRequestData);r=function(e){if(!e)return{};let t=tE()?sp||sd:sp||sd||a5;try{return a3(e,t)}catch{if(sS)try{return a3(e,a5)}catch{a4()}a4()}}(t)}catch(e){if(e&&sE(e))throw e}let n=null!=(t=null==(e=sU.getStore())?void 0:e.get("requestData"))?t:r;return(null==n?void 0:n.secretKey)||(null==n?void 0:n.publishableKey)?sP(n):sP({})};class a8{static createDefaultDirectives(){return Object.entries(this.DEFAULT_DIRECTIVES).reduce((e,[t,r])=>(e[t]=new Set(r),e),{})}static isKeyword(e){return this.KEYWORDS.has(e.replace(/^'|'$/g,""))}static formatValue(e){let t=e.replace(/^'|'$/g,"");return this.isKeyword(t)?`'${t}'`:e}static handleDirectiveValues(e){let t=new Set;return e.includes("'none'")||e.includes("none")?t.add("'none'"):e.forEach(e=>t.add(this.formatValue(e))),t}}a8.KEYWORDS=new Set(["none","self","strict-dynamic","unsafe-eval","unsafe-hashes","unsafe-inline"]),a8.DEFAULT_DIRECTIVES={"connect-src":["self","https://clerk-telemetry.com","https://*.clerk-telemetry.com","https://api.stripe.com","https://maps.googleapis.com"],"default-src":["self"],"form-action":["self"],"frame-src":["self","https://challenges.cloudflare.com","https://*.js.stripe.com","https://js.stripe.com","https://hooks.stripe.com"],"img-src":["self","https://img.clerk.com"],"script-src":["self","unsafe-inline","https:","http:","https://*.js.stripe.com","https://js.stripe.com","https://maps.googleapis.com"],"style-src":["self","unsafe-inline"],"worker-src":["self","blob:"]};let a9="__clerk_keys_";async function a7(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function oe(){let e=process.env.PWD;if(!e)return`${a9}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await a7(t);return`${a9}${r}`}async function ot(e){let t;if(!sS)return;let r=await oe();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}let or={REDIRECT_TO_URL:"CLERK_PROTECT_REDIRECT_TO_URL",REDIRECT_TO_SIGN_IN:"CLERK_PROTECT_REDIRECT_TO_SIGN_IN",REDIRECT_TO_SIGN_UP:"CLERK_PROTECT_REDIRECT_TO_SIGN_UP"},on={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},oi=new Set(Object.values(on)),os="NEXT_HTTP_ERROR_FALLBACK";function oa(e){if(!function(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===os&&oi.has(Number(r))}(e))return;let[,t]=e.digest.split(";");return Number(t)}let oo="NEXT_REDIRECT";function ol(e,t,r="replace",n=307){let i=Error(oo);throw i.digest=`${oo};${r};${e};${n};`,i.clerk_digest=or.REDIRECT_TO_URL,Object.assign(i,t),i}function oc(e,t){return null===t?"":t||e}function ou(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,i=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===oo&&("replace"===n||"push"===n)&&"string"==typeof i&&!isNaN(s)&&307===s}function od(){let e=Error(os);throw e.digest=`${os};${on.UNAUTHORIZED}`,e}let oh=e=>{if(e&&!e.unauthenticatedUrl&&!e.unauthorizedUrl&&!e.token&&(1!==Object.keys(e).length||!("token"in e)))return e},op=e=>{var t,r;return!!e.headers.get(i3.Headers.NextUrl)&&((null==(t=e.headers.get(rw.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(rw.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(i3.Headers.NextAction))},of=e=>{var t;return"document"===e.headers.get(rw.Headers.SecFetchDest)||"iframe"===e.headers.get(rw.Headers.SecFetchDest)||(null==(t=e.headers.get(rw.Headers.Accept))?void 0:t.includes("text/html"))||og(e)||oy(e)},og=e=>!!e.headers.get(i3.Headers.NextUrl)&&!op(e)||om(),om=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},oy=e=>!!e.headers.get(i3.Headers.NextjsData),ob=e=>[e[0]instanceof Request?e[0]:void 0,e[0]instanceof Request?e[1]:void 0],o_=e=>["function"==typeof e[0]?e[0]:void 0,(2===e.length?e[1]:"function"==typeof e[0]?{}:e[0])||{}],ov=e=>"/clerk-sync-keyless"===e.nextUrl.pathname,ow=e=>{let t=e.nextUrl.searchParams.get("returnUrl"),r=new URL(e.url);return r.pathname="",z.redirect(t||r.toString())},ok=(e,t)=>({...t,...a1(e,t),acceptsToken:"any"}),oS=e=>(t={})=>{!function(e,t){ol(e,{clerk_digest:or.REDIRECT_TO_SIGN_IN,returnBackUrl:oc(e,t)})}(e.clerkUrl.toString(),t.returnBackUrl)},oE=e=>(t={})=>{!function(e,t){ol(e,{clerk_digest:or.REDIRECT_TO_SIGN_UP,returnBackUrl:oc(e,t)})}(e.clerkUrl.toString(),t.returnBackUrl)},oT=(e,t,r)=>async(n,i)=>(function(e){let{redirectToSignIn:t,authObject:r,redirect:n,notFound:i,request:s,unauthorized:a}=e;return async(...e)=>{var o,l,c,u,d,h;let p=oh(e[0]),f=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(l=e[1])?void 0:l.unauthenticatedUrl),g=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(u=e[1])?void 0:u.unauthorizedUrl),m=(null==(d=e[0])?void 0:d.token)||(null==(h=e[1])?void 0:h.token)||ic.SessionToken,y=()=>r.tokenType!==ic.SessionToken?a():g?n(g):i();return ig(r.tokenType,m)?r.tokenType!==ic.SessionToken?r.isAuthenticated?r:y():"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:y():r.has(p)?r:y():r:f?n(f):of(s)?t():i():y()}})({request:e,redirect:e=>ol(e,{redirectUrl:e}),notFound:()=>(function(){let e=Object.defineProperty(Error(i4),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i4,e})(),unauthorized:od,authObject:iw({authObject:t,acceptsToken:(null==n?void 0:n.token)||(null==i?void 0:i.token)||ic.SessionToken}),redirectToSignIn:r})(n,i),ox=(e,t,r)=>async n=>{var i;let s=null!=(i=null==n?void 0:n.acceptsToken)?i:ic.SessionToken,a=iw({authObject:e,acceptsToken:s});return a.tokenType===ic.SessionToken&&ig(ic.SessionToken,s)?Object.assign(a,{redirectToSignIn:t,redirectToSignUp:r}):a},oO=(e,t,r,n)=>{var i;if(oa(e)===on.UNAUTHORIZED){let e=new z(null,{status:401}),t=n.toAuth();if(t&&t.tokenType===ic.OAuthToken){let t=tb(n.publishableKey);return i8(e,"WWW-Authenticate",`Bearer resource_metadata="https://${null==t?void 0:t.frontendApi}/.well-known/oauth-protected-resource"`)}return e}if(function(e){return"object"==typeof e&&null!==e&&"digest"in e&&"NEXT_NOT_FOUND"===e.digest||oa(e)===on.NOT_FOUND}(e))return i8(z.rewrite(new URL(`/clerk_${Date.now()}`,r.url)),rw.Headers.AuthReason,"protect-rewrite");let s=function(e){return!!ou(e)&&"clerk_digest"in e&&e.clerk_digest===or.REDIRECT_TO_SIGN_IN}(e),a=function(e){return!!ou(e)&&"clerk_digest"in e&&e.clerk_digest===or.REDIRECT_TO_SIGN_UP}(e);if(s||a){let r=rE({redirectAdapter:a2,baseUrl:t.clerkUrl,signInUrl:n.signInUrl,signUpUrl:n.signUpUrl,publishableKey:n.publishableKey,sessionStatus:null==(i=n.toAuth())?void 0:i.sessionStatus}),{returnBackUrl:a}=e;return r[s?"redirectToSignIn":"redirectToSignUp"]({returnBackUrl:a})}if(ou(e))return a2(e.redirectUrl);throw e},oC=((...e)=>{let[t,r]=ob(e),[n,i]=o_(e);return sU.run(sN,()=>{let e=sa("clerkMiddleware",e=>async(t,r)=>{var s,a;let o="function"==typeof i?await i(t):i,l=await ot(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value}),c=function(e,t){return e||t(),e}(o.publishableKey||sh||(null==l?void 0:l.publishableKey),()=>aQ.throwMissingPublishableKeyError()),u=function(e,t){return e||t(),e}(o.secretKey||sd||(null==l?void 0:l.secretKey),()=>aQ.throwMissingSecretKeyError()),d={publishableKey:c,secretKey:u,signInUrl:o.signInUrl||sb,signUpUrl:o.signUpUrl||s_,...o};sN.set("requestData",d);let h=await a6();d.debug&&e.enable();let p=iP(t);e.debug("options",d),e.debug("url",()=>p.toJSON());let f=t.headers.get(rw.Headers.Authorization);f&&f.startsWith("Basic ")&&e.debug("Basic Auth detected");let g=t.headers.get(rw.Headers.ContentSecurityPolicy);g&&e.debug("Content-Security-Policy detected",()=>({value:g}));let m=await h.authenticateRequest(p,ok(p,d));if(e.debug("requestState",()=>({status:m.status,headers:JSON.stringify(Object.fromEntries(m.headers)),reason:m.reason})),m.headers.get(rw.Headers.Location))return new Response(null,{status:307,headers:m.headers});if(m.status===ik.Handshake)throw Error("Clerk: handshake status without redirect");let y=m.toAuth();e.debug("auth",()=>({auth:y,debug:y.debug()}));let b=oS(p),_=oE(p),v=await oT(p,y,b),w=ox(y,b,_);w.protect=v;let k=z.next();try{k=await sU.run(sN,async()=>null==n?void 0:n(w,t,r))||k}catch(e){k=oO(e,p,t,m)}if(d.contentSecurityPolicy){let{headers:t}=function(e,t){var r;let n=[],i=t.strict?function(){let e=new Uint8Array(16);return crypto.getRandomValues(e),btoa(Array.from(e,e=>String.fromCharCode(e)).join(""))}():void 0,s=function(e,t,r,n){let i=Object.entries(a8.DEFAULT_DIRECTIVES).reduce((e,[t,r])=>(e[t]=new Set(r),e),{});if(i["connect-src"].add(t),e&&(i["script-src"].delete("http:"),i["script-src"].delete("https:"),i["script-src"].add("'strict-dynamic'"),n&&i["script-src"].add(`'nonce-${n}'`)),r){let e=new Map;Object.entries(r).forEach(([t,r])=>{let n=Array.isArray(r)?r:[r];a8.DEFAULT_DIRECTIVES[t]?function(e,t,r){if(r.includes("'none'")||r.includes("none")){e[t]=new Set(["'none'"]);return}let n=new Set;e[t].forEach(e=>{n.add(a8.formatValue(e))}),r.forEach(e=>{n.add(a8.formatValue(e))}),e[t]=n}(i,t,n):function(e,t,r){if(r.includes("'none'")||r.includes("none"))return e.set(t,new Set(["'none'"]));let n=new Set;r.forEach(e=>{let t=a8.formatValue(e);n.add(t)}),e.set(t,n)}(e,t,n)}),e.forEach((e,t)=>{i[t]=e})}return Object.entries(i).sort(([e],[t])=>e.localeCompare(t)).map(([e,t])=>{let r=Array.from(t).map(e=>({raw:e,formatted:a8.formatValue(e)}));return`${e} ${r.map(e=>e.formatted).join(" ")}`}).join("; ")}(null!=(r=t.strict)&&r,e,t.directives,i);return t.reportTo&&(s+="; report-to csp-endpoint",n.push([rw.Headers.ReportingEndpoints,`csp-endpoint="${t.reportTo}"`])),t.reportOnly?n.push([rw.Headers.ContentSecurityPolicyReportOnly,s]):n.push([rw.Headers.ContentSecurityPolicy,s]),i&&n.push([rw.Headers.Nonce,i]),{headers:n}}((null!=(a=null==(s=tb(c))?void 0:s.frontendApi)?a:"").replace("$",""),d.contentSecurityPolicy);t.forEach(([e,t])=>{i8(k,e,t)}),e.debug("Clerk generated CSP",()=>({headers:t}))}if(m.headers&&m.headers.forEach((t,r)=>{r===rw.Headers.ContentSecurityPolicy&&e.debug("Content-Security-Policy detected",()=>({value:t})),k.headers.append(r,t)}),i6(k))return e.debug("handlerResult is redirect"),st(p,k,d);d.debug&&a0(k,p,{[rw.Headers.EnableDebug]:"true"});let S=u===(null==l?void 0:l.secretKey)?{publishableKey:null==l?void 0:l.publishableKey,secretKey:null==l?void 0:l.secretKey}:{};return!function(e,t,r,n,i){let s,{reason:a,message:o,status:l,token:c}=r;if(t||(t=z.next()),t.headers.get(i3.Headers.NextRedirect))return;"1"===t.headers.get(i3.Headers.NextResume)&&(t.headers.delete(i3.Headers.NextResume),s=new URL(e.url));let u=t.headers.get(i3.Headers.NextRewrite);if(u){let t=new URL(e.url);if((s=new URL(u)).origin!==t.origin)return}if(s){let r=function(e,t){var r;let n=e=>!e||!Object.values(e).some(e=>void 0!==e);if(n(e)&&n(t))return;if(e.secretKey&&!sp)return void sj.warnOnce("Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys");let i=tE()?sp||(r=()=>aQ.throwMissingSecretKeyError(),sd||r(),sd):sp||sd||a5;return aK.encrypt(JSON.stringify({...t,...e}),i).toString()}(n,i);a0(t,e,{[rw.Headers.AuthStatus]:l,[rw.Headers.AuthToken]:c||"",[rw.Headers.AuthSignature]:c?aF(c,(null==n?void 0:n.secretKey)||sd||i.secretKey||"").toString():"",[rw.Headers.AuthMessage]:o||"",[rw.Headers.AuthReason]:a||"",[rw.Headers.ClerkUrl]:e.clerkUrl.toString(),...r?{[rw.Headers.ClerkRequestData]:r}:{}}),t.headers.set(i3.Headers.NextRewrite,s.href)}}(p,k,m,o,S),k}),s=async(t,r)=>{var n,s;if(ov(t))return ow(t);let a="function"==typeof i?await i(t):i,o=await ot(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value}),l=!(a.publishableKey||sh||(null==o?void 0:o.publishableKey)),c=null!=(s=null==(n=sI(t,rw.Headers.Authorization))?void 0:n.replace("Bearer ",""))?s:"";if(l&&!ih(c)){let e=z.next();return a0(e,t,{[rw.Headers.AuthStatus]:"signed-out"}),e}return e(t,r)},a=async(t,r)=>sS?s(t,r):e(t,r);return t&&r?a(t,r):a})})(),oR={matcher:["/((?!.*..*|_next).*)","/","/(api|trpc)(.*)"]};r(199);let oP={...i},oI=oP.middleware||oP.default,oA="/middleware";if("function"!=typeof oI)throw Object.defineProperty(Error(`The Middleware "${oA}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function oN(e){return e5({...e,page:oA,handler:async(...e)=>{try{return await oI(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await l(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},381:(e,t,r)=>{"use strict";r.d(t,{o:()=>s});var n=r(716);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.l.get(t,r,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return n.l.get(t,a,i)},set(t,r,i,s){if("symbol"==typeof r)return n.l.set(t,r,i,s);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return n.l.set(t,o??r,i,s)},has(t,r){if("symbol"==typeof r)return n.l.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&n.l.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.l.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||n.l.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},412:(e,t,r)=>{"use strict";let n=r(550),{snakeCase:i}=r(74),s={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==s))throw Error("obj must be array of plain objects")}else if(e.constructor!==s)throw Error("obj must be an plain object");return n(e,function(e,r){var n,s,a,o,l;return[(n=t.exclude,s=e,n.some(function(e){return"string"==typeof e?e===s:e.test(s)}))?e:i(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},427:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(620).xl)()},521:e=>{"use strict";e.exports=require("node:async_hooks")},535:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(58).xl)()},550:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,s,a=new WeakMap)=>{if(s={deep:!1,target:{},...s},a.has(e))return a.get(e);a.set(e,s.target);let{target:o}=s;delete s.target;let l=e=>e.map(e=>n(e)?i(e,t,s,a):e);if(Array.isArray(e))return l(e);for(let[c,u]of Object.entries(e)){let d=t(c,u,e);if(d===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=d;"__proto__"!==h&&(s.deep&&f&&n(p)&&(p=Array.isArray(p)?l(p):i(p,t,s,a)),o[h]=p)}return o};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r},552:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let i=r(201),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:s,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},554:(e,t)=>{"use strict";t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||s,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),s=-1===r?o:r;if(t>s){c=e.lastIndexOf(";",t-1)+1;continue}let u=n(e,c,t),d=i(e,t,u),h=e.slice(u,d);if(void 0===a[h]){let r=n(e,t+1,s),o=i(e,s,r),c=l(e.slice(r,o));a[h]=c}c=s+1}while(c<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function s(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},557:(e,t,r)=>{"use strict";r.d(t,{t3:()=>l,I3:()=>d,Ui:()=>c,xI:()=>a,Pk:()=>o});var n=r(815),i=r(16);r(602),r(115),r(535),r(801);let s="function"==typeof n.unstable_postpone;function a(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function o(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function l(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function c(e,t,r){(function(){if(!s)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(u(e,t))}function u(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&h(e.message)}function h(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===h(u("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function p(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},602:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},620:(e,t,r)=>{"use strict";r.d(t,{cg:()=>o,xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}function o(e){return s?s.bind(e):i.bind(e)}},716:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...n]=o(e),{domain:i,expires:s,httponly:a,maxage:l,path:d,samesite:h,secure:p,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var m,y,b={name:t,value:decodeURIComponent(r),domain:i,...s&&{expires:new Date(s)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:d,...h&&{sameSite:c.includes(m=(m=h).toLowerCase())?m:void 0},...p&&{secure:!0},...g&&{priority:u.includes(y=(y=g).toLowerCase())?y:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let l of n(s))i.call(e,l)||l===a||t(e,l,{get:()=>s[l],enumerable:!(o=r(s,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var c=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},725:(e,t,r)=>{"use strict";r.d(t,{Ud:()=>n.stringifyCookie,VO:()=>n.ResponseCookies,tm:()=>n.RequestCookies});var n=r(724)},792:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,s.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,o.h)(t)||(0,a.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(801);let i=Symbol.for("react.postpone");var s=r(199),a=r(557),o=r(16)},801:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>o});let i="HANGING_PROMISE_REJECTION";class s extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let a=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new s(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new s(t)),o=a.get(e);if(o)o.push(i);else{let t=[i];a.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(l),r}}function l(){}},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var o=new i(n,s||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,c,u=this._events[o],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,s),!0;case 6:return u.fn.call(u.context,t,n,i,s,a),!0}for(c=1,l=Array(d-1);c<d;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var h,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),d){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];u[c].fn.apply(u[c].context,l)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var l=0,c=[],u=o.length;l<u;l++)(o[l].fn!==t||i&&!o[l].once||n&&o[l].context!==n)&&c.push(o[l]);c.length?this._events[s]=1===c.length?c[0]:c:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let s=i/2|0,a=n+s;0>=r(e[a],t)?(n=++a,i-=s+1):i=s}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let s=(e,t,r)=>new Promise((s,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void s(e);let o=setTimeout(()=>{if("function"==typeof r){try{s(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(o)},t);n(e.then(s,a),()=>{clearTimeout(o)})});e.exports=s,e.exports.default=s,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),s=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let s=async()=>{this._pendingCount++,this._intervalCount++;try{let s=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await s)}catch(e){i(e)}this._next()};this._queue.enqueue(s,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},815:(e,t,r)=>{"use strict";e.exports=r(35)},818:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>l,K8:()=>u,hm:()=>d});var n=r(725),i=r(716),s=r(535),a=r(115);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new o}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return i.l.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");class u{static wrap(e,t){let r=new n.VO(new Headers);for(let t of e.getAll())r.set(t);let a=[],o=new Set,l=()=>{let e=s.J.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of a){let r=new n.VO(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{l()}};default:return i.l.get(e,t,r)}}});return u}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return h("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return h("cookies().set"),e.set(...r),t};default:return i.l.get(e,r,n)}}});return t}function h(e){if("action"!==(0,a.XN)(e).phase)throw new o}},821:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},830:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(58).xl)()},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},s=t.split(n),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var c=s.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let n=r(201),i=r(552);function s(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),s=r(930),a="context",o=new n.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(a,e,s.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(a,s.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),s=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:s.DiagLogLevel.INFO})=>{var n,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,a.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!=(o=r.logLevel)?o:s.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),s=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(a,e,s.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(a)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(a,s.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),s=r(194),a=r(277),o=r(369),l=r(930),c="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=s.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=s.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(c)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),s=r(139),a=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=s.wrapSpanContext,this.isSpanContextValid=s.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function s(e){return e.getValue(i)||void 0}t.getBaggage=s,t.getActiveBaggage=function(){return s(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),s=r(830),a=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:s.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return s("debug",this._namespace,e)}error(...e){return s("error",this._namespace,e)}info(...e){return s("info",this._namespace,e)}warn(...e){return s("warn",this._namespace,e)}verbose(...e){return s("verbose",this._namespace,e)}}function s(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),s=r(130),a=i.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var s;let a=l[o]=null!=(s=l[o])?s:{version:i.VERSION};if(!n&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=l[o])?void 0:t.version;if(n&&(0,s.isCompatible)(n))return null==(r=l[o])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function s(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return a(e);let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease||s.major!==o.major)return a(e);if(0===s.major)return s.minor===o.minor&&s.patch<=o.patch?(t.add(e),!0):a(e);return s.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=s,t.isCompatible=s(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class s extends n{add(e,t){}}t.NoopUpDownCounterMetric=s;class a extends n{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new s,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),s=r(403),a=r(139),o=n.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){var n;if(null==t?void 0:t.root)return new s.NonRecordingSpan;let l=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=l)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,a.isSpanContextValid)(l)?new s.NonRecordingSpan(l):new s.NonRecordingSpan}startActiveSpan(e,t,r,n){let s,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(s=t,l=r):(s=t,a=r,l=n);let c=null!=a?a:o.active(),u=this.startSpan(e,s,c),d=(0,i.setSpan)(c,u);return o.with(d,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class s{getTracer(e,t,r){var i;return null!=(i=this.getDelegateTracer(e,t,r))?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=s},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),s=r(491),a=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(s.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let s=r.slice(0,i),a=r.slice(i+1,t.length);(0,n.validateKey)(s)&&(0,n.validateValue)(a)&&e.set(s,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,s=RegExp(`^(?:${n}|${i})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return s.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),s=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return s.test(e)&&e!==n.INVALID_TRACEID}function l(e){return a.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var s=n[e]={exports:{}},a=!0;try{t[e].call(s.exports,s,s.exports,i),a=!1}finally{a&&delete n[e]}return s.exports}i.ab="//";var s={};(()=>{Object.defineProperty(s,"__esModule",{value:!0}),s.trace=s.propagation=s.metrics=s.diag=s.context=s.INVALID_SPAN_CONTEXT=s.INVALID_TRACEID=s.INVALID_SPANID=s.isValidSpanId=s.isValidTraceId=s.isSpanContextValid=s.createTraceState=s.TraceFlags=s.SpanStatusCode=s.SpanKind=s.SamplingDecision=s.ProxyTracerProvider=s.ProxyTracer=s.defaultTextMapSetter=s.defaultTextMapGetter=s.ValueType=s.createNoopMeter=s.DiagLogLevel=s.DiagConsoleLogger=s.ROOT_CONTEXT=s.createContextKey=s.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(s,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(s,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(s,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(s,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(s,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var a=i(102);Object.defineProperty(s,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=i(901);Object.defineProperty(s,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=i(194);Object.defineProperty(s,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(s,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=i(125);Object.defineProperty(s,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=i(846);Object.defineProperty(s,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(s,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var h=i(357);Object.defineProperty(s,"SpanKind",{enumerable:!0,get:function(){return h.SpanKind}});var p=i(847);Object.defineProperty(s,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=i(475);Object.defineProperty(s,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=i(98);Object.defineProperty(s,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=i(139);Object.defineProperty(s,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(s,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(s,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var y=i(476);Object.defineProperty(s,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(s,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(s,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let b=i(67);Object.defineProperty(s,"context",{enumerable:!0,get:function(){return b.context}});let _=i(506);Object.defineProperty(s,"diag",{enumerable:!0,get:function(){return _.diag}});let v=i(886);Object.defineProperty(s,"metrics",{enumerable:!0,get:function(){return v.metrics}});let w=i(939);Object.defineProperty(s,"propagation",{enumerable:!0,get:function(){return w.propagation}});let k=i(845);Object.defineProperty(s,"trace",{enumerable:!0,get:function(){return k.trace}}),s.default={context:b.context,diag:_.diag,metrics:v.metrics,propagation:w.propagation,trace:k.trace}})(),e.exports=s})()}},e=>{var t=e(e.s=360);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map