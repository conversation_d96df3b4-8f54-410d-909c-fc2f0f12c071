{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!.*..*|_next).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!.*..*|_next).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Tr_ORCYlcgV4T52u73sYn", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "69HoocCtrlXiWYdhsJi50/YxwEM501ngBzAVuXH7TLQ=", "__NEXT_PREVIEW_MODE_ID": "f64b623bc0b6a7af838d685ae0338400", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ec1017078e2e025822ef0a30829890d0eeddafa829d0551bb93eda7c6dddd9c6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6601960a1d8ae775133d393b9a01e3cf8ef01b7cd20f5a9c024af464678dc11e"}}}, "functions": {}, "sortedMiddleware": ["/"]}