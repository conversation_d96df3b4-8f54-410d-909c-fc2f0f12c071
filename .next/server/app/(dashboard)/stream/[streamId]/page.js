(()=>{var e={};e.id=843,e.ids=[843],e.modules={540:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx","default")},886:(e,s,t)=>{"use strict";t.d(s,{ConvexClientProvider:()=>d});var a=t(60687),r=t(77861),l=t(39541),i=t(71946),n=t(41330);let c=new r.eH("https://wonderful-kangaroo-238.convex.cloud");function d({children:e}){return(0,a.jsx)(i.lJ,{publishableKey:"pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ",children:(0,a.jsx)(l.q,{client:c,useAuth:n.d,children:e})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14394:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20651:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["(dashboard)",{children:["stream",{children:["[streamId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,540)),"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx"]}]},{}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"/home/<USER>/streamyard-clonez/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/stream/[streamId]/page",pathname:"/stream/[streamId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25056:(e,s,t)=>{"use strict";t.d(s,{y:()=>r});var a=t(6475);let r=(0,a.createServerReference)("7f2049dbe655e5196d542e38101773d6697a94154a",a.callServer,void 0,a.findSourceMapURL,"invalidateCacheAction")},25208:(e,s,t)=>{"use strict";t.d(s,{ConvexClientProvider:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/convex-provider.tsx","ConvexClientProvider")},27910:e=>{"use strict";e.exports=require("stream")},28369:(e,s,t)=>{Promise.resolve().then(t.bind(t,886)),Promise.resolve().then(t.bind(t,89675)),Promise.resolve().then(t.bind(t,92892)),Promise.resolve().then(t.bind(t,94593))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30884:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},33873:e=>{"use strict";e.exports=require("path")},38097:(e,s,t)=>{Promise.resolve().then(t.bind(t,25208)),Promise.resolve().then(t.bind(t,99733)),Promise.resolve().then(t.bind(t,83066)),Promise.resolve().then(t.bind(t,80363))},49444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},58014:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x,metadata:()=>o});var a=t(37413),r=t(59258),l=t.n(r);t(82704);var i=t(25208),n=t(83066),c=t(99733),d=t(80363);let o={title:"Meet Clone - Video meetings for everyone",description:"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings."};function x({children:e}){return(0,a.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsx)("body",{className:l().className,suppressHydrationWarning:!0,children:(0,a.jsx)(i.ConvexClientProvider,{children:(0,a.jsx)(c.LivepeerProvider,{children:(0,a.jsxs)(n.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!1,storageKey:"streamyard-clone-theme",children:[(0,a.jsx)(d.Toaster,{theme:"light",position:"bottom-center"}),e]})})})})})}},58071:(e,s,t)=>{Promise.resolve().then(t.bind(t,540))},58947:(e,s,t)=>{"use strict";t.r(s),t.d(s,{"7f126a7a969b1105c221f5710d89250ac20d205bab":()=>a.at,"7f2049dbe655e5196d542e38101773d6697a94154a":()=>r.y,"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a":()=>a.ai,"7fc968e88d4d7d85319b0c692c8eba3563d91942c2":()=>a.ot});var a=t(74006),r=t(3937)},62799:(e,s,t)=>{Promise.resolve().then(t.bind(t,70677))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68999:(e,s,t)=>{"use strict";t.d(s,{F:()=>a});let a=t(95778).dp},70677:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eO});var a=t(60687),r=t(16189),l=t(56499),i=t(43210),n=t(77861),c=t(68999);function d({streamId:e,currentUserId:s}){return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Moderation Panel"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Moderation features coming soon..."})]})}var o=t(62688);let x=(0,o.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var h=t(41312);let m=(0,o.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),u=(0,o.A)("tv",[["path",{d:"m17 2-5 5-5-5",key:"16satq"}],["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",key:"1e6viu"}]]),p=(0,o.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),g=(0,o.A)("film",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M7 3v18",key:"bbkbws"}],["path",{d:"M3 7.5h4",key:"zfgn84"}],["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 16.5h4",key:"1230mu"}],["path",{d:"M17 3v18",key:"in4fa5"}],["path",{d:"M17 7.5h4",key:"myr1c1"}],["path",{d:"M17 16.5h4",key:"go4c1d"}]]),b=(0,o.A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),y=(0,o.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),j=(0,o.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),v=(0,o.A)("move",[["path",{d:"M12 2v20",key:"t6zp3m"}],["path",{d:"m15 19-3 3-3-3",key:"11eu04"}],["path",{d:"m19 9 3 3-3 3",key:"1mg7y2"}],["path",{d:"M2 12h20",key:"9i4pu4"}],["path",{d:"m5 9-3 3 3 3",key:"j64kie"}],["path",{d:"m9 5 3-3 3 3",key:"l8vdw6"}]]),f=(0,o.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),N=(0,o.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),w=(0,o.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),k=[{id:"single",name:"Single",type:"preset",icon:(0,a.jsx)(x,{className:"w-5 h-5"}),description:"Highlights one participant",config:{participants:1,arrangement:"single",showScreen:!1}},{id:"group",name:"Group",type:"preset",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"}),description:"Grid layout for multiple participants",config:{participants:4,arrangement:"grid",showScreen:!1}},{id:"spotlight",name:"Spotlight",type:"preset",icon:(0,a.jsx)(m,{className:"w-5 h-5"}),description:"Highlights main speaker with others below",config:{participants:4,arrangement:"spotlight",showScreen:!1}},{id:"news",name:"News",type:"preset",icon:(0,a.jsx)(u,{className:"w-5 h-5"}),description:"Side-by-side presenter and screen",config:{participants:1,arrangement:"side-by-side",showScreen:!0,screenPosition:"side"}},{id:"screen",name:"Screen",type:"preset",icon:(0,a.jsx)(p,{className:"w-5 h-5"}),description:"Full screen share with small presenter",config:{participants:1,arrangement:"pip",showScreen:!0,screenPosition:"main"}},{id:"cinema",name:"Cinema",type:"preset",icon:(0,a.jsx)(g,{className:"w-5 h-5"}),description:"Full screen presentation only",config:{participants:0,arrangement:"custom",showScreen:!0,screenPosition:"main"}}];function C({selectedLayout:e,onLayoutChange:s,onCreateCustomLayout:t,customLayouts:r=[]}){let[l,n]=(0,i.useState)(!1),[c,d]=(0,i.useState)(null),o=[...k,...r],x=e=>{"custom"===e.type&&(d(e),n(!0))},h=e=>{console.log("Delete layout:",e)};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2",children:[o.map(t=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex flex-col items-center p-3 rounded-lg border-2 transition-all min-w-[80px] ${e===t.id?"border-blue-500 bg-blue-500/20 text-blue-400":"border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500"}`,title:t.description,children:[(0,a.jsx)("div",{className:"mb-2",children:t.icon}),(0,a.jsx)("span",{className:"text-xs font-medium",children:t.name})]}),"custom"===t.type&&(0,a.jsx)("div",{className:"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>x(t),className:"p-1 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors",children:(0,a.jsx)(b,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:()=>h(t.id),className:"p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors",children:(0,a.jsx)(y,{className:"w-3 h-3"})})]})})]},t.id)),(0,a.jsxs)("button",{onClick:t,className:"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 text-gray-400 hover:border-gray-500 hover:text-gray-300 transition-colors min-w-[80px]",children:[(0,a.jsx)(j,{className:"w-5 h-5 mb-2"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Custom"})]})]}),e&&(0,a.jsx)("div",{className:"bg-gray-700 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:o.find(s=>s.id===e)?.name}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:o.find(s=>s.id===e)?.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(v,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(f,{className:"w-4 h-4"})})]})]})}),l&&c&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Edit Layout"}),(0,a.jsx)("button",{onClick:()=>n(!1),className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(N,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Layout Name"}),(0,a.jsx)("input",{type:"text",value:c.name,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,a.jsx)("input",{type:"text",value:c.description,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>n(!1),className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{n(!1),d(null)},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(w,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save"})]})]})]})]})})]})}let M=(0,o.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),A=(0,o.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);function S({isOpen:e,onClose:s,onSave:t,existingLayout:r}){let[l,n]=(0,i.useState)([]),[c,d]=(0,i.useState)(null),[o,h]=(0,i.useState)(null),[m,u]=(0,i.useState)(""),[g,b]=(0,i.useState)(!0),v=(0,i.useRef)(null),f=e=>{let s={id:`element-${Date.now()}`,type:e,x:50,y:50,width:"dynamic-grid"===e?300:150,height:"dynamic-grid"===e?200:100,zIndex:l.length,shape:"rectangle",fit:"cover"};n([...l,s]),d(s.id)},k=(e,s)=>{n(l.map(t=>t.id===e?{...t,...s}:t))},C=e=>{n(l.filter(s=>s.id!==e)),c===e&&d(null)},S=e=>{let s=l.find(s=>s.id===e);s&&n([...l,{...s,id:`element-${Date.now()}`,x:s.x+20,y:s.y+20}])},P=(e,s)=>{e.preventDefault(),d(s),h(s)};if(!e)return null;let z=l.find(e=>e.id===c);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/80 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Custom Layout Builder"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("input",{type:"text",placeholder:"Layout name...",value:m,onChange:e=>u(e.target.value),className:"px-3 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm focus:border-blue-500 focus:outline-none"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>b(!g),className:`px-3 py-1 rounded text-sm transition-colors ${g?"bg-purple-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:g?"Preview":"Edit"}),(0,a.jsxs)("button",{onClick:()=>{if(!m.trim())return void alert("Please enter a layout name");t({id:`custom-${Date.now()}`,name:m,type:"custom",elements:l,createdAt:Date.now()}),s()},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(w,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save"})]}),(0,a.jsx)("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(N,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[g&&(0,a.jsxs)("div",{className:"w-64 bg-gray-800 border-r border-gray-700 p-4 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-white mb-3",children:"Add Elements"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("button",{onClick:()=>f("camera"),className:"w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(x,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Camera Slot"})]}),(0,a.jsxs)("button",{onClick:()=>f("dynamic-grid"),className:"w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(M,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Dynamic Grid"})]}),(0,a.jsxs)("button",{onClick:()=>f("screen"),className:"w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(p,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Media Slot"})]})]})]}),z&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-white mb-3",children:"Properties"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Shape"}),(0,a.jsxs)("select",{value:z.shape,onChange:e=>k(z.id,{shape:e.target.value}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:"rectangle",children:"Rectangle"}),(0,a.jsx)("option",{value:"rounded",children:"Rounded"}),(0,a.jsx)("option",{value:"circle",children:"Circle"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Fit"}),(0,a.jsxs)("select",{value:z.fit,onChange:e=>k(z.id,{fit:e.target.value}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:"cover",children:"Fill"}),(0,a.jsx)("option",{value:"contain",children:"Fit"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>S(z.id),className:"flex-1 flex items-center justify-center space-x-1 p-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Copy"})]}),(0,a.jsxs)("button",{onClick:()=>C(z.id),className:"flex-1 flex items-center justify-center space-x-1 p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:[(0,a.jsx)(y,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Delete"})]})]})]})]})]}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsxs)("div",{ref:v,className:"w-full h-full bg-black rounded-lg relative overflow-hidden border-2 border-dashed border-gray-600",onMouseMove:e=>{if(!o||!v.current)return;let s=v.current.getBoundingClientRect();k(o,{x:e.clientX-s.left,y:e.clientY-s.top})},onMouseUp:()=>{h(null)},style:{aspectRatio:"16/9"},children:[l.map(e=>(0,a.jsxs)("div",{className:`absolute border-2 transition-all cursor-move ${c===e.id?"border-blue-500 bg-blue-500/20":"border-gray-500 bg-gray-500/20 hover:border-gray-400"} ${"circle"===e.shape?"rounded-full":"rounded"===e.shape?"rounded-lg":""}`,style:{left:e.x,top:e.y,width:e.width,height:e.height,zIndex:e.zIndex},onMouseDown:s=>P(s,e.id),children:[(0,a.jsxs)("div",{className:"flex items-center justify-center h-full text-white text-sm",children:["camera"===e.type&&(0,a.jsx)(x,{className:"w-6 h-6"}),"screen"===e.type&&(0,a.jsx)(p,{className:"w-6 h-6"}),"dynamic-grid"===e.type&&(0,a.jsx)(M,{className:"w-6 h-6"})]}),c===e.id&&g&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-se-resize"}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-ne-resize"}),(0,a.jsx)("div",{className:"absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-nw-resize"}),(0,a.jsx)("div",{className:"absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-sw-resize"})]})]},e.id)),0===l.length&&(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(j,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Add elements to create your layout"})]})})]})})]})]})})}let P=(0,o.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),z=(0,o.A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]]),L=(0,o.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]),q=(0,o.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),F=(0,o.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),T=(0,o.A)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]]),I=(0,o.A)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]]),R=(0,o.A)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]]),$=(0,o.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),E=(0,o.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);function H({isOpen:e,onClose:s,elements:t,onUpdateElements:r}){let[l,n]=(0,i.useState)(null),[c,d]=(0,i.useState)("logos"),o=e=>{let s={id:`element-${Date.now()}`,type:e,content:"text"===e?"Sample Text":"",x:50,y:50,width:"text"===e?200:100,height:"text"===e?40:100,visible:!0,zIndex:t.length,style:{fontSize:24,fontWeight:"normal",color:"#ffffff",backgroundColor:"banner"===e?"#000000":"transparent",opacity:1,borderRadius:0,textAlign:"left",fontFamily:"Arial"}};r([...t,s]),n(s.id)},x=(e,s)=>{r(t.map(t=>t.id===e?{...t,...s}:t))},h=(e,s)=>{let a=t.find(s=>s.id===e);a&&x(e,{style:{...a.style,...s}})},m=e=>{r(t.filter(s=>s.id!==e)),l===e&&n(null)},u=e=>{let s=t.find(s=>s.id===e);s&&x(e,{visible:!s.visible})};if(!e)return null;let p=t.find(e=>e.id===l);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Branding & Graphics"}),(0,a.jsx)("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(N,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsxs)("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[(0,a.jsx)("div",{className:"flex border-b border-gray-700",children:[{id:"logos",label:"Logos",icon:P},{id:"text",label:"Text",icon:z},{id:"banners",label:"Banners",icon:L},{id:"backgrounds",label:"Backgrounds",icon:q}].map(({id:e,label:s,icon:t})=>(0,a.jsxs)("button",{onClick:()=>d(e),className:`flex-1 flex items-center justify-center space-x-2 p-3 transition-colors ${c===e?"bg-blue-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,children:[(0,a.jsx)(t,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:s})]},e))}),(0,a.jsxs)("div",{className:"flex-1 p-4 overflow-y-auto",children:["logos"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>o("logo"),className:"w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-gray-600 text-gray-400 rounded-lg hover:border-gray-500 hover:text-gray-300 transition-colors",children:[(0,a.jsx)(F,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Upload Logo"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Recommended: PNG with transparent background, 300x300px max"})]}),"text"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>o("text"),className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Text"})]}),p?.type==="text"&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Text Content"}),(0,a.jsx)("input",{type:"text",value:p.content,onChange:e=>x(p.id,{content:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Font Size"}),(0,a.jsx)("input",{type:"number",value:p.style.fontSize,onChange:e=>h(p.id,{fontSize:parseInt(e.target.value)}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Color"}),(0,a.jsx)("input",{type:"color",value:p.style.color,onChange:e=>h(p.id,{color:e.target.value}),className:"w-full h-8 bg-gray-700 border border-gray-600 rounded"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Font Weight"}),(0,a.jsxs)("select",{value:p.style.fontWeight,onChange:e=>h(p.id,{fontWeight:e.target.value}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:"normal",children:"Normal"}),(0,a.jsx)("option",{value:"bold",children:"Bold"}),(0,a.jsx)("option",{value:"lighter",children:"Light"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Alignment"}),(0,a.jsx)("div",{className:"flex space-x-1",children:[{value:"left",icon:T},{value:"center",icon:I},{value:"right",icon:R}].map(({value:e,icon:s})=>(0,a.jsx)("button",{onClick:()=>h(p.id,{textAlign:e}),className:`flex-1 p-2 rounded transition-colors ${p.style.textAlign===e?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:(0,a.jsx)(s,{className:"w-4 h-4 mx-auto"})},e))})]})]})]}),"banners"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>o("banner"),className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Banner"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Create scrolling text banners and lower thirds"})]}),"backgrounds"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>o("background"),className:"w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-gray-600 text-gray-400 rounded-lg hover:border-gray-500 hover:text-gray-300 transition-colors",children:[(0,a.jsx)(F,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Upload Background"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-blue-500 to-purple-600 rounded cursor-pointer"}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-green-500 to-blue-500 rounded cursor-pointer"}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-purple-500 to-pink-500 rounded cursor-pointer"}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-orange-500 to-red-500 rounded cursor-pointer"})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-700 p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-white mb-2",children:"Elements"}),(0,a.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:t.map(e=>(0,a.jsxs)("div",{className:`flex items-center justify-between p-2 rounded transition-colors cursor-pointer ${l===e.id?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,onClick:()=>n(e.id),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs",children:e.type}),(0,a.jsx)("span",{className:"text-xs opacity-75 truncate max-w-20",children:e.content||"Untitled"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{onClick:s=>{s.stopPropagation(),u(e.id)},className:"p-1 hover:bg-gray-600 rounded",children:e.visible?(0,a.jsx)($,{className:"w-3 h-3"}):(0,a.jsx)(E,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:s=>{s.stopPropagation(),m(e.id)},className:"p-1 hover:bg-red-600 rounded",children:(0,a.jsx)(y,{className:"w-3 h-3"})})]})]},e.id))})]})]}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"w-full h-full bg-black rounded-lg relative overflow-hidden",children:(0,a.jsx)("div",{className:"absolute inset-4 border border-dashed border-gray-600 rounded",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(L,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Preview your branding elements"})]})})})})})]})]})})}let V=(0,o.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),D=(0,o.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),U=(0,o.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),O=(0,o.A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),B=(0,o.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var G=t(82719),_=t(73259),K=t(46915),W=t(2943);let Y=(0,o.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),X=(0,o.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function Z({isOpen:e,onClose:s,guests:t,onUpdateGuests:r,streamId:l}){let[n,c]=(0,i.useState)("invite"),[d,o]=(0,i.useState)(""),[m,u]=(0,i.useState)("guest"),[p,g]=(0,i.useState)(""),[b,j]=(0,i.useState)([]),v=(e,s)=>{r(t.map(t=>t.id===e?{...t,status:s}:t))},f=(e,s)=>{r(t.map(t=>t.id===e?{...t,...s}:t))},N=e=>{r(t.filter(s=>s.id!==e))},w=e=>{v(e,"on-stage")},k=e=>{v(e,"backstage")},C=()=>`${window.location.origin}/join/${l}`;if(!e)return null;let M=t.filter(e=>"pending"===e.status),S=t.filter(e=>"backstage"===e.status),P=t.filter(e=>"on-stage"===e.status);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Guest Management"}),(0,a.jsx)("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(V,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"flex border-b border-gray-700",children:[{id:"invite",label:"Invite Guests",icon:D},{id:"manage",label:"Manage Guests",icon:h.A},{id:"backstage",label:"Backstage",icon:$}].map(({id:e,label:s,icon:t})=>(0,a.jsxs)("button",{onClick:()=>c(e),className:`flex items-center space-x-2 px-6 py-3 transition-colors ${n===e?"bg-blue-600 text-white border-b-2 border-blue-400":"text-gray-400 hover:text-white hover:bg-gray-800"}`,children:[(0,a.jsx)(t,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:s}),"backstage"===e&&S.length>0&&(0,a.jsx)("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:S.length})]},e))}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:["invite"===n&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-3",children:"Share Invite Link"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:C(),readOnly:!0,className:"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"}),(0,a.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(C())},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Copy"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"Anyone with this link can request to join your stream"})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-3",children:"Send Email Invitation"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Role"}),(0,a.jsxs)("select",{value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none",children:[(0,a.jsx)("option",{value:"guest",children:"Guest"}),(0,a.jsx)("option",{value:"moderator",children:"Moderator"}),(0,a.jsx)("option",{value:"co-host",children:"Co-Host"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Personal Message (Optional)"}),(0,a.jsx)("textarea",{value:p,onChange:e=>g(e.target.value),placeholder:"Add a personal message to your invitation...",rows:3,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("button",{onClick:()=>{d.trim()&&(r([...t,{id:`guest-${Date.now()}`,name:d.split("@")[0],email:d,status:"invited",role:m,isAudioMuted:!1,isVideoOff:!1,isVisible:!0}]),o(""),g(""),console.log("Sending invitation to:",d))},disabled:!d.trim(),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)(U,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Send Invitation"})]})]})]})]}),"manage"===n&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-white mb-3 flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"On Stage"}),(0,a.jsx)("span",{className:"bg-green-500 text-white text-xs px-2 py-1 rounded-full",children:P.length})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[P.map(e=>(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["co-host"===e.role&&(0,a.jsx)(O,{className:"w-4 h-4 text-yellow-500"}),"moderator"===e.role&&(0,a.jsx)(B,{className:"w-4 h-4 text-blue-500"}),"guest"===e.role&&(0,a.jsx)(x,{className:"w-4 h-4 text-gray-400"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>f(e.id,{isAudioMuted:!e.isAudioMuted}),className:`p-2 rounded transition-colors ${e.isAudioMuted?"bg-red-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:e.isAudioMuted?(0,a.jsx)(G.A,{className:"w-4 h-4"}):(0,a.jsx)(_.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>f(e.id,{isVideoOff:!e.isVideoOff}),className:`p-2 rounded transition-colors ${e.isVideoOff?"bg-red-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:e.isVideoOff?(0,a.jsx)(K.A,{className:"w-4 h-4"}):(0,a.jsx)(W.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>k(e.id),className:"px-3 py-1 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors text-sm",children:"Move to Backstage"}),(0,a.jsx)("button",{onClick:()=>N(e.id),className:"p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:(0,a.jsx)(y,{className:"w-4 h-4"})})]})]})},e.id)),0===P.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No guests on stage"})]})]}),M.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-white mb-3 flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Pending Approval"}),(0,a.jsx)("span",{className:"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full",children:M.length})]}),(0,a.jsx)("div",{className:"space-y-2",children:M.map(e=>(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>v(e.id,"backstage"),className:"flex items-center space-x-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors",children:[(0,a.jsx)(Y,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Approve"})]}),(0,a.jsxs)("button",{onClick:()=>N(e.id),className:"flex items-center space-x-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:[(0,a.jsx)(V,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Deny"})]})]})]})},e.id))})]})]}),"backstage"===n&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white",children:"Backstage Area"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Guests can prepare here before going live"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[S.map(e=>(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center",children:(0,a.jsx)(x,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Ready to go live"})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("button",{onClick:()=>w(e.id),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(X,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Bring to Stage"})]})})]})},e.id)),0===S.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No guests in backstage"})]})]})]})]})})}let J=(0,o.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),Q=(0,o.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),ee=(0,o.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var es=t(58887);let et=(0,o.A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),ea=(0,o.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),er=(0,o.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),el={youtube:J,facebook:Q,twitch:u,instagram:ee,twitter:es.A,linkedin:et,custom:ea},ei={youtube:"text-red-600 bg-red-100",facebook:"text-blue-600 bg-blue-100",twitch:"text-purple-600 bg-purple-100",instagram:"text-pink-600 bg-pink-100",twitter:"text-blue-400 bg-blue-50",linkedin:"text-blue-700 bg-blue-100",custom:"text-gray-600 bg-gray-100"};function en({isOpen:e,onClose:s,destinations:t,onUpdateDestinations:r}){let[l,n]=(0,i.useState)(!1),[c,d]=(0,i.useState)("youtube"),[o,x]=(0,i.useState)(""),[h,m]=(0,i.useState)(""),[u,p]=(0,i.useState)(""),[g,b]=(0,i.useState)(!1),v=e=>({youtube:"rtmp://a.rtmp.youtube.com/live2",facebook:"rtmps://live-api-s.facebook.com:443/rtmp",twitch:"rtmp://live.twitch.tv/live",instagram:"rtmps://live-upload.instagram.com:443/rtmp",twitter:"rtmp://live.twitter.com/live",linkedin:"rtmp://live.linkedin.com/live"})[e]||"",f=e=>{r(t.map(s=>s.id===e?{...s,isEnabled:!s.isEnabled}:s))},N=e=>{r(t.filter(s=>s.id!==e))},w=e=>{r(t.map(s=>s.id===e?{...s,status:"connected"}:s))};return e?(0,a.jsxs)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:[(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Stream Destinations"}),(0,a.jsx)("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(V,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white",children:"Connected Platforms"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Manage where your stream will be broadcast"})]}),(0,a.jsxs)("button",{onClick:()=>n(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Destination"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.map(e=>{let s=el[e.platform],t=ei[e.platform];return(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${t}`,children:(0,a.jsx)(s,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-white",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${"connected"===e.status?"bg-green-500":"streaming"===e.status?"bg-red-500 animate-pulse":"error"===e.status?"bg-red-500":"bg-gray-500"}`}),(0,a.jsx)("span",{className:"text-xs text-gray-400 capitalize",children:e.status}),"streaming"===e.status&&void 0!==e.viewerCount&&(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",e.viewerCount," viewers"]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>f(e.id),className:`p-2 rounded transition-colors ${e.isEnabled?"bg-green-600 text-white":"bg-gray-600 text-gray-300"}`,children:e.isEnabled?(0,a.jsx)(Y,{className:"w-4 h-4"}):(0,a.jsx)(V,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>w(e.id),className:"p-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:(0,a.jsx)(er,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>N(e.id),className:"p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:(0,a.jsx)(y,{className:"w-4 h-4"})})]})]}),e.streamKey&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-gray-700 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Stream Key"}),(0,a.jsx)("button",{onClick:()=>b(!g),className:"text-gray-400 hover:text-white",children:g?(0,a.jsx)(E,{className:"w-3 h-3"}):(0,a.jsx)($,{className:"w-3 h-3"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("code",{className:"text-xs text-gray-300 font-mono flex-1 truncate",children:g?e.streamKey:"••••••••••••••••"}),(0,a.jsx)("button",{onClick:()=>navigator.clipboard.writeText(e.streamKey||""),className:"p-1 text-gray-400 hover:text-white",children:(0,a.jsx)(A,{className:"w-3 h-3"})})]})]})]},e.id)})}),0===t.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(ea,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No destinations configured"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Add streaming destinations to broadcast your content"}),(0,a.jsxs)("button",{onClick:()=>n(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add Your First Destination"})]})]})]})]}),l&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-60",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg w-full max-w-md p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Add Stream Destination"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Platform"}),(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white",children:[(0,a.jsx)("option",{value:"youtube",children:"YouTube"}),(0,a.jsx)("option",{value:"facebook",children:"Facebook"}),(0,a.jsx)("option",{value:"twitch",children:"Twitch"}),(0,a.jsx)("option",{value:"instagram",children:"Instagram"}),(0,a.jsx)("option",{value:"twitter",children:"Twitter"}),(0,a.jsx)("option",{value:"linkedin",children:"LinkedIn"}),(0,a.jsx)("option",{value:"custom",children:"Custom RTMP"})]})]}),"custom"===c&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Name"}),(0,a.jsx)("input",{type:"text",value:u,onChange:e=>p(e.target.value),placeholder:"Custom destination name",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Stream Key"}),(0,a.jsx)("input",{type:"password",value:o,onChange:e=>x(e.target.value),placeholder:"Enter your stream key",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"})]}),"custom"===c&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"RTMP URL"}),(0,a.jsx)("input",{type:"text",value:h,onChange:e=>m(e.target.value),placeholder:"rtmp://your-server.com/live",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)("button",{onClick:()=>n(!1),className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{r([...t,{id:`dest-${Date.now()}`,platform:c,name:"custom"===c?u:c.charAt(0).toUpperCase()+c.slice(1),status:"disconnected",streamKey:o,rtmpUrl:"custom"===c?h:v(c),isEnabled:!0}]),n(!1),x(""),m(""),p("")},disabled:!o||"custom"===c&&(!u||!h),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Add Destination"})]})]})]})})]}):null}let ec=(0,o.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ed=(0,o.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),eo=(0,o.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ex=(0,o.A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),eh=(0,o.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),em=(0,o.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),eu=(0,o.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),ep=(0,o.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),eg=(0,o.A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);function eb({isOpen:e,onClose:s,streamId:t,isLive:r}){let[l,n]=(0,i.useState)({status:"offline",bitrate:0,fps:0,resolution:"1920x1080",latency:0,droppedFrames:0,bandwidth:0,cpuUsage:0,memoryUsage:0}),[c,d]=(0,i.useState)({current:0,peak:0,total:0,trend:"stable",chatMessages:0,engagement:0}),[o,x]=(0,i.useState)(5);if(!e)return null;let h=(e=>{switch(e){case"excellent":case"good":return Y;case"fair":return ec;case"poor":return V;default:return ed}})(l.status);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-6xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Stream Monitor"}),(0,a.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-1 rounded-full ${(e=>{switch(e){case"excellent":return"text-green-500 bg-green-100";case"good":return"text-blue-500 bg-blue-100";case"fair":return"text-yellow-500 bg-yellow-100";case"poor":return"text-orange-500 bg-orange-100";default:return"text-gray-500 bg-gray-100"}})(l.status)}`,children:[(0,a.jsx)(h,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:l.status})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:o,onChange:e=>x(Number(e.target.value)),className:"px-3 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:1,children:"1s"}),(0,a.jsx)("option",{value:5,children:"5s"}),(0,a.jsx)("option",{value:10,children:"10s"}),(0,a.jsx)("option",{value:30,children:"30s"})]}),(0,a.jsx)("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(V,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Stream Health"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Bitrate"}),(0,a.jsx)(eo,{className:"w-4 h-4 text-blue-400"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-white",children:[l.bitrate.toFixed(0)," ",(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"kbps"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"FPS"}),(0,a.jsx)(W.A,{className:"w-4 h-4 text-green-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:l.fps})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Latency"}),(0,a.jsx)(ex,{className:"w-4 h-4 text-yellow-400"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-white",children:[l.latency.toFixed(0)," ",(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"ms"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Dropped Frames"}),(0,a.jsx)(ec,{className:"w-4 h-4 text-red-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:l.droppedFrames})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"System Resources"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"CPU Usage"}),(0,a.jsxs)("span",{className:"text-white",children:[l.cpuUsage.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${l.cpuUsage}%`}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Memory Usage"}),(0,a.jsxs)("span",{className:"text-white",children:[l.memoryUsage.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:`${l.memoryUsage}%`}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Bandwidth"}),(0,a.jsxs)("span",{className:"text-white",children:[(l.bandwidth/1e3).toFixed(1)," Mbps"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-purple-500 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(100,l.bandwidth/5e3*100)}%`}})})]})]})]})]})}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Viewer Analytics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Current Viewers"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)($,{className:"w-4 h-4 text-blue-400"}),"up"===c.trend&&(0,a.jsx)(eh,{className:"w-3 h-3 text-green-400"}),"down"===c.trend&&(0,a.jsx)(em,{className:"w-3 h-3 text-red-400"})]})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:c.current})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Peak Viewers"}),(0,a.jsx)(eu,{className:"w-4 h-4 text-green-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:c.peak})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Total Views"}),(0,a.jsx)(ep,{className:"w-4 h-4 text-purple-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:c.total})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Chat Messages"}),(0,a.jsx)(eg,{className:"w-4 h-4 text-yellow-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:c.chatMessages})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"Engagement"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Engagement Rate"}),(0,a.jsxs)("span",{className:"text-white",children:[c.engagement.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300",style:{width:`${c.engagement}%`}})}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-2",children:"Based on chat activity, viewer retention, and interaction"})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"Stream Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Resolution"}),(0,a.jsx)("span",{className:"text-white",children:l.resolution})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Duration"}),(0,a.jsx)("span",{className:"text-white",children:r?"Live":"Offline"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Status"}),(0,a.jsx)("span",{className:`capitalize ${"excellent"===l.status?"text-green-400":"good"===l.status?"text-blue-400":"fair"===l.status?"text-yellow-400":"poor"===l.status?"text-orange-400":"text-gray-400"}`,children:l.status})]})]})]})]})})]})})]})})}let ey=(0,o.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),ej=(0,o.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ev=(0,o.A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var ef=t(84027);let eN=(0,o.A)("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]),ew=(0,o.A)("ban",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]]),ek=(0,o.A)("smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);function eC({streamId:e,currentUserId:s,isModerator:t,isVisible:r,onToggleVisibility:l}){let[n,c]=(0,i.useState)([]),[d,o]=(0,i.useState)(""),[x,h]=(0,i.useState)(!1),[u,p]=(0,i.useState)(5),[g,b]=(0,i.useState)(!1),[j,v]=(0,i.useState)([]),[f,N]=(0,i.useState)(!1),[w,k]=(0,i.useState)("all"),C=(0,i.useRef)(null),M=()=>{d.trim()&&(c([...n,{id:Date.now().toString(),username:"You",message:d,timestamp:new Date,type:"message",isOwner:!0}]),o(""))},A=e=>{c(n.filter(s=>s.id!==e))},S=e=>{c(n.map(s=>s.id===e?{...s,isPinned:!s.isPinned}:s))},P=e=>{console.log("Banning user:",e)},z=n.filter(e=>"all"===w||("donations"===w?"donation"===e.type:"follows"===w?"follow"===e.type||"subscription"===e.type:"messages"!==w||"message"===e.type)),L=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),q=e=>{switch(e){case"donation":return(0,a.jsx)(ey,{className:"w-4 h-4 text-yellow-500"});case"follow":return(0,a.jsx)(ej,{className:"w-4 h-4 text-red-500"});case"subscription":return(0,a.jsx)(m,{className:"w-4 h-4 text-purple-500"});default:return null}};return(0,a.jsxs)("div",{className:"h-full flex flex-col bg-gray-900",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(ev,{className:"w-5 h-5 text-blue-400"}),(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Stream Chat"}),(0,a.jsxs)("span",{className:"text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded",children:[n.length," messages"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:w,onChange:e=>k(e.target.value),className:"text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white",children:[(0,a.jsx)("option",{value:"all",children:"All"}),(0,a.jsx)("option",{value:"messages",children:"Messages"}),(0,a.jsx)("option",{value:"donations",children:"Donations"}),(0,a.jsx)("option",{value:"follows",children:"Follows"})]}),t&&(0,a.jsx)("button",{onClick:()=>N(!f),className:"p-1 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(ef.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:l,className:"p-1 text-gray-400 hover:text-white transition-colors",children:r?(0,a.jsx)($,{className:"w-4 h-4"}):(0,a.jsx)(E,{className:"w-4 h-4"})})]})]}),f&&t&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-gray-800 rounded-lg space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"Slow Mode"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:x,onChange:e=>h(e.target.checked),className:"rounded"}),x&&(0,a.jsxs)("select",{value:u,onChange:e=>p(Number(e.target.value)),className:"text-xs bg-gray-700 border border-gray-600 rounded px-1 py-1 text-white",children:[(0,a.jsx)("option",{value:5,children:"5s"}),(0,a.jsx)("option",{value:10,children:"10s"}),(0,a.jsx)("option",{value:30,children:"30s"}),(0,a.jsx)("option",{value:60,children:"1m"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"Followers Only"}),(0,a.jsx)("input",{type:"checkbox",checked:g,onChange:e=>b(e.target.checked),className:"rounded"})]})]})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:[z.map(e=>(0,a.jsxs)("div",{className:`group relative ${e.isHighlighted?"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-2":""} ${e.isPinned?"bg-blue-500/10 border border-blue-500/30 rounded-lg p-2":""}`,children:[e.isPinned&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-blue-400 mb-1",children:[(0,a.jsx)(eN,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Pinned Message"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:q(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:`text-sm font-medium ${e.isOwner?"text-green-400":e.isModerator?"text-blue-400":"text-gray-300"}`,children:e.username}),e.isModerator&&(0,a.jsx)(B,{className:"w-3 h-3 text-blue-400"}),"donation"===e.type&&e.amount&&(0,a.jsxs)("span",{className:"text-xs bg-yellow-500 text-black px-2 py-1 rounded font-medium",children:["$",e.amount.toFixed(2)]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:L(e.timestamp)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-200 break-words",children:e.message}),e.reactions&&e.reactions.length>0&&(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-2",children:e.reactions.map((e,s)=>(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-xs bg-gray-700 hover:bg-gray-600 rounded-full px-2 py-1 transition-colors",children:[(0,a.jsx)("span",{children:e.emoji}),(0,a.jsx)("span",{className:"text-gray-300",children:e.count})]},s))})]}),t&&(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{onClick:()=>S(e.id),className:"p-1 text-gray-400 hover:text-blue-400 transition-colors",children:(0,a.jsx)(eN,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:()=>A(e.id),className:"p-1 text-gray-400 hover:text-red-400 transition-colors",children:(0,a.jsx)(y,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:()=>P(e.username),className:"p-1 text-gray-400 hover:text-red-400 transition-colors",children:(0,a.jsx)(ew,{className:"w-3 h-3"})})]})})]})]},e.id)),(0,a.jsx)("div",{ref:C})]}),(0,a.jsxs)("div",{className:"p-4 border-t border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)("input",{type:"text",value:d,onChange:e=>o(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),M())},placeholder:"Type a message...",className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none",maxLength:500}),(0,a.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1",children:(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-yellow-400 transition-colors",children:(0,a.jsx)(ek,{className:"w-4 h-4"})})})]}),(0,a.jsx)("button",{onClick:M,disabled:!d.trim(),className:"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:(0,a.jsx)(U,{className:"w-4 h-4"})})]}),x&&(0,a.jsxs)("p",{className:"text-xs text-yellow-400 mt-2",children:["Slow mode is enabled (",u,"s between messages)"]})]})]})}let eM=(0,o.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),eA=(0,o.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),eS=(0,o.A)("screen-share",[["path",{d:"M13 3H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-3",key:"i8wdob"}],["path",{d:"M8 21h8",key:"1ev6f3"}],["path",{d:"M12 17v4",key:"1riwvh"}],["path",{d:"m17 8 5-5",key:"fqif7o"}],["path",{d:"M17 3h5v5",key:"1o3tu8"}]]),eP=(0,o.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),ez=(0,o.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var eL=t(11921),eq=t(96093),eF=t(89967);function eT(){let e=(0,eL.f)(),{localParticipant:s}=(0,eq.C)(),[t,a]=(0,i.useState)({isMuted:!1,isVideoOff:!1,isScreenSharing:!1,isRecording:!1,isLoading:{audio:!1,video:!1,screenShare:!1,recording:!1},error:null});(0,eq.t)([{source:eF.CC.Source.Camera,withPlaceholder:!1},{source:eF.CC.Source.Microphone,withPlaceholder:!1},{source:eF.CC.Source.ScreenShare,withPlaceholder:!1}]);let r=(0,i.useCallback)((e,s)=>{a(t=>({...t,isLoading:{...t.isLoading,[e]:s}}))},[]),l=(0,i.useCallback)(e=>{a(s=>({...s,error:e}))},[]),n=(0,i.useCallback)(()=>{l(null)},[l]),c=(0,i.useCallback)(async()=>{if(!s)return void l("Not connected to room");try{r("audio",!0),l(null);let e=s.getTrackPublication(eF.CC.Source.Microphone);e?await s.setMicrophoneEnabled(e.isMuted):await s.setMicrophoneEnabled(!0)}catch(e){console.error("Failed to toggle microphone:",e),l("Failed to toggle microphone")}finally{r("audio",!1)}},[s,r,l]),d=(0,i.useCallback)(async()=>{if(!s)return void l("Not connected to room");try{r("video",!0),l(null);let e=s.getTrackPublication(eF.CC.Source.Camera);e?await s.setCameraEnabled(e.isMuted):await s.setCameraEnabled(!0)}catch(e){console.error("Failed to toggle camera:",e),l("Failed to toggle camera")}finally{r("video",!1)}},[s,r,l]),o=(0,i.useCallback)(async()=>{if(!s)return void l("Not connected to room");try{r("screenShare",!0),l(null);let e=s.getTrackPublication(eF.CC.Source.ScreenShare);e&&!e.isMuted?await s.setScreenShareEnabled(!1):await s.setScreenShareEnabled(!0)}catch(e){console.error("Failed to toggle screen share:",e),l("Failed to toggle screen share")}finally{r("screenShare",!1)}},[s,r,l]),x=(0,i.useCallback)(async()=>{if(!e)return void l("Not connected to room");try{r("recording",!0),l(null),a(e=>({...e,isRecording:!e.isRecording})),console.log(t.isRecording?"Stop recording":"Start recording")}catch(e){console.error("Failed to toggle recording:",e),l("Failed to toggle recording")}finally{r("recording",!1)}},[e,t.isRecording,r,l]);return{...t,toggleMute:c,toggleVideo:d,toggleScreenShare:o,toggleRecording:x,clearError:n}}function eI({onToggleChat:e,onToggleParticipants:s,onShowMore:t,className:r=""}){let{isMuted:l,isVideoOff:i,isScreenSharing:n,isRecording:c,isLoading:d,error:o,toggleMute:x,toggleVideo:m,toggleScreenShare:u,toggleRecording:p,clearError:g}=eT(),b=async()=>{await x()},y=async()=>{await m()},j=async()=>{await u()},v=async()=>{await p()};return(0,a.jsxs)("div",{className:`bg-gray-800 border-t border-gray-700 p-2 md:p-4 ${r}`,children:[o&&(0,a.jsxs)("div",{className:"mb-3 p-3 bg-red-900/50 border border-red-500/50 rounded-lg flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eM,{className:"w-4 h-4 text-red-400"}),(0,a.jsx)("span",{className:"text-sm text-red-200",children:o})]}),(0,a.jsx)("button",{onClick:g,className:"p-1 text-red-400 hover:text-red-300 transition-colors",children:(0,a.jsx)(N,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,a.jsx)("button",{onClick:b,disabled:d.audio,className:`relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ${l?"bg-red-600 text-white hover:bg-red-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,title:l?"Unmute microphone":"Mute microphone",children:d.audio?(0,a.jsx)(eA,{className:"w-4 h-4 md:w-5 md:h-5 animate-spin"}):l?(0,a.jsx)(G.A,{className:"w-4 h-4 md:w-5 md:h-5"}):(0,a.jsx)(_.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:y,disabled:d.video,className:`relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ${i?"bg-red-600 text-white hover:bg-red-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,title:i?"Turn on camera":"Turn off camera",children:d.video?(0,a.jsx)(eA,{className:"w-4 h-4 md:w-5 md:h-5 animate-spin"}):i?(0,a.jsx)(K.A,{className:"w-4 h-4 md:w-5 md:h-5"}):(0,a.jsx)(W.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:j,disabled:d.screenShare,className:`relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ${n?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,title:n?"Stop screen sharing":"Start screen sharing",children:d.screenShare?(0,a.jsx)(eA,{className:"w-4 h-4 md:w-5 md:h-5 animate-spin"}):(0,a.jsx)(eS,{className:"w-4 h-4 md:w-5 md:h-5"})})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 md:space-x-3",children:(0,a.jsxs)("button",{onClick:v,disabled:d.recording,className:`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 rounded-lg transition-colors text-sm disabled:opacity-50 ${c?"bg-red-600 text-white hover:bg-red-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,title:c?"Stop recording":"Start recording",children:[d.recording?(0,a.jsx)(eA,{className:"w-4 h-4 animate-spin"}):(0,a.jsx)(eP,{className:`w-4 h-4 ${c?"fill-current":""}`}),(0,a.jsx)("span",{className:"hidden sm:inline",children:c?"Stop Recording":"Record"}),(0,a.jsx)("span",{className:"sm:hidden",children:c?"Stop":"Rec"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,a.jsx)("button",{onClick:e,className:"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors",title:"Toggle chat",children:(0,a.jsx)(ev,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:s,className:"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors",title:"Manage participants",children:(0,a.jsx)(h.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:t,className:"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors",title:"More options",children:(0,a.jsx)(ez,{className:"w-4 h-4 md:w-5 md:h-5"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center mt-2 space-x-4 text-xs text-gray-400",children:[c&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{children:"Recording"})]}),n&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{children:"Screen sharing"})]}),l&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("span",{children:"Muted"})]}),i&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("span",{children:"Camera off"})]})]})]})}var eR=t(54274),e$=t(79409);function eE({token:e,room:s,children:t}){return(0,a.jsxs)(eR.L,{video:!0,audio:!0,token:e,serverUrl:"wss://streamyard-clonez-1zofz2li.livekit.cloud","data-lk-theme":"default",onConnected:()=>console.log("Connected to room:",s),onDisconnected:()=>console.log("Disconnected from room:",s),onError:e=>console.error("LiveKit error:",e),children:[t,(0,a.jsx)(e$.R,{})]})}function eH({onShowBranding:e}){let s=(0,eq.t)([{source:eF.CC.Source.Camera,withPlaceholder:!0},{source:eF.CC.Source.ScreenShare,withPlaceholder:!1}],{onlySubscribed:!1}),{isRecording:t}=eT();return(0,a.jsxs)("div",{className:"w-full h-full bg-black rounded-lg overflow-hidden relative",children:[(0,a.jsx)(e$.G,{tracks:s,style:{height:"100%"},children:(0,a.jsx)(e$.P,{})}),(0,a.jsxs)("div",{className:"absolute top-4 left-4 flex space-x-2",children:[(0,a.jsx)("button",{onClick:e,className:"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors",title:"Branding & Graphics",children:(0,a.jsx)(L,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:e,className:"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors",title:"Add Logo",children:(0,a.jsx)(P,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:e,className:"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors",title:"Add Text",children:(0,a.jsx)(z,{className:"w-4 h-4"})})]}),t&&(0,a.jsxs)("div",{className:"absolute top-4 right-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-lg",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"REC"})]})]})}t(14394);let eV=(0,o.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),eD=(0,o.A)("minimize-2",[["path",{d:"m14 10 7-7",key:"oa77jy"}],["path",{d:"M20 10h-6V4",key:"mjg0md"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M4 14h6v6",key:"rmj7iw"}]]);function eU({streamId:e,currentUserId:s}){let t=(0,n.IT)(c.F.streams.get,{streamId:e}),r=(0,n.IT)(c.F.users.getCurrentUser),l=(0,n.n_)(c.F.streams.startStream),o=(0,n.n_)(c.F.streams.endStream);(0,n.y3)(c.F.livekit.createToken);let[x,m]=(0,i.useState)("none"),[u,g]=(0,i.useState)("single"),[b,y]=(0,i.useState)(!1),[j,v]=(0,i.useState)(!1),[f,N]=(0,i.useState)(!1),[w,k]=(0,i.useState)(!1),[M,A]=(0,i.useState)(!1),[P,z]=(0,i.useState)([]),[L,q]=(0,i.useState)([]),[F,T]=(0,i.useState)([]),[I,R]=(0,i.useState)([]),[E,V]=(0,i.useState)(!0),[U,O]=(0,i.useState)(null),B=async()=>{try{await l({streamId:e})}catch(e){console.error("Failed to start stream:",e)}},G=async()=>{try{await o({streamId:e})}catch(e){console.error("Failed to end stream:",e)}};if(!t||!r)return(0,a.jsx)("div",{className:"flex items-center justify-center h-screen bg-gray-900",children:(0,a.jsx)("div",{className:"text-white text-lg",children:"Loading studio..."})});let _="master"===r.globalRole||"admin"===r.globalRole||t.hostId===s;return U?(0,a.jsx)(eE,{token:U,room:e,children:(0,a.jsxs)("div",{className:"h-screen flex flex-col bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-gray-800 border-b border-gray-700 px-4 md:px-6 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4 min-w-0 flex-1",children:[(0,a.jsx)("h1",{className:"text-lg md:text-xl font-semibold text-white truncate",children:t.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${t.isLive?"bg-red-500 animate-pulse":"bg-gray-500"}`}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:t.isLive?"LIVE":"OFFLINE"}),void 0!==t.participantCount&&(0,a.jsxs)("span",{className:"text-sm text-gray-400 hidden sm:flex items-center ml-4",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-1"}),t.participantCount]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[(0,a.jsxs)("button",{onClick:()=>A(!0),className:"flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors",children:[(0,a.jsx)($,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"0 viewers"})]}),(0,a.jsxs)("button",{onClick:()=>k(!0),className:"flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors",children:[(0,a.jsx)(p,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden md:inline",children:"Destinations"})]})]}),_&&(0,a.jsx)("div",{className:"flex space-x-2",children:t.isLive?(0,a.jsxs)("button",{onClick:G,className:"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,a.jsx)(eV,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"End Stream"})]}):(0,a.jsxs)("button",{onClick:B,className:"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[(0,a.jsx)(J,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Go Live"})]})}),(0,a.jsx)("button",{onClick:()=>m("settings"===x?"none":"settings"),className:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:(0,a.jsx)(ef.A,{className:"w-5 h-5"})})]})]})}),(0,a.jsxs)("div",{className:"flex-1 flex relative",children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"h-full bg-black rounded-lg relative overflow-hidden border border-gray-700",children:(0,a.jsx)(eH,{onShowBranding:()=>v(!0)})})}),(0,a.jsx)("div",{className:"bg-gray-800 border-t border-gray-700 p-2 md:p-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0",children:[(0,a.jsx)("div",{className:"flex-1 overflow-x-auto",children:(0,a.jsx)(C,{selectedLayout:u,onLayoutChange:e=>{g(e),console.log("Layout changed to:",e)},onCreateCustomLayout:()=>{y(!0)},customLayouts:P})}),(0,a.jsx)("div",{className:"flex items-center justify-center md:justify-start space-x-2 md:ml-4",children:(0,a.jsxs)("button",{onClick:()=>N(!0),className:"flex items-center space-x-2 px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors text-sm",children:[(0,a.jsx)(D,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Invite Guests"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Invite"})]})})]})})]}),"none"!==x&&(0,a.jsxs)("div",{className:"w-full md:w-80 bg-gray-800 border-l border-gray-700 flex flex-col absolute md:relative inset-0 md:inset-auto z-10 md:z-auto",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["chat"===x&&"Chat","participants"===x&&"Participants","settings"===x&&"Settings"]}),(0,a.jsx)("button",{onClick:()=>m("none"),className:"p-1 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(eD,{className:"w-4 h-4"})})]})}),(0,a.jsxs)("div",{className:"flex-1 p-4 overflow-y-auto",children:["chat"===x&&(0,a.jsx)("div",{className:"h-full -m-4",children:(0,a.jsx)(eC,{streamId:e,currentUserId:s,isModerator:_,isVisible:E,onToggleVisibility:()=>V(!E)})}),"participants"===x&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(D,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Invite Guests"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"No participants yet"})]}),"settings"===x&&_&&(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(d,{streamId:e,currentUserId:s})})]})]})]}),(0,a.jsx)(eI,{onToggleChat:()=>m("chat"===x?"none":"chat"),onToggleParticipants:()=>m("participants"===x?"none":"participants"),onShowMore:()=>{}}),(0,a.jsx)(S,{isOpen:b,onClose:()=>y(!1),onSave:e=>{z([...P,e]),g(e.id)}}),(0,a.jsx)(H,{isOpen:j,onClose:()=>v(!1),elements:L,onUpdateElements:q}),(0,a.jsx)(Z,{isOpen:f,onClose:()=>N(!1),guests:F,onUpdateGuests:T,streamId:e}),(0,a.jsx)(en,{isOpen:w,onClose:()=>k(!1),destinations:I,onUpdateDestinations:R}),(0,a.jsx)(eb,{isOpen:M,onClose:()=>A(!1),streamId:e,isLive:t.isLive||!1})]})}):(0,a.jsx)("div",{className:"flex items-center justify-center h-screen bg-gray-900",children:(0,a.jsx)("div",{className:"text-white text-lg",children:"Connecting to LiveKit..."})})}function eO(){let e=(0,r.useParams)().streamId,{user:s}=(0,l.Jd)();return s?(0,a.jsx)(eU,{streamId:e,currentUserId:s.id}):(0,a.jsx)("div",{children:"Please sign in to access the stream."})}},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80363:(e,s,t)=>{"use strict";t.d(s,{Toaster:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx","Toaster")},82704:()=>{},83066:(e,s,t)=>{"use strict";t.d(s,{ThemeProvider:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/theme-provider.tsx","ThemeProvider")},89675:(e,s,t)=>{"use strict";t.d(s,{LivepeerProvider:()=>r});var a=t(60687);function r({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}},92892:(e,s,t)=>{"use strict";t.d(s,{ThemeProvider:()=>l});var a=t(60687);t(43210);var r=t(10218);function l({children:e,...s}){return(0,a.jsx)(r.N,{...s,children:e})}},94593:(e,s,t)=>{"use strict";t.d(s,{Toaster:()=>l});var a=t(60687),r=t(52581);let l=({...e})=>(0,a.jsx)(r.l$,{className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},99733:(e,s,t)=>{"use strict";t.d(s,{LivepeerProvider:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call LivepeerProvider() from the server but LivepeerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/livepeer-provider.tsx","LivepeerProvider")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[387,6,249,413],()=>t(20651));module.exports=a})();