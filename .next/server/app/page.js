(()=>{var e={};e.id=974,e.ids=[974],e.modules={886:(e,t,r)=>{"use strict";r.d(t,{ConvexClientProvider:()=>s});var n=r(60687),l=r(77861),a=r(39541),o=r(71946),i=r(41330);let u=new l.eH("https://wonderful-kangaroo-238.convex.cloud");function s({children:e}){return(0,n.jsx)(o.lJ,{publishableKey:"pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ",children:(0,n.jsx)(a.q,{client:u,useAuth:i.d,children:e})})}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],l=r[0];if(Array.isArray(n)&&Array.isArray(l)){if(n[0]!==l[0]||n[2]!==l[2])return!0}else if(n!==l)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],o=Object.values(r[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return s}});let n=r(51550),l=r(59656);var a=l._("_maxConcurrency"),o=l._("_runningCount"),i=l._("_queue"),u=l._("_processNext");class s{enqueue(e){let t,r,l=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,o)[o]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,o)[o]--,n._(this,u)[u]()}};return n._(this,i)[i].push({promiseFn:l,task:a}),n._(this,u)[u](),l}bump(e){let t=n._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,i)[i].splice(t,1)[0];n._(this,i)[i].unshift(e),n._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,o)[o]=0,n._(this,i)[i]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,o)[o]<n._(this,a)[a]||e)&&n._(this,i)[i].length>0){var t;null==(t=n._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return s},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return d}});let n=r(59008),l=r(59154),a=r(75076);function o(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function i(e,t,r){return o(e,t===l.PrefetchKind.FULL,r)}function u(e){let{url:t,nextUrl:r,tree:n,prefetchCache:a,kind:i,allowAliasing:u=!0}=e,s=function(e,t,r,n,a){for(let i of(void 0===t&&(t=l.PrefetchKind.TEMPORARY),[r,null])){let r=o(e,!0,i),u=o(e,!1,i),s=e.search?r:u,c=n.get(s);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(u);if(a&&e.search&&t!==l.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==l.PrefetchKind.FULL&&a){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,i,r,a,u);return s?(s.status=h(s),s.kind!==l.PrefetchKind.FULL&&i===l.PrefetchKind.FULL&&s.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:null!=i?i:l.PrefetchKind.TEMPORARY})}),i&&s.kind===l.PrefetchKind.TEMPORARY&&(s.kind=i),s):c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:i||l.PrefetchKind.TEMPORARY})}function s(e){let{nextUrl:t,tree:r,prefetchCache:n,url:a,data:o,kind:u}=e,s=o.couldBeIntercepted?i(a,u,t):i(a,u),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(o),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:o.staleTime,key:s,status:l.PrefetchCacheEntryStatus.fresh,url:a};return n.set(s,c),c}function c(e){let{url:t,kind:r,tree:o,nextUrl:u,prefetchCache:s}=e,c=i(t,r),d=a.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:o,nextUrl:u,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:l}=e,a=n.get(l);if(!a)return;let o=i(t,a.kind,r);return n.set(o,{...a,key:o}),n.delete(l),o}({url:t,existingCacheKey:c,nextUrl:u,prefetchCache:s})),e.prerendered){let t=s.get(null!=r?r:c);t&&(t.kind=l.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:o,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:l.PrefetchCacheEntryStatus.fresh,url:t};return s.set(c,f),f}function d(e){for(let[t,r]of e)h(r)===l.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:a}=e;return -1!==a?Date.now()<r+a?l.PrefetchCacheEntryStatus.fresh:l.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.fresh:t===l.PrefetchKind.AUTO&&Date.now()<r+p?l.PrefetchCacheEntryStatus.stale:t===l.PrefetchKind.FULL&&Date.now()<r+p?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let n=r(96127);function l(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(59154),r(25232),r(29651),r(28627),r(78866),r(75076),r(97936),r(35429);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(83913),l=r(89752),a=r(86770),o=r(79772),i=r(33123),u=r(33898),s=r(59435);function c(e,t,r,c,f){let p,h=t.tree,g=t.cache,m=(0,o.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=d(r,Object.fromEntries(c.searchParams));let{seedData:o,isRootRender:s,pathToSegment:f}=t,y=["",...f];r=d(r,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,h,r,m),b=(0,l.createEmptyCacheNode)();if(s&&o){let t=o[1];b.loading=o[3],b.rsc=t,function e(t,r,l,a,o){if(0!==Object.keys(a[1]).length)for(let u in a[1]){let s,c=a[1][u],d=c[0],f=(0,i.createRouterCacheKey)(d),p=null!==o&&void 0!==o[2][u]?o[2][u]:null;if(null!==p){let e=p[1],r=p[3];s={lazyData:null,rsc:d.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else s={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=r.parallelRoutes.get(u);h?h.set(f,s):r.parallelRoutes.set(u,new Map([[f,s]])),e(t,s,l,c,p)}}(e,b,g,r,o)}else b.rsc=g.rsc,b.prefetchRsc=g.prefetchRsc,b.loading=g.loading,b.parallelRoutes=new Map(g.parallelRoutes),(0,u.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,g,t);v&&(h=v,g=b,p=!0)}return!!p&&(f.patchedTree=h,f.cache=g,f.canonicalUrl=m,f.hashFragment=c.hash,(0,s.handleMutable)(t,f))}function d(e,t){let[r,l,...a]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),l,...a];let o={};for(let[e,r]of Object.entries(l))o[e]=d(r,t);return[r,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[i,u]=a,s=(0,n.createRouterCacheKey)(u),c=r.parallelRoutes.get(i);if(!c)return;let d=t.parallelRoutes.get(i);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(i,d)),o)return void d.delete(s);let f=c.get(s),p=d.get(s);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(s,p)),e(p,f,(0,l.getNextFlightSegmentPath)(a)))}}});let n=r(33123),l=r(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18626:(e,t,r)=>{Promise.resolve().then(r.bind(r,26527))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,l,,o]=t;for(let i in n.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=r,t[3]="refresh"),l)e(l[i],r)}},refreshInactiveParallelSegments:function(){return o}});let n=r(56928),l=r(59008),a=r(83913);async function o(e){let t=new Set;await i({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function i(e){let{navigatedAt:t,state:r,updatedTree:a,updatedCache:o,includeNextUrl:u,fetchedSegments:s,rootTree:c=a,canonicalUrl:d}=e,[,f,p,h]=a,g=[];if(p&&p!==d&&"refresh"===h&&!s.has(p)){s.add(p);let e=(0,l.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:u?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,o,o,e)});g.push(e)}for(let e in f){let n=i({navigatedAt:t,state:r,updatedTree:f[e],updatedCache:o,includeNextUrl:u,fetchedSegments:s,rootTree:c,canonicalUrl:d});g.push(n)}await Promise.all(g)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},25056:(e,t,r)=>{"use strict";r.d(t,{y:()=>l});var n=r(6475);let l=(0,n.createServerReference)("7f2049dbe655e5196d542e38101773d6697a94154a",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},25208:(e,t,r)=>{"use strict";r.d(t,{ConvexClientProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/convex-provider.tsx","ConvexClientProvider")},25232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,r){let{url:x,isExternalUrl:P,navigateType:R,shouldScroll:j,allowAliasing:E}=r,T={},{hash:w}=x,O=(0,l.createHrefFromUrl)(x),N="push"===R;if((0,m.prunePrefetchCache)(t.prefetchCache),T.preserveCustomHistoryState=!1,T.pendingPush=N,P)return b(t,T,x.toString(),N);if(document.getElementById("__next-page-redirect"))return b(t,T,O,N);let C=(0,m.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:E}),{treeAtTimeOfPrefetch:M,data:S}=C;return f.prefetchQueue.bump(S),S.then(f=>{let{flightData:m,canonicalUrl:P,postponed:R}=f,E=Date.now(),S=!1;if(C.lastUsedTime||(C.lastUsedTime=E,S=!0),C.aliased){let n=(0,v.handleAliasedPrefetchEntry)(E,t,m,x,T);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof m)return b(t,T,m,N);let A=P?(0,l.createHrefFromUrl)(P):O;if(w&&t.canonicalUrl.split("#",1)[0]===A.split("#",1)[0])return T.onlyHashChange=!0,T.canonicalUrl=A,T.shouldScroll=j,T.hashFragment=w,T.scrollableSegments=[],(0,c.handleMutable)(t,T);let U=t.tree,L=t.cache,I=[];for(let e of m){let{pathToSegment:r,seedData:l,head:c,isHeadPartial:f,isRootRender:m}=e,v=e.tree,P=["",...r],j=(0,o.applyRouterStatePatchToTree)(P,U,v,O);if(null===j&&(j=(0,o.applyRouterStatePatchToTree)(P,M,v,O)),null!==j){if(l&&m&&R){let e=(0,g.startPPRNavigation)(E,L,U,v,l,c,f,!1,I);if(null!==e){if(null===e.route)return b(t,T,O,N);j=e.route;let r=e.node;null!==r&&(T.cache=r);let l=e.dynamicRequestTree;if(null!==l){let r=(0,n.fetchServerResponse)(x,{flightRouterState:l,nextUrl:t.nextUrl});(0,g.listenForDynamicRequest)(e,r)}}else j=v}else{if((0,u.isNavigatingToNewRootLayout)(U,j))return b(t,T,O,N);let n=(0,p.createEmptyCacheNode)(),l=!1;for(let t of(C.status!==s.PrefetchCacheEntryStatus.stale||S?l=(0,d.applyFlightData)(E,L,n,e,C):(l=function(e,t,r,n){let l=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),_(n).map(e=>[...r,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),l=!0;return l}(n,L,r,v),C.lastUsedTime=E),(0,i.shouldHardNavigate)(P,U)?(n.rsc=L.rsc,n.prefetchRsc=L.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(n,L,r),T.cache=n):l&&(T.cache=n,L=n),_(v))){let e=[...r,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&I.push(e)}}U=j}}return T.patchedTree=U,T.canonicalUrl=A,T.scrollableSegments=I,T.hashFragment=w,T.shouldScroll=j,(0,c.handleMutable)(t,T)},()=>t)}}});let n=r(59008),l=r(79772),a=r(18468),o=r(86770),i=r(65951),u=r(2030),s=r(59154),c=r(59435),d=r(56928),f=r(75076),p=r(89752),h=r(83913),g=r(65956),m=r(5334),y=r(97464),v=r(9707);function b(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function _(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,l]of Object.entries(n))for(let n of _(l))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26527:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(60687),l=r(77861),a=r(68999),o=r(56499),i=r(85814),u=r.n(i),s=r(43210),c=r(16189),d=r(2943),f=r(41312),p=r(84027);function h(){let{user:e}=(0,o.Jd)(),t=(0,l.IT)(a.F.rooms.getMyRooms),[r,i]=(0,s.useState)(""),h=(0,c.useRouter)(),g=()=>{if(r.trim()){let e=r.trim().toLowerCase().replace(/\s+/g,"-");h.push(`/rooms/${e}`)}},m=()=>{if(r.trim()){let e=r.trim().toLowerCase().replace(/\s+/g,"-");h.push(`/rooms/${e}`)}};return e?(0,n.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-6 py-4",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,n.jsx)(d.A,{className:"w-5 h-5 text-white"})}),(0,n.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Meet Clone"})]}),(0,n.jsx)("div",{className:"flex items-center space-x-2 md:space-x-4",children:(0,n.jsxs)("span",{className:"text-gray-600 hidden md:inline",children:["Welcome back, ",e.firstName]})})]})}),(0,n.jsxs)("div",{className:"max-w-4xl mx-auto px-4 md:px-6 py-12",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Premium video meetings."}),(0,n.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Now free for everyone."})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-8",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,n.jsx)(d.A,{className:"w-6 h-6 text-blue-600"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"New Meeting"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Start an instant meeting"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("input",{type:"text",placeholder:"Enter a room name",value:r,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none",onKeyPress:e=>"Enter"===e.key&&g()}),(0,n.jsx)("button",{onClick:g,disabled:!r.trim(),className:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Start meeting"})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-8",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4",children:(0,n.jsx)(f.A,{className:"w-6 h-6 text-green-600"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Join Meeting"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Join with a meeting ID"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("input",{type:"text",placeholder:"Enter meeting ID or room name",value:r,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none",onKeyPress:e=>"Enter"===e.key&&m()}),(0,n.jsx)("button",{onClick:m,disabled:!r.trim(),className:"w-full px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Join"})]})]})]}),t&&t.length>0&&(0,n.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent meetings"}),(0,n.jsx)("div",{className:"space-y-3",children:t.slice(0,5).map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,n.jsx)(d.A,{className:"w-4 h-4 text-gray-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e._creationTime).toLocaleDateString()})]})]}),(0,n.jsx)(u(),{href:`/rooms/${e.name}`,className:"px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg font-medium transition-colors",children:"Join"})]},e._id))})]})]})]}):(0,n.jsx)("main",{className:"min-h-screen bg-gray-50",children:(0,n.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,n.jsx)("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,n.jsx)(d.A,{className:"w-5 h-5 text-white"})}),(0,n.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Meet Clone"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(u(),{href:"/sign-in",className:"text-gray-600 hover:text-gray-900 font-medium",children:"Sign In"}),(0,n.jsx)(u(),{href:"/sign-up",className:"px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors",children:"Get Started"})]})]})}),(0,n.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 py-12",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,n.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Video meetings for everyone"}),(0,n.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings."}),(0,n.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,n.jsx)(u(),{href:"/sign-up",className:"px-8 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors",children:"Start a meeting"}),(0,n.jsx)(u(),{href:"/sign-in",className:"px-8 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors",children:"Sign In"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(d.A,{className:"w-6 h-6 text-blue-600"})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"HD Video & Audio"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Crystal clear video and audio quality for professional meetings."})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(f.A,{className:"w-6 h-6 text-green-600"})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Up to 100 participants"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Host large meetings with participants from around the world."})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(p.A,{className:"w-6 h-6 text-purple-600"})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Easy to use"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Simple interface that works on any device, no downloads required."})]})]})]})})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28369:(e,t,r)=>{Promise.resolve().then(r.bind(r,886)),Promise.resolve().then(r.bind(r,89675)),Promise.resolve().then(r.bind(r,92892)),Promise.resolve().then(r.bind(r,94593))},28627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(79772),l=r(70642);function a(e,t){var r;let{url:a,tree:o}=t,i=(0,n.createHrefFromUrl)(a),u=o||e.tree,s=e.cache;return{canonicalUrl:i,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:s,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(r=(0,l.extractPathFromFlightRouterState)(u))?r:a.pathname}}r(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(79772),l=r(86770),a=r(2030),o=r(25232),i=r(56928),u=r(59435),s=r(89752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof r)return(0,o.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of r){let{segmentPath:r,tree:u}=t,g=(0,l.applyRouterStatePatchToTree)(["",...r],p,u,e.canonicalUrl);if(null===g)return e;if((0,a.isNavigatingToNewRootLayout)(p,g))return(0,o.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let m=c?(0,n.createHrefFromUrl)(c):void 0;m&&(f.canonicalUrl=m);let y=(0,s.createEmptyCacheNode)();(0,i.applyFlightData)(d,h,y,t),f.patchedTree=g,f.cache=y,h=y,p=g}return(0,u.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30884:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},32708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},33873:e=>{"use strict";e.exports=require("path")},33898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return u},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return s}});let n=r(34400),l=r(41500),a=r(33123),o=r(83913);function i(e,t,r,i,u,s){let{segmentPath:c,seedData:d,tree:f,head:p}=i,h=t,g=r;for(let t=0;t<c.length;t+=2){let r=c[t],i=c[t+1],m=t===c.length-2,y=(0,a.createRouterCacheKey)(i),v=g.parallelRoutes.get(r);if(!v)continue;let b=h.parallelRoutes.get(r);b&&b!==v||(b=new Map(v),h.parallelRoutes.set(r,b));let _=v.get(y),x=b.get(y);if(m){if(d&&(!x||!x.lazyData||x===_)){let t=d[0],r=d[1],a=d[3];x={lazyData:null,rsc:s||t!==o.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:s&&_?new Map(_.parallelRoutes):new Map,navigatedAt:e},_&&s&&(0,n.invalidateCacheByRouterState)(x,_,f),s&&(0,l.fillLazyItemsTillLeafWithHead)(e,x,_,f,d,p,u),b.set(y,x)}continue}x&&_&&(x===_&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},b.set(y,x)),h=x,g=_)}}function u(e,t,r,n,l){i(e,t,r,n,l,!0)}function s(e,t,r,n,l){i(e,t,r,n,l,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let n=r(33123);function l(e,t,r){for(let l in r[1]){let a=r[1][l][0],o=(0,n.createRouterCacheKey)(a),i=t.parallelRoutes.get(l);if(i){let t=new Map(i);t.delete(o),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return N}});let n=r(11264),l=r(11448),a=r(13944),o=r(59154),i=r(6361),u=r(79772),s=r(25232),c=r(86770),d=r(2030),f=r(59435),p=r(41500),h=r(89752),g=r(68214),m=r(96493),y=r(22308),v=r(74007),b=r(36875),_=r(97860),x=r(5334),P=r(25942),R=r(26736),j=r(24642);r(50593);let{createFromFetch:E,createTemporaryReferenceSet:T,encodeReply:w}=r(19357);async function O(e,t,r){let o,u,{actionId:s,actionArgs:c}=r,d=T(),f=(0,j.extractInfoFromServerReferenceId)(s),p="use-cache"===f.type?(0,j.omitUnusedArgs)(c,f):c,h=await w(p,{temporaryReferences:d}),g=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:s,[a.NEXT_ROUTER_STATE_TREE_HEADER]:(0,v.prepareFlightRouterStateForRequest)(e.tree),...{},...t?{[a.NEXT_URL]:t}:{}},body:h}),m=g.headers.get("x-action-redirect"),[y,b]=(null==m?void 0:m.split(";"))||[];switch(b){case"push":o=_.RedirectType.push;break;case"replace":o=_.RedirectType.replace;break;default:o=void 0}let x=!!g.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(g.headers.get("x-action-revalidated")||"[[],0,0]");u={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){u={paths:[],tag:!1,cookie:!1}}let P=y?(0,i.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,R=g.headers.get("content-type");if(null==R?void 0:R.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await E(Promise.resolve(g),{callServer:n.callServer,findSourceMapURL:l.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:P,redirectType:o,revalidatedParts:u,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:P,redirectType:o,revalidatedParts:u,isPrerender:x}}if(g.status>=400)throw Object.defineProperty(Error("text/plain"===R?await g.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:P,redirectType:o,revalidatedParts:u,isPrerender:x}}function N(e,t){let{resolve:r,reject:n}=t,l={},a=e.tree;l.preserveCustomHistoryState=!1;let i=e.nextUrl&&(0,g.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return O(e,i,t).then(async g=>{let j,{actionResult:E,actionFlightData:T,redirectLocation:w,redirectType:O,isPrerender:N,revalidatedParts:C}=g;if(w&&(O===_.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=j=(0,u.createHrefFromUrl)(w,!1)),!T)return(r(E),w)?(0,s.handleExternalUrl)(e,l,w.href,e.pushRef.pendingPush):e;if("string"==typeof T)return r(E),(0,s.handleExternalUrl)(e,l,T,e.pushRef.pendingPush);let M=C.paths.length>0||C.tag||C.cookie;for(let n of T){let{tree:o,seedData:u,head:f,isRootRender:g}=n;if(!g)return console.log("SERVER ACTION APPLY FAILED"),r(E),e;let b=(0,c.applyRouterStatePatchToTree)([""],a,o,j||e.canonicalUrl);if(null===b)return r(E),(0,m.handleSegmentMismatch)(e,t,o);if((0,d.isNavigatingToNewRootLayout)(a,b))return r(E),(0,s.handleExternalUrl)(e,l,j||e.canonicalUrl,e.pushRef.pendingPush);if(null!==u){let t=u[1],r=(0,h.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=u[3],(0,p.fillLazyItemsTillLeafWithHead)(v,r,void 0,o,u,f,void 0),l.cache=r,l.prefetchCache=new Map,M&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:r,includeNextUrl:!!i,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=b,a=b}return w&&j?(M||((0,x.createSeededPrefetchCacheEntry)({url:w,data:{flightData:T,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:N?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),n((0,b.getRedirectError)((0,R.hasBasePath)(j)?(0,P.removeBasePath)(j):j,O||_.RedirectType.push))):r(E),(0,f.handleMutable)(e,l)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38097:(e,t,r)=>{Promise.resolve().then(r.bind(r,25208)),Promise.resolve().then(r.bind(r,99733)),Promise.resolve().then(r.bind(r,83066)),Promise.resolve().then(r.bind(r,80363))},41500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,o,i,u,s){if(0===Object.keys(o[1]).length){r.head=u;return}for(let c in o[1]){let d,f=o[1][c],p=f[0],h=(0,n.createRouterCacheKey)(p),g=null!==i&&void 0!==i[2][c]?i[2][c]:null;if(a){let n=a.parallelRoutes.get(c);if(n){let a,o=(null==s?void 0:s.kind)==="auto"&&s.status===l.PrefetchCacheEntryStatus.reusable,i=new Map(n),d=i.get(h);a=null!==g?{lazyData:null,rsc:g[1],prefetchRsc:null,head:null,prefetchHead:null,loading:g[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:o&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},i.set(h,a),e(t,a,d,f,g||null,u,s),r.parallelRoutes.set(c,i);continue}}if(null!==g){let e=g[1],r=g[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let m=r.parallelRoutes.get(c);m?m.set(h,d):r.parallelRoutes.set(c,new Map([[h,d]])),e(t,d,void 0,f,g,u,s)}}}});let n=r(33123),l=r(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let n=r(33123);function l(e,t){return function e(t,r,l){if(0===Object.keys(r).length)return[t,l];let a=Object.keys(r).filter(e=>"children"!==e);for(let o of("children"in r&&a.unshift("children"),a)){let[a,i]=r[o],u=t.parallelRoutes.get(o);if(!u)continue;let s=(0,n.createRouterCacheKey)(a),c=u.get(s);if(!c)continue;let d=e(c,i,l+"/"+s);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49444:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},50170:(e,t,r)=>{Promise.resolve().then(r.bind(r,90597))},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return u},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return o},navigate:function(){return l},prefetch:function(){return n},reschedulePrefetchTask:function(){return s},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return i}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,l=r,a=r,o=r,i=r,u=r,s=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let n=r(43210);function l(e,t){let r=(0,n.useRef)(null),l=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=a(e,n)),t&&(l.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(41500),l=r(33898);function a(e,t,r,a,o){let{tree:i,seedData:u,head:s,isRootRender:c}=a;if(null===u)return!1;if(c){let l=u[1];r.loading=u[3],r.rsc=l,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,i,u,s,o)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,l.fillCacheWithNewSubTreeData)(e,r,t,a,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var n=r(37413),l=r(59258),a=r.n(l);r(82704);var o=r(25208),i=r(83066),u=r(99733),s=r(80363);let c={title:"Meet Clone - Video meetings for everyone",description:"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings."};function d({children:e}){return(0,n.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,n.jsx)("body",{className:a().className,suppressHydrationWarning:!0,children:(0,n.jsx)(o.ConvexClientProvider,{children:(0,n.jsx)(u.LivepeerProvider,{children:(0,n.jsxs)(i.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!1,storageKey:"streamyard-clone-theme",children:[(0,n.jsx)(s.Toaster,{theme:"light",position:"bottom-center"}),e]})})})})})}},58947:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f126a7a969b1105c221f5710d89250ac20d205bab":()=>n.at,"7f2049dbe655e5196d542e38101773d6697a94154a":()=>l.y,"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a":()=>n.ai,"7fc968e88d4d7d85319b0c692c8eba3563d91942c2":()=>n.ot});var n=r(74006),l=r(3937)},59435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(70642);function l(e){return void 0!==e}function a(e,t){var r,a;let o=null==(r=t.shouldScroll)||r,i=e.nextUrl;if(l(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?i=r:i||(i=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>l});var n=0;function l(e){return"__private_"+n+++"_"+e}},60981:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>s});var n=r(65239),l=r(48088),a=r(88170),o=r.n(a),i=r(30893),u={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>i[e]);r.d(t,u);let s=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90597)),"/home/<USER>/streamyard-clonez/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"/home/<USER>/streamyard-clonez/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["/home/<USER>/streamyard-clonez/app/page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return g},publicAppRouterInstance:function(){return b}});let n=r(59154),l=r(8830),a=r(43210),o=r(91992);r(50593);let i=r(19129),u=r(96127),s=r(89752),c=r(75076),d=r(73406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:r,setState:n}=e,l=t.state;t.pending=r;let a=r.payload,i=t.action(l,a);function u(e){r.discarded||(t.state=e,f(t,n),r.resolve(e))}(0,o.isThenable)(i)?i.then(u,e=>{f(t,n),r.reject(e)}):u(i)}function h(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let l={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=o,p({actionQueue:e,action:o,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(r,e,t),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function g(){return null}function m(){return null}function y(e,t,r,l){let a=new URL((0,u.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(l);(0,i.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:a,isExternalUrl:(0,s.isExternalURL)(a),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function v(e,t){(0,i.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),l=(0,s.createPrefetchURL)(e);if(null!==l){var a;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:l,kind:null!=(a=null==t?void 0:t.kind)?a:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var r;y(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var r;y(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,i.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[a,o]=r,[i,u]=t;return(0,l.matchSegment)(i,a)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),o[u]):!!Array.isArray(i)}}});let n=r(74007),l=r(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return s},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],l=t.parallelRoutes,o=new Map(l);for(let t in n){let r=n[t],i=r[0],u=(0,a.createRouterCacheKey)(i),s=l.get(t);if(void 0!==s){let n=s.get(u);if(void 0!==n){let l=e(n,r),a=new Map(s);a.set(u,l),o.set(t,a)}}}let i=t.rsc,u=y(i)&&"pending"===i.status;return{lazyData:null,rsc:i,head:t.head,prefetchHead:u?t.prefetchHead:[null,null],prefetchRsc:u?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let n=r(83913),l=r(14077),a=r(33123),o=r(2030),i=r(5334),u={route:null,node:null,dynamicRequestTree:null,children:null};function s(e,t,r,o,i,s,f,p,h){return function e(t,r,o,i,s,f,p,h,g,m,y){let v=o[1],b=i[1],_=null!==f?f[2]:null;s||!0===i[4]&&(s=!0);let x=r.parallelRoutes,P=new Map(x),R={},j=null,E=!1,T={};for(let r in b){let o,i=b[r],d=v[r],f=x.get(r),w=null!==_?_[r]:null,O=i[0],N=m.concat([r,O]),C=(0,a.createRouterCacheKey)(O),M=void 0!==d?d[0]:void 0,S=void 0!==f?f.get(C):void 0;if(null!==(o=O===n.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,i,S,s,void 0!==w?w:null,p,h,N,y):g&&0===Object.keys(i[1]).length?c(t,d,i,S,s,void 0!==w?w:null,p,h,N,y):void 0!==d&&void 0!==M&&(0,l.matchSegment)(O,M)&&void 0!==S&&void 0!==d?e(t,S,d,i,s,w,p,h,g,N,y):c(t,d,i,S,s,void 0!==w?w:null,p,h,N,y))){if(null===o.route)return u;null===j&&(j=new Map),j.set(r,o);let e=o.node;if(null!==e){let t=new Map(f);t.set(C,e),P.set(r,t)}let t=o.route;R[r]=t;let n=o.dynamicRequestTree;null!==n?(E=!0,T[r]=n):T[r]=t}else R[r]=i,T[r]=i}if(null===j)return null;let w={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:P,navigatedAt:t};return{route:d(i,R),node:w,dynamicRequestTree:E?d(i,T):null,children:j}}(e,t,r,o,!1,i,s,f,p,[],h)}function c(e,t,r,n,l,s,c,p,h,g){return!l&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,r))?u:function e(t,r,n,l,o,u,s,c){let p,h,g,m,y=r[1],v=0===Object.keys(y).length;if(void 0!==n&&n.navigatedAt+i.DYNAMIC_STALETIME_MS>t)p=n.rsc,h=n.loading,g=n.head,m=n.navigatedAt;else if(null===l)return f(t,r,null,o,u,s,c);else if(p=l[1],h=l[3],g=v?o:null,m=t,l[4]||u&&v)return f(t,r,l,o,u,s,c);let b=null!==l?l[2]:null,_=new Map,x=void 0!==n?n.parallelRoutes:null,P=new Map(x),R={},j=!1;if(v)c.push(s);else for(let r in y){let n=y[r],l=null!==b?b[r]:null,i=null!==x?x.get(r):void 0,d=n[0],f=s.concat([r,d]),p=(0,a.createRouterCacheKey)(d),h=e(t,n,void 0!==i?i.get(p):void 0,l,o,u,f,c);_.set(r,h);let g=h.dynamicRequestTree;null!==g?(j=!0,R[r]=g):R[r]=n;let m=h.node;if(null!==m){let e=new Map;e.set(p,m),P.set(r,e)}}return{route:r,node:{lazyData:null,rsc:p,prefetchRsc:null,head:g,prefetchHead:null,loading:h,parallelRoutes:P,navigatedAt:m},dynamicRequestTree:j?d(r,R):null,children:_}}(e,r,n,s,c,p,h,g)}function d(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function f(e,t,r,n,l,o,i){let u=d(t,t[1]);return u[3]="refetch",{route:t,node:function e(t,r,n,l,o,i,u){let s=r[1],c=null!==n?n[2]:null,d=new Map;for(let r in s){let n=s[r],f=null!==c?c[r]:null,p=n[0],h=i.concat([r,p]),g=(0,a.createRouterCacheKey)(p),m=e(t,n,void 0===f?null:f,l,o,h,u),y=new Map;y.set(g,m),d.set(r,y)}let f=0===d.size;f&&u.push(i);let p=null!==n?n[1]:null,h=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:f?l:[null,null],loading:void 0!==h?h:null,rsc:v(),head:f?v():null,navigatedAt:t}}(e,t,r,n,l,o,i),dynamicRequestTree:u,children:null}}function p(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:o,head:i}=t;o&&function(e,t,r,n,o){let i=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=i.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(n,t)){i=e;continue}}}return}!function e(t,r,n,o){if(null===t.dynamicRequestTree)return;let i=t.children,u=t.node;if(null===i){null!==u&&(function e(t,r,n,o,i){let u=r[1],s=n[1],c=o[2],d=t.parallelRoutes;for(let t in u){let r=u[t],n=s[t],o=c[t],f=d.get(t),p=r[0],h=(0,a.createRouterCacheKey)(p),m=void 0!==f?f.get(h):void 0;void 0!==m&&(void 0!==n&&(0,l.matchSegment)(p,n[0])&&null!=o?e(m,r,n,o,i):g(r,m,null))}let f=t.rsc,p=o[1];null===f?t.rsc=p:y(f)&&f.resolve(p);let h=t.head;y(h)&&h.resolve(i)}(u,t.route,r,n,o),t.dynamicRequestTree=null);return}let s=r[1],c=n[2];for(let t in r){let r=s[t],n=c[t],a=i.get(t);if(void 0!==a){let t=a.route[0];if((0,l.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,o)}}}(i,r,n,o)}(e,r,n,o,i)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)g(e.route,r,t);else for(let e of n.values())h(e,t);e.dynamicRequestTree=null}function g(e,t,r){let n=e[1],l=t.parallelRoutes;for(let e in n){let t=n[e],o=l.get(e);if(void 0===o)continue;let i=t[0],u=(0,a.createRouterCacheKey)(i),s=o.get(u);void 0!==s&&g(t,s,r)}let o=t.rsc;y(o)&&(null===r?o.resolve(null):o.reject(r));let i=t.head;y(i)&&i.resolve(null)}let m=Symbol();function y(e){return e&&e.tag===m}function v(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=m,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68999:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});let n=r(95778).dp},70642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return s},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],a=Array.isArray(t),o=a?t[1]:t;!o||o.startsWith(l.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):a&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(72859),l=r(83913),a=r(14077),o=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=o(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function s(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===l.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(l.PAGE_SEGMENT_KEY))return"";let a=[i(r)],o=null!=(t=e[1])?t:{},c=o.children?s(o.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let r=s(t);void 0!==r&&a.push(r)}return u(a)}function c(e,t){let r=function e(t,r){let[l,o]=t,[u,c]=r,d=i(l),f=i(u);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(l,u)){var p;return null!=(p=s(r))?p:""}for(let t in o)if(c[t]){let r=e(o[t],c[t]);if(null!==r)return i(u)+"/"+r}return null}(e,t);return null==r||"/"===r?r:u(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73024:e=>{"use strict";e.exports=require("node:fs")},73406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return s},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return _},onNavigationIntent:function(){return x},pingVisibleLinks:function(){return R},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return b}}),r(63690);let n=r(89752),l=r(59154),a=r(50593),o=r(43210),i=null,u={pending:!0},s={pending:!1};function c(e){(0,o.startTransition)(()=>{null==i||i.setOptimisticLinkStatus(s),null==e||e.setOptimisticLinkStatus(u),i=e})}function d(e){i===e&&(i=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;_(t.target,e)}},{rootMargin:"200px"}):null;function g(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==h&&h.observe(e)}function m(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,r,n,l,a){if(l){let l=m(t);if(null!==l){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:a};return g(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,r,n){let l=m(t);null!==l&&g(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==h&&h.unobserve(e)}function _(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?p.add(r):p.delete(r),P(r))}function x(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,P(r))}function P(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function R(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of p){let o=n.prefetchTask;if(null!==o&&n.cacheVersion===r&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let i=(0,a.createCacheKey)(n.prefetchHref,e),u=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(i,t,n.kind===l.PrefetchKind.FULL,u),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},75076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let n=r(5144),l=r(5334),a=new n.PromiseQueue(5),o=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76760:e=>{"use strict";e.exports=require("node:path")},77022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let n=r(43210),l=r(51215),a="next-route-announcer";function o(e){let{tree:t}=e,[r,o]=(0,n.useState)(null);(0,n.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,n.useState)(""),s=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&u(e),s.current=e},[t]),r?(0,l.createPortal)(i,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77598:e=>{"use strict";e.exports=require("node:crypto")},78866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(59008),l=r(79772),a=r(86770),o=r(2030),i=r(25232),u=r(59435),s=r(41500),c=r(89752),d=r(96493),f=r(68214),p=r(22308);function h(e,t){let{origin:r}=t,h={},g=e.canonicalUrl,m=e.tree;h.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,n.fetchServerResponse)(new URL(g,r),{flightRouterState:[m[0],m[1],m[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,i.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){let{tree:n,seedData:u,head:f,isRootRender:_}=r;if(!_)return console.log("REFRESH FAILED"),e;let x=(0,a.applyRouterStatePatchToTree)([""],m,n,e.canonicalUrl);if(null===x)return(0,d.handleSegmentMismatch)(e,t,n);if((0,o.isNavigatingToNewRootLayout)(m,x))return(0,i.handleExternalUrl)(e,h,g,e.pushRef.pendingPush);let P=c?(0,l.createHrefFromUrl)(c):void 0;if(c&&(h.canonicalUrl=P),null!==u){let e=u[1],t=u[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,s.fillLazyItemsTillLeafWithHead)(b,y,void 0,n,u,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:x,updatedCache:y,includeNextUrl:v,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=y,h.patchedTree=x,m=x}return(0,u.handleMutable)(e,h)},()=>e)}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80363:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx","Toaster")},82704:()=>{},83066:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/theme-provider.tsx","ThemeProvider")},85814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},useLinkStatus:function(){return v}});let n=r(40740),l=r(60687),a=n._(r(43210)),o=r(30195),i=r(22142),u=r(59154),s=r(53038),c=r(79289),d=r(96127);r(50148);let f=r(73406),p=r(39413),h=r(63690);function g(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function m(e){let t,r,n,[o,m]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:b,as:_,children:x,prefetch:P=null,passHref:R,replace:j,shallow:E,scroll:T,onClick:w,onMouseEnter:O,onTouchStart:N,legacyBehavior:C=!1,onNavigate:M,ref:S,unstable_dynamicOnHover:A,...U}=e;t=x,C&&("string"==typeof t||"number"==typeof t)&&(t=(0,l.jsx)("a",{children:t}));let L=a.default.useContext(i.AppRouterContext),I=!1!==P,k=null===P?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:D,as:F}=a.default.useMemo(()=>{let e=g(b);return{href:e,as:_?g(_):e}},[b,_]);C&&(r=a.default.Children.only(t));let H=C?r&&"object"==typeof r&&r.ref:S,K=a.default.useCallback(e=>(null!==L&&(v.current=(0,f.mountLinkInstance)(e,D,L,k,I,m)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[I,D,L,k,m]),z={ref:(0,s.useMergedRef)(K,H),onClick(e){C||"function"!=typeof w||w(e),C&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,l,o,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){l&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(r||t,l?"replace":"push",null==o||o,n.current)})}}(e,D,F,v,j,T,M))},onMouseEnter(e){C||"function"!=typeof O||O(e),C&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===A)},onTouchStart:function(e){C||"function"!=typeof N||N(e),C&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===A)}};return(0,c.isAbsoluteUrl)(F)?z.href=F:C&&!R&&("a"!==r.type||"href"in r.props)||(z.href=(0,d.addBasePath)(F)),n=C?a.default.cloneElement(r,z):(0,l.jsx)("a",{...U,...z,children:t}),(0,l.jsx)(y.Provider,{value:o,children:n})}r(32708);let y=(0,a.createContext)(f.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,u){let s,[c,d,f,p,h]=r;if(1===t.length){let e=i(r,n);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,u),e}let[g,m]=t;if(!(0,a.matchSegment)(g,c))return null;if(2===t.length)s=i(d[m],n);else if(null===(s=e((0,l.getNextFlightSegmentPath)(t),d[m],n,u)))return null;let y=[t[0],{...d,[m]:s},f,p];return h&&(y[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(y,u),y}}});let n=r(83913),l=r(74007),a=r(14077),o=r(22308);function i(e,t){let[r,l]=e,[o,u]=t;if(o===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(r,o)){let t={};for(let e in l)void 0!==u[e]?t[e]=i(l[e],u[e]):t[e]=l[e];for(let e in u)t[e]||(t[e]=u[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89675:(e,t,r)=>{"use strict";r.d(t,{LivepeerProvider:()=>l});var n=r(60687);function l({children:e}){return(0,n.jsx)(n.Fragment,{children:e})}},89752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return N},createPrefetchURL:function(){return w},default:function(){return A},isExternalURL:function(){return T}});let n=r(40740),l=r(60687),a=n._(r(43210)),o=r(22142),i=r(59154),u=r(79772),s=r(10449),c=r(19129),d=n._(r(35656)),f=r(35416),p=r(96127),h=r(77022),g=r(67086),m=r(44397),y=r(89330),v=r(25942),b=r(26736),_=r(70642),x=r(12776),P=r(63690),R=r(36875),j=r(97860);r(73406);let E={};function T(e){return e.origin!==window.location.origin}function w(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return T(t)?null:t}function O(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,l={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(l,"",n)):window.history.replaceState(l,"",n)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function N(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function C(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function M(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,l=null!==n?n:r;return(0,a.useDeferredValue)(r,l)}function S(e){let t,{actionQueue:r,assetPrefix:n,globalError:u}=e,f=(0,c.useActionQueue)(r),{canonicalUrl:p}=f,{searchParams:x,pathname:T}=(0,a.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[p]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(E.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,j.isRedirectError)(t)){e.preventDefault();let r=(0,R.getURLFromRedirectError)(t);(0,R.getRedirectTypeFromError)(t)===j.RedirectType.push?P.publicAppRouterInstance.push(r,{}):P.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:w}=f;if(w.mpaNavigation){if(E.pendingMpaPath!==p){let e=window.location;w.pendingPush?e.assign(p):e.replace(p),E.pendingMpaPath=p}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=C(t),l&&r(l)),e(t,n,l)},window.history.replaceState=function(e,n,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=C(e),l&&r(l)),t(e,n,l)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,P.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:N,tree:S,nextUrl:A,focusAndScrollRef:U}=f,L=(0,a.useMemo)(()=>(0,m.findHeadInCache)(N,S[1]),[N,S]),k=(0,a.useMemo)(()=>(0,_.getSelectedParams)(S),[S]),D=(0,a.useMemo)(()=>({parentTree:S,parentCacheNode:N,parentSegmentPath:null,url:p}),[S,N,p]),F=(0,a.useMemo)(()=>({tree:S,focusAndScrollRef:U,nextUrl:A}),[S,U,A]);if(null!==L){let[e,r]=L;t=(0,l.jsx)(M,{headCacheNode:e},r)}else t=null;let H=(0,l.jsxs)(g.RedirectBoundary,{children:[t,N.rsc,(0,l.jsx)(h.AppRouterAnnouncer,{tree:S})]});return H=(0,l.jsx)(d.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:H}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(O,{appRouterState:f}),(0,l.jsx)(I,{}),(0,l.jsx)(s.PathParamsContext.Provider,{value:k,children:(0,l.jsx)(s.PathnameContext.Provider,{value:T,children:(0,l.jsx)(s.SearchParamsContext.Provider,{value:x,children:(0,l.jsx)(o.GlobalLayoutRouterContext.Provider,{value:F,children:(0,l.jsx)(o.AppRouterContext.Provider,{value:P.publicAppRouterInstance,children:(0,l.jsx)(o.LayoutRouterContext.Provider,{value:D,children:H})})})})})})]})}function A(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:a}=e;return(0,x.useNavFailureHandler)(),(0,l.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,l.jsx)(S,{actionQueue:t,assetPrefix:a,globalError:[r,n]})})}let U=new Set,L=new Set;function I(){let[,e]=a.default.useState(0),t=U.size;return(0,a.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==U.size&&r(),()=>{L.delete(r)}},[t,e]),[...U].map((e,t)=>(0,l.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=U.size;return U.add(e),U.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90597:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/streamyard-clonez/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/app/page.tsx","default")},92892:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var n=r(60687);r(43210);var l=r(10218);function a({children:e,...t}){return(0,n.jsx)(l.N,{...t,children:e})}},94593:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>a});var n=r(60687),l=r(52581);let a=({...e})=>(0,n.jsx)(l.l$,{className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},96493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let n=r(25232);function l(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[i,u]=a,s=(0,l.createRouterCacheKey)(u),c=r.parallelRoutes.get(i),d=t.parallelRoutes.get(i);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(i,d));let f=null==c?void 0:c.get(s),p=d.get(s);if(o){p&&p.lazyData&&p!==f||d.set(s,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!f){p||d.set(s,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(s,p)),e(p,f,(0,n.getNextFlightSegmentPath)(a))}}});let n=r(74007),l=r(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(59008),r(79772),r(86770),r(2030),r(25232),r(59435),r(56928),r(89752),r(96493),r(68214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99733:(e,t,r)=>{"use strict";r.d(t,{LivepeerProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call LivepeerProvider() from the server but LivepeerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/livepeer-provider.tsx","LivepeerProvider")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[387,6,249],()=>r(60981));module.exports=n})();