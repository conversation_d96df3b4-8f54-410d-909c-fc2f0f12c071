(()=>{var e={};e.id=202,e.ids=[202],e.modules={886:(e,t,r)=>{"use strict";r.d(t,{ConvexClientProvider:()=>c});var s=r(60687),n=r(77861),o=r(39541),a=r(71946),i=r(41330);let l=new n.eH("https://wonderful-kangaroo-238.convex.cloud");function c({children:e}){return(0,s.jsx)(a.lJ,{publishableKey:"pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ",children:(0,s.jsx)(o.q,{client:l,useAuth:i.d,children:e})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25056:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});var s=r(6475);let n=(0,s.createServerReference)("7f2049dbe655e5196d542e38101773d6697a94154a",s.callServer,void 0,s.findSourceMapURL,"invalidateCacheAction")},25208:(e,t,r)=>{"use strict";r.d(t,{ConvexClientProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/convex-provider.tsx","ConvexClientProvider")},27910:e=>{"use strict";e.exports=require("stream")},28369:(e,t,r)=>{Promise.resolve().then(r.bind(r,886)),Promise.resolve().then(r.bind(r,89675)),Promise.resolve().then(r.bind(r,92892)),Promise.resolve().then(r.bind(r,94593))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30884:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},31836:(e,t,r)=>{Promise.resolve().then(r.bind(r,32977))},32977:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},36993:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),n=r(48088),o=r(88170),a=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["rooms",{children:["[roomName]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32977)),"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"/home/<USER>/streamyard-clonez/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/rooms/[roomName]/page",pathname:"/rooms/[roomName]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},38097:(e,t,r)=>{Promise.resolve().then(r.bind(r,25208)),Promise.resolve().then(r.bind(r,99733)),Promise.resolve().then(r.bind(r,83066)),Promise.resolve().then(r.bind(r,80363))},49444:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>d});var s=r(37413),n=r(59258),o=r.n(n);r(82704);var a=r(25208),i=r(83066),l=r(99733),c=r(80363);let d={title:"Meet Clone - Video meetings for everyone",description:"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings."};function u({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:o().className,suppressHydrationWarning:!0,children:(0,s.jsx)(a.ConvexClientProvider,{children:(0,s.jsx)(l.LivepeerProvider,{children:(0,s.jsxs)(i.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!1,storageKey:"streamyard-clone-theme",children:[(0,s.jsx)(c.Toaster,{theme:"light",position:"bottom-center"}),e]})})})})})}},58947:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f126a7a969b1105c221f5710d89250ac20d205bab":()=>s.at,"7f2049dbe655e5196d542e38101773d6697a94154a":()=>n.y,"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a":()=>s.ai,"7fc968e88d4d7d85319b0c692c8eba3563d91942c2":()=>s.ot});var s=r(74006),n=r(3937)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68999:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});let s=r(95778).dp},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80363:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx","Toaster")},82342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var s=r(60687),n=r(16189),o=r(77861),a=r(68999),i=r(56499),l=r(43210),c=r(96093),d=r(79409),u=r(54274),m=r(11921),h=r(89967);function p({messageFormatter:e,messageDecoder:t,messageEncoder:r,channelTopic:s,...n}){let o=l.useRef(null),a=l.useRef(null),i=l.useMemo(()=>({messageDecoder:t,messageEncoder:r,channelTopic:s}),[t,r,s]),{chatMessages:h,send:p,isSending:v}=(0,c.w)(i),x=(0,m.j)(),g=l.useRef(0);async function b(e){e.preventDefault(),a.current&&""!==a.current.value.trim()&&(await p(a.current.value),a.current.value="",a.current.focus())}return l.useEffect(()=>{var e;o&&(null==(e=o.current)||e.scrollTo({top:o.current.scrollHeight}))},[o,h]),l.useEffect(()=>{var e,t,r,s,n;if(!x||0===h.length)return;if(null!=(e=x.widget.state)&&e.showChat&&h.length>0&&g.current!==(null==(t=h[h.length-1])?void 0:t.timestamp)){g.current=null==(r=h[h.length-1])?void 0:r.timestamp;return}let o=h.filter(e=>!g.current||e.timestamp>g.current).length,{widget:a}=x;o>0&&(null==(s=a.state)?void 0:s.unreadMessages)!==o&&(null==(n=a.dispatch)||n.call(a,{msg:"unread_msg",count:o}))},[h,null==x?void 0:x.widget]),l.createElement("div",{...n,className:"lk-chat"},l.createElement("div",{className:"lk-chat-header"},"Messages",x&&l.createElement(d.C,{className:"lk-close-button"},l.createElement(d.S,null))),l.createElement("ul",{className:"lk-list lk-chat-messages",ref:o},n.children?h.map((t,r)=>(0,u.c)(n.children,{entry:t,key:t.id??r,messageFormatter:e})):h.map((t,r,s)=>{let n=r>=1&&s[r-1].from===t.from,o=r>=1&&t.timestamp-s[r-1].timestamp<6e4;return l.createElement(d.a,{key:t.id??r,hideName:n,hideTimestamp:!1!==n&&o,entry:t,messageFormatter:e})})),l.createElement("form",{className:"lk-chat-form",onSubmit:b},l.createElement("input",{className:"lk-form-control lk-chat-form-input",disabled:v,ref:a,type:"text",placeholder:"Enter a message...",onInput:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),onKeyUp:e=>e.stopPropagation()}),l.createElement("button",{type:"submit",className:"lk-button lk-chat-form-button",disabled:v},"Send")))}function v({kind:e,initialSelection:t,onActiveDeviceChange:r,tracks:s,requestPermissions:n=!1,...o}){let[a,i]=l.useState(!1),[c,u]=l.useState([]),[h,p]=l.useState(!0),[v,x]=l.useState(n),g=(e,t)=>{m.l.debug("handle device change"),i(!1),null==r||r(e,t)},b=l.useRef(null),f=l.useRef(null);l.useLayoutEffect(()=>{a&&x(!0)},[a]),l.useLayoutEffect(()=>{let e;return b.current&&f.current&&(c||h)&&(e=(0,m.ao)(b.current,f.current,(e,t)=>{f.current&&Object.assign(f.current.style,{left:`${e}px`,top:`${t}px`})})),p(!1),()=>{null==e||e()}},[b,f,c,h]);let y=l.useCallback(e=>{f.current&&e.target!==b.current&&a&&(0,m.ap)(f.current,e)&&i(!1)},[a,f,b]);return l.useEffect(()=>(document.addEventListener("click",y),()=>{document.removeEventListener("click",y)}),[y]),l.createElement(l.Fragment,null,l.createElement("button",{className:"lk-button lk-button-menu","aria-pressed":a,...o,onClick:()=>i(!a),ref:b},o.children),!o.disabled&&l.createElement("div",{className:"lk-device-menu",ref:f,style:{visibility:a?"visible":"hidden"}},e?l.createElement(d.M,{initialSelection:t,onActiveDeviceChange:t=>g(e,t),onDeviceListChange:u,kind:e,track:null==s?void 0:s[e],requestPermissions:v}):l.createElement(l.Fragment,null,l.createElement("div",{className:"lk-device-menu-heading"},"Audio inputs"),l.createElement(d.M,{kind:"audioinput",onActiveDeviceChange:e=>g("audioinput",e),onDeviceListChange:u,track:null==s?void 0:s.audioinput,requestPermissions:v}),l.createElement("div",{className:"lk-device-menu-heading"},"Video inputs"),l.createElement(d.M,{kind:"videoinput",onActiveDeviceChange:e=>g("videoinput",e),onDeviceListChange:u,track:null==s?void 0:s.videoinput,requestPermissions:v}))))}let x=l.forwardRef(function(e,t){let{mergedProps:r}=function({props:e}){let{dispatch:t,state:r}=(0,m.a)().widget,s="lk-button lk-settings-toggle";return{mergedProps:l.useMemo(()=>(0,u.m)(e,{className:s,onClick:()=>{t&&t({msg:"toggle_settings"})},"aria-pressed":null!=r&&r.showSettings?"true":"false"}),[e,s,t,r])}}({props:e});return l.createElement("button",{ref:t,...r},e.children)}),g=e=>{switch(e){case h.CC.Source.Camera:return 1;case h.CC.Source.Microphone:return 2;case h.CC.Source.ScreenShare:return 3;default:return 0}};function b({variation:e,controls:t,saveUserChoices:r=!0,onDeviceError:s,...n}){var o;let[a,i]=l.useState(!1),p=(0,m.j)();l.useEffect(()=>{var e,t;(null==(e=null==p?void 0:p.widget.state)?void 0:e.showChat)!==void 0&&i(null==(t=null==p?void 0:p.widget.state)?void 0:t.showChat)},[null==(o=null==p?void 0:p.widget.state)?void 0:o.showChat]);let b=(0,c.z)(`(max-width: ${a?1e3:760}px)`)?"minimal":"verbose";e??(e=b);let f={leave:!0,...t},y=(0,c.A)();if(y){let e=e=>y.canPublish&&(0===y.canPublishSources.length||y.canPublishSources.includes(g(e)));f.camera??(f.camera=e(h.CC.Source.Camera)),f.microphone??(f.microphone=e(h.CC.Source.Microphone)),f.screenShare??(f.screenShare=e(h.CC.Source.ScreenShare)),f.chat??(f.chat=y.canPublishData&&(null==t?void 0:t.chat))}else f.camera=!1,f.chat=!1,f.microphone=!1,f.screenShare=!1;let C=l.useMemo(()=>"minimal"===e||"verbose"===e,[e]),j=l.useMemo(()=>"textOnly"===e||"verbose"===e,[e]),N=(0,m.aq)(),[k,w]=l.useState(!1),P=l.useCallback(e=>{w(e)},[w]),E=(0,u.a)({className:"lk-control-bar"},n),{saveAudioInputEnabled:S,saveVideoInputEnabled:A,saveAudioInputDeviceId:T,saveVideoInputDeviceId:M}=(0,c.x)({preventSave:!r}),L=l.useCallback((e,t)=>t?S(e):null,[S]),R=l.useCallback((e,t)=>t?A(e):null,[A]);return l.createElement("div",{...E},f.microphone&&l.createElement("div",{className:"lk-button-group"},l.createElement(d.T,{source:h.CC.Source.Microphone,showIcon:C,onChange:L,onDeviceError:e=>null==s?void 0:s({source:h.CC.Source.Microphone,error:e})},j&&"Microphone"),l.createElement("div",{className:"lk-button-group-menu"},l.createElement(v,{kind:"audioinput",onActiveDeviceChange:(e,t)=>T(t??"default")}))),f.camera&&l.createElement("div",{className:"lk-button-group"},l.createElement(d.T,{source:h.CC.Source.Camera,showIcon:C,onChange:R,onDeviceError:e=>null==s?void 0:s({source:h.CC.Source.Camera,error:e})},j&&"Camera"),l.createElement("div",{className:"lk-button-group-menu"},l.createElement(v,{kind:"videoinput",onActiveDeviceChange:(e,t)=>M(t??"default")}))),f.screenShare&&N&&l.createElement(d.T,{source:h.CC.Source.ScreenShare,captureOptions:{audio:!0,selfBrowserSurface:"include"},showIcon:C,onChange:P,onDeviceError:e=>null==s?void 0:s({source:h.CC.Source.ScreenShare,error:e})},j&&(k?"Stop screen share":"Share screen")),f.chat&&l.createElement(d.C,null,C&&l.createElement(d.c,null),j&&"Chat"),f.settings&&l.createElement(x,null,C&&l.createElement(d.d,null),j&&"Settings"),f.leave&&l.createElement(d.D,null,C&&l.createElement(d.e,null),j&&"Leave"),l.createElement(d.f,null))}var f=r(41312),y=r(58887);let C=(0,r(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var j=r(73259),N=r(82719),k=r(2943),w=r(46915);function P(){let[e,t]=(0,l.useState)(!1),[r,n]=(0,l.useState)(!1),o=(0,c.K)(),a=(0,c.t)([{source:h.CC.Source.Camera,withPlaceholder:!0},{source:h.CC.Source.ScreenShare,withPlaceholder:!1}],{onlySubscribed:!1});return(0,s.jsxs)("div",{className:"h-screen bg-gray-900 flex flex-col",children:[(0,s.jsxs)("div",{className:"bg-gray-800 text-white px-4 py-3 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("h1",{className:"font-medium",children:"Meeting Room"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[o.length," participants"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>n(!r),className:`p-2 rounded-lg transition-colors ${r?"bg-blue-600":"hover:bg-gray-700"}`,children:(0,s.jsx)(f.A,{className:"w-5 h-5"})}),(0,s.jsx)("button",{onClick:()=>t(!e),className:`p-2 rounded-lg transition-colors ${e?"bg-blue-600":"hover:bg-gray-700"}`,children:(0,s.jsx)(y.A,{className:"w-5 h-5"})}),(0,s.jsx)("button",{className:"p-2 hover:bg-gray-700 rounded-lg transition-colors",children:(0,s.jsx)(C,{className:"w-5 h-5"})})]})]}),(0,s.jsxs)("div",{className:"flex-1 flex relative overflow-hidden",children:[(0,s.jsx)("div",{className:"flex-1 p-4",children:(0,s.jsx)(d.G,{tracks:a,style:{height:"100%"},children:(0,s.jsx)(d.P,{})})}),(e||r)&&(0,s.jsxs)("div",{className:"w-80 bg-white border-l border-gray-200 flex flex-col",children:[(0,s.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("button",{onClick:()=>{n(!0),t(!1)},className:`pb-2 border-b-2 transition-colors ${r?"border-blue-600 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:["Participants (",o.length,")"]}),(0,s.jsx)("button",{onClick:()=>{t(!0),n(!1)},className:`pb-2 border-b-2 transition-colors ${e?"border-blue-600 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:"Chat"})]})}),(0,s.jsxs)("div",{className:"flex-1 overflow-hidden",children:[r&&(0,s.jsx)("div",{className:"p-4 space-y-3",children:o.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium",children:e.name?.[0]?.toUpperCase()||"A"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("p",{className:"font-medium text-gray-900",children:[e.name||"Anonymous",e.isLocal&&" (You)"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[e.isMicrophoneEnabled?(0,s.jsx)(j.A,{className:"w-3 h-3 text-green-500"}):(0,s.jsx)(N.A,{className:"w-3 h-3 text-red-500"}),e.isCameraEnabled?(0,s.jsx)(k.A,{className:"w-3 h-3 text-green-500"}):(0,s.jsx)(w.A,{className:"w-3 h-3 text-red-500"})]})]})]},e.identity))}),e&&(0,s.jsx)("div",{className:"h-full",children:(0,s.jsx)(p,{style:{height:"100%"}})})]})]})]}),(0,s.jsx)("div",{className:"bg-gray-800 p-4",children:(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)(b,{variation:"minimal"})})}),(0,s.jsx)(d.R,{})]})}function E({roomName:e,token:t,userName:r}){let n="wss://streamyard-clonez-1zofz2li.livekit.cloud";return console.log("VideoConference connecting:",{serverUrl:n,roomName:e,userName:r,hasToken:!!t}),(0,s.jsx)(u.L,{video:!0,audio:!0,token:t,serverUrl:n,"data-lk-theme":"default",style:{height:"100vh"},children:(0,s.jsx)(P,{})})}var S=r(84027);function A({roomName:e,userName:t,onJoin:r}){let[n,o]=(0,l.useState)(t),[a,i]=(0,l.useState)(!0),[c,d]=(0,l.useState)(!0),u=(0,l.useRef)(null),[m,h]=(0,l.useState)(null),p=()=>{m&&(m.getTracks().forEach(e=>e.stop()),h(null))};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Ready to join?"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Meeting: ",e]})]}),(0,s.jsxs)("div",{className:"relative bg-gray-900 rounded-lg overflow-hidden mb-6 aspect-video",children:[a?(0,s.jsx)("video",{ref:u,autoPlay:!0,muted:!0,playsInline:!0,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-xl font-semibold",children:(n||t||"A")[0].toUpperCase()})})}),(0,s.jsxs)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3",children:[(0,s.jsx)("button",{onClick:()=>i(!a),className:`p-3 rounded-full transition-colors ${a?"bg-gray-600 hover:bg-gray-700":"bg-red-600 hover:bg-red-700"}`,children:a?(0,s.jsx)(k.A,{className:"w-5 h-5 text-white"}):(0,s.jsx)(w.A,{className:"w-5 h-5 text-white"})}),(0,s.jsx)("button",{onClick:()=>d(!c),className:`p-3 rounded-full transition-colors ${c?"bg-gray-600 hover:bg-gray-700":"bg-red-600 hover:bg-red-700"}`,children:c?(0,s.jsx)(j.A,{className:"w-5 h-5 text-white"}):(0,s.jsx)(N.A,{className:"w-5 h-5 text-white"})})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{htmlFor:"displayName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your name"}),(0,s.jsx)("input",{id:"displayName",type:"text",value:n,onChange:e=>o(e.target.value),placeholder:"Enter your name",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"})]}),(0,s.jsx)("button",{onClick:()=>{p(),r(n||t||"Anonymous")},disabled:!n.trim(),className:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Join meeting"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("button",{className:"inline-flex items-center space-x-2 text-sm text-gray-500 hover:text-gray-700",children:[(0,s.jsx)(S.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Check your audio and video settings"})]})})]})})}function T(){let{roomName:e}=(0,n.useParams)(),{user:t}=(0,i.Jd)(),[r,c]=(0,l.useState)(!1),[d,u]=(0,l.useState)(null),[m,h]=(0,l.useState)(""),p=(0,o.IT)(a.F.rooms.getRoom,{name:e}),v=(0,o.n_)(a.F.rooms.create),x=(0,o.y3)(a.F.livekit.createToken),g=async t=>{try{p||await v({name:e,title:`Meeting: ${e}`,description:`Video meeting room for ${e}`});let r=await x({roomName:e,participantName:t||m||"Anonymous"});u(r),c(!0)}catch(e){console.error("Failed to join room:",e)}};return t?r&&d?(0,s.jsx)(E,{roomName:e,token:d,userName:m}):(0,s.jsx)(A,{roomName:e,userName:m,onJoin:g}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Please sign in to join the meeting"}),(0,s.jsx)("a",{href:"/sign-in",className:"px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors",children:"Sign In"})]})})}},82704:()=>{},83066:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/theme-provider.tsx","ThemeProvider")},89675:(e,t,r)=>{"use strict";r.d(t,{LivepeerProvider:()=>n});var s=r(60687);function n({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},89980:(e,t,r)=>{Promise.resolve().then(r.bind(r,82342))},92892:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(60687);r(43210);var n=r(10218);function o({children:e,...t}){return(0,s.jsx)(n.N,{...t,children:e})}},94593:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});var s=r(60687),n=r(52581);let o=({...e})=>(0,s.jsx)(n.l$,{className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},99733:(e,t,r)=>{"use strict";r.d(t,{LivepeerProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call LivepeerProvider() from the server but LivepeerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/livepeer-provider.tsx","LivepeerProvider")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[387,6,249,413],()=>r(36993));module.exports=s})();