(()=>{var e={};e.id=578,e.ids=[578],e.modules={886:(e,r,t)=>{"use strict";t.d(r,{ConvexClientProvider:()=>l});var o=t(60687),n=t(77861),s=t(39541),i=t(71946),d=t(41330);let a=new n.eH("https://wonderful-kangaroo-238.convex.cloud");function l({children:e}){return(0,o.jsx)(i.lJ,{publishableKey:"pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ",children:(0,o.jsx)(s.q,{client:a,useAuth:d.d,children:e})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9708:(e,r,t)=>{Promise.resolve().then(t.bind(t,63441)),Promise.resolve().then(t.bind(t,62808)),Promise.resolve().then(t.bind(t,7791)),Promise.resolve().then(t.bind(t,12918)),Promise.resolve().then(t.bind(t,13920)),Promise.resolve().then(t.bind(t,62278))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12822:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f2049dbe655e5196d542e38101773d6697a94154a":()=>o.y});var o=t(3937)},15788:(e,r,t)=>{Promise.resolve().then(t.bind(t,93596)),Promise.resolve().then(t.bind(t,85850)),Promise.resolve().then(t.bind(t,93821)),Promise.resolve().then(t.bind(t,58758)),Promise.resolve().then(t.bind(t,41330)),Promise.resolve().then(t.bind(t,21313))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23145:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var o=t(65239),n=t(48088),s=t(88170),i=t.n(s),d=t(30893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["(auth)",{children:["sign-up",{children:["[[...sign-up]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69873)),"/home/<USER>/streamyard-clonez/app/(auth)/sign-up/[[...sign-up]]/page.tsx"]}]},{}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"/home/<USER>/streamyard-clonez/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/home/<USER>/streamyard-clonez/app/(auth)/sign-up/[[...sign-up]]/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/sign-up/[[...sign-up]]/page",pathname:"/sign-up/[[...sign-up]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},25056:(e,r,t)=>{"use strict";t.d(r,{y:()=>n});var o=t(6475);let n=(0,o.createServerReference)("7f2049dbe655e5196d542e38101773d6697a94154a",o.callServer,void 0,o.findSourceMapURL,"invalidateCacheAction")},25208:(e,r,t)=>{"use strict";t.d(r,{ConvexClientProvider:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/convex-provider.tsx","ConvexClientProvider")},27910:e=>{"use strict";e.exports=require("stream")},28369:(e,r,t)=>{Promise.resolve().then(t.bind(t,886)),Promise.resolve().then(t.bind(t,89675)),Promise.resolve().then(t.bind(t,92892)),Promise.resolve().then(t.bind(t,94593))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30884:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},33873:e=>{"use strict";e.exports=require("path")},38097:(e,r,t)=>{Promise.resolve().then(t.bind(t,25208)),Promise.resolve().then(t.bind(t,99733)),Promise.resolve().then(t.bind(t,83066)),Promise.resolve().then(t.bind(t,80363))},49444:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,metadata:()=>c});var o=t(37413),n=t(59258),s=t.n(n);t(82704);var i=t(25208),d=t(83066),a=t(99733),l=t(80363);let c={title:"Meet Clone - Video meetings for everyone",description:"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings."};function u({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:s().className,suppressHydrationWarning:!0,children:(0,o.jsx)(i.ConvexClientProvider,{children:(0,o.jsx)(a.LivepeerProvider,{children:(0,o.jsxs)(d.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!1,storageKey:"streamyard-clone-theme",children:[(0,o.jsx)(l.Toaster,{theme:"light",position:"bottom-center"}),e]})})})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69873:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(37413),n=t(62278);function s(){return(0,o.jsx)(n.SignUp,{})}},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx","Toaster")},82704:()=>{},83066:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/theme-provider.tsx","ThemeProvider")},84754:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f126a7a969b1105c221f5710d89250ac20d205bab":()=>o.at,"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a":()=>o.ai,"7fc968e88d4d7d85319b0c692c8eba3563d91942c2":()=>o.ot});var o=t(81163)},89675:(e,r,t)=>{"use strict";t.d(r,{LivepeerProvider:()=>n});var o=t(60687);function n({children:e}){return(0,o.jsx)(o.Fragment,{children:e})}},92892:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});var o=t(60687);t(43210);var n=t(10218);function s({children:e,...r}){return(0,o.jsx)(n.N,{...r,children:e})}},94593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>s});var o=t(60687),n=t(52581);let s=({...e})=>(0,o.jsx)(n.l$,{className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},99733:(e,r,t)=>{"use strict";t.d(r,{LivepeerProvider:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call LivepeerProvider() from the server but LivepeerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/streamyard-clonez/components/livepeer-provider.tsx","LivepeerProvider")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[387,757],()=>t(23145));module.exports=o})();