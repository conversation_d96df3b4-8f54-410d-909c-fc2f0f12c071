"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[253],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3312:(e,t,r)=>{r.d(t,{dp:()=>en.dp});var n=r(9089),s=r(2800),i=r(1388);function a(e,t){if("undefined"==typeof Convex||void 0===Convex.syscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");return JSON.parse(Convex.syscall(e,JSON.stringify(t)))}async function o(e,t){let r;if("undefined"==typeof Convex||void 0===Convex.asyncSyscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");try{r=await Convex.asyncSyscall(e,JSON.stringify(t))}catch(e){if(void 0!==e.data){let t=new s.i(e.message);throw t.data=(0,i.du)(e.data),t}throw Error(e.message)}return JSON.parse(r)}r(5947);var l=Object.defineProperty,c=(e,t,r)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,u=(e,t,r)=>c(e,"symbol"!=typeof t?t+"":t,r);class d{constructor(){u(this,"_isExpression"),u(this,"_value")}}var h=Object.defineProperty,y=(e,t,r)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,f=(e,t,r)=>y(e,"symbol"!=typeof t?t+"":t,r);class p extends d{constructor(e){super(),f(this,"inner"),this.inner=e}serialize(){return this.inner}}function m(e){return e instanceof p?e.serialize():{$literal:(0,i.cy)(e)}}let v={eq(e,t){if("string"!=typeof e)throw Error("The first argument to `q.eq` must be a field name.");return new p({$eq:[m(new p({$field:e})),m(t)]})},or:(...e)=>new p({$or:e.map(m)})};var b=Object.defineProperty,g=(e,t,r)=>t in e?b(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,w=(e,t,r)=>g(e,"symbol"!=typeof t?t+"":t,r);class x{constructor(){w(this,"_isExpression"),w(this,"_value")}}var q=Object.defineProperty,E=(e,t,r)=>t in e?q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,k=(e,t,r)=>E(e,"symbol"!=typeof t?t+"":t,r);class C extends x{constructor(e){super(),k(this,"inner"),this.inner=e}serialize(){return this.inner}}function $(e){return e instanceof C?e.serialize():{$literal:(0,i.cy)(e)}}let j={eq:(e,t)=>new C({$eq:[$(e),$(t)]}),neq:(e,t)=>new C({$neq:[$(e),$(t)]}),lt:(e,t)=>new C({$lt:[$(e),$(t)]}),lte:(e,t)=>new C({$lte:[$(e),$(t)]}),gt:(e,t)=>new C({$gt:[$(e),$(t)]}),gte:(e,t)=>new C({$gte:[$(e),$(t)]}),add:(e,t)=>new C({$add:[$(e),$(t)]}),sub:(e,t)=>new C({$sub:[$(e),$(t)]}),mul:(e,t)=>new C({$mul:[$(e),$(t)]}),div:(e,t)=>new C({$div:[$(e),$(t)]}),mod:(e,t)=>new C({$mod:[$(e),$(t)]}),neg:e=>new C({$neg:$(e)}),and:(...e)=>new C({$and:e.map($)}),or:(...e)=>new C({$or:e.map($)}),not:e=>new C({$not:$(e)}),field:e=>new C({$field:e})};var I=Object.defineProperty,A=(e,t,r)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,P=(e,t,r)=>A(e,"symbol"!=typeof t?t+"":t,r);class S{constructor(){P(this,"_isIndexRange")}}var _=Object.defineProperty,N=(e,t,r)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,O=(e,t,r)=>N(e,"symbol"!=typeof t?t+"":t,r);class T extends S{constructor(e){super(),O(this,"rangeExpressions"),O(this,"isConsumed"),this.rangeExpressions=e,this.isConsumed=!1}static new(){return new T([])}consume(){if(this.isConsumed)throw Error("IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes");this.isConsumed=!0}eq(e,t){return this.consume(),new T(this.rangeExpressions.concat({type:"Eq",fieldPath:e,value:(0,i.cy)(t)}))}gt(e,t){return this.consume(),new T(this.rangeExpressions.concat({type:"Gt",fieldPath:e,value:(0,i.cy)(t)}))}gte(e,t){return this.consume(),new T(this.rangeExpressions.concat({type:"Gte",fieldPath:e,value:(0,i.cy)(t)}))}lt(e,t){return this.consume(),new T(this.rangeExpressions.concat({type:"Lt",fieldPath:e,value:(0,i.cy)(t)}))}lte(e,t){return this.consume(),new T(this.rangeExpressions.concat({type:"Lte",fieldPath:e,value:(0,i.cy)(t)}))}export(){return this.consume(),this.rangeExpressions}}var F=Object.defineProperty,J=(e,t,r)=>t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Q=(e,t,r)=>J(e,"symbol"!=typeof t?t+"":t,r);class R{constructor(){Q(this,"_isSearchFilter")}}function M(e,t,r,n){if(void 0===e)throw TypeError(`Must provide arg ${t} \`${n}\` to \`${r}\``)}var V=Object.defineProperty,z=(e,t,r)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,L=(e,t,r)=>z(e,"symbol"!=typeof t?t+"":t,r);class B extends R{constructor(e){super(),L(this,"filters"),L(this,"isConsumed"),this.filters=e,this.isConsumed=!1}static new(){return new B([])}consume(){if(this.isConsumed)throw Error("SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.");this.isConsumed=!0}search(e,t){return M(e,1,"search","fieldName"),M(t,2,"search","query"),this.consume(),new B(this.filters.concat({type:"Search",fieldPath:e,value:t}))}eq(e,t){return M(e,1,"eq","fieldName"),2!=arguments.length&&M(t,2,"search","value"),this.consume(),new B(this.filters.concat({type:"Eq",fieldPath:e,value:(0,i.cy)(t)}))}export(){return this.consume(),this.filters}}var D=r(3216),W=Object.defineProperty,G=(e,t,r)=>t in e?W(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,U=(e,t,r)=>G(e,"symbol"!=typeof t?t+"":t,r);function Z(e){throw Error("consumed"===e?"This query is closed and can't emit any more values.":"This query has been chained with another operator and can't be reused.")}Symbol.asyncIterator;class H{constructor(e){U(this,"state"),U(this,"tableNameForErrorMessages"),this.state={type:"preparing",query:e},"FullTableScan"===e.source.type?this.tableNameForErrorMessages=e.source.tableName:this.tableNameForErrorMessages=e.source.indexName.split(".")[0]}takeQuery(){if("preparing"!==this.state.type)throw Error("A query can only be chained once and can't be chained after iteration begins.");let e=this.state.query;return this.state={type:"closed"},e}startQuery(){if("executing"===this.state.type)throw Error("Iteration can only begin on a query once.");("closed"===this.state.type||"consumed"===this.state.type)&&Z(this.state.type);let{queryId:e}=a("1.0/queryStream",{query:this.state.query,version:D.r});return this.state={type:"executing",queryId:e},e}closeQuery(){"executing"===this.state.type&&a("1.0/queryCleanup",{queryId:this.state.queryId}),this.state={type:"consumed"}}order(e){M(e,1,"order","order");let t=this.takeQuery();if("Search"===t.source.type)throw Error("Search queries must always be in relevance order. Can not set order manually.");if(null!==t.source.order)throw Error("Queries may only specify order at most once");return t.source.order=e,new H(t)}filter(e){M(e,1,"filter","predicate");let t=this.takeQuery();if(t.operators.length>=256)throw Error("Can't construct query with more than 256 operators");return t.operators.push({filter:$(e(j))}),new H(t)}limit(e){M(e,1,"limit","n");let t=this.takeQuery();return t.operators.push({limit:e}),new H(t)}[Symbol.asyncIterator](){return this.startQuery(),this}async next(){("closed"===this.state.type||"consumed"===this.state.type)&&Z(this.state.type);let e="preparing"===this.state.type?this.startQuery():this.state.queryId,{value:t,done:r}=await o("1.0/queryStreamNext",{queryId:e});return r&&this.closeQuery(),{value:(0,n.du)(t),done:r}}return(){return this.closeQuery(),Promise.resolve({done:!0,value:void 0})}async paginate(e){if(M(e,1,"paginate","options"),"number"!=typeof e?.numItems||e.numItems<0)throw Error(`\`options.numItems\` must be a positive number. Received \`${e?.numItems}\`.`);let t=this.takeQuery(),r=e.numItems,s=e.cursor,i=e?.endCursor??null,a=e.maximumRowsRead??null,{page:l,isDone:c,continueCursor:u,splitCursor:d,pageStatus:h}=await o("1.0/queryPage",{query:t,cursor:s,endCursor:i,pageSize:r,maximumRowsRead:a,maximumBytesRead:e.maximumBytesRead,version:D.r});return{page:l.map(e=>(0,n.du)(e)),isDone:c,continueCursor:u,splitCursor:d,pageStatus:h}}async collect(){let e=[];for await(let t of this)e.push(t);return e}async take(e){M(e,1,"take","n");if(!Number.isInteger(e)||e<0)throw TypeError("Arg 1 `n` to `take` must be a non-negative integer");return this.limit(e).collect()}async first(){let e=await this.take(1);return 0===e.length?null:e[0]}async unique(){let e=await this.take(2);if(0===e.length)return null;if(2===e.length)throw Error(`unique() query returned more than one result from table ${this.tableNameForErrorMessages}:
 [${e[0]._id}, ${e[1]._id}, ...]`);return e[0]}}async function K(e,t){if(validateArg(e,1,"get","id"),"string"!=typeof e)throw Error(`Invalid argument \`id\` for \`db.get\`, expected string but got '${typeof e}': ${e}`);let r={id:convexToJson(e),isSystem:t,version};return jsonToConvex(await performAsyncSyscall("1.0/get",r))}async function X(e,t){if(e.startsWith("_"))throw Error("System tables (prefixed with `_`) are read-only.");return validateArg(e,1,"insert","table"),validateArg(t,2,"insert","value"),jsonToConvex(await performAsyncSyscall("1.0/insert",{table:e,value:convexToJson(t)}))._id}async function Y(e,t){validateArg(e,1,"patch","id"),validateArg(t,2,"patch","value"),await performAsyncSyscall("1.0/shallowMerge",{id:convexToJson(e),value:patchValueToJson(t)})}async function ee(e,t){validateArg(e,1,"replace","id"),validateArg(t,2,"replace","value"),await performAsyncSyscall("1.0/replace",{id:convexToJson(e),value:convexToJson(t)})}async function et(e){validateArg(e,1,"delete","id"),await performAsyncSyscall("1.0/remove",{id:convexToJson(e)})}var er=r(8039);er.v.object({numItems:er.v.number(),cursor:er.v.union(er.v.string(),er.v.null()),endCursor:er.v.optional(er.v.union(er.v.string(),er.v.null())),id:er.v.optional(er.v.number()),maximumRowsRead:er.v.optional(er.v.number()),maximumBytesRead:er.v.optional(er.v.number())});var en=r(2828),es=Object.defineProperty,ei=Object.defineProperty,ea=Object.defineProperty,eo=(e,t,r)=>t in e?ea(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,el=(e,t,r)=>eo(e,"symbol"!=typeof t?t+"":t,r);class ec{constructor(e,t){el(this,"_definition"),el(this,"_name"),this._definition=e,this._name=t,setReferencePath(this,`_reference/childComponent/${t}`)}get exports(){return function e(t,r){return new Proxy({},{get(n,s){if("string"==typeof s)return e(t,[...r,s]);if(s===toReferencePath){let e=`_reference/childComponent/${t}`;for(let t of r)e+=`/${t}`;return e}}})}(this._name,[])}}function eu(e){let t=[];for(let[r,n]of Object.entries(e)){let e;e="string"==typeof n?{type:"leaf",leaf:n}:eu(n),t.push([r,e])}return{type:"branch",branch:t}}function ed(e){return e.map(([e,t,r])=>{let n=null;if(null!==r)for(let[e,t]of(n=[],Object.entries(r)))void 0!==t&&n.push([e,{type:"value",value:JSON.stringify(convexToJson(t))}]);let s=t.componentDefinitionPath;if(!s)throw Error("no .componentPath for component definition "+JSON.stringify(t,null,2));return{name:e,path:s,args:n}})}var eh=Object.defineProperty,ey=(e,t,r)=>t in e?eh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ef=(e,t,r)=>ey(e,"symbol"!=typeof t?t+"":t,r);class ep{constructor(e){ef(this,"indexes"),ef(this,"searchIndexes"),ef(this,"vectorIndexes"),ef(this,"validator"),this.indexes=[],this.searchIndexes=[],this.vectorIndexes=[],this.validator=e}" indexes"(){return this.indexes}index(e,t){return this.indexes.push({indexDescriptor:e,fields:t}),this}searchIndex(e,t){return this.searchIndexes.push({indexDescriptor:e,searchField:t.searchField,filterFields:t.filterFields||[]}),this}vectorIndex(e,t){return this.vectorIndexes.push({indexDescriptor:e,vectorField:t.vectorField,dimensions:t.dimensions,filterFields:t.filterFields||[]}),this}self(){return this}export(){let e=this.validator.json;if("object"!=typeof e)throw Error("Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)");return{indexes:this.indexes,searchIndexes:this.searchIndexes,vectorIndexes:this.vectorIndexes,documentType:e}}}function em(e){return new ep((0,er.d)(e)?e:er.v.object(e))}class ev{constructor(e,t){ef(this,"tables"),ef(this,"strictTableNameTypes"),ef(this,"schemaValidation"),this.tables=e,this.schemaValidation=t?.schemaValidation===void 0||t.schemaValidation}export(){return JSON.stringify({tables:Object.entries(this.tables).map(([e,t])=>{let{indexes:r,searchIndexes:n,vectorIndexes:s,documentType:i}=t.export();return{tableName:e,indexes:r,searchIndexes:n,vectorIndexes:s,documentType:i}}),schemaValidation:this.schemaValidation})}}new ev({_scheduled_functions:em({name:er.v.string(),args:er.v.array(er.v.any()),scheduledTime:er.v.float64(),completedTime:er.v.optional(er.v.float64()),state:er.v.union(er.v.object({kind:er.v.literal("pending")}),er.v.object({kind:er.v.literal("inProgress")}),er.v.object({kind:er.v.literal("success")}),er.v.object({kind:er.v.literal("failed"),error:er.v.string()}),er.v.object({kind:er.v.literal("canceled")}))}),_storage:em({sha256:er.v.string(),size:er.v.float64(),contentType:er.v.optional(er.v.string())})},void 0)},7580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9803:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9946:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:u="",children:d,iconNode:h,...y}=e;return(0,n.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:a?24*Number(i)/Number(s):i,className:o("lucide",u),...!d&&!l(y)&&{"aria-hidden":"true"},...y},[...h.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:l,...c}=r;return(0,n.createElement)(u,{ref:i,iconNode:t,className:o("lucide-".concat(s(a(e))),"lucide-".concat(e),l),...c})});return r.displayName=a(e),r}}}]);