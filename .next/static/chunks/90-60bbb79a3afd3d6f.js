"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[90],{99:(e,t,r)=>{r.d(t,{FJ:()=>y,YF:()=>b}),r(852);var n,i,o,s,l,a,u,d,c,p,h,g,f,m,v,k=r(5649);r(9509),n=new WeakMap,i=new WeakMap,o=new WeakSet,s=function(e){let{sk:t,pk:r,payload:n,...i}=e,o={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>o[e]))},l=function(){let e=localStorage.getItem((0,k.S7)(this,n));return e?JSON.parse(e):{}},a=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,k.S7)(this,n)),!1}};u=new WeakMap,d=new WeakMap,c=new WeakMap,p=new WeakMap,h=new WeakMap,g=new WeakSet,f=function(e,t){let r=Math.random();return!!(r<=(0,k.S7)(this,u).samplingRate&&(void 0===t||r<=t))&&!(0,k.S7)(this,d).isEventThrottled(e)},m=function(){fetch(new URL("/v1/event",(0,k.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,k.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,k.OV)(this,p,[])}).catch(()=>void 0)},v=function(){let e={name:(0,k.S7)(this,c).sdk,version:(0,k.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e};function y(e,t){return{event:"METHOD_CALLED",payload:{method:e,...t}}}function b(e){return{event:"FRAMEWORK_METADATA",eventSamplingRate:.1,payload:e}}},852:(e,t,r)=>{r.d(t,{zz:()=>i});var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},1634:(e,t,r)=>{function n(e){return"clerkError"in e}r.d(t,{$R:()=>n,_r:()=>s,cR:()=>i});var i=class e extends Error{constructor(t,{code:r}){let n="\uD83D\uDD12 Clerk:",i=RegExp(n.replace(" ","\\s*"),"i"),o=t.replace(i,""),s=`${n} ${o.trim()}

(code="${r}")
`;super(s),this.toString=()=>`[${this.name}]
Message:${this.message}`,Object.setPrototypeOf(this,e.prototype),this.code=r,this.message=s,this.clerkRuntimeError=!0,this.name="ClerkRuntimeError"}},o=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function s({packageName:e,customMessages:t}){let r=e,n={...o,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}},1972:(e,t,r)=>{let n;r.d(t,{ED:()=>eX,pc:()=>e5,TS:()=>e4,IC:()=>e6,Rs:()=>e1,cl:()=>t1,Tn:()=>tX,Jl:()=>t5,e3:()=>eH,MZ:()=>tv,Kz:()=>te,ho:()=>tm,hQ:()=>e0,WD:()=>e9,Z5:()=>ts,D_:()=>ta,Wp:()=>ty,wV:()=>tc,g7:()=>th,Jd:()=>tf});var i={};r.r(i),r.d(i,{SWRConfig:()=>eE,default:()=>eU,mutate:()=>et,preload:()=>ed,unstable_serialize:()=>eC,useSWRConfig:()=>eu});var o=r(99),s=(...e)=>{},l=()=>{let e=s,t=s;return{promise:new Promise((r,n)=>{e=r,t=n}),resolve:e,reject:t}};r(852);var a=r(1634),u="reverification-error",d=e=>({clerk_error:{type:"forbidden",reason:u,metadata:{reverification:e}}}),c=e=>e&&"object"==typeof e&&"clerk_error"in e&&e.clerk_error?.type==="forbidden"&&e.clerk_error?.reason===u,p=r(5018),h=r(5649),g=r(2115),f=r(9033),m=Object.prototype.hasOwnProperty;let v=new WeakMap,k=()=>{},y=k(),b=Object,_=e=>e===y,P=e=>"function"==typeof e,S=(e,t)=>({...e,...t}),w=e=>P(e.then),C={},j={},O="undefined",E=typeof window!=O,U=typeof document!=O,M=E&&"Deno"in window,z=()=>E&&typeof window.requestAnimationFrame!=O,I=(e,t)=>{let r=v.get(e);return[()=>!_(t)&&e.get(t)||C,n=>{if(!_(t)){let i=e.get(t);t in j||(j[t]=i),r[5](t,S(i,n),i||C)}},r[6],()=>!_(t)&&t in j?j[t]:!_(t)&&e.get(t)||C]},L=!0,[A,R]=E&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[k,k],T={initFocus:e=>(U&&document.addEventListener("visibilitychange",e),A("focus",e),()=>{U&&document.removeEventListener("visibilitychange",e),R("focus",e)}),initReconnect:e=>{let t=()=>{L=!0,e()},r=()=>{L=!1};return A("online",t),A("offline",r),()=>{R("online",t),R("offline",r)}}},W=!g.useId,x=!E||M,N=e=>z()?window.requestAnimationFrame(e):setTimeout(e,1),D=x?g.useEffect:g.useLayoutEffect,F="undefined"!=typeof navigator&&navigator.connection,B=!x&&F&&(["slow-2g","2g"].includes(F.effectiveType)||F.saveData),$=new WeakMap,V=e=>b.prototype.toString.call(e),K=(e,t)=>e==="[object ".concat(t,"]"),J=0,q=e=>{let t,r,n=typeof e,i=V(e),o=K(i,"Date"),s=K(i,"RegExp"),l=K(i,"Object");if(b(e)!==e||o||s)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=$.get(e))return t;if(t=++J+"~",$.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=q(e[r])+",";$.set(e,t)}if(l){t="#";let n=b.keys(e).sort();for(;!_(r=n.pop());)_(e[r])||(t+=r+":"+q(e[r])+",");$.set(e,t)}}return t},G=e=>{if(P(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?q(e):"",t]},Q=0,Z=()=>++Q;async function H(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i,o,s]=t,l=S({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),a=l.populateCache,u=l.rollbackOnError,d=l.optimisticData,c=e=>"function"==typeof u?u(e):!1!==u,p=l.throwOnError;if(P(i)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&i(n.get(t)._k)&&e.push(t);return Promise.all(e.map(h))}return h(i);async function h(e){let r,[i]=G(e);if(!i)return;let[s,u]=I(n,i),[h,g,f,m]=v.get(n),k=()=>{let t=h[i];return(P(l.revalidate)?l.revalidate(s().data,e):!1!==l.revalidate)&&(delete f[i],delete m[i],t&&t[0])?t[0](2).then(()=>s().data):s().data};if(t.length<3)return k();let b=o,S=!1,C=Z();g[i]=[C,0];let j=!_(d),O=s(),E=O.data,U=O._c,M=_(U)?E:U;if(j&&u({data:d=P(d)?d(M,E):d,_c:M}),P(b))try{b=b(M)}catch(e){r=e,S=!0}if(b&&w(b)){if(b=await b.catch(e=>{r=e,S=!0}),C!==g[i][0]){if(S)throw r;return b}S&&j&&c(r)&&(a=!0,u({data:M,_c:y}))}if(a&&!S&&(P(a)?u({data:a(b,M),error:y,_c:y}):u({data:b,error:y,_c:y})),g[i][1]=Z(),Promise.resolve(k()).then(()=>{u({_c:y})}),S){if(p)throw r;return}return b}}let Y=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},X=(e,t)=>{if(!v.has(e)){let r=S(T,t),n=Object.create(null),i=H.bind(y,e),o=k,s=Object.create(null),l=(e,t)=>{let r=s[e]||[];return s[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},a=(t,r,n)=>{e.set(t,r);let i=s[t];if(i)for(let e of i)e(r,n)},u=()=>{if(!v.has(e)&&(v.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,a,l]),!x)){let t=r.initFocus(setTimeout.bind(y,Y.bind(y,n,0))),i=r.initReconnect(setTimeout.bind(y,Y.bind(y,n,1)));o=()=>{t&&t(),i&&i(),v.delete(e)}}};return u(),[e,i,u,o]}return[e,v.get(e)[4]]},[ee,et]=X(new Map),er=S({onLoadingSlow:k,onSuccess:k,onError:k,onErrorRetry:(e,t,r,n,i)=>{let o=r.errorRetryCount,s=i.retryCount,l=~~((Math.random()+.5)*(1<<(s<8?s:8)))*r.errorRetryInterval;(_(o)||!(s>o))&&setTimeout(n,l,i)},onDiscarded:k,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:B?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:B?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(m.call(t,n)&&++i&&!m.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:ee,mutate:et,fallback:{}},{isOnline:()=>L,isVisible:()=>{let e=U&&document.visibilityState;return _(e)||"hidden"!==e}}),en=(e,t)=>{let r=S(e,t);if(t){let{use:n,fallback:i}=e,{use:o,fallback:s}=t;n&&o&&(r.use=n.concat(o)),i&&s&&(r.fallback=S(i,s))}return r},ei=(0,g.createContext)({}),eo="$inf$",es=E&&window.__SWR_DEVTOOLS_USE__,el=es?window.__SWR_DEVTOOLS_USE__:[],ea=e=>P(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],eu=()=>S(er,(0,g.useContext)(ei)),ed=(e,t)=>{let[r,n]=G(e),[,,,i]=v.get(ee);if(i[r])return i[r];let o=t(n);return i[r]=o,o},ec=el.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=G(t),[,,,i]=v.get(ee);if(n.startsWith(eo))return r(...e);let o=i[n];return _(o)?r(...e):(delete i[n],o)});return e(t,i,n)}),ep=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}},eh=(e,t)=>(...r)=>{let[n,i,o]=ea(r),s=(o.use||[]).concat(t);return e(n,i,{...o,use:s})};es&&(window.__SWR_DEVTOOLS_REACT__=g);let eg=()=>{},ef=eg(),em=Object,ev=e=>e===ef,ek=e=>"function"==typeof e,ey=new WeakMap,eb=e=>em.prototype.toString.call(e),e_=(e,t)=>e===`[object ${t}]`,eP=0,eS=e=>{let t,r,n=typeof e,i=eb(e),o=e_(i,"Date"),s=e_(i,"RegExp"),l=e_(i,"Object");if(em(e)!==e||o||s)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=ey.get(e))return t;if(t=++eP+"~",ey.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eS(e[r])+",";ey.set(e,t)}if(l){t="#";let n=em.keys(e).sort();for(;!ev(r=n.pop());)ev(e[r])||(t+=r+":"+eS(e[r])+",");ey.set(e,t)}}return t},ew=e=>{if(ek(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eS(e):"",t]},eC=e=>ew(e)[0],ej=g.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),eO={dedupe:!0},eE=b.defineProperty(e=>{let{value:t}=e,r=(0,g.useContext)(ei),n=P(t),i=(0,g.useMemo)(()=>n?t(r):t,[n,r,t]),o=(0,g.useMemo)(()=>n?i:en(r,i),[n,r,i]),s=i&&i.provider,l=(0,g.useRef)(y);s&&!l.current&&(l.current=X(s(o.cache||ee),i));let a=l.current;return a&&(o.cache=a[0],o.mutate=a[1]),D(()=>{if(a)return a[2]&&a[2](),a[3]},[]),(0,g.createElement)(ei.Provider,S(e,{value:o}))},"defaultValue",{value:er}),eU=(n=(e,t,r)=>{let{cache:n,compare:i,suspense:o,fallbackData:s,revalidateOnMount:l,revalidateIfStale:a,refreshInterval:u,refreshWhenHidden:d,refreshWhenOffline:c,keepPreviousData:p}=r,[h,m,k,b]=v.get(n),[C,j]=G(e),O=(0,g.useRef)(!1),E=(0,g.useRef)(!1),U=(0,g.useRef)(C),M=(0,g.useRef)(t),z=(0,g.useRef)(r),L=()=>z.current,A=()=>L().isVisible()&&L().isOnline(),[R,T,F,B]=I(n,C),$=(0,g.useRef)({}).current,V=_(s)?_(r.fallback)?y:r.fallback[C]:s,K=(e,t)=>{for(let r in $)if("data"===r){if(!i(e[r],t[r])&&(!_(e[r])||!i(en,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},J=(0,g.useMemo)(()=>{let e=!!C&&!!t&&(_(l)?!L().isPaused()&&!o&&!1!==a:l),r=t=>{let r=S(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=R(),i=B(),s=r(n),u=n===i?s:r(i),d=s;return[()=>{let e=r(R());return K(e,d)?(d.data=e.data,d.isLoading=e.isLoading,d.isValidating=e.isValidating,d.error=e.error,d):(d=e,e)},()=>u]},[n,C]),q=(0,f.useSyncExternalStore)((0,g.useCallback)(e=>F(C,(t,r)=>{K(r,t)||e()}),[n,C]),J[0],J[1]),Q=!O.current,Y=h[C]&&h[C].length>0,X=q.data,ee=_(X)?V&&w(V)?ej(V):V:X,et=q.error,er=(0,g.useRef)(ee),en=p?_(X)?_(er.current)?ee:er.current:X:ee,ei=(!Y||!!_(et))&&(Q&&!_(l)?l:!L().isPaused()&&(o?!_(ee)&&a:_(ee)||a)),eo=!!(C&&t&&Q&&ei),es=_(q.isValidating)?eo:q.isValidating,el=_(q.isLoading)?eo:q.isLoading,ea=(0,g.useCallback)(async e=>{let t,n,o=M.current;if(!C||!o||E.current||L().isPaused())return!1;let s=!0,l=e||{},a=!k[C]||!l.dedupe,u=()=>W?!E.current&&C===U.current&&O.current:C===U.current,d={isValidating:!1,isLoading:!1},c=()=>{T(d)},p=()=>{let e=k[C];e&&e[1]===n&&delete k[C]},g={isValidating:!0};_(R().data)&&(g.isLoading=!0);try{if(a&&(T(g),r.loadingTimeout&&_(R().data)&&setTimeout(()=>{s&&u()&&L().onLoadingSlow(C,r)},r.loadingTimeout),k[C]=[o(j),Z()]),[t,n]=k[C],t=await t,a&&setTimeout(p,r.dedupingInterval),!k[C]||k[C][1]!==n)return a&&u()&&L().onDiscarded(C),!1;d.error=y;let e=m[C];if(!_(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return c(),a&&u()&&L().onDiscarded(C),!1;let l=R().data;d.data=i(l,t)?l:t,a&&u()&&L().onSuccess(t,C,r)}catch(r){p();let e=L(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,a&&u()&&(e.onError(r,C,e),(!0===t||P(t)&&t(r))&&(!L().revalidateOnFocus||!L().revalidateOnReconnect||A())&&e.onErrorRetry(r,C,e,e=>{let t=h[C];t&&t[0]&&t[0](3,e)},{retryCount:(l.retryCount||0)+1,dedupe:!0})))}return s=!1,c(),!0},[C,n]),eu=(0,g.useCallback)((...e)=>H(n,U.current,...e),[]);if(D(()=>{M.current=t,z.current=r,_(X)||(er.current=X)}),D(()=>{if(!C)return;let e=ea.bind(y,eO),t=0;L().revalidateOnFocus&&(t=Date.now()+L().focusThrottleInterval);let r=ep(C,h,(r,n={})=>{if(0==r){let r=Date.now();L().revalidateOnFocus&&r>t&&A()&&(t=r+L().focusThrottleInterval,e())}else if(1==r)L().revalidateOnReconnect&&A()&&e();else if(2==r)return ea();else if(3==r)return ea(n)});return E.current=!1,U.current=C,O.current=!0,T({_k:j}),ei&&!k[C]&&(_(ee)||x?e():N(e)),()=>{E.current=!0,r()}},[C]),D(()=>{let e;function t(){let t=P(u)?u(R().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!R().error&&(d||L().isVisible())&&(c||L().isOnline())?ea(eO).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,d,c,C]),(0,g.useDebugValue)(en),o&&_(ee)&&C){if(!W&&x)throw Error("Fallback data is required when using Suspense in SSR.");M.current=t,z.current=r,E.current=!1;let e=b[C];if(_(e)||ej(eu(e)),_(et)){let e=ea(eO);_(en)||(e.status="fulfilled",e.value=!0),ej(e)}else throw et}return{mutate:eu,get data(){return $.data=!0,en},get error(){return $.error=!0,et},get isValidating(){return $.isValidating=!0,es},get isLoading(){return $.isLoading=!0,el}}},function(...e){let t=eu(),[r,i,o]=ea(e),s=en(t,o),l=n,{use:a}=s,u=(a||[]).concat(ec);for(let e=u.length;e--;)l=u[e](l);return l(r,i||s.fetcher||null,s)}),eM=()=>{},ez=eM(),eI=Object,eL=e=>e===ez,eA=e=>"function"==typeof e,eR=new WeakMap,eT=e=>eI.prototype.toString.call(e),eW=(e,t)=>e===`[object ${t}]`,ex=0,eN=e=>{let t,r,n=typeof e,i=eT(e),o=eW(i,"Date"),s=eW(i,"RegExp"),l=eW(i,"Object");if(eI(e)!==e||o||s)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=eR.get(e))return t;if(t=++ex+"~",eR.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eN(e[r])+",";eR.set(e,t)}if(l){t="#";let n=eI.keys(e).sort();for(;!eL(r=n.pop());)eL(e[r])||(t+=r+":"+eN(e[r])+",");eR.set(e,t)}}return t},eD=e=>{if(eA(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eN(e):"",t]},eF=e=>eD(e?e(0,null):null)[0],eB=Promise.resolve(),e$=eh(eU,e=>(t,r,n)=>{let i,o=(0,g.useRef)(!1),{cache:s,initialSize:l=1,revalidateAll:a=!1,persistSize:u=!1,revalidateFirstPage:d=!0,revalidateOnMount:c=!1,parallel:p=!1}=n,[,,,h]=v.get(ee);try{(i=eF(t))&&(i=eo+i)}catch(e){}let[m,k,b]=I(s,i),S=(0,g.useCallback)(()=>_(m()._l)?l:m()._l,[s,i,l]);(0,f.useSyncExternalStore)((0,g.useCallback)(e=>i?b(i,()=>{e()}):()=>{},[s,i]),S,S);let w=(0,g.useCallback)(()=>{let e=m()._l;return _(e)?l:e},[i,l]),C=(0,g.useRef)(w());D(()=>{if(!o.current){o.current=!0;return}i&&k({_l:u?C.current:w()})},[i,s]);let j=c&&!o.current,O=e(i,async e=>{let i=m()._i,o=m()._r;k({_r:y});let l=[],u=w(),[c]=I(s,e),g=c().data,f=[],v=null;for(let e=0;e<u;++e){let[u,c]=G(t(e,p?null:v));if(!u)break;let[m,k]=I(s,u),y=m().data,b=a||i||_(y)||d&&!e&&!_(g)||j||g&&!_(g[e])&&!n.compare(g[e],y);if(r&&("function"==typeof o?o(y,c):b)){let t=async()=>{if(u in h){let e=h[u];delete h[u],y=await e}else y=await r(c);k({data:y,_k:c}),l[e]=y};p?f.push(t):await t()}else l[e]=y;p||(v=y)}return p&&await Promise.all(f.map(e=>e())),k({_i:y}),l},n),E=(0,g.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},n=!1!==r.revalidate;return i?(n&&(_(e)?k({_i:!0,_r:r.revalidate}):k({_i:!1,_r:r.revalidate})),arguments.length?O.mutate(e,{...r,revalidate:n}):O.mutate()):eB},[i,s]),U=(0,g.useCallback)(e=>{let r;if(!i)return eB;let[,n]=I(s,i);if(P(e)?r=e(w()):"number"==typeof e&&(r=e),"number"!=typeof r)return eB;n({_l:r}),C.current=r;let o=[],[l]=I(s,i),a=null;for(let e=0;e<r;++e){let[r]=G(t(e,a)),[n]=I(s,r),i=r?n().data:y;if(_(i))return E(l().data);o.push(i),a=i}return E(o)},[i,s,E,w]);return{size:w(),setSize:U,mutate:E,get data(){return O.data},get error(){return O.error},get isValidating(){return O.isValidating},get isLoading(){return O.isLoading}}});var eV=Object.prototype.hasOwnProperty;function eK(e,t,r){for(r of e.keys())if(eJ(r,t))return r}function eJ(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&eJ(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=eK(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=eK(t,i))||!eJ(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(eV.call(e,r)&&++n&&!eV.call(t,r)||!(r in t)||!eJ(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}let eq=W?e=>{e()}:g.startTransition,eG=e=>{let[,t]=(0,g.useState)({}),r=(0,g.useRef)(!1),n=(0,g.useRef)(e),i=(0,g.useRef)({data:!1,error:!1,isValidating:!1}),o=(0,g.useCallback)(e=>{let o=!1,s=n.current;for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&s[t]!==e[t]&&(s[t]=e[t],i.current[t]&&(o=!0));o&&!r.current&&t({})},[]);return D(()=>(r.current=!1,()=>{r.current=!0})),[n,i.current,o]},eQ=eh(eU,()=>(e,t,r={})=>{let{mutate:n}=eu(),i=(0,g.useRef)(e),o=(0,g.useRef)(t),s=(0,g.useRef)(r),l=(0,g.useRef)(0),[a,u,d]=eG({data:y,error:y,isMutating:!1}),c=a.current,p=(0,g.useCallback)(async(e,t)=>{let[r,a]=G(i.current);if(!o.current)throw Error("Can’t trigger the mutation: missing fetcher.");if(!r)throw Error("Can’t trigger the mutation: missing key.");let u=S(S({populateCache:!1,throwOnError:!0},s.current),t),c=Z();l.current=c,d({isMutating:!0});try{let t=await n(r,o.current(a,{arg:e}),S(u,{throwOnError:!0}));return l.current<=c&&(eq(()=>d({data:t,isMutating:!1,error:void 0})),null==u.onSuccess||u.onSuccess.call(u,t,r,u)),t}catch(e){if(l.current<=c&&(eq(()=>d({error:e,isMutating:!1})),null==u.onError||u.onError.call(u,e,r,u),u.throwOnError))throw e}},[]),h=(0,g.useCallback)(()=>{l.current=Z(),d({data:y,error:y,isMutating:!1})},[]);return D(()=>{i.current=e,o.current=t,s.current=r}),{trigger:p,reset:h,get data(){return u.data=!0,c.data},get error(){return u.error=!0,c.error},get isMutating(){return u.isMutating=!0,c.isMutating}}});function eZ(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var eH=(e,t)=>{let{assertCtxFn:r=eZ}=t||{},n=g.createContext(void 0);return n.displayName=e,[n,()=>{let t=g.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=g.useContext(n);return e?e.value:{}}]},eY={};(0,h.VA)(eY,{useSWR:()=>eU,useSWRInfinite:()=>e$}),(0,h.ie)(eY,i);var[eX,e0]=eH("ClerkInstanceContext"),[e1,e2]=eH("UserContext"),[e5,e9]=eH("ClientContext"),[e6,e3]=eH("SessionContext"),[e8,e7]=(g.createContext({}),eH("OrganizationContext")),e4=({children:e,organization:t,swrConfig:r})=>g.createElement(eY.SWRConfig,{value:r},g.createElement(e8.Provider,{value:{value:{organization:t}}},e));function te(e){if(!g.useContext(eX)){if("function"==typeof e)return void e();throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function tt(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var tr=(e,t)=>{let r="boolean"==typeof e&&e,n=(0,g.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),i=(0,g.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),o={};for(let n of Object.keys(t))o[n]=r?t[n]:e?.[n]??t[n];return{...o,initialPage:n.current,pageSize:i.current}},tn={dedupingInterval:6e4,focusThrottleInterval:12e4},ti=(e,t,r,n)=>{let[i,o]=(0,g.useState)(e.initialPage??1),s=(0,g.useRef)(e.initialPage??1),l=(0,g.useRef)(e.pageSize??10),a=r.enabled??!0,u="cache"===r.__experimental_mode,d=r.infinite??!1,c=r.keepPreviousData??!1,p={...n,...e,initialPage:i,pageSize:l.current},{data:h,isValidating:f,isLoading:m,error:v,mutate:k}=eU(!d&&a&&(u||t)?p:null,!u&&t?r=>{let i=tt(r,n);return t({...e,...i})}:null,{keepPreviousData:c,...tn}),{data:y,isLoading:b,isValidating:_,error:P,size:S,setSize:w,mutate:C}=e$(t=>d&&a?{...e,...n,initialPage:s.current+t,pageSize:l.current}:null,e=>{let r=tt(e,n);return t?.(r)},tn),j=(0,g.useMemo)(()=>d?S:i,[d,S,i]),O=(0,g.useCallback)(e=>d?void w(e):o(e),[w]),E=(0,g.useMemo)(()=>d?y?.map(e=>e?.data).flat()??[]:h?.data??[],[d,h,y]),U=(0,g.useMemo)(()=>d?y?.[y?.length-1]?.total_count||0:h?.total_count??0,[d,h,y]),M=d?b:m,z=d?_:f,I=(d?P:v)??null,L=(0,g.useCallback)(()=>{O(e=>Math.max(0,e+1))},[O]),A=(0,g.useCallback)(()=>{O(e=>Math.max(0,e-1))},[O]),R=(s.current-1)*l.current,T=Math.ceil((U-R)/l.current),W=U-R*l.current>j*l.current,x=(j-1)*l.current>R*l.current,N=d?e=>C(e,{revalidate:!1}):e=>k(e,{revalidate:!1});return{data:E,count:U,error:I,isLoading:M,isFetching:z,isError:!!I,page:j,pageCount:T,fetchPage:O,fetchNext:L,fetchPrevious:A,hasNextPage:W,hasPreviousPage:x,revalidate:d?()=>C():()=>k(),setData:N}},to={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function ts(e){var t,r;let{domains:n,membershipRequests:i,memberships:s,invitations:l,subscriptions:a}=e||{};te("useOrganization");let{organization:u}=e7(),d=e3(),c=tr(n,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),p=tr(i,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),h=tr(s,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1,query:void 0}),g=tr(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),f=tr(a,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),m=e0();m.telemetry?.record((0,o.FJ)("useOrganization"));let v=void 0===n?void 0:{initialPage:c.initialPage,pageSize:c.pageSize,enrollmentMode:c.enrollmentMode},k=void 0===i?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,status:p.status},y=void 0===s?void 0:{initialPage:h.initialPage,pageSize:h.pageSize,role:h.role,query:h.query},b=void 0===l?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,status:g.status},_=void 0===a?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,orgId:u?.id},P=ti({...v},u?.getDomains,{keepPreviousData:c.keepPreviousData,infinite:c.infinite,enabled:!!v},{type:"domains",organizationId:u?.id}),S=ti({...k},u?.getMembershipRequests,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!k},{type:"membershipRequests",organizationId:u?.id}),w=ti(y||{},u?.getMemberships,{keepPreviousData:h.keepPreviousData,infinite:h.infinite,enabled:!!y},{type:"members",organizationId:u?.id}),C=ti({...b},u?.getInvitations,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!b},{type:"invitations",organizationId:u?.id}),j=ti({..._},u?.getSubscriptions,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!_},{type:"subscriptions",organizationId:u?.id});return void 0===u?{isLoaded:!1,organization:void 0,membership:void 0,domains:to,membershipRequests:to,memberships:to,invitations:to,subscriptions:to}:null===u?{isLoaded:!0,organization:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null,subscriptions:null}:!m.loaded&&u?{isLoaded:!0,organization:u,membership:void 0,domains:to,membershipRequests:to,memberships:to,invitations:to,subscriptions:to}:{isLoaded:m.loaded,organization:u,membership:(t=d.user.organizationMemberships,r=u.id,t.find(e=>e.organization.id===r)),domains:P,membershipRequests:S,memberships:w,invitations:C,subscriptions:j}}var tl={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function ta(e){let{userMemberships:t,userInvitations:r,userSuggestions:n}=e||{};te("useOrganizationList");let i=tr(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),s=tr(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),l=tr(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),a=e0(),u=e2();a.telemetry?.record((0,o.FJ)("useOrganizationList"));let d=void 0===t?void 0:{initialPage:i.initialPage,pageSize:i.pageSize},c=void 0===r?void 0:{initialPage:s.initialPage,pageSize:s.pageSize,status:s.status},p=void 0===n?void 0:{initialPage:l.initialPage,pageSize:l.pageSize,status:l.status},h=!!(a.loaded&&u),g=ti(d||{},u?.getOrganizationMemberships,{keepPreviousData:i.keepPreviousData,infinite:i.infinite,enabled:!!d},{type:"userMemberships",userId:u?.id}),f=ti({...c},u?.getOrganizationInvitations,{keepPreviousData:s.keepPreviousData,infinite:s.infinite,enabled:!!c},{type:"userInvitations",userId:u?.id}),m=ti({...p},u?.getOrganizationSuggestions,{keepPreviousData:l.keepPreviousData,infinite:l.infinite,enabled:!!p},{type:"userSuggestions",userId:u?.id});return h?{isLoaded:h,setActive:a.setActive,createOrganization:a.createOrganization,userMemberships:g,userInvitations:f,userSuggestions:m}:{isLoaded:!1,createOrganization:void 0,setActive:void 0,userMemberships:tl,userInvitations:tl,userSuggestions:tl}}var tu="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,td="useSession",tc=(e={})=>{te(td);let t=e3(),r=e0();if(r.telemetry?.record((0,o.FJ)(td)),void 0===t)return{isLoaded:!1,isSignedIn:void 0,session:void 0};let n=t?.status==="pending"&&(e.treatPendingAsSignedOut??r.__internal_getOption("treatPendingAsSignedOut"));return null===t||n?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:t}},tp="useSessionList",th=()=>{te(tp);let e=e0(),t=e9(),r=e0();return(r.telemetry?.record((0,o.FJ)(tp)),t)?{isLoaded:!0,sessions:t.sessions,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setActive:void 0}},tg="useUser";function tf(){te(tg);let e=e2(),t=e0();return(t.telemetry?.record((0,o.FJ)(tg)),void 0===e)?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var tm=()=>(te("useClerk"),e0()),tv=eJ;async function tk(e){try{let t=await e;if(t instanceof Response)return t.json();return t}catch(e){if((0,a.$R)(e)&&e.errors.find(({code:e})=>"session_reverification_required"===e))return d();throw e}}var ty=(e,t)=>{let{__internal_openReverification:r,telemetry:n}=tm(),i=(0,g.useRef)(e),s=(0,g.useRef)(t);return n?.record((0,o.FJ)("useReverification",{onNeedsReverification:!!t?.onNeedsReverification})),tu(()=>{i.current=e,s.current=t}),(0,g.useCallback)((...e)=>(function(e){return function(t){return async(...r)=>{let n=await tk(t(...r));if(c(n)){let i=l(),o=(0,p.D)(n.clerk_error.metadata?.reverification),s=o?o().level:void 0,u=()=>{i.reject(new a.cR("User cancelled attempted verification",{code:"reverification_cancelled"}))},d=()=>{i.resolve(!0)};void 0===e.onNeedsReverification?e.openUIComponent?.({level:s,afterVerification:d,afterVerificationCancelled:u}):e.onNeedsReverification({cancel:u,complete:d,level:s}),await i.promise,n=await tk(t(...r))}return n}}})({openUIComponent:r,telemetry:n,...s.current})(i.current)(...e),[r,n])};function tb({hookName:e,resourceType:t,useFetcher:r}){return function(n){let{for:i,...s}=n;te(e);let l=r(i),a=tr(s,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,__experimental_mode:void 0}),u=e0(),d=e2(),{organization:c}=e7();u.telemetry?.record((0,o.FJ)(e));let p=void 0===s?void 0:{initialPage:a.initialPage,pageSize:a.pageSize,..."organization"===i?{orgId:c?.id}:{}},h=!!(u.loaded&&d);return ti(p||{},l,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!p&&h,__experimental_mode:a.__experimental_mode},{type:t,userId:d?.id,..."organization"===i?{orgId:c?.id}:{}})}}tb({hookName:"useStatements",resourceType:"commerce-statements",useFetcher:()=>e0().billing.getStatements}),tb({hookName:"usePaymentAttempts",resourceType:"commerce-payment-attempts",useFetcher:()=>e0().billing.getPaymentAttempts}),tb({hookName:"usePaymentMethods",resourceType:"commerce-payment-methods",useFetcher:e=>{let{organization:t}=e7(),r=e2();return"organization"===e?t?.getPaymentSources:r?.getPaymentSources}}),tb({hookName:"useSubscriptionItems",resourceType:"commerce-subscription-items",useFetcher:()=>e0().billing.getSubscriptions});var t_=e=>{let t=(0,g.useRef)(e);return(0,g.useEffect)(()=>{t.current=e},[e]),t.current},tP=(e,t,r)=>{let n=!!r,i=(0,g.useRef)(r);(0,g.useEffect)(()=>{i.current=r},[r]),(0,g.useEffect)(()=>{if(!n||!e)return()=>{};let r=(...e)=>{i.current&&i.current(...e)};return e.on(t,r),()=>{e.off(t,r)}},[n,t,e,i])},tS=g.createContext(null);tS.displayName="ElementsContext";var tw=(e,t)=>{if(!e)throw Error(`Could not find Elements context; You need to wrap the part of your app that ${t} in an <Elements> provider.`);return e},tC=({stripe:e,options:t,children:r})=>{let n=g.useMemo(()=>tM(e),[e]),[i,o]=g.useState(()=>({stripe:"sync"===n.tag?n.stripe:null,elements:"sync"===n.tag?n.stripe.elements(t):null}));g.useEffect(()=>{let e=!0,r=e=>{o(r=>r.stripe?r:{stripe:e,elements:e.elements(t)})};return"async"!==n.tag||i.stripe?"sync"!==n.tag||i.stripe||r(n.stripe):n.stripePromise.then(t=>{t&&e&&r(t)}),()=>{e=!1}},[n,i,t]);let s=t_(e);g.useEffect(()=>{null!==s&&s!==e&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[s,e]);let l=t_(t);return g.useEffect(()=>{if(!i.elements)return;let e=tA(t,l,["clientSecret","fonts"]);e&&i.elements.update(e)},[t,l,i.elements]),g.createElement(tS.Provider,{value:i},r)},tj=e=>tw(g.useContext(tS),e),tO=()=>{let{elements:e}=tj("calls useElements()");return e},tE="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",tU=(e,t=tE)=>{if(null===e||tL(e))return e;throw Error(t)},tM=(e,t=tE)=>{if(tI(e))return{tag:"async",stripePromise:Promise.resolve(e).then(e=>tU(e,t))};let r=tU(e,t);return null===r?{tag:"empty"}:{tag:"sync",stripe:r}},tz=e=>null!==e&&"object"==typeof e,tI=e=>tz(e)&&"function"==typeof e.then,tL=e=>tz(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment,tA=(e,t,r)=>tz(e)?Object.keys(e).reduce((n,i)=>{let o=!tz(t)||!tT(e[i],t[i]);return r.includes(i)?(o&&console.warn(`Unsupported prop change: options.${i} is not a mutable property.`),n):o?{...n||{},[i]:e[i]}:n},null):null,tR="[object Object]",tT=(e,t)=>{if(!tz(e)||!tz(t))return e===t;let r=Array.isArray(e);if(r!==Array.isArray(t))return!1;let n=Object.prototype.toString.call(e)===tR;if(n!==(Object.prototype.toString.call(t)===tR))return!1;if(!n&&!r)return e===t;let i=Object.keys(e),o=Object.keys(t);if(i.length!==o.length)return!1;let s={};for(let e=0;e<i.length;e+=1)s[i[e]]=!0;for(let e=0;e<o.length;e+=1)s[o[e]]=!0;let l=Object.keys(s);return l.length===i.length&&l.every(r=>tT(e[r],t[r]))},tW=()=>{let{stripe:e}=tx("calls useStripe()");return e},tx=e=>tw(g.useContext(tS),e),tN=e=>e.charAt(0).toUpperCase()+e.slice(1),tD=((e,t)=>{let r=`${tN(e)}Element`,n=t?e=>{tx(`mounts <${r}>`);let{id:t,className:n}=e;return g.createElement("div",{id:t,className:n})}:({id:t,className:n,fallback:i,options:o={},onBlur:s,onFocus:l,onReady:a,onChange:u,onEscape:d,onClick:c,onLoadError:p,onLoaderStart:h,onNetworksChange:f,onConfirm:m,onCancel:v,onShippingAddressChange:k,onShippingRateChange:y})=>{let b,_=tx(`mounts <${r}>`),P="elements"in _?_.elements:null,[S,w]=g.useState(null),C=g.useRef(null),j=g.useRef(null),[O,E]=(0,g.useState)(!1);tP(S,"blur",s),tP(S,"focus",l),tP(S,"escape",d),tP(S,"click",c),tP(S,"loaderror",p),tP(S,"loaderstart",h),tP(S,"networkschange",f),tP(S,"confirm",m),tP(S,"cancel",v),tP(S,"shippingaddresschange",k),tP(S,"shippingratechange",y),tP(S,"change",u),a&&(b=()=>{E(!0),a(S)}),tP(S,"ready",b),g.useLayoutEffect(()=>{if(null===C.current&&null!==j.current&&P){let t=null;P&&(t=P.create(e,o)),C.current=t,w(t),t&&t.mount(j.current)}},[P,o]);let U=t_(o);return g.useEffect(()=>{if(!C.current)return;let e=tA(o,U,["paymentRequest"]);e&&"update"in C.current&&C.current.update(e)},[o,U]),g.useLayoutEffect(()=>()=>{if(C.current&&"function"==typeof C.current.destroy)try{C.current.destroy(),C.current=null}catch{}},[]),g.createElement(g.Fragment,null,!O&&i,g.createElement("div",{id:t,className:n,ref:j}))};return n.displayName=r,n.__elementType=e,n})("payment","undefined"==typeof window),[tF,tB]=eH("StripeLibsContext"),t$=({children:e})=>{let t=tm(),{data:r}=eU("clerk-stripe-sdk",async()=>({loadStripe:await t.__internal_loadStripeJs()}),{keepPreviousData:!0,revalidateOnFocus:!1,dedupingInterval:1/0});return g.createElement(tF.Provider,{value:{value:r||null}},e)},tV=()=>tm().__unstable__environment,tK=e=>{let{organization:t}=ts(),{user:r}=tf(),n="org"===e?t:r,i=tB(),{data:o,trigger:s}=eQ({key:"commerce-payment-source-initialize",resourceId:n?.id},()=>n?.initializePaymentSource({gateway:"stripe"})),l=tV();(0,g.useEffect)(()=>{s().catch(()=>{})},[]);let a=o?.externalGatewayId,u=o?.externalClientSecret,d=o?.paymentMethodOrder,c=l?.commerceSettings.billing.stripePublishableKey,{data:p}=eU(i&&a&&c?{key:"stripe-sdk",externalGatewayId:a,stripePublishableKey:c}:null,({stripePublishableKey:e,externalGatewayId:t})=>i?.loadStripe(e,{stripeAccount:t}),{keepPreviousData:!0,revalidateOnFocus:!1,dedupingInterval:6e4});return{stripe:p,initializePaymentSource:s,externalClientSecret:u,paymentMethodOrder:d}},[tJ,tq]=eH("PaymentElementContext"),[tG,tQ]=eH("StripeUtilsContext"),tZ=({children:e})=>{let t=tW(),r=tO();return g.createElement(tG.Provider,{value:{value:{stripe:t,elements:r}}},e)},tH=({children:e})=>g.createElement(tG.Provider,{value:{value:{}}},e),tY=({children:e,...t})=>{let r=tK(t.for),[n,i]=(0,g.useState)(!1);return g.createElement(tJ.Provider,{value:{value:{...t,...r,setIsPaymentElementReady:i,isPaymentElementReady:n}}},e)},tX=({children:e,...t})=>g.createElement(t$,null,g.createElement(tY,{...t},g.createElement(t0,null,e))),t0=e=>{let{stripe:t,externalClientSecret:r,stripeAppearance:n}=tq();return t&&r?g.createElement(tC,{key:r,stripe:t,options:{clientSecret:r,appearance:{variables:n}}},g.createElement(tZ,null,e.children)):g.createElement(tH,null,e.children)},t1=({fallback:e})=>{let{setIsPaymentElementReady:t,paymentMethodOrder:r,checkout:n,stripe:i,externalClientSecret:o,paymentDescription:s,for:l}=tq(),a=tV(),u=(0,g.useMemo)(()=>{if(n)return{recurringPaymentRequest:{paymentDescription:s||"",managementURL:"org"===l?a?.displayConfig.organizationProfileUrl||"":a?.displayConfig.userProfileUrl||"",regularBilling:{amount:n.totals.totalDueNow?.amount||n.totals.grandTotal.amount,label:n.plan.name,recurringPaymentIntervalUnit:"annual"===n.planPeriod?"year":"month"}}}},[n,s,l,a]),d=(0,g.useMemo)(()=>({layout:{type:"tabs",defaultCollapsed:!1},paymentMethodOrder:r,applePay:u}),[u,r]),c=(0,g.useCallback)(()=>{t(!0)},[t]);return i&&o?g.createElement(tD,{fallback:e,onReady:c,options:d}):g.createElement(g.Fragment,null,e)},t2=()=>{throw Error("Clerk: Unable to submit, Stripe libraries are not yet loaded. Be sure to check `isFormReady` before calling `submit`.")},t5=()=>{let{isPaymentElementReady:e,initializePaymentSource:t}=tq(),{stripe:r,elements:n}=tQ(),{externalClientSecret:i}=tq(),o=(0,g.useCallback)(async()=>{if(!r||!n)return t2();let{setupIntent:e,error:t}=await r.confirmSetup({elements:n,confirmParams:{return_url:window.location.href},redirect:"if_required"});return t?{data:null,error:{gateway:"stripe",error:{code:t.code,message:t.message,type:t.type}}}:{data:{gateway:"stripe",paymentToken:e.payment_method},error:null}},[r,n]),s=(0,g.useCallback)(async()=>{if(!r||!n)return t2();await t()},[r,n,t]),l=!!(r&&i);return l?{submit:o,reset:s,isFormReady:e,provider:{name:"stripe"},isProviderReady:l}:{submit:t2,reset:t2,isFormReady:!1,provider:void 0,isProviderReady:!1}}},1976:(e,t,r)=>{r.d(t,{Lq:()=>eC,B$:()=>c.B$,wF:()=>c.wF,lT:()=>c.lT,z0:()=>c.z0,A0:()=>c.A0,lJ:()=>eA,ul:()=>ek,PQ:()=>eP,oE:()=>e_,nC:()=>ev,NC:()=>eb,nm:()=>ew,EH:()=>c.EH,rm:()=>c.rm,m2:()=>c.m2,W5:()=>c.W5,mO:()=>c.mO,eG:()=>c.eG,Ls:()=>eo,hZ:()=>ej,M_:()=>eU,ct:()=>eE,Hx:()=>es,Ny:()=>eO,iB:()=>c.iB,Bl:()=>c.Bl,uF:()=>eg,Fv:()=>eu,cP:()=>eS,cl:()=>c.cl,Tn:()=>c.Tn,Jl:()=>c.Jl,As:()=>c.As,ho:()=>c.ho,ui:()=>c.ui,Z5:()=>c.Z5,D_:()=>c.D_,Wp:()=>c.Wp,wV:()=>c.dy,g7:()=>c.g7,go:()=>c.go,yC:()=>c.yC,Jd:()=>c.Jd});var n,i,o,s,l,a,u,d,c=r(7626),p=e=>{throw TypeError(e)},h=(e,t,r)=>t.has(e)||p("Cannot "+r),g=(e,t,r)=>(h(e,t,"read from private field"),r?r.call(e):t.get(e)),f=(e,t,r)=>t.has(e)?p("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),m=(e,t,r,n)=>(h(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),v=(e,t,r)=>(h(e,t,"access private method"),r),k=r(9838),y=r(8416),b=r(2115),_=r(7650),P=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r};r(5649);var S=r(1972),w=r(6829),C=(e,t,r)=>!e&&r?j(r):O(t),j=e=>{let t=e.userId,r=e.user,n=e.sessionId,i=e.sessionStatus,o=e.sessionClaims,s=e.session,l=e.organization,a=e.orgId,u=e.orgRole,d=e.orgPermissions,c=e.orgSlug;return{userId:t,user:r,sessionId:n,session:s,sessionStatus:i,sessionClaims:o,organization:l,orgId:a,orgRole:u,orgPermissions:d,orgSlug:c,actor:e.actor,factorVerificationAge:e.factorVerificationAge}},O=e=>{let t=e.user?e.user.id:e.user,r=e.user,n=e.session?e.session.id:e.session,i=e.session,o=e.session?.status,s=e.session?e.session.lastActiveToken?.jwt?.claims:null,l=e.session?e.session.factorVerificationAge:null,a=i?.actor,u=e.organization,d=e.organization?e.organization.id:e.organization,c=u?.slug,p=u?r?.organizationMemberships?.find(e=>e.organization.id===d):u,h=p?p.permissions:p;return{userId:t,user:r,sessionId:n,session:i,sessionStatus:o,sessionClaims:s,organization:u,orgId:d,orgRole:p?p.role:p,orgSlug:c,orgPermissions:h,actor:a,factorVerificationAge:l}},E=r(3877),U=(e,t,r,n,i)=>{let{notify:o}=i||{},s=e.get(r);s||(s=[],e.set(r,s)),s.push(n),o&&t.has(r)&&n(t.get(r))},M=(e,t,r)=>(e.get(t)||[]).map(e=>e(r)),z=(e,t,r)=>{let n=e.get(t);n&&(r?n.splice(n.indexOf(r)>>>0,1):e.set(t,[]))},I=()=>{let e=new Map,t=new Map,r=new Map;return{on:(...r)=>U(e,t,...r),prioritizedOn:(...e)=>U(r,t,...e),emit:(n,i)=>{t.set(n,i),M(r,n,i),M(e,n,i)},off:(...t)=>z(e,...t),prioritizedOff:(...e)=>z(r,...e),internal:{retrieveListeners:t=>e.get(t)||[]}}},L={Status:"status"},A=()=>I();"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var R=e=>t=>{try{return b.Children.only(e)}catch{return c.sb.throw((0,c.Wq)(t))}},T=(e,t)=>(e||(e=t),"string"==typeof e&&(e=b.createElement("button",null,e)),e),W=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},x=new Map,N=e=>{let t=Array(e.length).fill(null),[r,n]=(0,b.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>n(r=>r.map((r,n)=>n===t?e:r)),unmount:()=>n(e=>e.map((e,r)=>r===t?null:e)),portal:()=>b.createElement(b.Fragment,null,r[t]?(0,_.createPortal)(e.component,r[t]):null)}))},D=(e,t)=>!!e&&b.isValidElement(e)&&(null==e?void 0:e.type)===t,F=(e,t)=>V({children:e,reorderItemsLabels:["account","security"],LinkComponent:ea,PageComponent:el,MenuItemsComponent:ec,componentName:"UserProfile"},t),B=(e,t)=>V({children:e,reorderItemsLabels:["general","members"],LinkComponent:em,PageComponent:ef,componentName:"OrganizationProfile"},t),$=e=>{let t=[],r=[em,ef,ec,el,ea];return b.Children.forEach(e,e=>{r.some(t=>D(e,t))||t.push(e)}),t},V=(e,t)=>{let{children:r,LinkComponent:n,PageComponent:i,MenuItemsComponent:o,reorderItemsLabels:s,componentName:l}=e,{allowForAnyChildren:a=!1}=t||{},u=[];b.Children.forEach(r,e=>{if(!D(e,i)&&!D(e,n)&&!D(e,o)){e&&!a&&(0,y.s2)((0,c.n)(l));return}let{props:t}=e,{children:r,label:d,url:p,labelIcon:h}=t;if(D(e,i))if(K(t,s))u.push({label:d});else{if(!J(t))return void(0,y.s2)((0,c.sR)(l));u.push({label:d,labelIcon:h,children:r,url:p})}if(D(e,n))if(!q(t))return void(0,y.s2)((0,c.D)(l));else u.push({label:d,labelIcon:h,url:p})});let d=[],p=[],h=[];u.forEach((e,t)=>{if(J(e)){d.push({component:e.children,id:t}),p.push({component:e.labelIcon,id:t});return}q(e)&&h.push({component:e.labelIcon,id:t})});let g=N(d),f=N(p),m=N(h),v=[],k=[];return u.forEach((e,t)=>{if(K(e,s))return void v.push({label:e.label});if(J(e)){let{portal:r,mount:n,unmount:i}=g.find(e=>e.id===t),{portal:o,mount:s,unmount:l}=f.find(e=>e.id===t);v.push({label:e.label,url:e.url,mount:n,unmount:i,mountIcon:s,unmountIcon:l}),k.push(r),k.push(o);return}if(q(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t);v.push({label:e.label,url:e.url,mountIcon:n,unmountIcon:i}),k.push(r);return}}),{customPages:v,customPagesPortals:k}},K=(e,t)=>{let{children:r,label:n,url:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},J=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!!t&&!!n&&!!i&&!!r},q=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!t&&!!n&&!!i&&!!r},G=e=>Q({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:ec,MenuActionComponent:ep,MenuLinkComponent:eh,UserProfileLinkComponent:ea,UserProfilePageComponent:el}),Q=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:n,UserProfileLinkComponent:i,UserProfilePageComponent:o,reorderItemsLabels:s})=>{let l=[],a=[],u=[];b.Children.forEach(e,e=>{if(!D(e,t)&&!D(e,i)&&!D(e,o)){e&&(0,y.s2)(c.P6);return}if(D(e,i)||D(e,o))return;let{props:a}=e;b.Children.forEach(a.children,e=>{if(!D(e,r)&&!D(e,n)){e&&(0,y.s2)(c.wm);return}let{props:t}=e,{label:i,labelIcon:o,href:a,onClick:u,open:d}=t;if(D(e,r))if(Z(t,s))l.push({label:i});else{if(!H(t))return void(0,y.s2)(c.Wv);let e={label:i,labelIcon:o};if(void 0!==u)l.push({...e,onClick:u});else{if(void 0===d)return void(0,y.s2)("Custom menu item must have either onClick or open property");l.push({...e,open:d.startsWith("/")?d:`/${d}`})}}if(D(e,n))if(!Y(t))return void(0,y.s2)(c.ld);else l.push({label:i,labelIcon:o,href:a})})});let d=[],p=[];l.forEach((e,t)=>{H(e)&&d.push({component:e.labelIcon,id:t}),Y(e)&&p.push({component:e.labelIcon,id:t})});let h=N(d),g=N(p);return l.forEach((e,t)=>{if(Z(e,s)&&a.push({label:e.label}),H(e)){let{portal:r,mount:n,unmount:i}=h.find(e=>e.id===t),o={label:e.label,mountIcon:n,unmountIcon:i};"onClick"in e?o.onClick=e.onClick:"open"in e&&(o.open=e.open),a.push(o),u.push(r)}if(Y(e)){let{portal:r,mount:n,unmount:i}=g.find(e=>e.id===t);a.push({label:e.label,href:e.href,mountIcon:n,unmountIcon:i}),u.push(r)}}),{customMenuItems:a,customMenuItemsPortals:u}},Z=(e,t)=>{let{children:r,label:n,onClick:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},H=e=>{let{label:t,labelIcon:r,onClick:n,open:i}=e;return!!r&&!!t&&("function"==typeof n||"string"==typeof i)},Y=e=>{let{label:t,href:r,labelIcon:n}=e;return!!r&&!!n&&!!t};function X(e){let t=(0,b.useRef)(),[r,n]=(0,b.useState)("rendering");return(0,b.useEffect)(()=>{if(!e)throw Error("Clerk: no component name provided, unable to detect mount.");"undefined"==typeof window||t.current||(t.current=(function(e){let{root:t=null==document?void 0:document.body,selector:r,timeout:n=0}=e;return new Promise((e,i)=>{if(!t)return void i(Error("No root element provided"));let o=t;if(r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)return void e();let s=new MutationObserver(n=>{for(let i of n)if("childList"===i.type&&(!o&&r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)){s.disconnect(),e();return}});s.observe(t,{childList:!0,subtree:!0}),n>0&&setTimeout(()=>{s.disconnect(),i(Error("Timeout waiting for element children"))},n)})})({selector:`[data-clerk-component="${e}"]`}).then(()=>{n("rendered")}).catch(()=>{n("error")}))},[e]),r}var ee=e=>"mount"in e,et=e=>"open"in e,er=e=>null==e?void 0:e.map(({mountIcon:e,unmountIcon:t,...r})=>r),en=class extends b.PureComponent{constructor(){super(...arguments),this.rootRef=b.createRef()}componentDidUpdate(e){var t,r,n,i;if(!ee(e)||!ee(this.props))return;let o=P(e.props,"customPages","customMenuItems","children"),s=P(this.props.props,"customPages","customMenuItems","children"),l=(null==(t=o.customPages)?void 0:t.length)!==(null==(r=s.customPages)?void 0:r.length),a=(null==(n=o.customMenuItems)?void 0:n.length)!==(null==(i=s.customMenuItems)?void 0:i.length),u=er(e.props.customMenuItems),d=er(this.props.props.customMenuItems);(!(0,S.MZ)(o,s)||!(0,S.MZ)(u,d)||l||a)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(ee(this.props)&&this.props.mount(this.rootRef.current,this.props.props),et(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(ee(this.props)&&this.props.unmount(this.rootRef.current),et(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return b.createElement(b.Fragment,null,!e&&b.createElement("div",{...t}),this.props.children)}},ei=e=>{var t,r;return b.createElement(b.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,b.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,b.createElement)(e,{key:t})))},eo=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"SignIn",renderWhileLoading:!0}),es=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"SignUp",renderWhileLoading:!0});function el({children:e}){return(0,y.s2)(c.$n),b.createElement(b.Fragment,null,e)}function ea({children:e}){return(0,y.s2)(c._I),b.createElement(b.Fragment,null,e)}var eu=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=F(n.children);return b.createElement(b.Fragment,null,i&&r,b.createElement(en,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:s},rootProps:o},b.createElement(ei,{customPagesPortals:l})))},{component:"UserProfile",renderWhileLoading:!0}),{Page:el,Link:ea}),ed=(0,b.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function ec({children:e}){return(0,y.s2)(c.UX),b.createElement(b.Fragment,null,e)}function ep({children:e}){return(0,y.s2)(c.aU),b.createElement(b.Fragment,null,e)}function eh({children:e}){return(0,y.s2)(c.Uw),b.createElement(b.Fragment,null,e)}var eg=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=F(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),a=Object.assign(n.userProfileProps||{},{customPages:s}),{customMenuItems:u,customMenuItemsPortals:d}=G(n.children),c=$(n.children),p={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...n,userProfileProps:a,customMenuItems:u}};return b.createElement(ed.Provider,{value:p},i&&r,e.loaded&&b.createElement(en,{component:t,...p,hideRootHtmlElement:!!n.__experimental_asProvider,rootProps:o},n.__experimental_asProvider?c:null,b.createElement(ei,{customPagesPortals:l,customMenuItemsPortals:d})))},{component:"UserButton",renderWhileLoading:!0}),{UserProfilePage:el,UserProfileLink:ea,MenuItems:ec,Action:ep,Link:eh,__experimental_Outlet:function(e){let t=(0,b.useContext)(ed),r={...t,props:{...t.props,...e}};return b.createElement(en,{...r})}});function ef({children:e}){return(0,y.s2)(c.vb),b.createElement(b.Fragment,null,e)}function em({children:e}){return(0,y.s2)(c.kf),b.createElement(b.Fragment,null,e)}var ev=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=B(n.children);return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:s},rootProps:o},b.createElement(ei,{customPagesPortals:l})))},{component:"OrganizationProfile",renderWhileLoading:!0}),{Page:ef,Link:em}),ek=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"CreateOrganization",renderWhileLoading:!0}),ey=(0,b.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),eb=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=B(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),a=Object.assign(n.organizationProfileProps||{},{customPages:s}),u=$(n.children),d={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...n,organizationProfileProps:a},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),b.createElement(ey.Provider,{value:d},b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{...d,hideRootHtmlElement:!!n.__experimental_asProvider},n.__experimental_asProvider?u:null,b.createElement(ei,{customPagesPortals:l}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0}),{OrganizationProfilePage:ef,OrganizationProfileLink:em,__experimental_Outlet:function(e){let t=(0,b.useContext)(ey),r={...t,props:{...t.props,...e}};return b.createElement(en,{...r})}}),e_=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"OrganizationList",renderWhileLoading:!0}),eP=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"GoogleOneTap",renderWhileLoading:!0}),eS=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"Waitlist",renderWhileLoading:!0}),ew=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"PricingTable",renderWhileLoading:!0}),eC=(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===X(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,i&&r,e.loaded&&b.createElement(en,{component:t,mount:e.mountApiKeys,unmount:e.unmountApiKeys,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"ApiKeys",renderWhileLoading:!0}),ej=(0,c.Q)(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:n,forceRedirectUrl:i,fallbackRedirectUrl:o,signUpForceRedirectUrl:s,mode:l,initialValues:a,withSignUp:u,oauthFlow:d,...c}=r,p=R(t=T(t,"Sign in"))("SignInButton"),h=()=>{let t={forceRedirectUrl:i,fallbackRedirectUrl:o,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:s,initialValues:a,withSignUp:u,oauthFlow:d};return"modal"===l?e.openSignIn({...t,appearance:r.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:o,signInForceRedirectUrl:i})},g=async e=>(p&&"object"==typeof p&&"props"in p&&await W(p.props.onClick)(e),h()),f={...c,onClick:g};return b.cloneElement(p,f)},{component:"SignInButton",renderWhileLoading:!0}),eO=(0,c.Q)(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,mode:l,unsafeMetadata:a,initialValues:u,oauthFlow:d,...c}=r,p=R(t=T(t,"Sign up"))("SignUpButton"),h=()=>{let t={fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,unsafeMetadata:a,initialValues:u,oauthFlow:d};return"modal"===l?e.openSignUp({...t,appearance:r.appearance}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:i})},g=async e=>(p&&"object"==typeof p&&"props"in p&&await W(p.props.onClick)(e),h()),f={...c,onClick:g};return b.cloneElement(p,f)},{component:"SignUpButton",renderWhileLoading:!0}),eE=(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:n="/",signOutOptions:i,...o}=r,s=R(t=T(t,"Sign out"))("SignOutButton"),l=()=>e.signOut({redirectUrl:n,...i}),a=async e=>(await W(s.props.onClick)(e),l()),u={...o,onClick:a};return b.cloneElement(s,u)},{component:"SignOutButton",renderWhileLoading:!0}),eU=(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:n,...i}=r,o=R(t=T(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:n||void 0})}()},l=async e=>(await W(o.props.onClick)(e),s()),a={...i,onClick:l};return b.cloneElement(o,a)},{component:"SignInWithMetamask",renderWhileLoading:!0});void 0===globalThis.__BUILD_DISABLE_RHC__&&(globalThis.__BUILD_DISABLE_RHC__=!1);var eM={name:"@clerk/clerk-react",version:"5.33.0",environment:"production"},ez=class e{constructor(e){f(this,u),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],f(this,n,"loading"),f(this,i),f(this,o),f(this,s),f(this,l,A()),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignInUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignInUrl",t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignUpUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignUpUrl",t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildNewSubscriptionRedirectUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildWaitlistUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.on=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.on)return this.clerkjs.on(...e);g(this,l).on(...e)},this.off=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.off)return this.clerkjs.off(...e);g(this,l).off(...e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.beforeLoad=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=e=>{var t;if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null==(t=g(this,l).internal.retrieveListeners("status"))||t.forEach(e=>{this.on("status",e,{notify:!0})}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenCheckout&&e.__internal_openCheckout(this.preopenCheckout),null!==this.preopenPlanDetails&&e.__internal_openPlanDetails(this.preopenPlanDetails),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__internal_openReverification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),null!==this.preOpenWaitlist&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),this.premountWaitlistNodes.forEach((t,r)=>{e.mountWaitlist(r,t)}),this.premountPricingTableNodes.forEach((t,r)=>{e.mountPricingTable(r,t)}),this.premountApiKeysNodes.forEach((t,r)=>{e.mountApiKeys(r,t)}),this.premountOAuthConsentNodes.forEach((t,r)=>{e.__internal_mountOAuthConsent(r,t)}),void 0===this.clerkjs.status&&g(this,l).emit(L.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async e=>{let t=await v(this,u,d).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.__experimental_navigateToTask=async e=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(e):this.preopenCheckout=e},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(e):this.preopenPlanDetails=e},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.mountPricingTable=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.unmountPricingTable=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.mountApiKeys=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(e,t):this.premountApiKeysNodes.set(e,t)},this.unmountApiKeys=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(e):this.premountApiKeysNodes.delete(e)},this.__internal_mountOAuthConsent=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(e,t):this.premountOAuthConsentNodes.set(e,t)},this.__internal_unmountOAuthConsent=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(e):this.premountOAuthConsentNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&this.loaded?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToWaitlist()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToWaitlist",e)},this.handleRedirectCallback=async e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&this.loaded?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=async(e,t)=>{var r;let n=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&this.loaded?null==(r=n())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",n)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithOKXWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithOKXWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await v(this,u,d).call(this)).authenticateWithGoogleOneTap(e),this.__internal_loadStripeJs=async()=>(await v(this,u,d).call(this)).__internal_loadStripeJs(),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("getOrganization",t)},this.joinWaitlist=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.joinWaitlist(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("joinWaitlist",t)},this.signOut=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.signOut(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("signOut",t)};let{Clerk:t=null,publishableKey:r}=e||{};m(this,s,r),m(this,o,null==e?void 0:e.proxyUrl),m(this,i,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=(0,E.M)()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=eM),g(this,l).emit(L.Status,"loading"),g(this,l).prioritizedOn(L.Status,e=>m(this,n,e)),g(this,s)&&this.loadClerkJS()}get publishableKey(){return g(this,s)}get loaded(){var e;return(null==(e=this.clerkjs)?void 0:e.loaded)||!1}get status(){var e;return this.clerkjs?(null==(e=this.clerkjs)?void 0:e.status)||(this.clerkjs.loaded?"ready":"loading"):g(this,n)}static getOrCreateInstance(t){return(0,E.M)()&&g(this,a)&&(!t.Clerk||g(this,a).Clerk===t.Clerk)&&g(this,a).publishableKey===t.publishableKey||m(this,a,new e(t)),g(this,a)}static clearInstance(){m(this,a,null)}get domain(){return"undefined"!=typeof window&&window.location?(0,y.VK)(g(this,i),new URL(window.location.href),""):"function"==typeof g(this,i)?c.sb.throw(c.Vo):g(this,i)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?(0,y.VK)(g(this,o),new URL(window.location.href),""):"function"==typeof g(this,o)?c.sb.throw(c.Vo):g(this,o)||""}__internal_getOption(e){var t,r;return(null==(t=this.clerkjs)?void 0:t.__internal_getOption)?null==(r=this.clerkjs)?void 0:r.__internal_getOption(e):this.options[e]}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?(0,y.VK)(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&c.sb.throw(c.Vo)}async loadClerkJS(){var e,t;if("browser"===this.mode&&!this.loaded){"undefined"!=typeof window&&(window.__clerk_publishable_key=g(this,s),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(g(this,s),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(e),await e.load(this.options)):(e=this.Clerk).loaded||(this.beforeLoad(e),await e.load(this.options)),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await (0,k._R)({...this.options,publishableKey:g(this,s),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){g(this,l).emit(L.Status,"error"),console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}get isSignedIn(){return!!this.clerkjs&&this.clerkjs.isSignedIn}get billing(){var e;return null==(e=this.clerkjs)?void 0:e.billing}get apiKeys(){var e;return null==(e=this.clerkjs)?void 0:e.apiKeys}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};function eI(e){let{isomorphicClerkOptions:t,initialState:r,children:n}=e,{isomorphicClerk:i,clerkStatus:o}=eL(t),[s,l]=b.useState({client:i.client,session:i.session,user:i.user,organization:i.organization});b.useEffect(()=>i.addListener(e=>l({...e})),[]);let a=C(i.loaded,s,r),u=b.useMemo(()=>({value:i}),[o]),d=b.useMemo(()=>({value:s.client}),[s.client]),{sessionId:p,sessionStatus:h,sessionClaims:g,session:f,userId:m,user:v,orgId:k,actor:y,organization:_,orgRole:P,orgSlug:w,orgPermissions:j,factorVerificationAge:O}=a,E=b.useMemo(()=>({value:{sessionId:p,sessionStatus:h,sessionClaims:g,userId:m,actor:y,orgId:k,orgRole:P,orgSlug:w,orgPermissions:j,factorVerificationAge:O}}),[p,h,m,y,k,P,w,O,null==g?void 0:g.__raw]),U=b.useMemo(()=>({value:f}),[p,f]),M=b.useMemo(()=>({value:v}),[m,v]),z=b.useMemo(()=>({value:{organization:_}}),[k,_]);return b.createElement(c.SW.Provider,{value:u},b.createElement(S.pc.Provider,{value:d},b.createElement(S.IC.Provider,{value:U},b.createElement(S.TS,{...z.value},b.createElement(c.cy.Provider,{value:E},b.createElement(S.Rs.Provider,{value:M},n))))))}n=new WeakMap,i=new WeakMap,o=new WeakMap,s=new WeakMap,l=new WeakMap,a=new WeakMap,u=new WeakSet,d=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},f(ez,a);var eL=e=>{let t=b.useRef(ez.getOrCreateInstance(e)),[r,n]=b.useState(t.current.status);return b.useEffect(()=>{t.current.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),b.useEffect(()=>{t.current.__unstable__updateProps({options:e})},[e.localization]),b.useEffect(()=>(t.current.on("status",n),()=>{t.current&&t.current.off("status",n),ez.clearInstance()}),[]),{isomorphicClerk:t.current,clerkStatus:r}},eA=function(e,t,r){let n=e.displayName||e.name||t||"Component",i=n=>(!function(e,t,r=1){b.useEffect(()=>{let n=x.get(e)||0;return n==r?c.sb.throw(t):(x.set(e,n+1),()=>{x.set(e,(x.get(e)||1)-1)})},[])}(t,r),b.createElement(e,{...n}));return i.displayName=`withMaxAllowedInstancesGuard(${n})`,i}(function(e){let{initialState:t,children:r,__internal_bypassMissingPublishableKey:n,...i}=e,{publishableKey:o="",Clerk:s}=i;return s||n||(o?o&&!(0,w.rA)(o)&&c.sb.throwInvalidPublishableKeyError({key:o}):c.sb.throwMissingPublishableKeyError()),b.createElement(eI,{initialState:t,isomorphicClerkOptions:i},r)},"ClerkProvider",c.yN);eA.displayName="ClerkProvider",(0,c.wV)({packageName:"@clerk/clerk-react"}),(0,k.kX)("@clerk/clerk-react")},2163:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>a,Vc:()=>l,gE:()=>i,iM:()=>n,mG:()=>o,ub:()=>s});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],o=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],s=[".accountsstage.dev"],l="https://api.lclclerk.com",a="https://api.clerkstage.dev",u="https://api.clerk.com"},2436:(e,t,r)=>{var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,s=n.useEffect,l=n.useLayoutEffect,a=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,d=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&d({inst:i})},[e,r,t]),s(function(){return u(i)&&d({inst:i}),e(function(){u(i)&&d({inst:i})})},[e]),a(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},3789:(e,t,r)=>{r.d(t,{_r:()=>n._r});var n=r(1634);r(5649)},3877:(e,t,r)=>{function n(){return"undefined"!=typeof window}r.d(t,{M:()=>n}),RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i"),r(5649)},4129:(e,t,r)=>{r.d(t,{Fj:()=>o,MC:()=>i,b_:()=>n});var n=()=>!1,i=()=>!1,o=()=>{try{return!0}catch{}return!1}},5018:(e,t,r)=>{r.d(t,{D:()=>g,M2:()=>v,MR:()=>m});var n={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},i=new Set(["first_factor","second_factor","multi_factor"]),o=new Set(["strict_mfa","strict","moderate","lax"]),s=e=>"number"==typeof e&&e>0,l=e=>i.has(e),a=e=>o.has(e),u=e=>e.replace(/^(org:)*/,"org:"),d=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(u(e.permission)):e.role?u(n)===u(e.role):null:null},c=(e,t)=>{let{org:r,user:n}=h(e),[i,o]=t.split(":"),s=o||i;return"org"===i?r.includes(s):"user"===i?n.includes(s):[...r,...n].includes(s)},p=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?c(r,e.feature):e.plan&&n?c(n,e.plan):null},h=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},g=e=>{if(!e)return!1;let t="string"==typeof e&&a(e),r="object"==typeof e&&l(e.level)&&s(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?n[e]:e).bind(null,e)},f=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=g(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[o,s]=t,l=-1!==o?i>o:null,a=-1!==s?i>s:null;switch(n){case"first_factor":return l;case"second_factor":return -1!==s?a:l;case"multi_factor":return -1===s?l:l&&a}},m=e=>t=>{if(!e.userId)return!1;let r=p(t,e),n=d(t,e),i=f(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},v=({authObject:{sessionId:e,sessionStatus:t,userId:r,actor:n,orgId:i,orgRole:o,orgSlug:s,signOut:l,getToken:a,has:u,sessionClaims:d},options:{treatPendingAsSignedOut:c=!0}})=>void 0===e&&void 0===r?{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:l,getToken:a}:null===e&&null===r?{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:c&&"pending"===t?{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:e&&d&&r&&i&&o?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:i,orgRole:o,orgSlug:s||null,has:u,signOut:l,getToken:a}:e&&d&&r&&!i?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:null,orgRole:null,orgSlug:null,has:u,signOut:l,getToken:a}:void 0},5649:(e,t,r)=>{r.d(t,{OV:()=>h,S7:()=>p,VA:()=>a,ie:()=>d,jq:()=>g});var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,l=e=>{throw TypeError(e)},a=(e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})},u=(e,t,r,l)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of o(t))s.call(e,a)||a===r||n(e,a,{get:()=>t[a],enumerable:!(l=i(t,a))||l.enumerable});return e},d=(e,t,r)=>(u(e,t,"default"),r&&u(r,t,"default")),c=(e,t,r)=>t.has(e)||l("Cannot "+r),p=(e,t,r)=>(c(e,t,"read from private field"),r?r.call(e):t.get(e)),h=(e,t,r,n)=>(c(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),g=(e,t,r)=>(c(e,t,"access private method"),r)},5695:(e,t,r)=>{var n=r(8999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},6829:(e,t,r)=>{r.d(t,{RZ:()=>u,rA:()=>a,q5:()=>l});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,i=r(2163),o="pk_live_";function s(e){if(!e.endsWith("$"))return!1;let t=e.slice(0,-1);return!t.includes("$")&&t.includes(".")}function l(e,t={}){let r;if(!(e=e||"")||!a(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!a(e))throw Error("Publishable key not valid.");return null}let i=e.startsWith(o)?"production":"development";try{r=n(e.split("_")[2])}catch{if(t.fatal)throw Error("Publishable key not valid: Failed to decode key.");return null}if(!s(r)){if(t.fatal)throw Error("Publishable key not valid: Decoded key has invalid format.");return null}let u=r.slice(0,-1);return t.proxyUrl?u=t.proxyUrl:"development"!==i&&t.domain&&t.isSatellite&&(u=`clerk.${t.domain}`),{instanceType:i,frontendApi:u}}function a(e=""){try{if(!(e.startsWith(o)||e.startsWith("pk_test_")))return!1;let t=e.split("_");if(3!==t.length)return!1;let r=t[2];if(!r)return!1;let i=n(r);return s(i)}catch{return!1}}function u(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=i.gE.some(e=>r.endsWith(e)),e.set(r,n)),n}}}},6898:(e,t,r)=>{r.d(t,{FJ:()=>n.FJ,YF:()=>n.YF});var n=r(99);r(852),r(5649)},7626:(e,t,r)=>{r.d(t,{cy:()=>h,B$:()=>ei,wF:()=>H,lT:()=>Z,z0:()=>G,A0:()=>Q,SW:()=>f,EH:()=>Y,rm:()=>en,m2:()=>er,W5:()=>X,mO:()=>ee,eG:()=>et,iB:()=>J,Bl:()=>q,cl:()=>l.cl,Tn:()=>l.Tn,Jl:()=>l.Jl,D:()=>j,wm:()=>M,sR:()=>C,n:()=>w,sb:()=>c,s7:()=>E,Wq:()=>k,yN:()=>v,kd:()=>O,kf:()=>S,vb:()=>P,wV:()=>p,Vo:()=>y,As:()=>D,ho:()=>l.ho,hP:()=>F,ui:()=>B,Z5:()=>l.Z5,D_:()=>l.D_,Wp:()=>l.Wp,dy:()=>l.wV,g7:()=>l.g7,go:()=>$,yC:()=>V,Jd:()=>l.Jd,P6:()=>U,aU:()=>I,ld:()=>A,Wv:()=>R,UX:()=>z,Uw:()=>L,_I:()=>_,$n:()=>b,Q:()=>K});var n=r(3789),i=r(5018);r(5649);var o=r(6898),s=r(2115),l=r(1972),a=r(4129),u=new Set,d=(e,t,r)=>{let n=(0,a.MC)()||(0,a.Fj)(),i=r??e;u.has(i)||n||(u.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},c=(0,n._r)({packageName:"@clerk/clerk-react"});function p(e){c.setMessages(e).setPackageName(e)}var[h,g]=(0,l.e3)("AuthContext"),f=l.ED,m=l.hQ,v="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",k=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,y="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",b="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",_="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",P="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",S="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",w=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,C=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,j=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,O=e=>`The <${e}/> component uses path-based routing by default unless a different routing strategy is provided using the \`routing\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \`path\` prop. Example: <${e} path={'/my-path'} />`,E=e=>`The \`path\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \`routing='path'\` to the <${e}/> component, or drop the \`path\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`,U="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",M="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",z="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",I="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",L="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",A="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",R="Missing props. <UserButton.Action /> component requires the following props: label.",T=e=>{(0,l.Kz)(()=>{c.throwMissingClerkProviderError({source:e})})},W=e=>new Promise(t=>{let r=n=>{["ready","degraded"].includes(n)&&(t(),e.off("status",r))};e.on("status",r,{notify:!0})}),x=e=>async t=>(await W(e),e.session)?e.session.getToken(t):null,N=e=>async(...t)=>(await W(e),e.signOut(...t)),D=(e={})=>{var t,r;T("useAuth");let{treatPendingAsSignedOut:n,...i}=null!=e?e:{},l=g();void 0===l.sessionId&&void 0===l.userId&&(l=null!=i?i:{});let a=m(),u=(0,s.useCallback)(x(a),[a]),d=(0,s.useCallback)(N(a),[a]);return null==(t=a.telemetry)||t.record((0,o.FJ)("useAuth",{treatPendingAsSignedOut:n})),F({...l,getToken:u,signOut:d},{treatPendingAsSignedOut:null!=n?n:null==(r=a.__internal_getOption)?void 0:r.call(a,"treatPendingAsSignedOut")})};function F(e,{treatPendingAsSignedOut:t=!0}={}){let{userId:r,orgId:n,orgRole:o,has:l,signOut:a,getToken:u,orgPermissions:d,factorVerificationAge:p,sessionClaims:h}=null!=e?e:{},g=(0,s.useCallback)(e=>l?l(e):(0,i.MR)({userId:r,orgId:n,orgRole:o,orgPermissions:d,factorVerificationAge:p,features:(null==h?void 0:h.fea)||"",plans:(null==h?void 0:h.pla)||""})(e),[l,r,n,o,d,p]),f=(0,i.M2)({authObject:{...e,getToken:u,signOut:a,has:g},options:{treatPendingAsSignedOut:t}});return f||c.throw("Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")}function B(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=s.useMemo(()=>e.createEmailLinkFlow(),[e]);return s.useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}var $=()=>{var e;T("useSignIn");let t=m(),r=(0,l.WD)();return(null==(e=t.telemetry)||e.record((0,o.FJ)("useSignIn")),r)?{isLoaded:!0,signIn:r.signIn,setActive:t.setActive}:{isLoaded:!1,signIn:void 0,setActive:void 0}},V=()=>{var e;T("useSignUp");let t=m(),r=(0,l.WD)();return(null==(e=t.telemetry)||e.record((0,o.FJ)("useSignUp")),r)?{isLoaded:!0,signUp:r.signUp,setActive:t.setActive}:{isLoaded:!1,signUp:void 0,setActive:void 0}},K=(e,t)=>{let r=("string"==typeof t?t:null==t?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=r;let n="string"==typeof t?void 0:t,i=t=>{T(r||"withClerk");let i=m();return i.loaded||(null==n?void 0:n.renderWhileLoading)?s.createElement(e,{...t,component:r,clerk:i}):null};return i.displayName=`withClerk(${r})`,i},J=({children:e,treatPendingAsSignedOut:t})=>{T("SignedIn");let{userId:r}=D({treatPendingAsSignedOut:t});return r?e:null},q=({children:e,treatPendingAsSignedOut:t})=>{T("SignedOut");let{userId:r}=D({treatPendingAsSignedOut:t});return null===r?e:null},G=({children:e})=>(T("ClerkLoaded"),m().loaded)?e:null,Q=({children:e})=>(T("ClerkLoading"),"loading"!==m().status)?null:e,Z=({children:e})=>(T("ClerkFailed"),"error"!==m().status)?null:e,H=({children:e})=>(T("ClerkDegraded"),"degraded"!==m().status)?null:e,Y=({children:e,fallback:t,treatPendingAsSignedOut:r,...n})=>{T("Protect");let{isLoaded:i,has:o,userId:s}=D({treatPendingAsSignedOut:r});if(!i)return null;let l=null!=t?t:null;return s?"function"==typeof n.condition?n.condition(o)?e:l:n.role||n.permission||n.feature||n.plan?o(n)?e:l:e:l},X=K(({clerk:e,...t})=>{let{client:r,session:n}=e,i=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return s.useEffect(()=>{null===n&&i?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),ee=K(({clerk:e,...t})=>(s.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),et=K(({clerk:e})=>(s.useEffect(()=>{d("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),er=K(({clerk:e})=>(s.useEffect(()=>{d("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),en=K(({clerk:e})=>(s.useEffect(()=>{d("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),ei=K(({clerk:e,...t})=>(s.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback")},8416:(e,t,r)=>{r.d(t,{VK:()=>o,b_:()=>n.b_,Fj:()=>n.Fj,s2:()=>i});var n=r(4129),i=e=>{(0,n.b_)()&&console.error(`Clerk: ${e}`)};function o(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}r(5649)},9033:(e,t,r)=>{e.exports=r(2436)},9838:(e,t,r)=>{r.d(t,{T5:()=>P,nO:()=>_,_R:()=>b,kX:()=>y});var n=(e,t="5.71.0")=>{if(e)return e;let r=i(t);return r?"snapshot"===r?"5.71.0":r:o(t)},i=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],o=e=>e.trim().replace(/^v/,"").split(".")[0];function s(e){return e.startsWith("/")}var l=/\/$|\/\?|\/#/,a={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},u=async e=>new Promise(t=>setTimeout(t,e)),d=(e,t)=>t?e*(1+Math.random()):e,c=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=d(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await u(r()),t++}},p=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:o,factor:s,retryImmediately:l,jitter:p}={...a,...t},h=c({initialDelay:i,maxDelayBetweenRetries:o,factor:s,jitter:p});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;l&&1===r?await u(d(100,p)):await h()}};async function h(e="",t){let{async:r,defer:n,beforeLoad:i,crossOrigin:o,nonce:s}=t||{};return p(()=>new Promise((t,l)=>{e||l(Error("loadScript cannot be called without a src")),document&&document.body||l("loadScript cannot be called when document does not exist");let a=document.createElement("script");o&&a.setAttribute("crossorigin",o),a.async=r||!1,a.defer=n||!1,a.addEventListener("load",()=>{a.remove(),t(a)}),a.addEventListener("error",()=>{a.remove(),l()}),a.src=e,a.nonce=s,i?.(a),document.body.appendChild(a)}),{shouldRetry:(e,t)=>t<=5})}var g=r(1634),f=r(6829),m="Clerk: Failed to load Clerk",{isDevOrStagingUrl:v}=(0,f.RZ)(),k=(0,g._r)({packageName:"@clerk/shared"});function y(e){k.setPackageName({packageName:e})}var b=async e=>{let t=document.querySelector("script[data-clerk-js-script]");return t?new Promise((e,r)=>{t.addEventListener("load",()=>{e(t)}),t.addEventListener("error",()=>{r(m)})}):e?.publishableKey?h(_(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:S(e)}).catch(()=>{throw Error(m)}):void k.throwMissingPublishableKeyError()},_=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:i,proxyUrl:o,domain:l,publishableKey:a}=e;if(t)return t;let u="";u=o&&function(e){var t;return!e||(t=e,/^http(s)?:\/\//.test(t||""))||s(e)}(o)?(function(e){return e?s(e)?new URL(e,window.location.origin).toString():e:""})(o).replace(/http(s)?:\/\//,""):l&&!v((0,f.q5)(a)?.frontendApi||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(l):(0,f.q5)(a)?.frontendApi||"";let d=r?`${r.replace(/\.+$/,"")}.`:"",c=n(i);return`https://${u}/npm/@clerk/clerk-js@${c}/dist/clerk.${d}browser.js`},P=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},S=e=>t=>{let r=P(e);for(let e in r)t.setAttribute(e,r[e])};r(5649)}}]);