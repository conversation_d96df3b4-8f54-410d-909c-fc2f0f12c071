"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[955],{1497:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},1951:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mic-off",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},3273:(e,t,n)=>{n.d(t,{$:()=>nM,A:()=>tq,B:()=>nz,D:()=>nu,E:()=>ne,G:()=>tj,L:()=>nW,N:()=>nb,O:()=>ny,P:()=>ng,Q:()=>nl,R:()=>nF,S:()=>nn,T:()=>nr,U:()=>tV,V:()=>nS,W:()=>tF,X:()=>tG,Y:()=>tQ,Z:()=>nv,_:()=>nA,a:()=>nO,a0:()=>t9,a1:()=>tA,ab:()=>n_,ac:()=>nR,ad:()=>nc,ae:()=>eo,af:()=>ea,ag:()=>nj,ah:()=>nN,ai:()=>n$,aj:()=>nx,ao:()=>tL,ap:()=>tN,aq:()=>tz,c:()=>nV,d:()=>na,e:()=>t0,f:()=>nY,h:()=>no,i:()=>nD,j:()=>nL,k:()=>nw,l:()=>tD,m:()=>tO,n:()=>function e(t,n,r,i){if(t.length<1)throw Error("At least one grid layout definition must be provided.");let o=[...t].map(e=>{var t,n;return{name:`${e.columns}x${e.rows}`,columns:e.columns,rows:e.rows,maxTiles:e.columns*e.rows,minWidth:null!=(t=e.minWidth)?t:0,minHeight:null!=(n=e.minHeight)?n:0,orientation:e.orientation}}).sort((e,t)=>e.maxTiles!==t.maxTiles?e.maxTiles-t.maxTiles:0!==e.minWidth||0!==t.minWidth?e.minWidth-t.minWidth:0!==e.minHeight||0!==t.minHeight?e.minHeight-t.minHeight:0);if(r<=0||i<=0)return o[0];let a=0,l=r/i>1?"landscape":"portrait",c=o.find((e,t,r)=>{a=t;let i=-1!==r.findIndex((n,r)=>{let i=!n.orientation||n.orientation===l,o=n.maxTiles===e.maxTiles;return r>t&&o&&i});return e.maxTiles>=n&&!i});if(void 0===c)if(c=o[o.length-1])tD.warn(`No layout found for: participantCount: ${n}, width/height: ${r}/${i} fallback to biggest available layout (${c}).`);else throw Error("No layout or fallback layout found.");if((r<c.minWidth||i<c.minHeight)&&a>0){let t=o[a-1];c=e(o.slice(0,a),t.maxTiles,r,i)}return c},o:()=>t4,p:()=>tR,q:()=>t8,s:()=>nE,t:()=>t7,u:()=>nU,v:()=>nt,w:()=>nH,x:()=>t1,y:()=>ni,z:()=>tH});var r=n(4267),i=n(2115);let o=Math.min,a=Math.max,l=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function b(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function g(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function w(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,a=b(t),l=m(b(t)),c=v(l),u=p(t),s="y"===a,d=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,y=i[c]/2-o[c]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:f};break;case"left":r={x:i.x-o.width,y:f};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let k=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:a}=n,l=o.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:s,y:d}=E(u,r,c),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:o,fn:m}=l[n],{x:v,y:b,data:y,reset:g}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:i,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});s=v??s,d=b??d,p={...p,[o]:{...p[o],...y}},g&&h<=50&&(h++,"object"==typeof g&&(g.placement&&(f=g.placement),g.rects&&(u=!0===g.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):g.rects),{x:s,y:d}=E(u,f,c)),n=-1)}return{x:s,y:d,placement:f,strategy:i,middlewareData:p}};async function S(e,t){var n,r;void 0===t&&(t={});let{x:i,y:o,platform:a,rects:l,elements:c,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:h=!1,padding:m=0}=f(t,e),v="number"!=typeof(r=m)?{top:0,right:0,bottom:0,left:0,...r}:{top:r,right:r,bottom:r,left:r},b=c[h?"floating"===p?"reference":"floating":p],y=w(await a.getClippingRect({element:null==(n=await (null==a.isElement?void 0:a.isElement(b)))||n?b:b.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(c.floating)),boundary:s,rootBoundary:d,strategy:u})),g="floating"===p?{x:i,y:o,width:l.floating.width,height:l.floating.height}:l.reference,E=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c.floating)),k=await (null==a.isElement?void 0:a.isElement(E))&&await (null==a.getScale?void 0:a.getScale(E))||{x:1,y:1},S=w(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:g,offsetParent:E,strategy:u}):g);return{top:(y.top-S.top+v.top)/k.y,bottom:(S.bottom-y.bottom+v.bottom)/k.y,left:(y.left-S.left+v.left)/k.x,right:(S.right-y.right+v.right)/k.x}}async function x(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),a=p(n),l=h(n),c="y"===b(n),u=["left","top"].includes(a)?-1:1,s=o&&c?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),c?{x:v*s,y:m*u}:{x:m*u,y:v*s}}function C(){return"u">typeof window}function P(e){return A(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function M(e){var t;return null==(t=(A(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function A(e){return!!C()&&(e instanceof Node||e instanceof T(e).Node)}function I(e){return!!C()&&(e instanceof Element||e instanceof T(e).Element)}function R(e){return!!C()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function O(e){return!(!C()||typeof ShadowRoot>"u")&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function L(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function N(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function _(e){let t=D(),n=I(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function D(){return!(typeof CSS>"u")&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function j(e){return["html","body","#document"].includes(P(e))}function z(e){return T(e).getComputedStyle(e)}function V(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function F(e){if("html"===P(e))return e;let t=e.assignedSlot||e.parentNode||O(e)&&e.host||M(e);return O(t)?t.host:t}function Y(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=F(t);return j(n)?t.ownerDocument?t.ownerDocument.body:t.body:R(n)&&L(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),a=T(i);if(o){let e=H(a);return t.concat(a,a.visualViewport||[],L(i)?i:[],e&&n?Y(e):[])}return t.concat(i,Y(i,[],n))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=R(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,c=l(n)!==o||l(r)!==a;return c&&(n=o,r=a),{width:n,height:r,$:c}}function W(e){return I(e)?e:e.contextElement}function $(e){let t=W(e);if(!R(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=U(t),a=(o?l(n.width):n.width)/r,c=(o?l(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let Z=u(0);function B(e){let t=T(e);return D()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Z}function q(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),a=W(e),l=u(1);t&&(r?I(r)&&(l=$(r)):l=$(e));let c=(void 0===(i=n)&&(i=!1),r&&(!i||r===T(a))&&i)?B(a):u(0),s=(o.left+c.x)/l.x,d=(o.top+c.y)/l.y,f=o.width/l.x,p=o.height/l.y;if(a){let e=T(a),t=r&&I(r)?T(r):r,n=e,i=H(n);for(;i&&r&&t!==n;){let e=$(i),t=i.getBoundingClientRect(),r=z(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=o,d+=a,i=H(n=T(i))}}return w({width:f,height:p,x:s,y:d})}function J(e,t){let n=V(e).scrollLeft;return t?t.left+n:q(M(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function G(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=M(e),i=n.visualViewport,o=r.clientWidth,a=r.clientHeight,l=0,c=0;if(i){o=i.width,a=i.height;let e=D();(!e||e&&"fixed"===t)&&(l=i.offsetLeft,c=i.offsetTop)}return{width:o,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=M(e),n=V(e),r=e.ownerDocument.body,i=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),c=-n.scrollTop;return"rtl"===z(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:l,y:c}}(M(e));else if(I(t))r=function(e,t){let n=q(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=R(e)?$(e):u(1),a=e.clientWidth*o.x,l=e.clientHeight*o.y;return{width:a,height:l,x:i*o.x,y:r*o.y}}(t,n);else{let n=B(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return w(r)}function X(e){return"static"===z(e).position}function K(e,t){if(!R(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return M(e)===n&&(n=n.ownerDocument.body),n}function ee(e,t){let n=T(e);if(N(e))return n;if(!R(e)){let t=F(e);for(;t&&!j(t);){if(I(t)&&!X(t))return t;t=F(t)}return n}let r=K(e,t);for(;r&&["table","td","th"].includes(P(r))&&X(r);)r=K(r,t);return r&&j(r)&&X(r)&&!_(r)?n:r||function(e){let t=F(e);for(;R(t)&&!j(t);){if(_(t))return t;if(N(t))break;t=F(t)}return null}(e)||n}let et=async function(e){let t=this.getOffsetParent||ee,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=R(t),i=M(t),o="fixed"===n,a=q(e,!0,o,t),l={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!o)if(("body"!==P(t)||L(i))&&(l=V(t)),r){let e=q(t,!0,o,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else i&&(c.x=J(i));let s=!i||r||o?u(0):Q(i,l);return{x:a.left+l.scrollLeft-c.x-s.x,y:a.top+l.scrollTop-c.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},en={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,a=M(r),l=!!t&&N(t.floating);if(r===a||l&&o)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),d=u(0),f=R(r);if((f||!f&&!o)&&(("body"!==P(r)||L(a))&&(c=V(r)),R(r))){let e=q(r);s=$(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||o?u(0):Q(a,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-c.scrollTop*s.y+d.y+p.y}},getDocumentElement:M,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,l=[..."clippingAncestors"===n?N(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Y(e,[],!1).filter(e=>I(e)&&"body"!==P(e)),i=null,o="fixed"===z(e).position,a=o?F(e):e;for(;I(a)&&!j(a);){let t=z(a),n=_(a);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||L(a)&&!n&&function e(t,n){let r=F(t);return!(r===n||!I(r)||j(r))&&("fixed"===z(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):i=t,a=F(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=l[0],u=l.reduce((e,n)=>{let r=G(t,n,i);return e.top=a(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=a(r.left,e.left),e},G(t,c,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:ee,getElementRects:et,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:$,isElement:I,isRTL:function(e){return"rtl"===z(e).direction}};function er(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ei=(e,t,n)=>{let r=new Map,i={platform:en,...n},o={...i.platform,_c:r};return k(e,t,{...i,platform:o})};var eo="u">typeof globalThis?globalThis:"u">typeof window?window:"u">typeof global?global:"u">typeof self?self:{};function ea(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var el,ec={exports:{}},eu=ec.exports;let es=ea(function(){var e;return el||(el=1,e=function(){var e=function(){},t="undefined",n=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),r=["trace","debug","info","warn","error"],i={},o=null;function a(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch{return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function l(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function c(){for(var n=this.getLevel(),i=0;i<r.length;i++){var o=r[i];this[o]=i<n?e:this.methodFactory(o,n,this.name)}if(this.log=this.debug,typeof console===t&&n<this.levels.SILENT)return"No console available for logging"}function u(e){return function(){typeof console!==t&&(c.call(this),this[e].apply(this,arguments))}}function s(r,i,o){var c;return"debug"===(c=r)&&(c="log"),typeof console!==t&&("trace"===c&&n?l:void 0!==console[c]?a(console,c):void 0!==console.log?a(console,"log"):e)||u.apply(this,arguments)}function d(e,n){var a,l,u,d=this,f="loglevel";function p(){var e;if(!(typeof window===t||!f)){try{e=window.localStorage[f]}catch{}if(typeof e===t)try{var n=window.document.cookie,r=encodeURIComponent(f),i=n.indexOf(r+"=");-1!==i&&(e=/^([^;]+)/.exec(n.slice(i+r.length+1))[1])}catch{}return void 0===d.levels[e]&&(e=void 0),e}}function h(e){var t=e;if("string"==typeof t&&void 0!==d.levels[t.toUpperCase()]&&(t=d.levels[t.toUpperCase()]),"number"==typeof t&&t>=0&&t<=d.levels.SILENT)return t;throw TypeError("log.setLevel() called with invalid level: "+e)}"string"==typeof e?f+=":"+e:"symbol"==typeof e&&(f=void 0),d.name=e,d.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},d.methodFactory=n||s,d.getLevel=function(){return u??l??a},d.setLevel=function(e,n){return u=h(e),!1!==n&&function(e){var n=(r[e]||"silent").toUpperCase();if(!(typeof window===t||!f)){try{window.localStorage[f]=n;return}catch{}try{window.document.cookie=encodeURIComponent(f)+"="+n+";"}catch{}}}(u),c.call(d)},d.setDefaultLevel=function(e){l=h(e),p()||d.setLevel(e,!1)},d.resetLevel=function(){u=null,function(){if(!(typeof window===t||!f)){try{window.localStorage.removeItem(f)}catch{}try{window.document.cookie=encodeURIComponent(f)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch{}}}(),c.call(d)},d.enableAll=function(e){d.setLevel(d.levels.TRACE,e)},d.disableAll=function(e){d.setLevel(d.levels.SILENT,e)},d.rebuild=function(){if(o!==d&&(a=h(o.getLevel())),c.call(d),o===d)for(var e in i)i[e].rebuild()},a=h(o?o.getLevel():"WARN");var m=p();null!=m&&(u=h(m)),c.call(d)}(o=new d).getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw TypeError("You must supply a name when creating a logger.");var t=i[e];return t||(t=i[e]=new d(e,o.methodFactory)),t};var f=typeof window!==t?window.log:void 0;return o.noConflict=function(){return typeof window!==t&&window.log===o&&(window.log=f),o},o.getLoggers=function(){return i},o.default=o,o},ec.exports?ec.exports=e():eu.log=e()),ec.exports}());var ed=function(e,t){return(ed=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function ef(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}ed(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function ep(e,t){var n,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){var u=[l,c];if(n)throw TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(o=0)),o;)try{if(n=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return o.label++,{value:u[1],done:!1};case 5:o.label++,r=u[1],u=[0];continue;case 7:u=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){o=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){o.label=u[1];break}if(6===u[0]&&o.label<i[1]){o.label=i[1],i=u;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(u);break}i[2]&&o.ops.pop(),o.trys.pop();continue}u=t.call(e,o)}catch(e){u=[6,e],r=0}finally{n=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}}function eh(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function em(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function ev(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function eb(e){return this instanceof eb?(this.v=e,this):new eb(e)}function ey(e){return"function"==typeof e}function eg(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var ew=eg(function(e){return function(t){e(this),this.message=t?t.length+` errors occurred during unsubscription:
`+t.map(function(e,t){return t+1+") "+e.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=t}});function eE(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ek=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var e,t,n,r,i;if(!this.closed){this.closed=!0;var o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var a=eh(o),l=a.next();!l.done;l=a.next())l.value.remove(this)}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}else o.remove(this);var c=this.initialTeardown;if(ey(c))try{c()}catch(e){i=e instanceof ew?e.errors:[e]}var u=this._finalizers;if(u){this._finalizers=null;try{for(var s=eh(u),d=s.next();!d.done;d=s.next()){var f=d.value;try{eC(f)}catch(e){i=i??[],e instanceof ew?i=ev(ev([],em(i)),em(e.errors)):i.push(e)}}}catch(e){n={error:e}}finally{try{d&&!d.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}}if(i)throw new ew(i)}},t.prototype.add=function(e){var n;if(e&&e!==this)if(this.closed)eC(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!=(n=this._finalizers)?n:[]).push(e)}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&eE(t,e)},t.prototype.remove=function(e){var n=this._finalizers;n&&eE(n,e),e instanceof t&&e._removeParent(this)},(e=new t).closed=!0,t.EMPTY=e,t}(),eS=ek.EMPTY;function ex(e){return e instanceof ek||e&&"closed"in e&&ey(e.remove)&&ey(e.add)&&ey(e.unsubscribe)}function eC(e){ey(e)?e():e.unsubscribe()}var eP={Promise:void 0},eT={setTimeout:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,ev([e,t],em(n)))}};function eM(e){eT.setTimeout(function(){throw e})}function eA(){}var eI=function(e){function t(t){var n=e.call(this)||this;return n.isStopped=!1,t?(n.destination=t,ex(t)&&t.add(n)):n.destination=eL,n}return ef(t,e),t.create=function(e,t,n){return new eO(e,t,n)},t.prototype.next=function(e){this.isStopped||this._next(e)},t.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(ek),eR=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){eM(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){eM(e)}else eM(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){eM(e)}},e}(),eO=function(e){function t(t,n,r){var i,o=e.call(this)||this;return o.destination=new eR(ey(t)||!t?{next:t??void 0,error:n??void 0,complete:r??void 0}:t),o}return ef(t,e),t}(eI),eL={closed:!0,next:eA,error:function(e){throw e},complete:eA},eN="function"==typeof Symbol&&Symbol.observable||"@@observable";function e_(e){return e}var eD=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(e,t,n){var r,i,o=this,a=!function(e){return e&&e instanceof eI||e&&ey(e.next)&&ey(e.error)&&ey(e.complete)&&ex(e)}(e)?new eO(e,t,n):e;return r=o.operator,i=o.source,a.add(r?r.call(a,i):i?o._subscribe(a):o._trySubscribe(a)),a},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var n=this;return new(t=ej(t))(function(t,r){var i=new eO({next:function(t){try{e(t)}catch(e){r(e),i.unsubscribe()}},error:r,complete:t});n.subscribe(i)})},e.prototype._subscribe=function(e){var t;return null==(t=this.source)?void 0:t.subscribe(e)},e.prototype[eN]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0===e.length?e_:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)})(this)},e.prototype.toPromise=function(e){var t=this;return new(e=ej(e))(function(e,n){var r;t.subscribe(function(e){return r=e},function(e){return n(e)},function(){return e(r)})})},e.create=function(t){return new e(t)},e}();function ej(e){var t;return null!=(t=e??eP.Promise)?t:Promise}function ez(e){return function(t){if(ey(null==t?void 0:t.lift))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}function eV(e,t,n,r,i){return new eF(e,t,n,r,i)}var eF=function(e){function t(t,n,r,i,o,a){var l=e.call(this,t)||this;return l.onFinalize=o,l.shouldUnsubscribe=a,l._next=n?function(e){try{n(e)}catch(e){t.error(e)}}:e.prototype._next,l._error=i?function(e){try{i(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,l._complete=r?function(){try{r()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,l}return ef(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;e.prototype.unsubscribe.call(this),n||null==(t=this.onFinalize)||t.call(this)}},t}(eI),eY=eg(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),eH=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return ef(t,e),t.prototype.lift=function(e){var t=new eU(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new eY},t.prototype.next=function(e){var t,n;if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));try{for(var r=eh(this.currentObservers),i=r.next();!i.done;i=r.next())i.value.next(e)}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}}},t.prototype.error=function(e){if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=e;for(var t=this.observers;t.length;)t.shift().error(e)}},t.prototype.complete=function(){if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;for(var e=this.observers;e.length;)e.shift().complete()}},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null==(e=this.observers)?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,n=this.hasError,r=this.isStopped,i=this.observers;return n||r?eS:(this.currentObservers=null,i.push(e),new ek(function(){t.currentObservers=null,eE(i,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,n=this.thrownError,r=this.isStopped;t?e.error(n):r&&e.complete()},t.prototype.asObservable=function(){var e=new eD;return e.source=this,e},t.create=function(e,t){return new eU(e,t)},t}(eD),eU=function(e){function t(t,n){var r=e.call(this)||this;return r.destination=t,r.source=n,r}return ef(t,e),t.prototype.next=function(e){var t,n;null==(n=null==(t=this.destination)?void 0:t.next)||n.call(t,e)},t.prototype.error=function(e){var t,n;null==(n=null==(t=this.destination)?void 0:t.error)||n.call(t,e)},t.prototype.complete=function(){var e,t;null==(t=null==(e=this.destination)?void 0:e.complete)||t.call(e)},t.prototype._subscribe=function(e){var t,n;return null!=(n=null==(t=this.source)?void 0:t.subscribe(e))?n:eS},t}(eH),eW=function(e){function t(t){var n=e.call(this)||this;return n._value=t,n}return ef(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),t.prototype._subscribe=function(t){var n=e.prototype._subscribe.call(this,t);return n.closed||t.next(this._value),n},t.prototype.getValue=function(){var e=this.hasError,t=this.thrownError,n=this._value;if(e)throw t;return this._throwIfClosed(),n},t.prototype.next=function(t){e.prototype.next.call(this,this._value=t)},t}(eH),e$={now:function(){return Date.now()}},eZ=function(e){function t(t,n){return e.call(this)||this}return ef(t,e),t.prototype.schedule=function(e,t){return this},t}(ek),eB={setInterval:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setInterval.apply(void 0,ev([e,t],em(n)))},clearInterval:function(e){return clearInterval(e)}},eq=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r.pending=!1,r}return ef(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var n,r=this.id,i=this.scheduler;return null!=r&&(this.id=this.recycleAsyncId(i,r,t)),this.pending=!0,this.delay=t,this.id=null!=(n=this.id)?n:this.requestAsyncId(i,this.id,t),this},t.prototype.requestAsyncId=function(e,t,n){return void 0===n&&(n=0),eB.setInterval(e.flush.bind(e,this),n)},t.prototype.recycleAsyncId=function(e,t,n){if(void 0===n&&(n=0),null!=n&&this.delay===n&&!1===this.pending)return t;null!=t&&eB.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var n=this._execute(e,t);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var n,r=!1;try{this.work(e)}catch(e){r=!0,n=e||Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),n},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,n=this.scheduler,r=n.actions;this.work=this.state=this.scheduler=null,this.pending=!1,eE(r,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(eZ),eJ=function(){function e(t,n){void 0===n&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(e,t,n){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(n,t)},e.now=e$.now,e}(),eQ=new(function(e){function t(t,n){void 0===n&&(n=eJ.now);var r=e.call(this,t,n)||this;return r.actions=[],r._active=!1,r}return ef(t,e),t.prototype.flush=function(e){var t,n=this.actions;if(this._active)return void n.push(e);this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,t){for(;e=n.shift();)e.unsubscribe();throw t}},t}(eJ))(eq);function eG(e){var t;return(t=e[e.length-1])&&ey(t.schedule)?e.pop():void 0}var eX=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function eK(e){return ey(null==e?void 0:e.then)}function e0(e){return Symbol.asyncIterator&&ey(null==e?void 0:e[Symbol.asyncIterator])}function e1(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var e5="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function e2(e){return ey(null==e?void 0:e[e5])}function e9(e){return function(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",function(e){return function(t){return Promise.resolve(t).then(e,u)}}),r[Symbol.asyncIterator]=function(){return this},r;function a(e,t){i[e]&&(r[e]=function(t){return new Promise(function(n,r){o.push([e,t,n,r])>1||l(e,t)})},t&&(r[e]=t(r[e])))}function l(e,t){try{var n;(n=i[e](t)).value instanceof eb?Promise.resolve(n.value.v).then(c,u):s(o[0][2],n)}catch(e){s(o[0][3],e)}}function c(e){l("next",e)}function u(e){l("throw",e)}function s(e,t){e(t),o.shift(),o.length&&l(o[0][0],o[0][1])}}(this,arguments,function(){var t,n,r;return ep(this,function(i){switch(i.label){case 0:t=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,eb(t.read())];case 3:return r=(n=i.sent()).value,n.done?[4,eb(void 0)]:[3,5];case 4:return[2,i.sent()];case 5:return[4,eb(r)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function e6(e){return ey(null==e?void 0:e.getReader)}function e7(e){if(e instanceof eD)return e;if(null!=e){var t,n,r,i;if(ey(e[eN])){return t=e,new eD(function(e){var n=t[eN]();if(ey(n.subscribe))return n.subscribe(e);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if(eX(e)){return n=e,new eD(function(e){for(var t=0;t<n.length&&!e.closed;t++)e.next(n[t]);e.complete()})}if(eK(e)){return r=e,new eD(function(e){r.then(function(t){e.closed||(e.next(t),e.complete())},function(t){return e.error(t)}).then(null,eM)})}if(e0(e))return e3(e);if(e2(e)){return i=e,new eD(function(e){var t,n;try{for(var r=eh(i),o=r.next();!o.done;o=r.next()){var a=o.value;if(e.next(a),e.closed)return}}catch(e){t={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}e.complete()})}if(e6(e))return e3(e9(e))}throw e1(e)}function e3(e){return new eD(function(t){(function(e,t){var n,r,i,o,a,l,c,u;return a=this,l=void 0,c=void 0,u=function(){var a;return ep(this,function(l){switch(l.label){case 0:l.trys.push([0,5,6,11]),n=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=eh(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise(function(r,i){var o,a,l;o=r,a=i,l=(t=e[n](t)).done,Promise.resolve(t.value).then(function(e){o({value:e,done:l})},a)})}}}(e),l.label=1;case 1:return[4,n.next()];case 2:if((r=l.sent()).done)return[3,4];if(a=r.value,t.next(a),t.closed)return[2];l.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return i={error:l.sent()},[3,11];case 6:return l.trys.push([6,,9,10]),r&&!r.done&&(o=n.return)?[4,o.call(n)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})},new(c||(c=Promise))(function(e,t){function n(e){try{i(u.next(e))}catch(e){t(e)}}function r(e){try{i(u.throw(e))}catch(e){t(e)}}function i(t){var i;t.done?e(t.value):((i=t.value)instanceof c?i:new c(function(e){e(i)})).then(n,r)}i((u=u.apply(a,l||[])).next())})})(e,t).catch(function(e){return t.error(e)})})}function e4(e,t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=!1);var o=t.schedule(function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function e8(e,t){return void 0===t&&(t=0),ez(function(n,r){n.subscribe(eV(r,function(n){return e4(r,e,function(){return r.next(n)},t)},function(){return e4(r,e,function(){return r.complete()},t)},function(n){return e4(r,e,function(){return r.error(n)},t)}))})}function te(e,t){return void 0===t&&(t=0),ez(function(n,r){r.add(e.schedule(function(){return n.subscribe(r)},t))})}function tt(e,t){if(!e)throw Error("Iterable cannot be null");return new eD(function(n){e4(n,t,function(){var r=e[Symbol.asyncIterator]();e4(n,t,function(){r.next().then(function(e){e.done?n.complete():n.next(e.value)})},0,!0)})})}function tn(e,t){return t?function(e,t){if(null!=e){if(ey(e[eN]))return e7(e).pipe(te(t),e8(t));if(eX(e))return new eD(function(n){var r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})});if(eK(e))return e7(e).pipe(te(t),e8(t));if(e0(e))return tt(e,t);if(e2(e))return new eD(function(n){var r;return e4(n,t,function(){r=e[e5](),e4(n,t,function(){var e,t,i;try{t=(e=r.next()).value,i=e.done}catch(e){n.error(e);return}i?n.complete():n.next(t)},0,!0)}),function(){return ey(null==r?void 0:r.return)&&r.return()}});if(e6(e))return tt(e9(e),t)}throw e1(e)}(e,t):e7(e)}function tr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=eG(e);return tn(e,n)}var ti=eg(function(e){return function(t){void 0===t&&(t=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t}});function to(e){throw new ti(e)}function ta(e,t){return ez(function(n,r){var i=0;n.subscribe(eV(r,function(n){r.next(e.call(t,n,i++))}))})}var tl=Array.isArray;function tc(e,t,n){return void 0===n&&(n=1/0),ey(t)?tc(function(n,r){return ta(function(e,i){return t(n,e,r,i)})(e7(e(n,r)))},n):("number"==typeof t&&(n=t),ez(function(t,r){var i,o,a,l,c,u,s;return i=n,o=[],a=0,l=0,c=!1,u=function(){!c||o.length||a||r.complete()},s=function(t){a++;var n=!1;e7(e(t,l++)).subscribe(eV(r,function(e){r.next(e)},function(){n=!0},void 0,function(){if(n)try{for(a--;o.length&&a<i;)!function(){var e=o.shift();s(e)}();u()}catch(e){r.error(e)}}))},t.subscribe(eV(r,function(e){return a<i?s(e):o.push(e)},function(){c=!0,u()})),function(){}}))}function tu(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return tc(e_,1)(tn(e,eG(e)))}var ts=["addListener","removeListener"],td=["addEventListener","removeEventListener"],tf=["on","off"];function tp(e,t){return function(n){return function(r){return e[n](t,r)}}}function th(e,t){return ez(function(n,r){var i=0;n.subscribe(eV(r,function(n){return e.call(t,n,i++)&&r.next(n)}))})}function tm(e,t){return e===t}function tv(e,t){var n;return ez((n=arguments.length>=2,function(r,i){var o=n,a=t,l=0;r.subscribe(eV(i,function(t){var n=l++;a=o?e(a,t,n):(o=!0,t),i.next(a)},void 0))}))}function tb(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=eG(e);return ez(function(t,r){(n?tu(e,t,n):tu(e,t)).subscribe(r)})}function ty(e){return ez(function(t,n){e7(e).subscribe(eV(n,function(){return n.complete()},eA)),n.closed||t.subscribe(n)})}var tg=Object.defineProperty,tw=Object.defineProperties,tE=Object.getOwnPropertyDescriptors,tk=Object.getOwnPropertySymbols,tS=Object.prototype.hasOwnProperty,tx=Object.prototype.propertyIsEnumerable,tC=(e,t,n)=>t in e?tg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,tP=(e,t)=>{for(var n in t||(t={}))tS.call(t,n)&&tC(e,n,t[n]);if(tk)for(var n of tk(t))tx.call(t,n)&&tC(e,n,t[n]);return e},tT=(e,t)=>tw(e,tE(t)),tM=(e,t,n)=>new Promise((r,i)=>{var o=e=>{try{l(n.next(e))}catch(e){i(e)}},a=e=>{try{l(n.throw(e))}catch(e){i(e)}},l=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,a);l((n=n.apply(e,t)).next())});function tA(e){var t,n,r;return!(typeof e>"u")&&(!!(t=e)&&t.hasOwnProperty("participant")&&t.hasOwnProperty("source")&&t.hasOwnProperty("track")&&"u">typeof(null==(n=t.publication)?void 0:n.track)||!!(r=e)&&r.hasOwnProperty("participant")&&r.hasOwnProperty("source")&&r.hasOwnProperty("publication")&&"u">typeof r.publication)}function tI(e){return!!e&&e.hasOwnProperty("participant")&&e.hasOwnProperty("source")&&typeof e.publication>"u"}function tR(e){if("string"==typeof e||"number"==typeof e)return`${e}`;if(tI(e))return`${e.participant.identity}_${e.source}_placeholder`;if(tA(e))return`${e.participant.identity}_${e.publication.source}_${e.publication.trackSid}`;throw Error(`Can't generate a id for the given track reference: ${e}`)}function tO(e,t){return!(typeof t>"u")&&(tA(e)?t.some(t=>t.participant.identity===e.participant.identity&&tA(t)&&t.publication.trackSid===e.publication.trackSid):!!tI(e)&&t.some(t=>t.participant.identity===e.participant.identity&&tI(t)&&t.source===e.source))}function tL(e,t,n){return function(e,t,n,r){void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,f=W(e),p=i||l?[...f?Y(f):[],...Y(t)]:[];p.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let h=f&&s?function(e,t){let n=null,r,i=M(e);function l(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function u(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(s||t(),!m||!v)return;let b=c(h),y=c(i.clientWidth-(p+m)),g={rootMargin:-b+"px "+-y+"px "+-c(i.clientHeight-(h+v))+"px "+-c(p)+"px",threshold:a(0,o(1,d))||1},w=!0;function E(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||er(f,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(E,{...g,root:i.ownerDocument})}catch{n=new IntersectionObserver(E,g)}n.observe(e)}(!0),l}(f,n):null,m=-1,v=null;u&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),f&&!d&&v.observe(f),v.observe(t));let b,y=d?q(e):null;return d&&function t(){let r=q(e);y&&!er(y,r)&&n(),y=r,b=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(b)}}(e,t,()=>tM(this,null,function*(){var r,i;let{x:l,y:c}=yield ei(e,t,{placement:"top",middleware:[{name:"offset",options:6,async fn(e){var t,n;let{x:r,y:i,placement:o,middlewareData:a}=e,l=await x(e,6);return o===(null==(t=a.offset)?void 0:t.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:r+l.x,y:i+l.y,data:{...l,placement:o}}}},(void 0===r&&(r={}),{name:"flip",options:r,async fn(e){var t,n,i,o,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:s,platform:d,elements:w}=e,{mainAxis:E=!0,crossAxis:k=!0,fallbackPlacements:x,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:T=!0,...M}=f(r,e);if(null!=(t=c.arrow)&&t.alignmentOffset)return{};let A=p(l),I=b(s),R=p(s)===s,O=await (null==d.isRTL?void 0:d.isRTL(w.floating)),L=x||(R||!T?[g(s)]:function(e){let t=g(e);return[y(e),t,y(t)]}(s)),N="none"!==P;!x&&N&&L.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":return n?t?i:r:t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(s,T,P,O));let _=[s,...L],D=await S(e,M),j=[],z=(null==(n=c.flip)?void 0:n.overflows)||[];if(E&&j.push(D[A]),k){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(b(e)),o=v(i),a="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=g(a)),[a,g(a)]}(l,u,O);j.push(D[e[0]],D[e[1]])}if(z=[...z,{placement:l,overflows:j}],!j.every(e=>e<=0)){let e=((null==(i=c.flip)?void 0:i.index)||0)+1,t=_[e];if(t)return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(o=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let e=null==(a=z.filter(e=>{if(N){let t=b(e.placement);return t===I||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}),{name:"shift",options:i={padding:5},async fn(e){let{x:t,y:n,placement:r}=e,{mainAxis:l=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(i,e),d={x:t,y:n},h=await S(e,s),v=b(p(r)),y=m(v),g=d[y],w=d[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=g+h[e],r=g-h[t];g=a(n,o(g,r))}if(c){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,o(w,r))}let E=u.fn({...e,[y]:g,[v]:w});return{...E,data:{x:E.x-t,y:E.y-n,enabled:{[y]:l,[v]:c}}}}}]});null==n||n(l,c)}))}function tN(e,t){return!e.contains(t.target)}var t_=[r.u9.ConnectionStateChanged,r.u9.RoomMetadataChanged,r.u9.ActiveSpeakersChanged,r.u9.ConnectionQualityChanged,r.u9.ParticipantConnected,r.u9.ParticipantDisconnected,r.u9.ParticipantPermissionsChanged,r.u9.ParticipantMetadataChanged,r.u9.ParticipantNameChanged,r.u9.ParticipantAttributesChanged,r.u9.TrackMuted,r.u9.TrackUnmuted,r.u9.TrackPublished,r.u9.TrackUnpublished,r.u9.TrackStreamStateChanged,r.u9.TrackSubscriptionFailed,r.u9.TrackSubscriptionPermissionChanged,r.u9.TrackSubscriptionStatusChanged,r.u9.LocalTrackPublished,r.u9.LocalTrackUnpublished],tD=(r.YI.TrackPublished,r.YI.TrackUnpublished,r.YI.TrackMuted,r.YI.TrackUnmuted,r.YI.TrackStreamStateChanged,r.YI.TrackSubscribed,r.YI.TrackUnsubscribed,r.YI.TrackSubscriptionPermissionChanged,r.YI.TrackSubscriptionFailed,r.YI.LocalTrackPublished,r.YI.LocalTrackUnpublished,r.YI.ConnectionQualityChanged,r.YI.IsSpeakingChanged,r.YI.ParticipantMetadataChanged,r.YI.ParticipantPermissionsChanged,r.YI.TrackMuted,r.YI.TrackUnmuted,r.YI.TrackPublished,r.YI.TrackUnpublished,r.YI.TrackStreamStateChanged,r.YI.TrackSubscriptionFailed,r.YI.TrackSubscriptionPermissionChanged,r.YI.TrackSubscriptionStatusChanged,r.YI.LocalTrackPublished,r.YI.LocalTrackUnpublished,es.getLogger("lk-components-js"));tD.setDefaultLevel("WARN");var tj=[{columns:1,rows:1},{columns:1,rows:2,orientation:"portrait"},{columns:2,rows:1,orientation:"landscape"},{columns:2,rows:2,minWidth:560},{columns:3,rows:3,minWidth:700},{columns:4,rows:4,minWidth:960},{columns:5,rows:5,minWidth:1100}];function tz(){return"u">typeof navigator&&navigator.mediaDevices&&!!navigator.mediaDevices.getDisplayMedia}function tV(e){return"object"==typeof e}function tF(e){return Array.isArray(e)&&e.filter(tV).length>0}function tY(e,t){var n,r,i,o;return(null!=(r=null==(n=e.joinedAt)?void 0:n.getTime())?r:0)-(null!=(o=null==(i=t.joinedAt)?void 0:i.getTime())?o:0)}function tH(e){let t=[],n=[],i=[],o=[];return e.forEach(e=>{e.participant.isLocal&&e.source===r.CC.Source.Camera?t.push(e):e.source===r.CC.Source.ScreenShare?n.push(e):e.source===r.CC.Source.Camera?i.push(e):o.push(e)}),[...t,...function(e){let t=[],n=[];return e.forEach(e=>{e.participant.isLocal?t.push(e):n.push(e)}),t.sort((e,t)=>tY(e.participant,t.participant)),n.sort((e,t)=>tY(e.participant,t.participant)),[...n,...t]}(n),...function(e){let t=[],n=[];return e.forEach(e=>{e.participant.isLocal?t.push(e):n.push(e)}),n.sort((e,t)=>{var n,r,i,o,a,l,c,u,s;return e.participant.isSpeaking&&t.participant.isSpeaking?(n=e.participant,t.participant.audioLevel-n.audioLevel):e.participant.isSpeaking!==t.participant.isSpeaking?(r=e.participant,i=t.participant,r.isSpeaking===i.isSpeaking?0:r.isSpeaking?-1:1):e.participant.lastSpokeAt!==t.participant.lastSpokeAt?(o=e.participant,a=t.participant,void 0!==o.lastSpokeAt||void 0!==a.lastSpokeAt?(null!=(c=null==(l=a.lastSpokeAt)?void 0:l.getTime())?c:0)-(null!=(s=null==(u=o.lastSpokeAt)?void 0:u.getTime())?s:0):0):tA(e)!==tA(t)?tA(e)?tA(t)?0:-1:+!!tA(t):e.participant.isCameraEnabled!==t.participant.isCameraEnabled?function(e,t){let n=e.participant.isCameraEnabled;return n!==t.participant.isCameraEnabled?n?-1:1:0}(e,t):tY(e.participant,t.participant)}),[...t,...n]}(i),...o]}function tU(e,t){return Array(Math.max(e.length,t.length)).fill([]).map((n,r)=>[e[r],t[r]])}function tW(e,t,n){return e.filter(e=>!t.map(e=>n(e)).includes(n(e)))}function t$(e){return e.map(e=>"string"==typeof e||"number"==typeof e?`${e}`:tR(e))}function tZ(e,t){let n=t.findIndex(t=>tR(t)===tR(e));if(-1===n)throw Error(`Element not part of the array: ${tR(e)} not in ${t$(t)}`);return n}function tB(e,t){return e.reduce((e,n,r)=>r%t==0?[...e,[n]]:[...e.slice(0,-1),[...e.slice(-1)[0],n]],[])}function tq(e,t,n){var r,i;let o=(r=e,i=t,r.map(e=>i.find(t=>tR(e)===tR(t)||"number"!=typeof e&&tI(e)&&tA(t)&&tI(e)&&tA(t)&&t.participant.identity===e.participant.identity&&t.source===e.source)??e));if(o.length<t.length){let e=tW(t,o,tR);o=[...o,...e]}if(tU(tB(o,n),tB(t,n)).forEach(([e,t],r)=>{if(e&&t){var i;let a={dropped:tW(i=tB(o,n)[r],t,tR),added:tW(t,i,tR)};(0!==a.added.length||0!==a.dropped.length)&&(tD.debug(`Detected visual changes on page: ${r}, current: ${t$(e)}, next: ${t$(t)}`,{changes:a}),a.added.length===a.dropped.length&&tU(a.added,a.dropped).forEach(([e,t])=>{if(e&&t)o=function(e,t,n){let r=tZ(e,n),i=tZ(t,n);return n.splice(r,1,t),n.splice(i,1,e),n}(e,t,o);else throw Error(`For a swap action we need a addition and a removal one is missing: ${e}, ${t}`)}),0===a.added.length&&a.dropped.length>0&&a.dropped.forEach(e=>{o=function(e,t){let n=tZ(e,t);return t.splice(n,1),t}(e,o)}),a.added.length>0&&0===a.dropped.length&&a.added.forEach(e=>{o=[...o,e]}))}}),o.length>t.length){let e=tW(o,t,tR);o=o.filter(t=>!e.map(tR).includes(tR(t)))}return o}function tJ(e){return`lk-${e}`}function tQ(e){let t=tG(e),n=t7(e.participant).pipe(ta(()=>tG(e)),tb(t));return{className:tJ(e.source===r.CC.Source.Camera||e.source===r.CC.Source.ScreenShare?"participant-media-video":"participant-media-audio"),trackObserver:n}}function tG(e){if(tA(e))return e.publication;{let{source:t,name:n,participant:r}=e;if(t&&n)return r.getTrackPublications().find(e=>e.source===t&&e.trackName===n);if(n)return r.getTrackPublicationByName(n);if(t)return r.getTrackPublication(t);throw Error("At least one of source and name needs to be defined")}}function tX(e,...t){return new eD(n=>{let r=()=>{n.next(e)};return t.forEach(t=>{e.on(t,r)}),()=>{t.forEach(t=>{e.off(t,r)})}}).pipe(tb(e))}function tK(e,t){return new eD(n=>{let r=(...e)=>{n.next(e)};return e.on(t,r),()=>{e.off(t,r)}})}function t0(e){return tK(e,r.u9.ConnectionStateChanged).pipe(ta(([e])=>e),tb(e.state))}function t1(e,t,n=!0){return tu(new eD(i=>{r.Wv.getLocalDevices(e,n).then(e=>{i.next(e),i.complete()}).catch(e=>{null==t||t(e),i.next([]),i.complete()})}),new eD(i=>{var o;let a=()=>tM(this,null,function*(){try{let t=yield r.Wv.getLocalDevices(e,n);i.next(t)}catch(e){null==t||t(e)}});if("u">typeof window){if(!window.isSecureContext)throw Error("Accessing media devices is available only in secure contexts (HTTPS and localhost), in some or all supporting browsers. See: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/mediaDevices");null==(o=null==navigator?void 0:navigator.mediaDevices)||o.addEventListener("devicechange",a)}return()=>{var e;null==(e=null==navigator?void 0:navigator.mediaDevices)||e.removeEventListener("devicechange",a)}}))}function t5(e){return tX(e,r.u9.AudioPlaybackStatusChanged).pipe(ta(e=>({canPlayAudio:e.canPlaybackAudio})))}function t2(e){return tX(e,r.u9.VideoPlaybackStatusChanged).pipe(ta(e=>({canPlayVideo:e.canPlaybackVideo})))}function t9(e,t){return tK(e,r.u9.ParticipantEncryptionStatusChanged).pipe(th(([,n])=>(null==t?void 0:t.identity)===(null==n?void 0:n.identity)||!n&&(null==t?void 0:t.identity)===e.localParticipant.identity),ta(([e])=>e),tb(null!=t&&t.isLocal?t.isE2EEEnabled:!!(null!=t&&t.isEncrypted)))}function t6(e,...t){return new eD(n=>{let r=()=>{n.next(e)};return t.forEach(t=>{e.on(t,r)}),()=>{t.forEach(t=>{e.off(t,r)})}}).pipe(tb(e))}function t7(e){return t6(e,r.YI.TrackMuted,r.YI.TrackUnmuted,r.YI.ParticipantPermissionsChanged,r.YI.TrackPublished,r.YI.TrackUnpublished,r.YI.LocalTrackPublished,r.YI.LocalTrackUnpublished,r.YI.MediaDevicesError,r.YI.TrackSubscriptionStatusChanged).pipe(ta(e=>{let{isMicrophoneEnabled:t,isCameraEnabled:n,isScreenShareEnabled:i}=e,o=e.getTrackPublication(r.CC.Source.Microphone);return{isCameraEnabled:n,isMicrophoneEnabled:t,isScreenShareEnabled:i,cameraTrack:e.getTrackPublication(r.CC.Source.Camera),microphoneTrack:o,participant:e}}))}function t3(e,t){return new eD(n=>{let r=(...e)=>{n.next(e)};return e.on(t,r),()=>{e.off(t,r)}})}function t4(e){var t,n,i,o;return t6(e.participant,r.YI.TrackMuted,r.YI.TrackUnmuted,r.YI.TrackSubscribed,r.YI.TrackUnsubscribed,r.YI.LocalTrackPublished,r.YI.LocalTrackUnpublished).pipe(ta(t=>{var n,r;let i=null!=(n=e.publication)?n:t.getTrackPublication(e.source);return null==(r=null==i?void 0:i.isMuted)||r}),tb(null==(o=null!=(i=null==(t=e.publication)?void 0:t.isMuted)?i:null==(n=e.participant.getTrackPublication(e.source))?void 0:n.isMuted)||o))}function t8(e){return t3(e,r.YI.IsSpeakingChanged).pipe(ta(([e])=>e))}function ne(e,t={}){var n;let i,o=new eD(e=>(i=e,()=>l.unsubscribe())).pipe(tb(Array.from(e.remoteParticipants.values()))),a=null!=(n=t.additionalRoomEvents)?n:t_,l=tX(e,...Array.from(new Set([r.u9.ParticipantConnected,r.u9.ParticipantDisconnected,r.u9.ConnectionStateChanged,...a]))).subscribe(e=>null==i?void 0:i.next(Array.from(e.remoteParticipants.values())));return e.remoteParticipants.size>0&&(null==i||i.next(Array.from(e.remoteParticipants.values()))),o}function nt(e){return t3(e,r.YI.ParticipantPermissionsChanged).pipe(ta(()=>e.permissions),tb(e.permissions))}function nn(e,t,n,i,o){let{localParticipant:a}=t,l=(e,t)=>{let n=!1;switch(e){case r.CC.Source.Camera:n=t.isCameraEnabled;break;case r.CC.Source.Microphone:n=t.isMicrophoneEnabled;break;case r.CC.Source.ScreenShare:n=t.isScreenShareEnabled}return n},c=t7(a).pipe(ta(t=>l(e,t.participant)),tb(l(e,a))),u=new eH;return{className:tJ("button"),toggle:(t,l)=>tM(this,null,function*(){try{switch(l??(l=n),u.next(!0),e){case r.CC.Source.Camera:return yield a.setCameraEnabled(t??!a.isCameraEnabled,l,i),a.isCameraEnabled;case r.CC.Source.Microphone:return yield a.setMicrophoneEnabled(t??!a.isMicrophoneEnabled,l,i),a.isMicrophoneEnabled;case r.CC.Source.ScreenShare:return yield a.setScreenShareEnabled(t??!a.isScreenShareEnabled,l,i),a.isScreenShareEnabled;default:throw TypeError("Tried to toggle unsupported source")}}catch(e){if(o&&e instanceof Error){null==o||o(e);return}throw e}finally{u.next(!1)}}),enabledObserver:c,pendingObserver:u.asObservable()}}function nr(){let e=!1,t=new eH,n=new eH;return{className:tJ("button"),toggle:r=>tM(this,null,function*(){n.next(!0),e=r??!e,t.next(e),n.next(!1)}),enabledObserver:t.asObservable(),pendingObserver:n.asObservable()}}function ni(e,t,n){let i=new eW(void 0),o=tK(t,r.u9.ActiveDeviceChanged).pipe(th(([t])=>t===e),ta(([e,t])=>(tD.debug("activeDeviceObservable | RoomEvent.ActiveDeviceChanged",{kind:e,deviceId:t}),t)));return{className:tJ("media-device-select"),activeDeviceObservable:o,setActiveMediaDevice:(n,...o)=>tM(this,[n,...o],function*(n,o={}){var a,l,c;if(t){let u;tD.debug(`Switching active device of kind "${e}" with id ${n}.`),yield t.switchActiveDevice(e,n,o.exact);let s=null!=(a=t.getActiveDevice(e))?a:n;s!==n&&"default"!==n&&tD.info(`We tried to select the device with id (${n}), but the browser decided to select the device with id (${s}) instead.`),"audioinput"===e?u=null==(l=t.localParticipant.getTrackPublication(r.CC.Source.Microphone))?void 0:l.track:"videoinput"===e&&(u=null==(c=t.localParticipant.getTrackPublication(r.CC.Source.Camera))?void 0:c.track);let d="default"===n&&!u||"default"===n&&(null==u?void 0:u.mediaStreamTrack.label.startsWith("Default"));i.next(d?n:s)}})}}function no(e){return{className:tJ("disconnect-button"),disconnect:t=>{e.disconnect(t)}}}function na(e){return{className:tJ("connection-quality"),connectionQualityObserver:t3(e,r.YI.ConnectionQualityChanged).pipe(ta(([e])=>e),tb(e.connectionQuality))}}function nl(e){let t="track-muted-indicator-camera";switch(e.source){case r.CC.Source.Camera:t="track-muted-indicator-camera";break;case r.CC.Source.Microphone:t="track-muted-indicator-microphone"}return{className:tJ(t),mediaMutedObserver:t4(e)}}function nc(e){return{className:"lk-participant-name",infoObserver:e?t6(e,r.YI.ParticipantMetadataChanged,r.YI.ParticipantNameChanged).pipe(ta(({name:e,identity:t,metadata:n})=>({name:e,identity:t,metadata:n})),tb({name:e.name,identity:e.identity,metadata:e.metadata})):void 0}}function nu(){return{className:tJ("participant-tile")}}var ns={CHAT:"lk.chat"},nd={CHAT:"lk-chat-topic"};function nf(e,t){return tM(this,arguments,function*(e,t,n={}){let{reliable:r,destinationIdentities:i,topic:o}=n;yield e.publishData(t,{destinationIdentities:i,topic:o,reliable:r})})}var np=new WeakMap,nh=e=>JSON.parse(new TextDecoder().decode(e)),nm=e=>new TextEncoder().encode(JSON.stringify(e));function nv(e,t){var n,i,o,a,l,c;let u=()=>{var t,n,i;return(null==(t=e.serverInfo)?void 0:t.edition)===1||!!(null!=(n=e.serverInfo)&&n.version)&&(0,r.Zy)(null==(i=e.serverInfo)?void 0:i.version,"1.8.2")>0},s=new eH,d=null!=(n=null==t?void 0:t.channelTopic)?n:ns.CHAT,f=null!=(i=null==t?void 0:t.channelTopic)?i:nd.CHAT,p=!1;np.has(e)||(p=!0);let h=null!=(o=np.get(e))?o:new Map,m=null!=(a=h.get(d))?a:new eH;h.set(d,m),np.set(e,h);let v=null!=(l=null==t?void 0:t.messageDecoder)?l:nh;if(p){e.registerTextStreamHandler(d,(t,n)=>tM(this,null,function*(){let{id:r,timestamp:i}=t.info;tn(t).pipe(tv((e,t)=>e+t),ta(t=>({id:r,timestamp:i,message:t,from:e.getParticipantByIdentity(n.identity)}))).subscribe({next:e=>m.next(e)})}));let{messageObservable:t}=function(e,t,n){let i,o=Array.isArray(t)?t:[t];return{messageObservable:tK(e,r.u9.DataReceived).pipe(th(([,,,e])=>void 0===t||void 0!==e&&o.includes(e)),ta(([e,t,,n])=>({payload:e,topic:n,from:t}))),isSendingObservable:new eD(e=>{i=e}),send:(t,...n)=>tM(this,[t,...n],function*(t,n={}){i.next(!0);try{yield nf(e.localParticipant,t,tP({topic:o[0]},n))}finally{i.next(!1)}})}}(e,[f]);t.pipe(ta(e=>{let t=v(e.payload);return!0==t.ignoreLegacy?void 0:tT(tP({},t),{from:e.from})}),th(e=>!!e),ty(s)).subscribe(m)}let b=m.pipe(tv((e,t)=>{if("id"in t&&e.find(e=>{var n,r;return(null==(n=e.from)?void 0:n.identity)===(null==(r=t.from)?void 0:r.identity)&&e.id===t.id})){let n=e.findIndex(e=>e.id===t.id);if(n>-1){let r=e[n];e[n]=tT(tP({},t),{timestamp:r.timestamp,editTimestamp:t.timestamp})}return[...e]}return[...e,t]},[]),ty(s)),y=new eW(!1),g=null!=(c=null==t?void 0:t.messageEncoder)?c:nm;return e.once(r.u9.Disconnected,function(){s.next(),s.complete(),m.complete(),np.delete(e),e.unregisterTextStreamHandler(d)}),{messageObservable:b,isSendingObservable:y,send:(t,n)=>tM(this,null,function*(){n||(n={}),null!=n.topic||(n.topic=d),y.next(!0);try{let r={id:(yield e.localParticipant.sendText(t,n)).id,timestamp:Date.now(),message:t},i=tT(tP({},r),{attachedFiles:n.attachments}),o=tT(tP({},i),{from:e.localParticipant,attributes:n.attributes});m.next(o);let a=g(tT(tP({},r),{ignoreLegacy:u()}));try{yield nf(e.localParticipant,a,{reliable:!0,topic:f})}catch(e){tD.info("could not send message in legacy chat format",e)}return o}finally{y.next(!1)}})}}function nb(){return{className:tJ("start-audio-button"),roomAudioPlaybackAllowedObservable:t5,handleStartAudioPlayback:e=>tM(this,null,function*(){tD.info("Start Audio for room: ",e),yield e.startAudio()})}}function ny(){return{className:tJ("start-audio-button"),roomVideoPlaybackAllowedObservable:t2,handleStartVideoPlayback:e=>tM(this,null,function*(){tD.info("Start Video for room: ",e),yield e.startVideo()})}}function ng(){return{className:[tJ("button"),tJ("chat-toggle")].join(" ")}}function nw(){return{className:[tJ("button"),tJ("focus-toggle-button")].join(" ")}}function nE(){return{className:"lk-room-container"}}function nk(e,t,n=!0){let r=[e.localParticipant,...Array.from(e.remoteParticipants.values())],i=[];return r.forEach(e=>{t.forEach(t=>{let r=Array.from(e.trackPublications.values()).filter(e=>e.source===t&&(!n||e.track)).map(t=>({participant:e,publication:t,source:t.source}));i.push(...r)})}),{trackReferences:i,participants:r}}function nS(e,t,n){var i,o;let a=null!=(i=n.additionalRoomEvents)?i:t_,l=null==(o=n.onlySubscribed)||o;return tX(e,...Array.from(new Set([r.u9.ParticipantConnected,r.u9.ParticipantDisconnected,r.u9.ConnectionStateChanged,r.u9.LocalTrackPublished,r.u9.LocalTrackUnpublished,r.u9.TrackPublished,r.u9.TrackUnpublished,r.u9.TrackSubscriptionStatusChanged,...a]).values())).pipe(ta(e=>{let n=nk(e,t,l);return tD.debug(`TrackReference[] was updated. (length ${n.trackReferences.length})`,n),n}),tb(nk(e,t,l)))}function nx(e,t=1e3){var n,r;if(null===e)return tr(!1);let i=(function e(t,n,r,i){if(ey(r)&&(i=r,r=void 0),i){return e(t,n,r).pipe((o=i,ta(function(e){return tl(e)?o.apply(void 0,ev([],em(e))):o(e)})))}var o,a,l,c,u=em(ey((a=t).addEventListener)&&ey(a.removeEventListener)?td.map(function(e){return function(i){return t[e](n,i,r)}}):ey((l=t).addListener)&&ey(l.removeListener)?ts.map(tp(t,n)):ey((c=t).on)&&ey(c.off)?tf.map(tp(t,n)):[],2),s=u[0],d=u[1];if(!s&&eX(t))return tc(function(t){return e(t,n,r)})(e7(t));if(!s)throw TypeError("Invalid event target");return new eD(function(e){var t=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.next(1<t.length?t:t[0])};return s(t),function(){return d(t)}})})(e,"mousemove",{passive:!0}).pipe(ta(()=>!0)),o=i.pipe(function(e,t){var n=e instanceof Date&&!isNaN(e)?{first:e}:"number"==typeof e?{each:e}:e,r=n.first,i=n.each,o=n.with,a=void 0===o?to:o,l=n.scheduler,c=void 0===l?eQ:l,u=n.meta,s=void 0===u?null:u;if(null==r&&null==i)throw TypeError("No timeout provided.");return ez(function(e,t){var n,o,l=null,u=0,d=function(e){o=e4(t,c,function(){try{n.unsubscribe(),e7(a({meta:s,lastValue:l,seen:u})).subscribe(t)}catch(e){t.error(e)}},e)};n=e.subscribe(eV(t,function(e){null==o||o.unsubscribe(),u++,t.next(l=e),i>0&&d(i)},void 0,void 0,function(){null!=o&&o.closed||null==o||o.unsubscribe(),l=null})),u||d(null!=r?"number"==typeof r?r:r-c.now():i)})}({each:t,with:()=>tu(tr(!1),o.pipe(ez(function(e,t){var n=!1,r=eV(t,function(){null==r||r.unsubscribe(),n=!0},eA);e7(i).subscribe(r),e.subscribe(eV(t,function(e){return n&&t.next(e)}))})))}),(void 0===r&&(r=e_),n=n??tm,ez(function(e,t){var i,o=!0;e.subscribe(eV(t,function(e){var a=r(e);(o||!n(i,a))&&(o=!1,i=a,t.next(e))}))})));return o}var nC={videoEnabled:!0,audioEnabled:!0,videoDeviceId:"default",audioDeviceId:"default",username:""},{load:nP,save:nT}=function(e){return{load:()=>(function(e){if(typeof localStorage>"u")return void tD.error("Local storage is not available.");try{let t=localStorage.getItem(e);if(!t)return void tD.warn(`Item with key ${e} does not exist in local storage.`);return JSON.parse(t)}catch(e){tD.error(`Error getting item from local storage: ${e}`);return}})(e),save:t=>(function(e,t){if(typeof localStorage>"u")return void tD.error("Local storage is not available.");try{if(t){let n=Object.fromEntries(Object.entries(t).filter(([,e])=>""!==e));localStorage.setItem(e,JSON.stringify(n))}}catch(e){tD.error(`Error setting item to local storage: ${e}`)}})(e,t)}}("lk-user-choices");function nM(e,t=!1){!0!==t&&nT(e)}function nA(e,t=!1){var n,r,i,o,a;let l={videoEnabled:null!=(n=null==e?void 0:e.videoEnabled)?n:nC.videoEnabled,audioEnabled:null!=(r=null==e?void 0:e.audioEnabled)?r:nC.audioEnabled,videoDeviceId:null!=(i=null==e?void 0:e.videoDeviceId)?i:nC.videoDeviceId,audioDeviceId:null!=(o=null==e?void 0:e.audioDeviceId)?o:nC.audioDeviceId,username:null!=(a=null==e?void 0:e.username)?a:nC.username};if(t)return l;{let e=nP();return tP(tP({},l),e??{})}}var nI=null;let nR=i.createContext(void 0);function nO(){let e=i.useContext(nR);if(!e)throw Error("Tried to access LayoutContext context outside a LayoutContextProvider provider.");return e}function nL(){return i.useContext(nR)}let nN=i.createContext(void 0);function n_(){return i.useContext(nN)}function nD(e){let t=n_(),n=e??t;if(!n)throw Error("No TrackRef, make sure you are inside a TrackRefContext or pass the TrackRef explicitly");return n}let nj=i.createContext(void 0);function nz(){return i.useContext(nj)}function nV(e){let t=nz(),n=n_(),r=e??t??(null==n?void 0:n.participant);if(!r)throw Error("No participant provided, make sure you are inside a participant context or pass the participant explicitly");return r}let nF=i.createContext(void 0);function nY(){let e=i.useContext(nF);if(!e)throw Error("tried to access room context outside of livekit room component");return e}function nH(){return i.useContext(nF)}function nU(e){let t=nH(),n=e??t;if(!n)throw Error("No room provided, make sure you are inside a Room context or pass the room explicitly");return n}let nW=i.createContext(void 0);function n$(e){let t=i.useContext(nW);if(!0===e){if(t)return t;throw Error("tried to access feature context, but none is present")}return t}},5203:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("video-off",[["path",{d:"M10.66 6H14a2 2 0 0 1 2 2v2.5l5.248-3.062A.5.5 0 0 1 22 7.87v8.196",key:"w8jjjt"}],["path",{d:"M16 16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2",key:"1xawa7"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},6515:(e,t,n)=>{let r;n.d(t,{A:()=>w,C:()=>g,K:()=>C,b:()=>A,c:()=>m,d:()=>v,e:()=>E,f:()=>P,g:()=>T,h:()=>R,i:()=>p,j:()=>d,k:()=>I,l:()=>S,m:()=>_,n:()=>b,o:()=>k,p:()=>M,t:()=>O,w:()=>L,x:()=>N,z:()=>f});var i=n(2115),o=n(3273),a=n(8530),l=n(4267);let c=e=>{let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),t},u=()=>r||(r=function(){let e=!1,t=[],n=new Map;if(typeof window>"u")return;let r=new ResizeObserver((r,i)=>{t=t.concat(r),e||window.requestAnimationFrame(()=>{let r=new Set;for(let e=0;e<t.length;e++){if(r.has(t[e].target))continue;r.add(t[e].target);let o=n.get(t[e].target);null==o||o.forEach(n=>n(t[e],i))}t=[],e=!1}),e=!0});return{observer:r,subscribe(e,t){r.observe(e);let i=n.get(e)??[];i.push(t),n.set(e,i)},unsubscribe(e,t){let i=n.get(e)??[];if(1===i.length){r.unobserve(e),n.delete(e);return}let o=i.indexOf(t);-1!==o&&i.splice(o,1),n.set(e,i)}}}()),s=e=>{let[t,n]=i.useState({width:0,height:0});return i.useLayoutEffect(()=>{if(e.current){let{width:t,height:r}=e.current.getBoundingClientRect();n({width:t,height:r})}},[e.current]),function(e,t){let n=u(),r=c(t);i.useLayoutEffect(()=>{let t=!1,i=e.current;if(i)return null==n||n.subscribe(i,o),()=>{t=!0,null==n||n.unsubscribe(i,o)};function o(e,n){t||r.current(e,n)}},[e.current,n,r]),null==n||n.observer}(e,i.useCallback(e=>n(e.contentRect),[])),t};function d(e,t,n=!0){let[r,o]=i.useState(t);return i.useEffect(()=>{if(n&&o(t),typeof window>"u"||!e)return;let r=e.subscribe(o);return()=>r.unsubscribe()},[e,n]),r}function f(e){let t=e=>"u">typeof window&&window.matchMedia(e).matches,[n,r]=i.useState(t(e));function o(){r(t(e))}return i.useEffect(()=>{let t=window.matchMedia(e);return o(),t.addListener?t.addListener(o):t.addEventListener("change",o),()=>{t.removeListener?t.removeListener(o):t.removeEventListener("change",o)}},[e]),n}function p(e={}){let t=(0,o.c)(e.participant),{className:n,connectionQualityObserver:r}=i.useMemo(()=>(0,o.d)(t),[t]);return{className:n,quality:d(r,t.connectionQuality)}}function h(e){let t=(0,o.u)(e);return d(i.useMemo(()=>(0,o.e)(t),[t]),t.state)}function m(e){let t=(0,o.f)(),n=h(t);return{buttonProps:i.useMemo(()=>{let{className:r,disconnect:i}=(0,o.h)(t);return(0,a.m)(e,{className:r,onClick:()=>i(e.stopTracks??!0),disabled:n===l.KN.Disconnected})},[t,e,n])}}function v({trackRef:e,props:t}){let n=(0,o.i)(e),r=(0,o.j)(),{className:l}=i.useMemo(()=>(0,o.k)(),[]),c=i.useMemo(()=>(0,o.m)(n,null==r?void 0:r.pin.state),[n,null==r?void 0:r.pin.state]);return{mergedProps:i.useMemo(()=>(0,a.m)(t,{className:l,onClick:e=>{var i,o,a,l,u;null==(i=t.onClick)||i.call(t,e),c?null==(a=null==r?void 0:(o=r.pin).dispatch)||a.call(o,{msg:"clear_pin"}):null==(u=null==r?void 0:(l=r.pin).dispatch)||u.call(l,{msg:"set_pin",trackReference:n})}}),[t,l,n,c,null==r?void 0:r.pin]),inFocus:c}}function b(e,t,n={}){let r=n.gridLayouts??o.G,{width:a,height:l}=s(e),c=(0,o.n)(r,t,a,l);return i.useEffect(()=>{e.current&&c&&(e.current.style.setProperty("--lk-col-count",null==c?void 0:c.columns.toString()),e.current.style.setProperty("--lk-row-count",null==c?void 0:c.rows.toString()))},[e,c]),{layout:c,containerWidth:a,containerHeight:l}}function y(e,t={}){var n,r;let a="string"==typeof e?t.participant:e.participant,l=(0,o.c)(a),c="string"==typeof e?{participant:l,source:e}:e,[u,s]=i.useState(!!(null!=(n=c.publication)&&n.isMuted||null!=(r=l.getTrackPublication(c.source))&&r.isMuted));return i.useEffect(()=>{let e=(0,o.o)(c).subscribe(s);return()=>e.unsubscribe()},[(0,o.p)(c)]),u}function g(e={}){let t=(0,o.u)(e.room),[n,r]=i.useState(t.localParticipant),[a,l]=i.useState(n.isMicrophoneEnabled),[c,u]=i.useState(n.isCameraEnabled),[s,d]=i.useState(n.isScreenShareEnabled),[f,p]=i.useState(n.lastMicrophoneError),[h,m]=i.useState(n.lastCameraError),[v,b]=i.useState(void 0),[y,w]=i.useState(void 0),E=e=>{u(e.isCameraEnabled),l(e.isMicrophoneEnabled),d(e.isScreenShareEnabled),w(e.cameraTrack),b(e.microphoneTrack),p(e.participant.lastMicrophoneError),m(e.participant.lastCameraError),r(e.participant)};return i.useEffect(()=>{let e=(0,o.t)(t.localParticipant).subscribe(E);return()=>e.unsubscribe()},[t]),{isMicrophoneEnabled:a,isScreenShareEnabled:s,isCameraEnabled:c,microphoneTrack:v,cameraTrack:y,lastMicrophoneError:f,lastCameraError:h,localParticipant:n}}function w(){let e=(0,o.f)();return d(i.useMemo(()=>(0,o.v)(e.localParticipant),[e]),e.localParticipant.permissions)}function E({kind:e,room:t,track:n,requestPermissions:r,onError:a}){let c=(0,o.w)(),u=i.useMemo(()=>t??c??new l.Wv,[t,c]),s=d(i.useMemo(()=>(0,o.x)(e,a,r),[e,r,a]),[]),[f,p]=i.useState((null==u?void 0:u.getActiveDevice(e))??"default"),{className:h,activeDeviceObservable:m,setActiveMediaDevice:v}=i.useMemo(()=>(0,o.y)(e,u),[e,u,n]);return i.useEffect(()=>{let e=m.subscribe(e=>{e&&(o.l.info("setCurrentDeviceId",e),p(e))});return()=>{null==e||e.unsubscribe()}},[m]),{devices:s,className:h,activeDeviceId:f,setActiveMediaDevice:v}}function k(e,t){let[n,r]=i.useState(1),a=Math.max(Math.ceil(t.length/e),1);n>a&&r(a);let l=n*e,c=l-e,u=e=>{r(t=>"next"===e?t===a?t:t+1:1===t?t:t-1)},s=(function(e,t,n={}){let r=i.useRef([]),a=i.useRef(-1),l=t!==a.current,c="function"==typeof n.customSortFunction?n.customSortFunction(e):(0,o.z)(e),u=[...c];if(!1===l)try{u=(0,o.A)(r.current,c,t)}catch(e){o.l.error("Error while running updatePages(): ",e)}return l?r.current=c:r.current=u,a.current=t,u})(t,e).slice(c,l);return{totalPageCount:a,nextPage:()=>u("next"),prevPage:()=>u("previous"),setPage:e=>{e>a?r(a):e<1?r(1):r(e)},firstItemIndex:c,lastItemIndex:l,tracks:s,currentPage:n}}function S({trackRef:e,onParticipantClick:t,disableSpeakingIndicator:n,htmlProps:r}){let c=(0,o.i)(e),u=i.useMemo(()=>{let{className:e}=(0,o.D)();return(0,a.m)(r,{className:e,onClick:e=>{var n;if(null==(n=r.onClick)||n.call(r,e),"function"==typeof t){let e=c.publication??c.participant.getTrackPublication(c.source);t({participant:c.participant,track:e})}}})},[r,t,c.publication,c.source,c.participant]),s=c.participant.getTrackPublication(l.CC.Source.Microphone),f=i.useMemo(()=>({participant:c.participant,source:l.CC.Source.Microphone,publication:s}),[s,c.participant]),p=y(c),h=y(f),m=function(e){let t=(0,o.c)(e);return d(i.useMemo(()=>(0,o.q)(t),[t]),t.isSpeaking)}(c.participant),v=function(e){if(e.publication instanceof l.HO){let t=e.publication.track;if(t){let{facingMode:e}=(0,l.EC)(t);return e}}return"undefined"}(c);return{elementProps:{"data-lk-audio-muted":h,"data-lk-video-muted":p,"data-lk-speaking":!0!==n&&m,"data-lk-local-participant":c.participant.isLocal,"data-lk-source":c.source,"data-lk-facing-mode":v,...u}}}function x(e={}){let t=(0,o.u)(e.room),[n,r]=i.useState([]);return i.useEffect(()=>{let n=(0,o.E)(t,{additionalRoomEvents:e.updateOnlyOn}).subscribe(r);return()=>n.unsubscribe()},[t,JSON.stringify(e.updateOnlyOn)]),n}function C(e={}){let t=x(e),{localParticipant:n}=g(e);return i.useMemo(()=>[n,...t],[n,t])}function P({room:e,props:t}){let n=(0,o.u)(e),{className:r,roomAudioPlaybackAllowedObservable:l,handleStartAudioPlayback:c}=i.useMemo(()=>(0,o.N)(),[]),{canPlayAudio:u}=d(i.useMemo(()=>l(n),[n,l]),{canPlayAudio:n.canPlaybackAudio});return{mergedProps:i.useMemo(()=>(0,a.m)(t,{className:r,onClick:()=>{c(n)},style:{display:u?"none":"block"}}),[t,r,u,c,n]),canPlayAudio:u}}function T({room:e,props:t}){let n=(0,o.u)(e),{className:r,roomVideoPlaybackAllowedObservable:l,handleStartVideoPlayback:c}=i.useMemo(()=>(0,o.O)(),[]),{canPlayVideo:u}=d(i.useMemo(()=>l(n),[n,l]),{canPlayVideo:n.canPlaybackVideo});return{mergedProps:i.useMemo(()=>(0,a.m)(t,{className:r,onClick:()=>{c(n)},style:{display:u?"none":"block"}}),[t,r,u,c,n]),canPlayVideo:u}}function M(e,t={}){let n=i.useRef(null),r=i.useRef(null),o=t.minSwipeDistance??50,a=e=>{r.current=null,n.current=e.targetTouches[0].clientX},l=e=>{r.current=e.targetTouches[0].clientX},c=i.useCallback(()=>{if(!n.current||!r.current)return;let e=n.current-r.current,i=e>o,a=e<-o;i&&t.onLeftSwipe&&t.onLeftSwipe(),a&&t.onRightSwipe&&t.onRightSwipe()},[o,t]);i.useEffect(()=>{let t=e.current;return t&&(t.addEventListener("touchstart",a,{passive:!0}),t.addEventListener("touchmove",l,{passive:!0}),t.addEventListener("touchend",c,{passive:!0})),()=>{t&&(t.removeEventListener("touchstart",a),t.removeEventListener("touchmove",l),t.removeEventListener("touchend",c))}},[e,c])}function A({props:e}){let{dispatch:t,state:n}=(0,o.a)().widget,{className:r}=i.useMemo(()=>(0,o.P)(),[]);return{mergedProps:i.useMemo(()=>(0,a.m)(e,{className:r,onClick:()=>{t&&t({msg:"toggle_chat"})},"aria-pressed":null!=n&&n.showChat?"true":"false","data-lk-unread-msgs":n?n.unreadMessages<10?n.unreadMessages.toFixed(0):"9+":"0"}),[e,r,t,n])}}function I(e){var t,n;let r=(0,o.i)(e),{className:a,mediaMutedObserver:l}=i.useMemo(()=>(0,o.Q)(r),[(0,o.p)(r)]);return{isMuted:d(l,!!(null!=(t=r.publication)&&t.isMuted||null!=(n=r.participant.getTrackPublication(r.source))&&n.isMuted)),className:a}}function R({source:e,onChange:t,initialState:n,captureOptions:r,publishOptions:l,onDeviceError:c,...u}){var s;let f=(0,o.w)(),p=null==(s=null==f?void 0:f.localParticipant)?void 0:s.getTrackPublication(e),h=i.useRef(!1),{toggle:m,className:v,pendingObserver:b,enabledObserver:y}=i.useMemo(()=>f?(0,o.S)(e,f,r,l,c):(0,o.T)(),[f,e,JSON.stringify(r),l]),g=d(b,!1),w=d(y,n??!!(null!=p&&p.isEnabled));i.useEffect(()=>{null==t||t(w,h.current),h.current=!1},[w,t]),i.useEffect(()=>{void 0!==n&&(o.l.debug("forcing initial toggle state",e,n),m(n))},[]);let E=i.useMemo(()=>(0,a.m)(u,{className:v}),[u,v]),k=i.useCallback(e=>{var t;h.current=!0,m().catch(()=>h.current=!1),null==(t=u.onClick)||t.call(u,e)},[u,m]);return{toggle:m,enabled:w,pending:g,track:p,buttonProps:{...E,"aria-pressed":w,"data-lk-source":e,"data-lk-enabled":w,disabled:g,onClick:k}}}function O(e=[l.CC.Source.Camera,l.CC.Source.Microphone,l.CC.Source.ScreenShare,l.CC.Source.ScreenShareAudio,l.CC.Source.Unknown],t={}){let n=(0,o.u)(t.room),[r,a]=i.useState([]),[c,u]=i.useState([]),s=i.useMemo(()=>e.map(e=>(0,o.U)(e)?e.source:e),[JSON.stringify(e)]);return i.useEffect(()=>{let e=(0,o.V)(n,s,{additionalRoomEvents:t.updateOnlyOn,onlySubscribed:t.onlySubscribed}).subscribe(({trackReferences:e,participants:t})=>{o.l.debug("setting track bundles",e,t),a(e),u(t)});return()=>e.unsubscribe()},[n,JSON.stringify(t.onlySubscribed),JSON.stringify(t.updateOnlyOn),JSON.stringify(e)]),i.useMemo(()=>{if(!(0,o.W)(e))return r;{let t=function(e,t){let n=new Map;if((0,o.W)(e)){let r=e.filter(e=>e.withPlaceholder).map(e=>e.source);t.forEach(e=>{let t=e.getTrackPublications().map(e=>{var t;return null==(t=e.track)?void 0:t.source}).filter(e=>void 0!==e),i=Array.from(function(e,t){let n=new Set(e);for(let e of t)n.delete(e);return n}(new Set(r),new Set(t)));i.length>0&&n.set(e.identity,i)})}return n}(e,c),n=Array.from(r);return c.forEach(e=>{t.has(e.identity)&&(t.get(e.identity)??[]).forEach(t=>{r.find(({participant:n,publication:r})=>e.identity===n.identity&&r.source===t)||(o.l.debug(`Add ${t} placeholder for participant ${e.identity}.`),n.push({participant:e,source:t}))})}),n}},[r,c,e])}function L(e){let t=(0,o.f)(),n=h(t),r=i.useMemo(()=>n===l.KN.Disconnected,[n]),a=i.useMemo(()=>(0,o.Z)(t,e),[t,e,r]),c=d(a.isSendingObservable,!1),u=d(a.messageObservable,[]);return{send:a.send,chatMessages:u,isSending:c}}function N(e={}){let[t,n]=i.useState((0,o._)(e.defaults,e.preventLoad??!1)),r=i.useCallback(e=>{n(t=>({...t,audioEnabled:e}))},[]),a=i.useCallback(e=>{n(t=>({...t,videoEnabled:e}))},[]),l=i.useCallback(e=>{n(t=>({...t,audioDeviceId:e}))},[]),c=i.useCallback(e=>{n(t=>({...t,videoDeviceId:e}))},[]),u=i.useCallback(e=>{n(t=>({...t,username:e}))},[]);return i.useEffect(()=>{(0,o.$)(t,e.preventSave??!1)},[t,e.preventSave]),{userChoices:t,saveAudioInputEnabled:r,saveVideoInputEnabled:a,saveAudioInputDeviceId:l,saveVideoInputDeviceId:c,saveUsername:u}}function _(e,t={}){let n=(0,o.c)(e),r=(0,o.u)(t.room);return d(i.useMemo(()=>(0,o.a0)(r,n),[r,n]),n.isLocal?n.isE2EEEnabled:!!(null!=n&&n.isEncrypted))}},8293:(e,t,n)=>{n.d(t,{C:()=>s,D:()=>d,G:()=>Q,M:()=>R,P:()=>Z,R:()=>G,S:()=>h,T:()=>N,a:()=>X,c:()=>m,d:()=>y,e:()=>g,f:()=>O});var r,i,o=n(2115),a=n(6515),l=n(8530),c=n(4267),u=n(3273);let s=o.forwardRef(function(e,t){let{mergedProps:n}=(0,a.b)({props:e});return o.createElement("button",{ref:t,...n},e.children)}),d=o.forwardRef(function(e,t){let{buttonProps:n}=(0,a.c)(e);return o.createElement("button",{ref:t,...n},e.children)}),f=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},o.createElement("path",{d:"M1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708L11 10.293V4.5A1.5 1.5 0 0 0 9.5 3H3.707zM0 4.5a1.5 1.5 0 0 1 .943-1.393l9.532 9.533c-.262.224-.603.36-.975.36h-8A1.5 1.5 0 0 1 0 11.5z"}),o.createElement("path",{d:"m15.2 3.6-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z"})),p=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},o.createElement("path",{d:"M0 4.5A1.5 1.5 0 0 1 1.5 3h8A1.5 1.5 0 0 1 11 4.5v7A1.5 1.5 0 0 1 9.5 13h-8A1.5 1.5 0 0 1 0 11.5zM15.2 3.6l-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z"})),h=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 24 24",...e},o.createElement("path",{fill:"#FFF",d:"M4.99 3.99a1 1 0 0 0-.697 1.717L10.586 12l-6.293 6.293a1 1 0 1 0 1.414 1.414L12 13.414l6.293 6.293a1 1 0 1 0 1.414-1.414L13.414 12l6.293-6.293a1 1 0 0 0-.727-1.717 1 1 0 0 0-.687.303L12 10.586 5.707 4.293a1 1 0 0 0-.717-.303z"})),m=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:18,fill:"none",...e},o.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 2.75A2.75 2.75 0 0 1 2.75 0h10.5A2.75 2.75 0 0 1 16 2.75v13.594a.75.75 0 0 1-1.234.572l-3.691-3.12a1.25 1.25 0 0 0-.807-.296H2.75A2.75 2.75 0 0 1 0 10.75v-8ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v8c0 .69.56 1.25 1.25 1.25h7.518c.65 0 1.279.23 1.775.65l2.457 2.077V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z",clipRule:"evenodd"}),o.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M3 4.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Z",clipRule:"evenodd"})),v=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},o.createElement("path",{fill:"currentcolor",fillRule:"evenodd",d:"M5.293 2.293a1 1 0 0 1 1.414 0l4.823 4.823a1.25 1.25 0 0 1 0 1.768l-4.823 4.823a1 1 0 0 1-1.414-1.414L9.586 8 5.293 3.707a1 1 0 0 1 0-1.414z",clipRule:"evenodd"})),b=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},o.createElement("g",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5},o.createElement("path",{d:"M10 1.75h4.25m0 0V6m0-4.25L9 7M6 14.25H1.75m0 0V10m0 4.25L7 9"}))),y=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},o.createElement("path",{fill:"currentcolor",fillRule:"evenodd",d:"M8.961.894C8.875-.298 7.125-.298 7.04.894c-.066.912-1.246 1.228-1.76.472-.67-.99-2.186-.115-1.664.96.399.824-.465 1.688-1.288 1.289-1.076-.522-1.95.994-.961 1.665.756.513.44 1.693-.472 1.759-1.192.086-1.192 1.836 0 1.922.912.066 1.228 1.246.472 1.76-.99.67-.115 2.186.96 1.664.824-.399 1.688.465 1.289 1.288-.522 1.076.994 1.95 1.665.961.513-.756 1.693-.44 1.759.472.086 1.192 1.836 1.192 1.922 0 .066-.912 1.246-1.228 1.76-.472.67.99 2.186.115 1.664-.96-.399-.824.465-1.688 1.288-1.289 1.076.522 1.95-.994.961-1.665-.756-.513-.44-1.693.472-1.759 1.192-.086 1.192-1.836 0-1.922-.912-.066-1.228-1.246-.472-1.76.99-.67.115-2.186-.96-1.664-.824.399-1.688-.465-1.289-1.288.522-1.076-.994-1.95-1.665-.961-.513.756-1.693.44-1.759-.472ZM8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Z",clipRule:"evenodd"})),g=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},o.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M2 2.75A2.75 2.75 0 0 1 4.75 0h6.5A2.75 2.75 0 0 1 14 2.75v10.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-.5a.75.75 0 0 1 1.5 0v.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25h-6.5c-.69 0-1.25.56-1.25 1.25v.5a.75.75 0 0 1-1.5 0v-.5Z",clipRule:"evenodd"}),o.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M8.78 7.47a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 1 1-1.06-1.06l.97-.97H1.75a.75.75 0 0 1 0-1.5h4.69l-.97-.97a.75.75 0 0 1 1.06-1.06l2.25 2.25Z",clipRule:"evenodd"})),w=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},o.createElement("path",{fill:"currentcolor",fillRule:"evenodd",d:"M4 6.104V4a4 4 0 1 1 8 0v2.104c1.154.326 2 1.387 2 2.646v4.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-4.5c0-1.259.846-2.32 2-2.646ZM5.5 4a2.5 2.5 0 0 1 5 0v2h-5V4Z",clipRule:"evenodd"})),E=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},o.createElement("path",{d:"M12.227 11.52a5.477 5.477 0 0 0 1.246-******** 0 0 0-.995-.1 4.478 4.478 0 0 1-.962 2.359l-1.07-1.07C10.794 9.247 11 8.647 11 8V3a3 3 0 0 0-6 0v1.293L1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708zM8 12.5c.683 0 1.33-.152 1.911-.425l.743.743c-.649.359-1.378.59-2.154.66V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .995-.098A4.5 4.5 0 0 0 8 12.5z"}),o.createElement("path",{d:"M8.743 10.907 5 7.164V8a3 3 0 0 0 3.743 2.907z"})),k=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},o.createElement("path",{fillRule:"evenodd",d:"M2.975 8.002a.5.5 0 0 1 .547.449 4.5 4.5 0 0 0 8.956 0 .5.5 0 1 1 .995.098A5.502 5.502 0 0 1 8.5 13.478V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .448-.547z",clipRule:"evenodd"}),o.createElement("path",{d:"M5 3a3 3 0 1 1 6 0v5a3 3 0 0 1-6 0z"})),S=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentcolor",...e},o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"})),x=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentcolor",...e},o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("g",{opacity:.25},o.createElement("path",{d:"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("path",{d:"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}))),C=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentcolor",...e},o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("g",{opacity:.25},o.createElement("path",{d:"M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("path",{d:"M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}),o.createElement("path",{d:"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z"}))),P=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"currentColor",...e},o.createElement("g",{opacity:.25},o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z"}),o.createElement("path",{d:"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z"}))),T=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:16,fill:"none",...e},o.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 2.75A2.75 2.75 0 0 1 2.75 0h14.5A2.75 2.75 0 0 1 20 2.75v10.5A2.75 2.75 0 0 1 17.25 16H2.75A2.75 2.75 0 0 1 0 13.25V2.75ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v10.5c0 .69.56 1.25 1.25 1.25h14.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z",clipRule:"evenodd"}),o.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M9.47 4.22a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v4.69a.75.75 0 0 1-1.5 0V6.56l-.97.97a.75.75 0 0 1-1.06-1.06l2.25-2.25Z",clipRule:"evenodd"})),M=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:16,fill:"none",...e},o.createElement("g",{fill:"currentColor"},o.createElement("path",{d:"M7.28 4.22a.75.75 0 0 0-1.06 1.06L8.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L10 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L11.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L10 6.94z"}),o.createElement("path",{fillRule:"evenodd",d:"M2.75 0A2.75 2.75 0 0 0 0 2.75v10.5A2.75 2.75 0 0 0 2.75 16h14.5A2.75 2.75 0 0 0 20 13.25V2.75A2.75 2.75 0 0 0 17.25 0zM1.5 2.75c0-.69.56-1.25 1.25-1.25h14.5c.69 0 1.25.56 1.25 1.25v10.5c0 .69-.56 1.25-1.25 1.25H2.75c-.69 0-1.25-.56-1.25-1.25z",clipRule:"evenodd"}))),A=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",...e},o.createElement("g",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5},o.createElement("path",{d:"M13.25 7H9m0 0V2.75M9 7l5.25-5.25M2.75 9H7m0 0v4.25M7 9l-5.25 5.25"}))),I=o.forwardRef(function({trackRef:e,...t},n){let r=(0,u.ab)(),{mergedProps:i,inFocus:l}=(0,a.d)({trackRef:e??r,props:t});return o.createElement(u.ac.Consumer,null,e=>void 0!==e&&o.createElement("button",{ref:n,...i},t.children?t.children:l?o.createElement(A,null):o.createElement(b,null)))}),R=o.forwardRef(function({kind:e,initialSelection:t,onActiveDeviceChange:n,onDeviceListChange:r,onDeviceSelectError:i,exactMatch:s,track:d,requestPermissions:f,onError:p,...h},m){let v=(0,u.w)(),b=o.useRef("default"),y=o.useCallback(e=>{v&&v.emit(c.u9.MediaDevicesError,e),null==p||p(e)},[v,p]),{devices:g,activeDeviceId:w,setActiveMediaDevice:E,className:k}=(0,a.e)({kind:e,room:v,track:d,requestPermissions:f,onError:y});o.useEffect(()=>{void 0!==t&&E(t)},[E]),o.useEffect(()=>{"function"==typeof r&&r(g)},[r,g]),o.useEffect(()=>{w!==b.current&&(null==n||n(w)),b.current=w},[w]);let S=async e=>{try{await E(e,{exact:s??!0})}catch(e){if(e instanceof Error)null==i||i(e);else throw e}},x=o.useMemo(()=>(0,l.a)(h,{className:k},{className:"lk-list"}),[k,h]),C=!!g.find(e=>e.label.toLowerCase().startsWith("default"));function P(e,t,n){return e===t||!C&&0===n&&"default"===t}return o.createElement("ul",{ref:m,...x},g.map((e,t)=>o.createElement("li",{key:e.deviceId,id:e.deviceId,"data-lk-active":P(e.deviceId,w,t),"aria-selected":P(e.deviceId,w,t),role:"option"},o.createElement("button",{className:"lk-button",onClick:()=>S(e.deviceId)},e.label))))}),O=o.forwardRef(function({label:e,...t},n){let r=(0,u.f)(),{mergedProps:i,canPlayAudio:l}=(0,a.f)({room:r,props:t}),{mergedProps:c,canPlayVideo:s}=(0,a.g)({room:r,props:i}),{style:d,...f}=c;return d.display=l&&s?"none":"block",o.createElement("button",{ref:n,style:d,...f},e??`Start ${l?"Video":"Audio"}`)});function L(e,t){switch(e){case c.CC.Source.Microphone:return t?o.createElement(k,null):o.createElement(E,null);case c.CC.Source.Camera:return t?o.createElement(p,null):o.createElement(f,null);case c.CC.Source.ScreenShare:return t?o.createElement(M,null):o.createElement(T,null);default:return}}let N=o.forwardRef(function({showIcon:e,...t},n){let{buttonProps:r,enabled:i}=(0,a.h)(t),[l,c]=o.useState(!1);return o.useEffect(()=>{c(!0)},[]),l&&o.createElement("button",{ref:n,...r},(e??!0)&&L(t.source,i),t.children)}),_=o.forwardRef(function(e,t){let{className:n,quality:r}=(0,a.i)(e),i=o.useMemo(()=>({...(0,l.a)(e,{className:n}),"data-lk-quality":r}),[r,e,n]);return o.createElement("div",{ref:t,...i},e.children??function(e){switch(e){case c._N.Excellent:return o.createElement(S,null);case c._N.Good:return o.createElement(x,null);case c._N.Poor:return o.createElement(C,null);default:return o.createElement(P,null)}}(r))}),D=o.forwardRef(function({participant:e,...t},n){let r=(0,u.c)(e),{className:i,infoObserver:c}=o.useMemo(()=>(0,u.ad)(r),[r]),{identity:s,name:d}=(0,a.j)(c,{name:r.name,identity:r.identity,metadata:r.metadata}),f=o.useMemo(()=>(0,l.a)(t,{className:i,"data-lk-participant-name":d}),[t,i,d]);return o.createElement("span",{ref:n,...f},""!==d?d:s,t.children)}),j=o.forwardRef(function({trackRef:e,show:t="always",...n},r){let{className:i,isMuted:c}=(0,a.k)(e),u="always"===t||"muted"===t&&c||"unmuted"===t&&!c,s=o.useMemo(()=>(0,l.a)(n,{className:i}),[i,n]);return u?o.createElement("div",{ref:r,...s,"data-lk-muted":c},n.children??L(e.source,!c)):null}),z=e=>o.createElement("svg",{width:320,height:320,viewBox:"0 0 320 320",preserveAspectRatio:"xMidYMid meet",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M160 180C204.182 180 240 144.183 240 100C240 55.8172 204.182 20 160 20C115.817 20 79.9997 55.8172 79.9997 100C79.9997 144.183 115.817 180 160 180Z",fill:"white",fillOpacity:.25}),o.createElement("path",{d:"M97.6542 194.614C103.267 191.818 109.841 192.481 115.519 195.141C129.025 201.466 144.1 205 159.999 205C175.899 205 190.973 201.466 204.48 195.141C210.158 192.481 216.732 191.818 222.345 194.614C262.703 214.719 291.985 253.736 298.591 300.062C300.15 310.997 291.045 320 280 320H39.9997C28.954 320 19.8495 310.997 21.4087 300.062C28.014 253.736 57.2966 214.72 97.6542 194.614Z",fill:"white",fillOpacity:.25}));function V(e,t={}){let[n,r]=o.useState((0,u.X)(e)),[i,a]=o.useState(null==n?void 0:n.isMuted),[c,s]=o.useState(null==n?void 0:n.isSubscribed),[d,f]=o.useState(null==n?void 0:n.track),[p,h]=o.useState("landscape"),m=o.useRef(),{className:v,trackObserver:b}=o.useMemo(()=>(0,u.Y)(e),[e.participant.sid??e.participant.identity,e.source,(0,u.a1)(e)&&e.publication.trackSid]);return o.useEffect(()=>{let e=b.subscribe(e=>{u.l.debug("update track",e),r(e),a(null==e?void 0:e.isMuted),s(null==e?void 0:e.isSubscribed),f(null==e?void 0:e.track)});return()=>null==e?void 0:e.unsubscribe()},[b]),o.useEffect(()=>{var n,r;return d&&(m.current&&d.detach(m.current),null!=(n=t.element)&&n.current&&!(e.participant.isLocal&&(null==d?void 0:d.kind)==="audio")&&d.attach(t.element.current)),m.current=null==(r=t.element)?void 0:r.current,()=>{m.current&&(null==d||d.detach(m.current))}},[d,t.element]),o.useEffect(()=>{var e,t;"number"==typeof(null==(e=null==n?void 0:n.dimensions)?void 0:e.width)&&"number"==typeof(null==(t=null==n?void 0:n.dimensions)?void 0:t.height)&&h(n.dimensions.width>n.dimensions.height?"landscape":"portrait")},[n]),{publication:n,isMuted:i,isSubscribed:c,track:d,elementProps:(0,l.a)(t.props,{className:v,"data-lk-local-participant":e.participant.isLocal,"data-lk-source":null==n?void 0:n.source,...(null==n?void 0:n.kind)==="video"&&{"data-lk-orientation":p}})}}var F=function(){if(i)return r;i=1;var e=NaN,t=/^\s+|\s+$/g,n=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,a=/^0o[0-7]+$/i,l=parseInt,c="object"==typeof u.ae&&u.ae&&u.ae.Object===Object&&u.ae,s="object"==typeof self&&self&&self.Object===Object&&self,d=c||s||Function("return this")(),f=Object.prototype.toString,p=Math.max,h=Math.min,m=function(){return d.Date.now()};function v(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function b(r){if("number"==typeof r)return r;if("symbol"==typeof(i=r)||i&&"object"==typeof i&&"[object Symbol]"==f.call(i))return e;if(v(r)){var i,c="function"==typeof r.valueOf?r.valueOf():r;r=v(c)?c+"":c}if("string"!=typeof r)return 0===r?r:+r;r=r.replace(t,"");var u=o.test(r);return u||a.test(r)?l(r.slice(2),u?2:8):n.test(r)?e:+r}return r=function(e,t,n){var r,i,o,a,l,c,u=0,s=!1,d=!1,f=!0;if("function"!=typeof e)throw TypeError("Expected a function");function y(t){var n=r,o=i;return r=i=void 0,u=t,a=e.apply(o,n)}function g(e){var n=e-c,r=e-u;return void 0===c||n>=t||n<0||d&&r>=o}function w(){var e,n,r,i=m();if(g(i))return E(i);l=setTimeout(w,(e=i-c,n=i-u,r=t-e,d?h(r,o-n):r))}function E(e){return l=void 0,f&&r?y(e):(r=i=void 0,a)}function k(){var e,n=m(),o=g(n);if(r=arguments,i=this,c=n,o){if(void 0===l)return u=e=c,l=setTimeout(w,t),s?y(e):a;if(d)return l=setTimeout(w,t),y(c)}return void 0===l&&(l=setTimeout(w,t)),a}return t=b(t)||0,v(n)&&(s=!!n.leading,o=(d="maxWait"in n)?p(b(n.maxWait)||0,t):o,f="trailing"in n?!!n.trailing:f),k.cancel=function(){void 0!==l&&clearTimeout(l),u=0,r=c=i=l=void 0},k.flush=function(){return void 0===l?a:E(m())},k}}();let Y=(0,u.af)(F),H=o.forwardRef(function({onTrackClick:e,onClick:t,onSubscriptionStatusChanged:n,trackRef:r,manageSubscription:i,...a},l){let s=(0,u.i)(r),d=o.useRef(null);o.useImperativeHandle(l,()=>d.current);let f=function({threshold:e=0,root:t=null,rootMargin:n="0%",freezeOnceVisible:r=!1,initialIsIntersecting:i=!1,onChange:a}={}){var l;let[c,u]=(0,o.useState)(null),[s,d]=(0,o.useState)(()=>({isIntersecting:i,entry:void 0})),f=(0,o.useRef)();f.current=a;let p=(null==(l=s.entry)?void 0:l.isIntersecting)&&r;(0,o.useEffect)(()=>{if(!c||!("IntersectionObserver"in window)||p)return;let r=new IntersectionObserver(e=>{let t=Array.isArray(r.thresholds)?r.thresholds:[r.thresholds];e.forEach(e=>{let n=e.isIntersecting&&t.some(t=>e.intersectionRatio>=t);d({isIntersecting:n,entry:e}),f.current&&f.current(n,e)})},{threshold:e,root:t,rootMargin:n});return r.observe(c),()=>{r.disconnect()}},[c,JSON.stringify(e),t,n,p,r]);let h=(0,o.useRef)(null);(0,o.useEffect)(()=>{var e;c||null==(e=s.entry)||!e.target||r||p||h.current===s.entry.target||(h.current=s.entry.target,d({isIntersecting:i,entry:void 0}))},[c,s.entry,r,p,i]);let m=[u,!!s.isIntersecting,s.entry];return m.ref=m[0],m.isIntersecting=m[1],m.entry=m[2],m}({root:d.current}),[p]=function(e,t,n){let r=e instanceof Function?e():e,[i,a]=(0,o.useState)(r),l=(0,o.useRef)(r),c=function(e,t=500,n){let r=(0,o.useRef)();var i=()=>{r.current&&r.current.cancel()};let a=(0,o.useRef)(i);a.current=i,(0,o.useEffect)(()=>()=>{a.current()},[]);let l=(0,o.useMemo)(()=>{let i=Y(e,t,n),o=(...e)=>i(...e);return o.cancel=()=>{i.cancel()},o.isPending=()=>!!r.current,o.flush=()=>i.flush(),o},[e,t,n]);return(0,o.useEffect)(()=>{r.current=Y(e,t,n)},[e,t,n]),l}(a,3e3,void 0);return l.current===r||(c(r),l.current=r),[i,c]}(f,0);o.useEffect(()=>{i&&s.publication instanceof c.u6&&(null==p?void 0:p.isIntersecting)===!1&&(null==f?void 0:f.isIntersecting)===!1&&s.publication.setSubscribed(!1)},[p,s,i]),o.useEffect(()=>{i&&s.publication instanceof c.u6&&(null==f?void 0:f.isIntersecting)===!0&&s.publication.setSubscribed(!0)},[f,s,i]);let{elementProps:h,publication:m,isSubscribed:v}=V(s,{element:d,props:a});return o.useEffect(()=>{null==n||n(!!v)},[v,n]),o.createElement("video",{ref:d,...h,muted:!0,onClick:n=>{null==t||t(n),null==e||e({participant:null==s?void 0:s.participant,track:m})}})}),U=o.forwardRef(function({trackRef:e,onSubscriptionStatusChanged:t,volume:n,...r},i){let a=(0,u.i)(e),l=o.useRef(null);o.useImperativeHandle(i,()=>l.current);let{elementProps:s,isSubscribed:d,track:f,publication:p}=V(a,{element:l,props:r});return o.useEffect(()=>{null==t||t(!!d)},[d,t]),o.useEffect(()=>{void 0===f||void 0===n||(f instanceof c.EQ?f.setVolume(n):u.l.warn("Volume can only be set on remote audio tracks."))},[n,f]),o.useEffect(()=>{void 0===p||void 0===r.muted||(p instanceof c.u6?p.setEnabled(!r.muted):u.l.warn("Can only call setEnabled on remote track publications."))},[r.muted,p,f]),o.createElement("audio",{ref:l,...s})});function W(e){let t=!!(0,u.B)();return e.participant&&!t?o.createElement(u.ag.Provider,{value:e.participant},e.children):o.createElement(o.Fragment,null,e.children)}function $(e){let t=!!(0,u.ab)();return e.trackRef&&!t?o.createElement(u.ah.Provider,{value:e.trackRef},e.children):o.createElement(o.Fragment,null,e.children)}let Z=o.forwardRef(function({trackRef:e,children:t,onParticipantClick:n,disableSpeakingIndicator:r,...i},l){var s,d;let f=(0,u.i)(e),{elementProps:p}=(0,a.l)({htmlProps:i,disableSpeakingIndicator:r,onParticipantClick:n,trackRef:f}),h=(0,a.m)(f.participant),m=(0,u.j)(),v=null==(s=(0,u.ai)())?void 0:s.autoSubscription,b=o.useCallback(e=>{f.source&&!e&&m&&m.pin.dispatch&&(0,u.m)(f,m.pin.state)&&m.pin.dispatch({msg:"clear_pin"})},[f,m]);return o.createElement("div",{ref:l,style:{position:"relative"},...p},o.createElement($,{trackRef:f},o.createElement(W,{participant:f.participant},t??o.createElement(o.Fragment,null,(0,u.a1)(f)&&((null==(d=f.publication)?void 0:d.kind)==="video"||f.source===c.CC.Source.Camera||f.source===c.CC.Source.ScreenShare)?o.createElement(H,{trackRef:f,onSubscriptionStatusChanged:b,manageSubscription:v}):(0,u.a1)(f)&&o.createElement(U,{trackRef:f,onSubscriptionStatusChanged:b}),o.createElement("div",{className:"lk-participant-placeholder"},o.createElement(z,null)),o.createElement("div",{className:"lk-participant-metadata"},o.createElement("div",{className:"lk-participant-metadata-item"},f.source===c.CC.Source.Camera?o.createElement(o.Fragment,null,h&&o.createElement(w,{style:{marginRight:"0.25rem"}}),o.createElement(j,{trackRef:{participant:f.participant,source:c.CC.Source.Microphone},show:"muted"}),o.createElement(D,null)):o.createElement(o.Fragment,null,o.createElement(T,{style:{marginRight:"0.25rem"}}),o.createElement(D,null,"'s screen"))),o.createElement(_,{className:"lk-participant-metadata-item"}))),o.createElement(I,{trackRef:f}))))});function B({tracks:e,...t}){return o.createElement(o.Fragment,null,e.map(e=>o.createElement(u.ah.Provider,{value:e,key:(0,u.p)(e)},(0,l.c)(t.children))))}function q({totalPageCount:e,nextPage:t,prevPage:n,currentPage:r,pagesContainer:i}){let[a,l]=o.useState(!1);return o.useEffect(()=>{let e;return i&&(e=(0,u.aj)(i.current,2e3).subscribe(l)),()=>{e&&e.unsubscribe()}},[i]),o.createElement("div",{className:"lk-pagination-control","data-lk-user-interaction":a},o.createElement("button",{className:"lk-button",onClick:n},o.createElement(v,null)),o.createElement("span",{className:"lk-pagination-count"},`${r} of ${e}`),o.createElement("button",{className:"lk-button",onClick:t},o.createElement(v,null)))}let J=o.forwardRef(function({totalPageCount:e,currentPage:t},n){let r=Array(e).fill("").map((e,n)=>n+1===t?o.createElement("span",{"data-lk-active":!0,key:n}):o.createElement("span",{key:n}));return o.createElement("div",{ref:n,className:"lk-pagination-indicator"},r)});function Q({tracks:e,...t}){let n=o.createRef(),r=o.useMemo(()=>(0,l.a)(t,{className:"lk-grid-layout"}),[t]),{layout:i}=(0,a.n)(n,e.length),c=(0,a.o)(i.maxTiles,e);return(0,a.p)(n,{onLeftSwipe:c.nextPage,onRightSwipe:c.prevPage}),o.createElement("div",{ref:n,"data-lk-pagination":c.totalPageCount>1,...r},o.createElement(B,{tracks:c.tracks},t.children),e.length>i.maxTiles&&o.createElement(o.Fragment,null,o.createElement(J,{totalPageCount:c.totalPageCount,currentPage:c.currentPage}),o.createElement(q,{pagesContainer:n,...c})))}function G({volume:e,muted:t}){let n=(0,a.t)([c.CC.Source.Microphone,c.CC.Source.ScreenShareAudio,c.CC.Source.Unknown],{updateOnlyOn:[],onlySubscribed:!0}).filter(e=>!e.participant.isLocal&&e.publication.kind===c.CC.Kind.Audio);return o.createElement("div",{style:{display:"none"}},n.map(n=>o.createElement(U,{key:(0,u.p)(n),trackRef:n,volume:e,muted:t})))}let X=o.forwardRef(function({entry:e,hideName:t=!1,hideTimestamp:n=!1,messageFormatter:r,...i},a){var l,c,u,s;let d=o.useMemo(()=>r?r(e.message):e.message,[e.message,r]),f=!!e.editTimestamp,p=new Date(e.timestamp),h="u">typeof navigator?navigator.language:"en-US",m=(null==(l=e.from)?void 0:l.name)??(null==(c=e.from)?void 0:c.identity);return o.createElement("li",{ref:a,className:"lk-chat-entry",title:p.toLocaleTimeString(h,{timeStyle:"full"}),"data-lk-message-origin":null!=(u=e.from)&&u.isLocal?"local":"remote",...i},(!n||!t||f)&&o.createElement("span",{className:"lk-meta-data"},!t&&o.createElement("strong",{className:"lk-participant-name"},m),(!n||f)&&o.createElement("span",{className:"lk-timestamp"},f&&"edited ",p.toLocaleTimeString(h,{timeStyle:"short"}))),o.createElement("span",{className:"lk-message-body"},d),o.createElement("span",{className:"lk-message-attachements"},null==(s=e.attachedFiles)?void 0:s.map(e=>e.type.startsWith("image/")&&o.createElement("img",{style:{maxWidth:"300px",maxHeight:"300px"},key:e.name,src:URL.createObjectURL(e),alt:e.name}))))})},8530:(e,t,n)=>{n.d(t,{L:()=>p,a:()=>u,c:()=>s,m:()=>l});var r=n(2115),i=n(3273),o=n(4267);function a(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}function l(...e){let t={...e[0]};for(let n=1;n<e.length;n++){let r=e[n];for(let e in r){let n=t[e],i=r[e];"function"==typeof n&&"function"==typeof i&&"o"===e[0]&&"n"===e[1]&&e.charCodeAt(2)>=65&&90>=e.charCodeAt(2)?t[e]=function(...e){return(...t)=>{for(let n of e)if("function"==typeof n)try{n(...t)}catch(e){console.error(e)}}}(n,i):("className"===e||"UNSAFE_className"===e)&&"string"==typeof n&&"string"==typeof i?t[e]=a(n,i):t[e]=void 0!==i?i:n}}return t}function c(e){return void 0!==e}function u(...e){return l(...e.filter(c))}function s(e,t,n){return r.Children.map(e,i=>r.isValidElement(i)&&r.Children.only(e)?(i.props.className&&(t??(t={}),t.className=a(i.props.className,t.className),t.style={...i.props.style,...t.style}),r.cloneElement(i,{...t,key:n})):i)}function d(e,t){return"processor"===e&&t&&"object"==typeof t&&"name"in t?t.name:"e2ee"===e&&t?"e2ee-enabled":t}n(9509);let f={connect:!0,audio:!1,video:!1},p=r.forwardRef(function(e,t){let{room:n,htmlProps:a}=function(e){let{token:t,serverUrl:n,options:a,room:c,connectOptions:u,connect:s,audio:p,video:h,screen:m,onConnected:v,onDisconnected:b,onError:y,onMediaDeviceFailure:g,onEncryptionError:w,simulateParticipants:E,...k}={...f,...e};a&&c&&i.l.warn("when using a manually created room, the options object will be ignored. set the desired options directly when creating the room instead.");let[S,x]=r.useState(),C=r.useRef(s);r.useEffect(()=>{x(c??new o.Wv(a))},[c,JSON.stringify(a,d)]);let P=r.useMemo(()=>{let{className:e}=(0,i.s)();return l(k,{className:e})},[k]);return r.useEffect(()=>{if(!S)return;let e=()=>{let e=S.localParticipant;i.l.debug("trying to publish local tracks"),Promise.all([e.setMicrophoneEnabled(!!p,"boolean"!=typeof p?p:void 0),e.setCameraEnabled(!!h,"boolean"!=typeof h?h:void 0),e.setScreenShareEnabled(!!m,"boolean"!=typeof m?m:void 0)]).catch(e=>{i.l.warn(e),null==y||y(e)})},t=(e,t)=>{let n=o.l6.getFailure(e);null==g||g(n,t)},n=e=>{null==w||w(e)},r=e=>{null==b||b(e)},a=()=>{null==v||v()};return S.on(o.u9.SignalConnected,e).on(o.u9.MediaDevicesError,t).on(o.u9.EncryptionError,n).on(o.u9.Disconnected,r).on(o.u9.Connected,a),()=>{S.off(o.u9.SignalConnected,e).off(o.u9.MediaDevicesError,t).off(o.u9.EncryptionError,n).off(o.u9.Disconnected,r).off(o.u9.Connected,a)}},[S,p,h,m,y,w,g,v,b]),r.useEffect(()=>{if(S){if(E)return void S.simulateParticipants({participants:{count:E},publish:{audio:!0,useRealTracks:!0}});if(s){if(C.current=!0,i.l.debug("connecting"),!t)return void i.l.debug("no token yet");if(!n){i.l.warn("no livekit url provided"),null==y||y(Error("no livekit url provided"));return}S.connect(n,t,u).catch(e=>{i.l.warn(e),!0===C.current&&(null==y||y(e))})}else i.l.debug("disconnecting because connect is false"),C.current=!1,S.disconnect()}},[s,t,JSON.stringify(u),S,y,n,E]),r.useEffect(()=>{if(S)return()=>{i.l.info("disconnecting on onmount"),S.disconnect()}},[S]),{room:S,htmlProps:P}}(e);return r.createElement("div",{ref:t,...a},n&&r.createElement(i.R.Provider,{value:n},r.createElement(i.L.Provider,{value:e.featureFlags},e.children)))})},9588:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]])}}]);