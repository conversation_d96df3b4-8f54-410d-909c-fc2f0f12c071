"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[761],{275:(e,t,s)=>{s.d(t,{A:()=>i});let i=Symbol.for("functionName")},1388:(e,t,s)=>{s.d(t,{E$:()=>g,cy:()=>b,du:()=>function e(t){if(null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t)return t;if(Array.isArray(t))return t.map(t=>e(t));if("object"!=typeof t)throw Error(`Unexpected type of ${t}`);let s=Object.entries(t);if(1===s.length){let e=s[0][0];if("$bytes"===e){if("string"!=typeof t.$bytes)throw Error(`Malformed $bytes field on ${t}`);return i.toByteArray(t.$bytes).buffer}if("$integer"===e){if("string"!=typeof t.$integer)throw Error(`Malformed $integer field on ${t}`);return d(t.$integer)}if("$float"===e){if("string"!=typeof t.$float)throw Error(`Malformed $float field on ${t}`);let e=i.toByteArray(t.$float);if(8!==e.byteLength)throw Error(`Received ${e.byteLength} bytes, expected 8 for $float`);let s=new DataView(e.buffer).getFloat64(0,!0);if(!c(s))throw Error(`Float ${s} should be encoded as a number`);return s}if("$set"===e)throw Error("Received a Set which is no longer supported as a Convex type.");if("$map"===e)throw Error("Received a Map which is no longer supported as a Convex type.")}let r={};for(let[s,i]of Object.entries(t))f(s),r[s]=e(i);return r},rz:()=>v});var i=s(5996),r=s(9414);let n=BigInt("-9223372036854775808"),o=BigInt("9223372036854775807"),a=BigInt("0"),u=BigInt("8"),h=BigInt("256");function c(e){return Number.isNaN(e)||!Number.isFinite(e)||Object.is(e,-0)}let l=DataView.prototype.setBigInt64?function(e){if(e<n||o<e)throw Error(`BigInt ${e} does not fit into a 64-bit signed integer.`);let t=new ArrayBuffer(8);return new DataView(t).setBigInt64(0,e,!0),i.fromByteArray(new Uint8Array(t))}:function(e){e<a&&(e-=n+n);let t=e.toString(16);t.length%2==1&&(t="0"+t);let s=new Uint8Array(new ArrayBuffer(8)),r=0;for(let i of t.match(/.{2}/g).reverse())s.set([parseInt(i,16)],r++),e>>=u;return i.fromByteArray(s)},d=DataView.prototype.getBigInt64?function(e){let t=i.toByteArray(e);if(8!==t.byteLength)throw Error(`Received ${t.byteLength} bytes, expected 8 for $integer`);return new DataView(t.buffer).getBigInt64(0,!0)}:function(e){let t=i.toByteArray(e);if(8!==t.byteLength)throw Error(`Received ${t.byteLength} bytes, expected 8 for $integer`);let s=a,r=a;for(let e of t)s+=BigInt(e)*h**r,r++;return s>o&&(s+=n+n),s};function f(e){if(e.length>1024)throw Error(`Field name ${e} exceeds maximum field name length 1024.`);if(e.startsWith("$"))throw Error(`Field name ${e} starts with a '$', which is reserved.`);for(let t=0;t<e.length;t+=1){let s=e.charCodeAt(t);if(s<32||s>=127)throw Error(`Field name ${e} has invalid character '${e[t]}': Field names can only contain non-control ASCII characters`)}}function g(e){return JSON.stringify(e,(e,t)=>void 0===t?"undefined":"bigint"==typeof t?`${t.toString()}n`:t)}function y(e,t,s,a){if(void 0===e){let e=s&&` (present at path ${s} in original object ${g(t)})`;throw Error(`undefined is not a valid Convex value${e}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`)}if(null===e)return e;if("bigint"==typeof e){if(e<n||o<e)throw Error(`BigInt ${e} does not fit into a 64-bit signed integer.`);return{$integer:l(e)}}if("number"==typeof e)if(!c(e))return e;else{let t=new ArrayBuffer(8);return new DataView(t).setFloat64(0,e,!0),{$float:i.fromByteArray(new Uint8Array(t))}}if("boolean"==typeof e||"string"==typeof e)return e;if(e instanceof ArrayBuffer)return{$bytes:i.fromByteArray(new Uint8Array(e))};if(Array.isArray(e))return e.map((e,i)=>y(e,t,s+`[${i}]`,!1));if(e instanceof Set)throw Error(p(s,"Set",[...e],t));if(e instanceof Map)throw Error(p(s,"Map",[...e],t));if(!(0,r.iY)(e)){let i=e?.constructor?.name;throw Error(p(s,i?`${i} `:"",e,t))}let u={},h=Object.entries(e);for(let[e,i]of(h.sort(([e,t],[s,i])=>e===s?0:e<s?-1:1),h))void 0!==i?(f(e),u[e]=y(i,t,s+`.${e}`,!1)):a&&(f(e),u[e]=m(i,t,s+`.${e}`));return u}function p(e,t,s,i){return e?`${t}${g(s)} is not a supported Convex type (present at path ${e} in original object ${g(i)}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`:`${t}${g(s)} is not a supported Convex type.`}function m(e,t,s){if(void 0===e)return{$undefined:null};if(void 0===t)throw Error(`Programming error. Current value is ${g(e)} but original value is undefined`);return y(e,t,s,!1)}function v(e){return y(e,e,"",!1)}function b(e){return m(e,e,"")}},2690:(e,t,s)=>{s.d(t,{N:()=>o});var i=s(2115),r=s(8862);let n=(0,i.createContext)(void 0);function o({children:e,client:t,useAuth:s}){let{isLoading:o,isAuthenticated:h,fetchAccessToken:c}=s(),[l,d]=(0,i.useState)(null);return o&&null!==l&&d(null),o||h||!1===l||d(!1),i.createElement(n.Provider,{value:{isLoading:null===l,isAuthenticated:h&&(l??!1)}},i.createElement(a,{authProviderAuthenticated:h,fetchAccessToken:c,authProviderLoading:o,client:t,setIsConvexAuthenticated:d}),i.createElement(r.hD,{client:t},e),i.createElement(u,{authProviderAuthenticated:h,fetchAccessToken:c,authProviderLoading:o,client:t,setIsConvexAuthenticated:d}))}function a({authProviderAuthenticated:e,fetchAccessToken:t,authProviderLoading:s,client:r,setIsConvexAuthenticated:n}){return(0,i.useEffect)(()=>{let s=!0;if(e)return r.setAuth(t,e=>{s&&n(()=>e)}),()=>{s=!1,n(e=>!e&&null)}},[e,t,s,r,n]),null}function u({authProviderAuthenticated:e,fetchAccessToken:t,authProviderLoading:s,client:r,setIsConvexAuthenticated:n}){return(0,i.useEffect)(()=>{if(e)return()=>{r.clearAuth(),n(()=>null)}},[e,t,s,r,n]),null}},2800:(e,t,s)=>{s.d(t,{i:()=>c});var i,r,n=s(1388),o=Object.defineProperty,a=(e,t,s)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,u=(e,t,s)=>a(e,"symbol"!=typeof t?t+"":t,s);let h=Symbol.for("ConvexError");class c extends(r=Error,i=h,r){constructor(e){super("string"==typeof e?e:(0,n.E$)(e)),u(this,"name","ConvexError"),u(this,"data"),u(this,i,!0),this.data=e}}},2828:(e,t,s)=>{s.d(t,{Gg:()=>o,dp:()=>a,qQ:()=>n});var i=s(275),r=s(5947);function n(e){let t=(0,r.Jc)(e);if(void 0===t.name){if(void 0!==t.functionHandle)throw Error(`Expected function reference like "api.file.func" or "internal.file.func", but received function handle ${t.functionHandle}`);if(void 0!==t.reference)throw Error(`Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference ${t.reference}`);throw Error(`Expected function reference like "api.file.func" or "internal.file.func", but received ${JSON.stringify(t)}`)}if("string"==typeof e)return e;let s=e[i.A];if(!s)throw Error(`${e} is not a functionReference`);return s}function o(e){return{[i.A]:e}}let a=function e(t=[]){return new Proxy({},{get(s,r){if("string"==typeof r)return e([...t,r]);if(r===i.A){if(t.length<2){let e=["api",...t].join(".");throw Error(`API path is expected to be of the form \`api.moduleName.functionName\`. Found: \`${e}\``)}let e=t.slice(0,-1).join("/"),s=t[t.length-1];return"default"===s?e:e+":"+s}return r===Symbol.toStringTag?"FunctionReference":void 0}})}()},3216:(e,t,s)=>{s.d(t,{r:()=>i});let i="1.25.1"},4761:(e,t,s)=>{s.d(t,{eH:()=>i.eH,y3:()=>i.y3,n_:()=>i.n_,IT:()=>i.IT}),s(2115),s(9089),s(6339),s(2828);var i=s(8862);s(6643);s(2690),s(7373)},5947:(e,t,s)=>{s.d(t,{Jc:()=>n});var i=s(275);let r=Symbol.for("toReferencePath");function n(e){let t;if("string"==typeof e)t=e.startsWith("function://")?{functionHandle:e}:{name:e};else if(e[i.A])t={name:e[i.A]};else{let s=e[r]??null;if(!s)throw Error(`${e} is not a functionReference`);t={reference:s}}return t}},5996:(e,t,s)=>{s.r(t),s.d(t,{byteLength:()=>c,fromByteArray:()=>d,toByteArray:()=>l});for(var i=[],r=[],n=Uint8Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=o.length;a<u;++a)i[a]=o[a],r[o.charCodeAt(a)]=a;function h(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var s=e.indexOf("=");-1===s&&(s=t);var i=s===t?0:4-s%4;return[s,i]}function c(e){var t=h(e),s=t[0],i=t[1];return(s+i)*3/4-i}function l(e){var t,s,i=h(e),o=i[0],a=i[1],u=new n((o+a)*3/4-a),c=0,l=a>0?o-4:o;for(s=0;s<l;s+=4)t=r[e.charCodeAt(s)]<<18|r[e.charCodeAt(s+1)]<<12|r[e.charCodeAt(s+2)]<<6|r[e.charCodeAt(s+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===a&&(t=r[e.charCodeAt(s)]<<2|r[e.charCodeAt(s+1)]>>4,u[c++]=255&t),1===a&&(t=r[e.charCodeAt(s)]<<10|r[e.charCodeAt(s+1)]<<4|r[e.charCodeAt(s+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u}function d(e){for(var t,s=e.length,r=s%3,n=[],o=0,a=s-r;o<a;o+=16383)n.push(function(e,t,s){for(var r,n=[],o=t;o<s;o+=3)r=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),n.push(i[r>>18&63]+i[r>>12&63]+i[r>>6&63]+i[63&r]);return n.join("")}(e,o,o+16383>a?a:o+16383));return 1===r?n.push(i[(t=e[s-1])>>2]+i[t<<4&63]+"=="):2===r&&n.push(i[(t=(e[s-2]<<8)+e[s-1])>>10]+i[t>>4&63]+i[t<<2&63]+"="),n.join("")}r[45]=62,r[95]=63},6339:(e,t,s)=>{s.d(t,{E:()=>d});var i=s(2115),r=s(8862),n=s(9089),o=s(2828),a=Object.defineProperty,u=(e,t,s)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,h=(e,t,s)=>u(e,"symbol"!=typeof t?t+"":t,s);class c{constructor(e){h(this,"createWatch"),h(this,"queries"),h(this,"listeners"),this.createWatch=e,this.queries={},this.listeners=new Set}setQueries(e){for(let t of Object.keys(e)){let{query:s,args:i}=e[t];if((0,o.qQ)(s),void 0===this.queries[t])this.addQuery(t,s,i);else{let e=this.queries[t];((0,o.qQ)(s)!==(0,o.qQ)(e.query)||JSON.stringify((0,n.rz)(i))!==JSON.stringify((0,n.rz)(e.args)))&&(this.removeQuery(t),this.addQuery(t,s,i))}}for(let t of Object.keys(this.queries))void 0===e[t]&&this.removeQuery(t)}subscribe(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}getLocalResults(e){let t={};for(let s of Object.keys(e)){let i,{query:r,args:n}=e[s];(0,o.qQ)(r);let a=this.createWatch(r,n);try{i=a.localQueryResult()}catch(e){if(e instanceof Error)i=e;else throw e}t[s]=i}return t}setCreateWatch(e){for(let t of(this.createWatch=e,Object.keys(this.queries))){let{query:e,args:s,watch:i}=this.queries[t],r=i.journal();this.removeQuery(t),this.addQuery(t,e,s,r)}}destroy(){for(let e of Object.keys(this.queries))this.removeQuery(e);this.listeners=new Set}addQuery(e,t,s,i){if(void 0!==this.queries[e])throw Error(`Tried to add a new query with identifier ${e} when it already exists.`);let r=this.createWatch(t,s,i),n=r.onUpdate(()=>this.notifyListeners());this.queries[e]={query:t,args:s,watch:r,unsubscribe:n}}removeQuery(e){let t=this.queries[e];if(void 0===t)throw Error(`No query found with identifier ${e}.`);t.unsubscribe(),delete this.queries[e]}notifyListeners(){for(let e of this.listeners)e()}}var l=s(7373);function d(e){let t=(0,r.BN)();if(void 0===t)throw Error("Could not find Convex client! `useQuery` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");var s=e,n=(0,i.useMemo)(()=>(e,s,i)=>t.watchQuery(e,s,{journal:i}),[t]);let[o]=(0,i.useState)(()=>new c(n));o.createWatch!==n&&o.setCreateWatch(n),(0,i.useEffect)(()=>()=>o.destroy(),[o]);let a=(0,i.useMemo)(()=>({getCurrentValue:()=>o.getLocalResults(s),subscribe:e=>(o.setQueries(s),o.subscribe(e))}),[o,s]);return(0,l.R)(a)}},6643:()=>{let e=()=>Array.from({length:4},()=>0),t=e(),s=e();function i(e,t){let s,i;if(e<128)return t[0]=e,1;if(e<=2047)s=1,i=192;else if(e<=65535)s=2,i=224;else if(e<=1114111)s=3,i=240;else throw Error("Invalid code point");t[0]=(e>>6*s)+i;let r=1;for(;s>0;s--){let i=e>>6*(s-1);t[r++]=128|63&i}return r}function r(e){return void 0===e?[0,void 0]:null===e?[1,null]:"bigint"==typeof e?[2,e]:"number"==typeof e?[3,e]:"boolean"==typeof e?[4,e]:"string"==typeof e?[5,e]:e instanceof ArrayBuffer?[6,Array.from(new Uint8Array(e)).map(r)]:Array.isArray(e)?[7,e.map(r)]:[8,Object.keys(e).sort().map(t=>[t,e[t]]).map(r)]}},7373:(e,t,s)=>{s.d(t,{R:()=>r});var i=s(2115);function r({getCurrentValue:e,subscribe:t}){let[s,r]=(0,i.useState)(()=>({getCurrentValue:e,subscribe:t,value:e()})),n=s.value;return(s.getCurrentValue!==e||s.subscribe!==t)&&(n=e(),r({getCurrentValue:e,subscribe:t,value:n})),(0,i.useEffect)(()=>{let s=!1,i=()=>{s||r(s=>{if(s.getCurrentValue!==e||s.subscribe!==t)return s;let i=e();return s.value===i?s:{...s,value:i}})},n=t(i);return i(),()=>{s=!0,n()}},[e,t]),n}},8039:(e,t,s)=>{s.d(t,{d:()=>k,v:()=>S});var i=s(1388),r=Object.defineProperty,n=(e,t,s)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,o=(e,t,s)=>n(e,"symbol"!=typeof t?t+"":t,s);class a{constructor({isOptional:e}){o(this,"type"),o(this,"fieldPaths"),o(this,"isOptional"),o(this,"isConvexValidator"),this.isOptional=e,this.isConvexValidator=!0}get optional(){return"optional"===this.isOptional}}class u extends a{constructor({isOptional:e,tableName:t}){super({isOptional:e}),o(this,"tableName"),o(this,"kind","id"),this.tableName=t}get json(){return{type:"id",tableName:this.tableName}}asOptional(){return new u({isOptional:"optional",tableName:this.tableName})}}class h extends a{constructor(){super(...arguments),o(this,"kind","float64")}get json(){return{type:"number"}}asOptional(){return new h({isOptional:"optional"})}}class c extends a{constructor(){super(...arguments),o(this,"kind","int64")}get json(){return{type:"bigint"}}asOptional(){return new c({isOptional:"optional"})}}class l extends a{constructor(){super(...arguments),o(this,"kind","boolean")}get json(){return{type:this.kind}}asOptional(){return new l({isOptional:"optional"})}}class d extends a{constructor(){super(...arguments),o(this,"kind","bytes")}get json(){return{type:this.kind}}asOptional(){return new d({isOptional:"optional"})}}class f extends a{constructor(){super(...arguments),o(this,"kind","string")}get json(){return{type:this.kind}}asOptional(){return new f({isOptional:"optional"})}}class g extends a{constructor(){super(...arguments),o(this,"kind","null")}get json(){return{type:this.kind}}asOptional(){return new g({isOptional:"optional"})}}class y extends a{constructor(){super(...arguments),o(this,"kind","any")}get json(){return{type:this.kind}}asOptional(){return new y({isOptional:"optional"})}}class p extends a{constructor({isOptional:e,fields:t}){super({isOptional:e}),o(this,"fields"),o(this,"kind","object"),this.fields=t}get json(){return{type:this.kind,value:globalThis.Object.fromEntries(globalThis.Object.entries(this.fields).map(([e,t])=>[e,{fieldType:t.json,optional:"optional"===t.isOptional}]))}}asOptional(){return new p({isOptional:"optional",fields:this.fields})}}class m extends a{constructor({isOptional:e,value:t}){super({isOptional:e}),o(this,"value"),o(this,"kind","literal"),this.value=t}get json(){return{type:this.kind,value:(0,i.rz)(this.value)}}asOptional(){return new m({isOptional:"optional",value:this.value})}}class v extends a{constructor({isOptional:e,element:t}){super({isOptional:e}),o(this,"element"),o(this,"kind","array"),this.element=t}get json(){return{type:this.kind,value:this.element.json}}asOptional(){return new v({isOptional:"optional",element:this.element})}}class b extends a{constructor({isOptional:e,key:t,value:s}){if(super({isOptional:e}),o(this,"key"),o(this,"value"),o(this,"kind","record"),"optional"===t.isOptional)throw Error("Record validator cannot have optional keys");if("optional"===s.isOptional)throw Error("Record validator cannot have optional values");this.key=t,this.value=s}get json(){return{type:this.kind,keys:this.key.json,values:{fieldType:this.value.json,optional:!1}}}asOptional(){return new b({isOptional:"optional",key:this.key,value:this.value})}}class w extends a{constructor({isOptional:e,members:t}){super({isOptional:e}),o(this,"members"),o(this,"kind","union"),this.members=t}get json(){return{type:this.kind,value:this.members.map(e=>e.json)}}asOptional(){return new w({isOptional:"optional",members:this.members})}}function k(e){return!!e.isConvexValidator}let S={id:e=>new u({isOptional:"required",tableName:e}),null:()=>new g({isOptional:"required"}),number:()=>new h({isOptional:"required"}),float64:()=>new h({isOptional:"required"}),bigint:()=>new c({isOptional:"required"}),int64:()=>new c({isOptional:"required"}),boolean:()=>new l({isOptional:"required"}),string:()=>new f({isOptional:"required"}),bytes:()=>new d({isOptional:"required"}),literal:e=>new m({isOptional:"required",value:e}),array:e=>new v({isOptional:"required",element:e}),object:e=>new p({isOptional:"required",fields:e}),record:(e,t)=>new b({isOptional:"required",key:e,value:t}),union:(...e)=>new w({isOptional:"required",members:e}),any:()=>new y({isOptional:"required"}),optional:e=>e.asOptional()}},8862:(e,t,s)=>{s.d(t,{hD:()=>em,eH:()=>eg,y3:()=>ew,BN:()=>ep,n_:()=>eb,IT:()=>ev});var i=s(3216),r=s(9089),n=Object.defineProperty,o=(e,t,s)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,a=(e,t,s)=>o(e,"symbol"!=typeof t?t+"":t,s);function u(e){switch(e){case"query":return"Q";case"mutation":return"M";case"action":return"A";case"any":return"?"}}class h{constructor(e){a(this,"_onLogLineFuncs"),a(this,"_verbose"),this._onLogLineFuncs={},this._verbose=e.verbose}addLogLineListener(e){let t=Math.random().toString(36).substring(2,15);for(let e=0;e<10&&void 0!==this._onLogLineFuncs[t];e++)t=Math.random().toString(36).substring(2,15);return this._onLogLineFuncs[t]=e,()=>{delete this._onLogLineFuncs[t]}}logVerbose(...e){if(this._verbose)for(let t of Object.values(this._onLogLineFuncs))t("debug",`${new Date().toISOString()}`,...e)}log(...e){for(let t of Object.values(this._onLogLineFuncs))t("info",...e)}warn(...e){for(let t of Object.values(this._onLogLineFuncs))t("warn",...e)}error(...e){for(let t of Object.values(this._onLogLineFuncs))t("error",...e)}}function c(e){let t=new h(e);return t.addLogLineListener((e,...t)=>{switch(e){case"debug":console.debug(...t);break;case"info":default:console.log(...t);break;case"warn":console.warn(...t);break;case"error":console.error(...t)}}),t}function l(e,t,s,i,r){let n=u(s);if("object"==typeof r&&(r=`ConvexError ${JSON.stringify(r.errorData,null,2)}`),"info"===t){let t=r.match(/^\[.*?\] /);if(null===t)return void e.error(`[CONVEX ${n}(${i})] Could not parse console.log`);let s=r.slice(1,t[0].length-2),o=r.slice(t[0].length);e.log(`%c[CONVEX ${n}(${i})] [${s}]`,"color:rgb(0, 145, 255)",o)}else e.error(`[CONVEX ${n}(${i})] ${r}`)}function d(e,t,s){let i=u(e);return`[CONVEX ${i}(${t})] ${s.errorMessage}
  Called by client`}function f(e,t){return t.data=e.errorData,t}function g(e){let t,s,i=e.split(":");return 1===i.length?(t=i[0],s="default"):(t=i.slice(0,i.length-1).join(":"),s=i[i.length-1]),t.endsWith(".js")&&(t=t.slice(0,-3)),`${t}:${s}`}function y(e,t){return JSON.stringify({udfPath:g(e),args:(0,r.rz)(t)})}var p=Object.defineProperty,m=(e,t,s)=>t in e?p(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,v=(e,t,s)=>m(e,"symbol"!=typeof t?t+"":t,s);class b{constructor(){v(this,"nextQueryId"),v(this,"querySetVersion"),v(this,"querySet"),v(this,"queryIdToToken"),v(this,"identityVersion"),v(this,"auth"),v(this,"outstandingQueriesOlderThanRestart"),v(this,"outstandingAuthOlderThanRestart"),v(this,"paused"),v(this,"pendingQuerySetModifications"),this.nextQueryId=0,this.querySetVersion=0,this.identityVersion=0,this.querySet=new Map,this.queryIdToToken=new Map,this.outstandingQueriesOlderThanRestart=new Set,this.outstandingAuthOlderThanRestart=!1,this.paused=!1,this.pendingQuerySetModifications=new Map}hasSyncedPastLastReconnect(){return 0===this.outstandingQueriesOlderThanRestart.size&&!this.outstandingAuthOlderThanRestart}markAuthCompletion(){this.outstandingAuthOlderThanRestart=!1}subscribe(e,t,s,i){let n=g(e),o=y(n,t),a=this.querySet.get(o);if(void 0!==a)return a.numSubscribers+=1,{queryToken:o,modification:null,unsubscribe:()=>this.removeSubscriber(o)};{let e=this.nextQueryId++;this.querySet.set(o,{id:e,canonicalizedUdfPath:n,args:t,numSubscribers:1,journal:s,componentPath:i}),this.queryIdToToken.set(e,o);let a=this.querySetVersion,u=this.querySetVersion+1,h={type:"Add",queryId:e,udfPath:n,args:[(0,r.rz)(t)],journal:s,componentPath:i};return this.paused?this.pendingQuerySetModifications.set(e,h):this.querySetVersion=u,{queryToken:o,modification:{type:"ModifyQuerySet",baseVersion:a,newVersion:u,modifications:[h]},unsubscribe:()=>this.removeSubscriber(o)}}}transition(e){for(let t of e.modifications)switch(t.type){case"QueryUpdated":case"QueryFailed":{this.outstandingQueriesOlderThanRestart.delete(t.queryId);let e=t.journal;if(void 0!==e){let s=this.queryIdToToken.get(t.queryId);void 0!==s&&(this.querySet.get(s).journal=e)}break}case"QueryRemoved":this.outstandingQueriesOlderThanRestart.delete(t.queryId);break;default:throw Error(`Invalid modification ${t.type}`)}}queryId(e,t){let s=y(g(e),t),i=this.querySet.get(s);return void 0!==i?i.id:null}isCurrentOrNewerAuthVersion(e){return e>=this.identityVersion}setAuth(e){this.auth={tokenType:"User",value:e};let t=this.identityVersion;return this.paused||(this.identityVersion=t+1),{type:"Authenticate",baseVersion:t,...this.auth}}setAdminAuth(e,t){let s={tokenType:"Admin",value:e,impersonating:t};this.auth=s;let i=this.identityVersion;return this.paused||(this.identityVersion=i+1),{type:"Authenticate",baseVersion:i,...s}}clearAuth(){this.auth=void 0,this.markAuthCompletion();let e=this.identityVersion;return this.paused||(this.identityVersion=e+1),{type:"Authenticate",tokenType:"None",baseVersion:e}}hasAuth(){return!!this.auth}isNewAuth(e){return this.auth?.value!==e}queryPath(e){let t=this.queryIdToToken.get(e);return t?this.querySet.get(t).canonicalizedUdfPath:null}queryArgs(e){let t=this.queryIdToToken.get(e);return t?this.querySet.get(t).args:null}queryToken(e){return this.queryIdToToken.get(e)??null}queryJournal(e){return this.querySet.get(e)?.journal}restart(e){this.unpause(),this.outstandingQueriesOlderThanRestart.clear();let t=[];for(let s of this.querySet.values()){let i={type:"Add",queryId:s.id,udfPath:s.canonicalizedUdfPath,args:[(0,r.rz)(s.args)],journal:s.journal,componentPath:s.componentPath};t.push(i),e.has(s.id)||this.outstandingQueriesOlderThanRestart.add(s.id)}this.querySetVersion=1;let s={type:"ModifyQuerySet",baseVersion:0,newVersion:1,modifications:t};if(!this.auth)return this.identityVersion=0,[s,void 0];this.outstandingAuthOlderThanRestart=!0;let i={type:"Authenticate",baseVersion:0,...this.auth};return this.identityVersion=1,[s,i]}pause(){this.paused=!0}resume(){let e=this.pendingQuerySetModifications.size>0?{type:"ModifyQuerySet",baseVersion:this.querySetVersion,newVersion:++this.querySetVersion,modifications:Array.from(this.pendingQuerySetModifications.values())}:void 0,t=void 0!==this.auth?{type:"Authenticate",baseVersion:this.identityVersion++,...this.auth}:void 0;return this.unpause(),[e,t]}unpause(){this.paused=!1,this.pendingQuerySetModifications.clear()}removeSubscriber(e){let t=this.querySet.get(e);if(t.numSubscribers>1)return t.numSubscribers-=1,null;{this.querySet.delete(e),this.queryIdToToken.delete(t.id),this.outstandingQueriesOlderThanRestart.delete(t.id);let s=this.querySetVersion,i=this.querySetVersion+1,r={type:"Remove",queryId:t.id};return this.paused?this.pendingQuerySetModifications.has(t.id)?this.pendingQuerySetModifications.delete(t.id):this.pendingQuerySetModifications.set(t.id,r):this.querySetVersion=i,{type:"ModifyQuerySet",baseVersion:s,newVersion:i,modifications:[r]}}}}var w=Object.defineProperty,k=(e,t,s)=>t in e?w(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,S=(e,t,s)=>k(e,"symbol"!=typeof t?t+"":t,s);class R{constructor(e){this.logger=e,S(this,"inflightRequests"),S(this,"requestsOlderThanRestart"),S(this,"inflightMutationsCount",0),S(this,"inflightActionsCount",0),this.inflightRequests=new Map,this.requestsOlderThanRestart=new Set}request(e,t){return new Promise(s=>{this.inflightRequests.set(e.requestId,{message:e,status:{status:t?"Requested":"NotSent",requestedAt:new Date,onResult:s}}),"Mutation"===e.type?this.inflightMutationsCount++:"Action"===e.type&&this.inflightActionsCount++})}onResponse(e){let t,s,i=this.inflightRequests.get(e.requestId);if(void 0===i||"Completed"===i.status.status)return null;let n="Mutation"===i.message.type?"mutation":"action",o=i.message.udfPath;for(let t of e.logLines)l(this.logger,"info",n,o,t);let a=i.status;if(e.success)t={success:!0,logLines:e.logLines,value:(0,r.du)(e.result)},s=()=>a.onResult(t);else{let i=e.result,{errorData:u}=e;l(this.logger,"error",n,o,i),t={success:!1,errorMessage:i,errorData:void 0!==u?(0,r.du)(u):void 0,logLines:e.logLines},s=()=>a.onResult(t)}return"ActionResponse"!==e.type&&e.success?(i.status={status:"Completed",result:t,ts:e.ts,onResolve:s},null):(s(),this.inflightRequests.delete(e.requestId),this.requestsOlderThanRestart.delete(e.requestId),"Action"===i.message.type?this.inflightActionsCount--:"Mutation"===i.message.type&&this.inflightMutationsCount--,{requestId:e.requestId,result:t})}removeCompleted(e){let t=new Map;for(let[s,i]of this.inflightRequests.entries()){let r=i.status;"Completed"===r.status&&r.ts.lessThanOrEqual(e)&&(r.onResolve(),t.set(s,r.result),"Mutation"===i.message.type?this.inflightMutationsCount--:"Action"===i.message.type&&this.inflightActionsCount--,this.inflightRequests.delete(s),this.requestsOlderThanRestart.delete(s))}return t}restart(){this.requestsOlderThanRestart=new Set(this.inflightRequests.keys());let e=[];for(let[t,s]of this.inflightRequests){if("NotSent"===s.status.status){s.status.status="Requested",e.push(s.message);continue}if("Mutation"===s.message.type)e.push(s.message);else if("Action"===s.message.type){if(this.inflightRequests.delete(t),this.requestsOlderThanRestart.delete(t),this.inflightActionsCount--,"Completed"===s.status.status)throw Error("Action should never be in 'Completed' state");s.status.onResult({success:!1,errorMessage:"Connection lost while action was in flight",logLines:[]})}}return e}resume(){let e=[];for(let[,t]of this.inflightRequests)if("NotSent"===t.status.status){t.status.status="Requested",e.push(t.message);continue}return e}hasIncompleteRequests(){for(let e of this.inflightRequests.values())if("Requested"===e.status.status)return!0;return!1}hasInflightRequests(){return this.inflightRequests.size>0}hasSyncedPastLastReconnect(){return 0===this.requestsOlderThanRestart.size}timeOfOldestInflightRequest(){if(0===this.inflightRequests.size)return null;let e=Date.now();for(let t of this.inflightRequests.values())"Completed"!==t.status.status&&t.status.requestedAt.getTime()<e&&(e=t.status.requestedAt.getTime());return new Date(e)}inflightMutations(){return this.inflightMutationsCount}inflightActions(){return this.inflightActionsCount}}var q=s(2828),A=s(9414),C=s(2800),O=Object.defineProperty,T=(e,t,s)=>t in e?O(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,x=(e,t,s)=>T(e,"symbol"!=typeof t?t+"":t,s);class M{constructor(e){x(this,"queryResults"),x(this,"modifiedQueries"),this.queryResults=e,this.modifiedQueries=[]}getQuery(e,...t){let s=(0,A.Zy)(t[0]),i=(0,q.qQ)(e),r=this.queryResults.get(y(i,s));if(void 0!==r)return M.queryValue(r.result)}getAllQueries(e){let t=[],s=(0,q.qQ)(e);for(let e of this.queryResults.values())e.udfPath===g(s)&&t.push({args:e.args,value:M.queryValue(e.result)});return t}setQuery(e,t,s){let i=(0,A.Zy)(t),r=(0,q.qQ)(e),n=y(r,i);this.queryResults.set(n,{udfPath:r,args:i,result:void 0===s?void 0:{success:!0,value:s,logLines:[]}}),this.modifiedQueries.push(n)}static queryValue(e){return void 0===e?void 0:e.success?e.value:void 0}}class E{constructor(){x(this,"queryResults"),x(this,"optimisticUpdates"),this.queryResults=new Map,this.optimisticUpdates=[]}ingestQueryResultsFromServer(e,t){this.optimisticUpdates=this.optimisticUpdates.filter(e=>!t.has(e.mutationId));let s=this.queryResults;this.queryResults=new Map(e);let i=new M(this.queryResults);for(let e of this.optimisticUpdates)e.update(i);let r=[];for(let[e,t]of this.queryResults){let i=s.get(e);(void 0===i||i.result!==t.result)&&r.push(e)}return r}applyOptimisticUpdate(e,t){this.optimisticUpdates.push({update:e,mutationId:t});let s=new M(this.queryResults);return e(s),s.modifiedQueries}rawQueryResult(e){return this.queryResults.get(e)}queryResult(e){let t=this.queryResults.get(e);if(void 0===t)return;let s=t.result;if(void 0!==s){if(s.success)return s.value;if(void 0!==s.errorData)throw f(s,new C.i(d("query",t.udfPath,s)));throw Error(d("query",t.udfPath,s))}}hasQueryResult(e){return void 0!==this.queryResults.get(e)}queryLogs(e){let t=this.queryResults.get(e);return t?.result?.logLines}}var I=Object.defineProperty,$=(e,t,s)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,Q=(e,t,s)=>$(e,"symbol"!=typeof t?t+"":t,s);class V{constructor(e,t){Q(this,"low"),Q(this,"high"),Q(this,"__isUnsignedLong__"),this.low=0|e,this.high=0|t,this.__isUnsignedLong__=!0}static isLong(e){return!0===(e&&e.__isUnsignedLong__)}static fromBytesLE(e){return new V(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24)}toBytesLE(){let e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]}static fromNumber(e){return isNaN(e)||e<0?L:e>=j?P:new V(e%_|0,e/_|0)}toString(){return(BigInt(this.high)*BigInt(_)+BigInt(this.low)).toString()}equals(e){return V.isLong(e)||(e=V.fromValue(e)),(this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low}notEquals(e){return!this.equals(e)}comp(e){return(V.isLong(e)||(e=V.fromValue(e)),this.equals(e))?0:e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1}lessThanOrEqual(e){return 0>=this.comp(e)}static fromValue(e){return"number"==typeof e?V.fromNumber(e):new V(e.low,e.high)}}let L=new V(0,0),_=0x100000000,j=0xffffffffffffffff,P=new V(-1,-1);var F=Object.defineProperty,U=(e,t,s)=>t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,N=(e,t,s)=>U(e,"symbol"!=typeof t?t+"":t,s);class B{constructor(e,t){N(this,"version"),N(this,"remoteQuerySet"),N(this,"queryPath"),N(this,"logger"),this.version={querySet:0,ts:V.fromNumber(0),identity:0},this.remoteQuerySet=new Map,this.queryPath=e,this.logger=t}transition(e){let t=e.startVersion;if(this.version.querySet!==t.querySet||this.version.ts.notEquals(t.ts)||this.version.identity!==t.identity)throw Error(`Invalid start version: ${t.ts.toString()}:${t.querySet}`);for(let t of e.modifications)switch(t.type){case"QueryUpdated":{let e=this.queryPath(t.queryId);if(e)for(let s of t.logLines)l(this.logger,"info","query",e,s);let s=(0,r.du)(t.value??null);this.remoteQuerySet.set(t.queryId,{success:!0,value:s,logLines:t.logLines});break}case"QueryFailed":{let e=this.queryPath(t.queryId);if(e)for(let s of t.logLines)l(this.logger,"info","query",e,s);let{errorData:s}=t;this.remoteQuerySet.set(t.queryId,{success:!1,errorMessage:t.errorMessage,errorData:void 0!==s?(0,r.du)(s):void 0,logLines:t.logLines});break}case"QueryRemoved":this.remoteQuerySet.delete(t.queryId);break;default:throw Error(`Invalid modification ${t.type}`)}this.version=e.endVersion}remoteQueryResults(){return this.remoteQuerySet}timestamp(){return this.version.ts}}function D(e){let t=r.o4.toByteArray(e);return V.fromBytesLE(Array.from(t))}var W=Object.defineProperty,z=(e,t,s)=>t in e?W(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,J=(e,t,s)=>z(e,"symbol"!=typeof t?t+"":t,s);let H={InternalServerError:{timeout:1e3},SubscriptionsWorkerFullError:{timeout:3e3},TooManyConcurrentRequests:{timeout:3e3},CommitterFullError:{timeout:3e3},AwsTooManyRequestsException:{timeout:3e3},ExecuteFullError:{timeout:3e3},SystemTimeoutError:{timeout:3e3},ExpiredInQueue:{timeout:3e3},VectorIndexesUnavailable:{timeout:1e3},SearchIndexesUnavailable:{timeout:1e3},VectorIndexTooLarge:{timeout:3e3},SearchIndexTooLarge:{timeout:3e3},TooManyWritesInTimePeriod:{timeout:3e3}};class Z{constructor(e,t,s,i){J(this,"socket"),J(this,"connectionCount"),J(this,"_hasEverConnected",!1),J(this,"lastCloseReason"),J(this,"defaultInitialBackoff"),J(this,"maxBackoff"),J(this,"retries"),J(this,"serverInactivityThreshold"),J(this,"reconnectDueToServerInactivityTimeout"),J(this,"uri"),J(this,"onOpen"),J(this,"onResume"),J(this,"onMessage"),J(this,"webSocketConstructor"),J(this,"logger"),J(this,"onServerDisconnectError"),this.webSocketConstructor=s,this.socket={state:"disconnected"},this.connectionCount=0,this.lastCloseReason="InitialConnect",this.defaultInitialBackoff=1e3,this.maxBackoff=16e3,this.retries=0,this.serverInactivityThreshold=3e4,this.reconnectDueToServerInactivityTimeout=null,this.uri=e,this.onOpen=t.onOpen,this.onResume=t.onResume,this.onMessage=t.onMessage,this.onServerDisconnectError=t.onServerDisconnectError,this.logger=i,this.connect()}setSocketState(e){this.socket=e,this._logVerbose(`socket state changed: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}`)}connect(){if("terminated"===this.socket.state)return;if("disconnected"!==this.socket.state&&"stopped"!==this.socket.state)throw Error("Didn't start connection from disconnected state: "+this.socket.state);let e=new this.webSocketConstructor(this.uri);this._logVerbose("constructed WebSocket"),this.setSocketState({state:"connecting",ws:e,paused:"no"}),this.resetServerInactivityTimeout(),e.onopen=()=>{if(this.logger.logVerbose("begin ws.onopen"),"connecting"!==this.socket.state)throw Error("onopen called with socket not in connecting state");this.setSocketState({state:"ready",ws:e,paused:"yes"===this.socket.paused?"uninitialized":"no"}),this.resetServerInactivityTimeout(),"no"===this.socket.paused&&(this._hasEverConnected=!0,this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})),"InitialConnect"!==this.lastCloseReason&&this.logger.log("WebSocket reconnected"),this.connectionCount+=1,this.lastCloseReason=null},e.onerror=e=>{let t=e.message;this.logger.log(`WebSocket error: ${t}`)},e.onmessage=e=>{this.resetServerInactivityTimeout();let t=function(e){switch(e.type){case"FatalError":case"AuthError":case"ActionResponse":case"Ping":return{...e};case"MutationResponse":if(e.success)return{...e,ts:D(e.ts)};return{...e};case"Transition":return{...e,startVersion:{...e.startVersion,ts:D(e.startVersion.ts)},endVersion:{...e.endVersion,ts:D(e.endVersion.ts)}}}}(JSON.parse(e.data));this._logVerbose(`received ws message with type ${t.type}`),this.onMessage(t).hasSyncedPastLastReconnect&&(this.retries=0)},e.onclose=e=>{if(this._logVerbose("begin ws.onclose"),null===this.lastCloseReason&&(this.lastCloseReason=e.reason??"OnCloseInvoked"),1e3!==e.code&&1001!==e.code&&1005!==e.code&&4040!==e.code){let t=`WebSocket closed with code ${e.code}`;e.reason&&(t+=`: ${e.reason}`),this.logger.log(t),this.onServerDisconnectError&&e.reason&&this.onServerDisconnectError(t)}let t=function(e){if(void 0===e)return"Unknown";for(let t of Object.keys(H))if(e.startsWith(t))return t;return"Unknown"}(e.reason);this.scheduleReconnect(t)}}socketState(){return this.socket.state}sendMessage(e){let t={type:e.type,..."Authenticate"===e.type&&"User"===e.tokenType?{value:`...${e.value.slice(-7)}`}:{}};if("ready"===this.socket.state&&"no"===this.socket.paused){let s=JSON.stringify(function(e){switch(e.type){case"Authenticate":case"ModifyQuerySet":case"Mutation":case"Action":case"Event":return{...e};case"Connect":if(void 0!==e.maxObservedTimestamp)return{...e,maxObservedTimestamp:function(e){let t=new Uint8Array(e.toBytesLE());return r.o4.fromByteArray(t)}(e.maxObservedTimestamp)};return{...e,maxObservedTimestamp:void 0}}}(e));try{this.socket.ws.send(s)}catch(e){this.logger.log(`Failed to send message on WebSocket, reconnecting: ${e}`),this.closeAndReconnect("FailedToSendMessage")}return this._logVerbose(`sent message with type ${e.type}: ${JSON.stringify(t)}`),!0}return this._logVerbose(`message not sent (socket state: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}): ${JSON.stringify(t)}`),!1}resetServerInactivityTimeout(){"terminated"!==this.socket.state&&(null!==this.reconnectDueToServerInactivityTimeout&&(clearTimeout(this.reconnectDueToServerInactivityTimeout),this.reconnectDueToServerInactivityTimeout=null),this.reconnectDueToServerInactivityTimeout=setTimeout(()=>{this.closeAndReconnect("InactiveServer")},this.serverInactivityThreshold))}scheduleReconnect(e){this.socket={state:"disconnected"};let t=this.nextBackoff(e);this.logger.log(`Attempting reconnect in ${t}ms`),setTimeout(()=>this.connect(),t)}closeAndReconnect(e){switch(this._logVerbose(`begin closeAndReconnect with reason ${e}`),this.socket.state){case"disconnected":case"terminated":case"stopped":return;case"connecting":case"ready":this.lastCloseReason=e,this.close(),this.scheduleReconnect("client");return;default:this.socket}}close(){switch(this.socket.state){case"disconnected":case"terminated":case"stopped":return Promise.resolve();case"connecting":{let e=this.socket.ws;return new Promise(t=>{e.onclose=()=>{this._logVerbose("Closed after connecting"),t()},e.onopen=()=>{this._logVerbose("Opened after connecting"),e.close()}})}case"ready":{this._logVerbose("ws.close called");let e=this.socket.ws,t=new Promise(t=>{e.onclose=()=>{t()}});return e.close(),t}default:return this.socket,Promise.resolve()}}terminate(){switch(this.reconnectDueToServerInactivityTimeout&&clearTimeout(this.reconnectDueToServerInactivityTimeout),this.socket.state){case"terminated":case"stopped":case"disconnected":case"connecting":case"ready":{let e=this.close();return this.setSocketState({state:"terminated"}),e}default:throw this.socket,Error(`Invalid websocket state: ${this.socket.state}`)}}stop(){switch(this.socket.state){case"terminated":return Promise.resolve();case"connecting":case"stopped":case"disconnected":case"ready":{let e=this.close();return this.socket={state:"stopped"},e}default:return this.socket,Promise.resolve()}}tryRestart(){switch(this.socket.state){case"stopped":break;case"terminated":case"connecting":case"ready":case"disconnected":this.logger.logVerbose("Restart called without stopping first");return;default:this.socket}this.connect()}pause(){switch(this.socket.state){case"disconnected":case"stopped":case"terminated":return;case"connecting":case"ready":this.socket={...this.socket,paused:"yes"};return;default:return void this.socket}}resume(){switch(this.socket.state){case"connecting":this.socket={...this.socket,paused:"no"};return;case"ready":"uninitialized"===this.socket.paused?(this.socket={...this.socket,paused:"no"},this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})):"yes"===this.socket.paused&&(this.socket={...this.socket,paused:"no"},this.onResume());return;case"terminated":case"stopped":case"disconnected":return;default:this.socket}this.connect()}connectionState(){return{isConnected:"ready"===this.socket.state,hasEverConnected:this._hasEverConnected,connectionCount:this.connectionCount,connectionRetries:this.retries}}_logVerbose(e){this.logger.logVerbose(e)}nextBackoff(e){let t=("client"===e?100:"Unknown"===e?this.defaultInitialBackoff:H[e].timeout)*Math.pow(2,this.retries);this.retries+=1;let s=Math.min(t,this.maxBackoff),i=s*(Math.random()-.5);return s+i}}class G extends Error{}G.prototype.name="InvalidTokenError";var X=Object.defineProperty,Y=(e,t,s)=>t in e?X(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,K=(e,t,s)=>Y(e,"symbol"!=typeof t?t+"":t,s);class ee{constructor(e,t,s){K(this,"authState",{state:"noAuth"}),K(this,"configVersion",0),K(this,"syncState"),K(this,"authenticate"),K(this,"stopSocket"),K(this,"tryRestartSocket"),K(this,"pauseSocket"),K(this,"resumeSocket"),K(this,"clearAuth"),K(this,"logger"),K(this,"refreshTokenLeewaySeconds"),K(this,"tokenConfirmationAttempts",0),this.syncState=e,this.authenticate=t.authenticate,this.stopSocket=t.stopSocket,this.tryRestartSocket=t.tryRestartSocket,this.pauseSocket=t.pauseSocket,this.resumeSocket=t.resumeSocket,this.clearAuth=t.clearAuth,this.logger=s.logger,this.refreshTokenLeewaySeconds=s.refreshTokenLeewaySeconds}async setConfig(e,t){this.resetAuthState(),this._logVerbose("pausing WS for auth token fetch"),this.pauseSocket();let s=await this.fetchTokenAndGuardAgainstRace(e,{forceRefreshToken:!1});s.isFromOutdatedConfig||(s.value?(this.setAuthState({state:"waitingForServerConfirmationOfCachedToken",config:{fetchToken:e,onAuthChange:t},hasRetried:!1}),this.authenticate(s.value)):(this.setAuthState({state:"initialRefetch",config:{fetchToken:e,onAuthChange:t}}),await this.refetchToken()),this._logVerbose("resuming WS after auth token fetch"),this.resumeSocket())}onTransition(e){if(this.syncState.isCurrentOrNewerAuthVersion(e.endVersion.identity)&&!(e.endVersion.identity<=e.startVersion.identity)){if("waitingForServerConfirmationOfCachedToken"===this.authState.state){this._logVerbose("server confirmed auth token is valid"),this.refetchToken(),this.authState.config.onAuthChange(!0);return}"waitingForServerConfirmationOfFreshToken"===this.authState.state&&(this._logVerbose("server confirmed new auth token is valid"),this.scheduleTokenRefetch(this.authState.token),this.tokenConfirmationAttempts=0,this.authState.hadAuth||this.authState.config.onAuthChange(!0))}}onAuthError(e){if(!1===e.authUpdateAttempted&&("waitingForServerConfirmationOfFreshToken"===this.authState.state||"waitingForServerConfirmationOfCachedToken"===this.authState.state))return void this._logVerbose("ignoring non-auth token expired error");let{baseVersion:t}=e;if(!this.syncState.isCurrentOrNewerAuthVersion(t+1))return void this._logVerbose("ignoring auth error for previous auth attempt");this.tryToReauthenticate(e)}async tryToReauthenticate(e){if(this._logVerbose(`attempting to reauthenticate: ${e.error}`),"noAuth"===this.authState.state||"waitingForServerConfirmationOfFreshToken"===this.authState.state&&this.tokenConfirmationAttempts>=2){this.logger.error(`Failed to authenticate: "${e.error}", check your server auth config`),this.syncState.hasAuth()&&this.syncState.clearAuth(),"noAuth"!==this.authState.state&&this.setAndReportAuthFailed(this.authState.config.onAuthChange);return}"waitingForServerConfirmationOfFreshToken"===this.authState.state&&(this.tokenConfirmationAttempts++,this._logVerbose(`retrying reauthentication, ${2-this.tokenConfirmationAttempts} attempts remaining`)),await this.stopSocket();let t=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});t.isFromOutdatedConfig||(t.value&&this.syncState.isNewAuth(t.value)?(this.authenticate(t.value),this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",config:this.authState.config,token:t.value,hadAuth:"notRefetching"===this.authState.state||"waitingForScheduledRefetch"===this.authState.state})):(this._logVerbose("reauthentication failed, could not fetch a new token"),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this.tryRestartSocket())}async refetchToken(){if("noAuth"===this.authState.state)return;this._logVerbose("refetching auth token");let e=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});e.isFromOutdatedConfig||(e.value?this.syncState.isNewAuth(e.value)?(this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",hadAuth:this.syncState.hasAuth(),token:e.value,config:this.authState.config}),this.authenticate(e.value)):this.setAuthState({state:"notRefetching",config:this.authState.config}):(this._logVerbose("refetching token failed"),this.syncState.hasAuth()&&this.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this._logVerbose("restarting WS after auth token fetch (if currently stopped)"),this.tryRestartSocket())}scheduleTokenRefetch(e){if("noAuth"===this.authState.state)return;let t=this.decodeToken(e);if(!t)return void this.logger.error("Auth token is not a valid JWT, cannot refetch the token");let{iat:s,exp:i}=t;if(!s||!i)return void this.logger.error("Auth token does not have required fields, cannot refetch the token");let r=i-s;if(r<=2)return void this.logger.error("Auth token does not live long enough, cannot refetch the token");let n=Math.min(1728e6,(r-this.refreshTokenLeewaySeconds)*1e3);n<=0&&(this.logger.warn(`Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${r}s`),n=0);let o=setTimeout(()=>{this._logVerbose("running scheduled token refetch"),this.refetchToken()},n);this.setAuthState({state:"waitingForScheduledRefetch",refetchTokenTimeoutId:o,config:this.authState.config}),this._logVerbose(`scheduled preemptive auth token refetching in ${n}ms`)}async fetchTokenAndGuardAgainstRace(e,t){let s=++this.configVersion;this._logVerbose(`fetching token with config version ${s}`);let i=await e(t);return this.configVersion!==s?(this._logVerbose(`stale config version, expected ${s}, got ${this.configVersion}`),{isFromOutdatedConfig:!0}):{isFromOutdatedConfig:!1,value:i}}stop(){this.resetAuthState(),this.configVersion++,this._logVerbose(`config version bumped to ${this.configVersion}`)}setAndReportAuthFailed(e){e(!1),this.resetAuthState()}resetAuthState(){this.setAuthState({state:"noAuth"})}setAuthState(e){let t="waitingForServerConfirmationOfFreshToken"===e.state?{hadAuth:e.hadAuth,state:e.state,token:`...${e.token.slice(-7)}`}:{state:e.state};switch(this._logVerbose(`setting auth state to ${JSON.stringify(t)}`),e.state){case"waitingForScheduledRefetch":case"notRefetching":case"noAuth":this.tokenConfirmationAttempts=0}"waitingForScheduledRefetch"===this.authState.state&&(clearTimeout(this.authState.refetchTokenTimeoutId),this.syncState.markAuthCompletion()),this.authState=e}decodeToken(e){try{return function(e,t){let s;if("string"!=typeof e)throw new G("Invalid token specified: must be a string");t||(t={});let i=+(!0!==t.header),r=e.split(".")[i];if("string"!=typeof r)throw new G(`Invalid token specified: missing part #${i+1}`);try{s=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var s;return s=t,decodeURIComponent(atob(s).replace(/(.)/g,(e,t)=>{let s=t.charCodeAt(0).toString(16).toUpperCase();return s.length<2&&(s="0"+s),"%"+s}))}catch(e){return atob(t)}}(r)}catch(e){throw new G(`Invalid token specified: invalid base64 for part #${i+1} (${e.message})`)}try{return JSON.parse(s)}catch(e){throw new G(`Invalid token specified: invalid json for part #${i+1} (${e.message})`)}}(e)}catch(e){return this._logVerbose(`Error decoding token: ${e instanceof Error?e.message:"Unknown error"}`),null}}_logVerbose(e){this.logger.logVerbose(`${e} [v${this.configVersion}]`)}}let et=["convexClientConstructed","convexWebSocketOpen","convexFirstMessageReceived"];function es(e){let t=e.name.slice(6);return{name:t=t.charAt(0).toLowerCase()+t.slice(1),startTime:e.startTime}}var ei=Object.defineProperty,er=(e,t,s)=>t in e?ei(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,en=(e,t,s)=>er(e,"symbol"!=typeof t?t+"":t,s);class eo{constructor(e,t,s){let r;if(en(this,"address"),en(this,"state"),en(this,"requestManager"),en(this,"webSocketManager"),en(this,"authenticationManager"),en(this,"remoteQuerySet"),en(this,"optimisticQueryResults"),en(this,"_transitionHandlerCounter",0),en(this,"_nextRequestId"),en(this,"_onTransitionFns",new Map),en(this,"_sessionId"),en(this,"firstMessageReceived",!1),en(this,"debug"),en(this,"logger"),en(this,"maxObservedTimestamp"),en(this,"mark",e=>{var t;this.debug&&(t=this.sessionId,"undefined"!=typeof performance&&performance.mark&&performance.mark(e,{detail:{sessionId:t}}))}),"object"==typeof e)throw Error("Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.");s?.skipConvexDeploymentUrlCheck!==!0&&(0,A.R6)(e);let n=(s={...s}).authRefreshTokenLeewaySeconds??2,o=s.webSocketConstructor;if(!o&&"undefined"==typeof WebSocket)throw Error("No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient");o=o||WebSocket,this.debug=s.reportDebugInfoToConvex??!1,this.address=e,this.logger=!1===s.logger?new h({verbose:s.verbose??!1}):!0!==s.logger&&s.logger?s.logger:c({verbose:s.verbose??!1});let a=e.search("://");if(-1===a)throw Error("Provided address was not an absolute URL.");let u=e.substring(a+3),l=e.substring(0,a);if("http"===l)r="ws";else if("https"===l)r="wss";else throw Error(`Unknown parent protocol ${l}`);let d=`${r}://${u}/api/${i.r}/sync`;this.state=new b,this.remoteQuerySet=new B(e=>this.state.queryPath(e),this.logger),this.requestManager=new R(this.logger),this.authenticationManager=new ee(this.state,{authenticate:e=>{let t=this.state.setAuth(e);return this.webSocketManager.sendMessage(t),t.baseVersion},stopSocket:()=>this.webSocketManager.stop(),tryRestartSocket:()=>this.webSocketManager.tryRestart(),pauseSocket:()=>{this.webSocketManager.pause(),this.state.pause()},resumeSocket:()=>this.webSocketManager.resume(),clearAuth:()=>{this.clearAuth()}},{logger:this.logger,refreshTokenLeewaySeconds:n}),this.optimisticQueryResults=new E,this.addOnTransitionHandler(e=>{t(e.queries.map(e=>e.token))}),this._nextRequestId=0,this._sessionId="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)});let{unsavedChangesWarning:f}=s;if("undefined"==typeof window||void 0===window.addEventListener){if(!0===f)throw Error("unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.")}else!1!==f&&window.addEventListener("beforeunload",e=>{if(this.requestManager.hasIncompleteRequests()){e.preventDefault();let t="Are you sure you want to leave? Your changes may not be saved.";return(e||window.event).returnValue=t,t}});this.webSocketManager=new Z(d,{onOpen:e=>{this.mark("convexWebSocketOpen"),this.webSocketManager.sendMessage({...e,type:"Connect",sessionId:this._sessionId,maxObservedTimestamp:this.maxObservedTimestamp});let t=new Set(this.remoteQuerySet.remoteQueryResults().keys());this.remoteQuerySet=new B(e=>this.state.queryPath(e),this.logger);let[s,i]=this.state.restart(t);for(let e of(i&&this.webSocketManager.sendMessage(i),this.webSocketManager.sendMessage(s),this.requestManager.restart()))this.webSocketManager.sendMessage(e)},onResume:()=>{let[e,t]=this.state.resume();for(let s of(t&&this.webSocketManager.sendMessage(t),e&&this.webSocketManager.sendMessage(e),this.requestManager.resume()))this.webSocketManager.sendMessage(s)},onMessage:e=>{switch(!this.firstMessageReceived&&(this.firstMessageReceived=!0,this.mark("convexFirstMessageReceived"),this.reportMarks()),e.type){case"Transition":{this.observedTimestamp(e.endVersion.ts),this.authenticationManager.onTransition(e),this.remoteQuerySet.transition(e),this.state.transition(e);let t=this.requestManager.removeCompleted(this.remoteQuerySet.timestamp());this.notifyOnQueryResultChanges(t);break}case"MutationResponse":{e.success&&this.observedTimestamp(e.ts);let t=this.requestManager.onResponse(e);null!==t&&this.notifyOnQueryResultChanges(new Map([[t.requestId,t.result]]));break}case"ActionResponse":this.requestManager.onResponse(e);break;case"AuthError":this.authenticationManager.onAuthError(e);break;case"FatalError":{let t=function(e,t){let s=`[CONVEX FATAL ERROR] ${t}`;return e.error(s),Error(s)}(this.logger,e.error);throw this.webSocketManager.terminate(),t}}return{hasSyncedPastLastReconnect:this.hasSyncedPastLastReconnect()}},onServerDisconnectError:s.onServerDisconnectError},o,this.logger),this.mark("convexClientConstructed")}hasSyncedPastLastReconnect(){return this.requestManager.hasSyncedPastLastReconnect()||this.state.hasSyncedPastLastReconnect()}observedTimestamp(e){(void 0===this.maxObservedTimestamp||this.maxObservedTimestamp.lessThanOrEqual(e))&&(this.maxObservedTimestamp=e)}getMaxObservedTimestamp(){return this.maxObservedTimestamp}notifyOnQueryResultChanges(e){let t=this.remoteQuerySet.remoteQueryResults(),s=new Map;for(let[e,i]of t){let t=this.state.queryToken(e);if(null!==t){let r={result:i,udfPath:this.state.queryPath(e),args:this.state.queryArgs(e)};s.set(t,r)}}let i=this.optimisticQueryResults.ingestQueryResultsFromServer(s,new Set(e.keys()));this.handleTransition({queries:i.map(e=>{let t=this.optimisticQueryResults.rawQueryResult(e);return{token:e,modification:{kind:"Updated",result:t.result}}}),reflectedMutations:Array.from(e).map(([e,t])=>({requestId:e,result:t})),timestamp:this.remoteQuerySet.timestamp()})}handleTransition(e){for(let t of this._onTransitionFns.values())t(e)}addOnTransitionHandler(e){let t=this._transitionHandlerCounter++;return this._onTransitionFns.set(t,e),()=>this._onTransitionFns.delete(t)}setAuth(e,t){this.authenticationManager.setConfig(e,t)}hasAuth(){return this.state.hasAuth()}setAdminAuth(e,t){let s=this.state.setAdminAuth(e,t);this.webSocketManager.sendMessage(s)}clearAuth(){let e=this.state.clearAuth();this.webSocketManager.sendMessage(e)}subscribe(e,t,s){let i=(0,A.Zy)(t),{modification:r,queryToken:n,unsubscribe:o}=this.state.subscribe(e,i,s?.journal,s?.componentPath);return null!==r&&this.webSocketManager.sendMessage(r),{queryToken:n,unsubscribe:()=>{let e=o();e&&this.webSocketManager.sendMessage(e)}}}localQueryResult(e,t){let s=y(e,(0,A.Zy)(t));return this.optimisticQueryResults.queryResult(s)}localQueryResultByToken(e){return this.optimisticQueryResults.queryResult(e)}hasLocalQueryResultByToken(e){return this.optimisticQueryResults.hasQueryResult(e)}localQueryLogs(e,t){let s=y(e,(0,A.Zy)(t));return this.optimisticQueryResults.queryLogs(s)}queryJournal(e,t){let s=y(e,(0,A.Zy)(t));return this.state.queryJournal(s)}connectionState(){let e=this.webSocketManager.connectionState();return{hasInflightRequests:this.requestManager.hasInflightRequests(),isWebSocketConnected:e.isConnected,hasEverConnected:e.hasEverConnected,connectionCount:e.connectionCount,connectionRetries:e.connectionRetries,timeOfOldestInflightRequest:this.requestManager.timeOfOldestInflightRequest(),inflightMutations:this.requestManager.inflightMutations(),inflightActions:this.requestManager.inflightActions()}}async mutation(e,t,s){let i=await this.mutationInternal(e,t,s);if(!i.success){if(void 0!==i.errorData)throw f(i,new C.i(d("mutation",e,i)));throw Error(d("mutation",e,i))}return i.value}async mutationInternal(e,t,s,i){let{mutationPromise:r}=this.enqueueMutation(e,t,s,i);return r}enqueueMutation(e,t,s,i){let n=(0,A.Zy)(t);this.tryReportLongDisconnect();let o=this.nextRequestId;if(this._nextRequestId++,void 0!==s){let e=s.optimisticUpdate;if(void 0!==e){let t=this.optimisticQueryResults.applyOptimisticUpdate(t=>{e(t,n)instanceof Promise&&this.logger.warn("Optimistic update handler returned a Promise. Optimistic updates should be synchronous.")},o).map(e=>{let t=this.localQueryResultByToken(e);return{token:e,modification:{kind:"Updated",result:void 0===t?void 0:{success:!0,value:t,logLines:[]}}}});this.handleTransition({queries:t,reflectedMutations:[],timestamp:this.remoteQuerySet.timestamp()})}}let a={type:"Mutation",requestId:o,udfPath:e,componentPath:i,args:[(0,r.rz)(n)]},u=this.webSocketManager.sendMessage(a);return{requestId:o,mutationPromise:this.requestManager.request(a,u)}}async action(e,t){let s=await this.actionInternal(e,t);if(!s.success){if(void 0!==s.errorData)throw f(s,new C.i(d("action",e,s)));throw Error(d("action",e,s))}return s.value}async actionInternal(e,t,s){let i=(0,A.Zy)(t),n=this.nextRequestId;this._nextRequestId++,this.tryReportLongDisconnect();let o={type:"Action",requestId:n,udfPath:e,componentPath:s,args:[(0,r.rz)(i)]},a=this.webSocketManager.sendMessage(o);return this.requestManager.request(o,a)}async close(){return this.authenticationManager.stop(),this.webSocketManager.terminate()}get url(){return this.address}get nextRequestId(){return this._nextRequestId}get sessionId(){return this._sessionId}reportMarks(){if(this.debug){let e=function(e){if("undefined"==typeof performance||!performance.getEntriesByName)return[];let t=[];for(let s of et){let i=performance.getEntriesByName(s).filter(e=>"mark"===e.entryType).filter(t=>t.detail.sessionId===e);t.push(...i)}return t.map(es)}(this.sessionId);this.webSocketManager.sendMessage({type:"Event",eventType:"ClientConnect",event:e})}}tryReportLongDisconnect(){if(!this.debug)return;let e=this.connectionState().timeOfOldestInflightRequest;null===e||Date.now()-e.getTime()<=6e4||fetch(`${this.address}/api/debug_event`,{method:"POST",headers:{"Content-Type":"application/json","Convex-Client":`npm-${i.r}`},body:JSON.stringify({event:"LongWebsocketDisconnect"})}).then(e=>{e.ok||this.logger.warn("Analytics request failed with response:",e.body)}).catch(e=>{this.logger.warn("Analytics response failed with error:",e)})}}var ea=Object.defineProperty,eu=Object.defineProperty,eh=s(2115),ec=s(6339),el=Object.defineProperty,ed=(e,t,s)=>t in e?el(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,ef=(e,t,s)=>ed(e,"symbol"!=typeof t?t+"":t,s);if(void 0===eh)throw Error("Required dependency 'react' not found");class eg{constructor(e,t){if(ef(this,"address"),ef(this,"cachedSync"),ef(this,"listeners"),ef(this,"options"),ef(this,"closed",!1),ef(this,"_logger"),ef(this,"adminAuth"),ef(this,"fakeUserIdentity"),void 0===e)throw Error("No address provided to ConvexReactClient.\nIf trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\nIf running locally, make sure to run `convex dev` and ensure the .env.local file is populated.");if("string"!=typeof e)throw Error(`ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof e} instead.`);if(!e.includes("://"))throw Error("Provided address was not an absolute URL.");this.address=e,this.listeners=new Map,this._logger=t?.logger===!1?new h({verbose:t?.verbose??!1}):t?.logger!==!0&&t?.logger?t.logger:c({verbose:t?.verbose??!1}),this.options={...t,logger:this._logger}}get url(){return this.address}get sync(){if(this.closed)throw Error("ConvexReactClient has already been closed.");return this.cachedSync||(this.cachedSync=new eo(this.address,e=>this.transition(e),this.options),this.adminAuth&&this.cachedSync.setAdminAuth(this.adminAuth,this.fakeUserIdentity)),this.cachedSync}setAuth(e,t){if("string"==typeof e)throw Error("Passing a string to ConvexReactClient.setAuth is no longer supported, please upgrade to passing in an async function to handle reauthentication.");this.sync.setAuth(e,t??(()=>{}))}clearAuth(){this.sync.clearAuth()}setAdminAuth(e,t){if(this.adminAuth=e,this.fakeUserIdentity=t,this.closed)throw Error("ConvexReactClient has already been closed.");this.cachedSync&&this.sync.setAdminAuth(e,t)}watchQuery(e,...t){let[s,i]=t,r=(0,q.qQ)(e);return{onUpdate:e=>{let{queryToken:t,unsubscribe:n}=this.sync.subscribe(r,s,i),o=this.listeners.get(t);return void 0!==o?o.add(e):this.listeners.set(t,new Set([e])),()=>{if(this.closed)return;let s=this.listeners.get(t);s.delete(e),0===s.size&&this.listeners.delete(t),n()}},localQueryResult:()=>{if(this.cachedSync)return this.cachedSync.localQueryResult(r,s)},localQueryLogs:()=>{if(this.cachedSync)return this.cachedSync.localQueryLogs(r,s)},journal:()=>{if(this.cachedSync)return this.cachedSync.queryJournal(r,s)}}}mutation(e,...t){let[s,i]=t,r=(0,q.qQ)(e);return this.sync.mutation(r,s,i)}action(e,...t){let s=(0,q.qQ)(e);return this.sync.action(s,...t)}query(e,...t){let s=this.watchQuery(e,...t),i=s.localQueryResult();return void 0!==i?Promise.resolve(i):new Promise((e,t)=>{let i=s.onUpdate(()=>{i();try{e(s.localQueryResult())}catch(e){t(e)}})})}connectionState(){return this.sync.connectionState()}get logger(){return this._logger}async close(){if(this.closed=!0,this.listeners=new Map,this.cachedSync){let e=this.cachedSync;this.cachedSync=void 0,await e.close()}}transition(e){for(let t of e){let e=this.listeners.get(t);if(e)for(let t of e)t()}}}let ey=eh.createContext(void 0);function ep(){return(0,eh.useContext)(ey)}let em=({client:e,children:t})=>eh.createElement(ey.Provider,{value:e},t);function ev(e,...t){let s="skip"===t[0],i="skip"===t[0]?{}:(0,A.Zy)(t[0]),n="string"==typeof e?(0,q.Gg)(e):e,o=(0,q.qQ)(n),a=(0,eh.useMemo)(()=>s?{}:{query:{query:n,args:i}},[JSON.stringify((0,r.rz)(i)),o,s]),u=(0,ec.E)(a).query;if(u instanceof Error)throw u;return u}function eb(e){let t="string"==typeof e?(0,q.Gg)(e):e,s=(0,eh.useContext)(ey);if(void 0===s)throw Error("Could not find Convex client! `useMutation` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");return(0,eh.useMemo)(()=>(function e(t,s,i){function r(e){return function(e){if("object"==typeof e&&null!==e&&"bubbles"in e&&"persist"in e&&"isDefaultPrevented"in e)throw Error("Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like `const handler = () => myMutation();` and using `handler` in the event handler.")}(e),s.mutation(t,e,{optimisticUpdate:i})}return r.withOptimisticUpdate=function(r){if(void 0!==i)throw Error(`Already specified optimistic update for mutation ${(0,q.qQ)(t)}`);return e(t,s,r)},r})(t,s),[s,(0,q.qQ)(t)])}function ew(e){let t=(0,eh.useContext)(ey),s="string"==typeof e?(0,q.Gg)(e):e;if(void 0===t)throw Error("Could not find Convex client! `useAction` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");return(0,eh.useMemo)(()=>function(e){return t.action(s,e)},[t,(0,q.qQ)(s)])}},9089:(e,t,s)=>{s.d(t,{du:()=>i.du,o4:()=>r,rz:()=>i.rz});var i=s(1388);s(8039);var r=s(5996);s(2800),s(6643)},9414:(e,t,s)=>{function i(e){if(void 0===e)return{};if(!n(e))throw Error(`The arguments to a Convex function must be an object. Received: ${e}`);return e}function r(e){if(void 0===e)throw Error("Client created with undefined deployment address. If you used an environment variable, check that it's set.");if("string"!=typeof e)throw Error(`Invalid deployment address: found ${e}".`);if(!(e.startsWith("http:")||e.startsWith("https:")))throw Error(`Invalid deployment address: Must start with "https://" or "http://". Found "${e}".`);try{new URL(e)}catch{throw Error(`Invalid deployment address: "${e}" is not a valid URL. If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}if(e.endsWith(".convex.site"))throw Error(`Invalid deployment address: "${e}" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}function n(e){let t="object"==typeof e,s=Object.getPrototypeOf(e),i=null===s||s===Object.prototype||s?.constructor?.name==="Object";return t&&i}s.d(t,{R6:()=>r,Zy:()=>i,iY:()=>n})}}]);