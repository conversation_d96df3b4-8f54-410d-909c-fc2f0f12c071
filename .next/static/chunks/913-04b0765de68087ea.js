"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[913],{3064:(e,t,n)=>{n.d(t,{hf:()=>h,ry:()=>o});var r=n(2115),a=n(8530),l=n(8293),c=n(6515),s=n(3273),i=n(4267);function o({messageFormatter:e,messageDecoder:t,messageEncoder:n,channelTopic:i,...o}){let u=r.useRef(null),m=r.useRef(null),d=r.useMemo(()=>({messageDecoder:t,messageEncoder:n,channelTopic:i}),[t,n,i]),{chatMessages:h,send:v,isSending:p}=(0,c.w)(d),g=(0,s.j)(),C=r.useRef(0);async function f(e){e.preventDefault(),m.current&&""!==m.current.value.trim()&&(await v(m.current.value),m.current.value="",m.current.focus())}return r.useEffect(()=>{var e;u&&(null==(e=u.current)||e.scrollTo({top:u.current.scrollHeight}))},[u,h]),r.useEffect(()=>{var e,t,n,r,a;if(!g||0===h.length)return;if(null!=(e=g.widget.state)&&e.showChat&&h.length>0&&C.current!==(null==(t=h[h.length-1])?void 0:t.timestamp)){C.current=null==(n=h[h.length-1])?void 0:n.timestamp;return}let l=h.filter(e=>!C.current||e.timestamp>C.current).length,{widget:c}=g;l>0&&(null==(r=c.state)?void 0:r.unreadMessages)!==l&&(null==(a=c.dispatch)||a.call(c,{msg:"unread_msg",count:l}))},[h,null==g?void 0:g.widget]),r.createElement("div",{...o,className:"lk-chat"},r.createElement("div",{className:"lk-chat-header"},"Messages",g&&r.createElement(l.C,{className:"lk-close-button"},r.createElement(l.S,null))),r.createElement("ul",{className:"lk-list lk-chat-messages",ref:u},o.children?h.map((t,n)=>(0,a.c)(o.children,{entry:t,key:t.id??n,messageFormatter:e})):h.map((t,n,a)=>{let c=n>=1&&a[n-1].from===t.from,s=n>=1&&t.timestamp-a[n-1].timestamp<6e4;return r.createElement(l.a,{key:t.id??n,hideName:c,hideTimestamp:!1!==c&&s,entry:t,messageFormatter:e})})),r.createElement("form",{className:"lk-chat-form",onSubmit:f},r.createElement("input",{className:"lk-form-control lk-chat-form-input",disabled:p,ref:m,type:"text",placeholder:"Enter a message...",onInput:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),onKeyUp:e=>e.stopPropagation()}),r.createElement("button",{type:"submit",className:"lk-button lk-chat-form-button",disabled:p},"Send")))}function u({kind:e,initialSelection:t,onActiveDeviceChange:n,tracks:a,requestPermissions:c=!1,...i}){let[o,u]=r.useState(!1),[m,d]=r.useState([]),[h,v]=r.useState(!0),[p,g]=r.useState(c),C=(e,t)=>{s.l.debug("handle device change"),u(!1),null==n||n(e,t)},f=r.useRef(null),E=r.useRef(null);r.useLayoutEffect(()=>{o&&g(!0)},[o]),r.useLayoutEffect(()=>{let e;return f.current&&E.current&&(m||h)&&(e=(0,s.ao)(f.current,E.current,(e,t)=>{E.current&&Object.assign(E.current.style,{left:`${e}px`,top:`${t}px`})})),v(!1),()=>{null==e||e()}},[f,E,m,h]);let k=r.useCallback(e=>{E.current&&e.target!==f.current&&o&&(0,s.ap)(E.current,e)&&u(!1)},[o,E,f]);return r.useEffect(()=>(document.addEventListener("click",k),()=>{document.removeEventListener("click",k)}),[k]),r.createElement(r.Fragment,null,r.createElement("button",{className:"lk-button lk-button-menu","aria-pressed":o,...i,onClick:()=>u(!o),ref:f},i.children),!i.disabled&&r.createElement("div",{className:"lk-device-menu",ref:E,style:{visibility:o?"visible":"hidden"}},e?r.createElement(l.M,{initialSelection:t,onActiveDeviceChange:t=>C(e,t),onDeviceListChange:d,kind:e,track:null==a?void 0:a[e],requestPermissions:p}):r.createElement(r.Fragment,null,r.createElement("div",{className:"lk-device-menu-heading"},"Audio inputs"),r.createElement(l.M,{kind:"audioinput",onActiveDeviceChange:e=>C("audioinput",e),onDeviceListChange:d,track:null==a?void 0:a.audioinput,requestPermissions:p}),r.createElement("div",{className:"lk-device-menu-heading"},"Video inputs"),r.createElement(l.M,{kind:"videoinput",onActiveDeviceChange:e=>C("videoinput",e),onDeviceListChange:d,track:null==a?void 0:a.videoinput,requestPermissions:p}))))}let m=r.forwardRef(function(e,t){let{mergedProps:n}=function({props:e}){let{dispatch:t,state:n}=(0,s.a)().widget,l="lk-button lk-settings-toggle";return{mergedProps:r.useMemo(()=>(0,a.m)(e,{className:l,onClick:()=>{t&&t({msg:"toggle_settings"})},"aria-pressed":null!=n&&n.showSettings?"true":"false"}),[e,l,t,n])}}({props:e});return r.createElement("button",{ref:t,...n},e.children)}),d=e=>{switch(e){case i.CC.Source.Camera:return 1;case i.CC.Source.Microphone:return 2;case i.CC.Source.ScreenShare:return 3;default:return 0}};function h({variation:e,controls:t,saveUserChoices:n=!0,onDeviceError:o,...h}){var v;let[p,g]=r.useState(!1),C=(0,s.j)();r.useEffect(()=>{var e,t;(null==(e=null==C?void 0:C.widget.state)?void 0:e.showChat)!==void 0&&g(null==(t=null==C?void 0:C.widget.state)?void 0:t.showChat)},[null==(v=null==C?void 0:C.widget.state)?void 0:v.showChat]);let f=(0,c.z)(`(max-width: ${p?1e3:760}px)`)?"minimal":"verbose";e??(e=f);let E={leave:!0,...t},k=(0,c.A)();if(k){let e=e=>k.canPublish&&(0===k.canPublishSources.length||k.canPublishSources.includes(d(e)));E.camera??(E.camera=e(i.CC.Source.Camera)),E.microphone??(E.microphone=e(i.CC.Source.Microphone)),E.screenShare??(E.screenShare=e(i.CC.Source.ScreenShare)),E.chat??(E.chat=k.canPublishData&&(null==t?void 0:t.chat))}else E.camera=!1,E.chat=!1,E.microphone=!1,E.screenShare=!1;let S=r.useMemo(()=>"minimal"===e||"verbose"===e,[e]),b=r.useMemo(()=>"textOnly"===e||"verbose"===e,[e]),w=(0,s.aq)(),[y,D]=r.useState(!1),N=r.useCallback(e=>{D(e)},[D]),M=(0,a.a)({className:"lk-control-bar"},h),{saveAudioInputEnabled:P,saveVideoInputEnabled:x,saveAudioInputDeviceId:A,saveVideoInputDeviceId:I}=(0,c.x)({preventSave:!n}),L=r.useCallback((e,t)=>t?P(e):null,[P]),T=r.useCallback((e,t)=>t?x(e):null,[x]);return r.createElement("div",{...M},E.microphone&&r.createElement("div",{className:"lk-button-group"},r.createElement(l.T,{source:i.CC.Source.Microphone,showIcon:S,onChange:L,onDeviceError:e=>null==o?void 0:o({source:i.CC.Source.Microphone,error:e})},b&&"Microphone"),r.createElement("div",{className:"lk-button-group-menu"},r.createElement(u,{kind:"audioinput",onActiveDeviceChange:(e,t)=>A(t??"default")}))),E.camera&&r.createElement("div",{className:"lk-button-group"},r.createElement(l.T,{source:i.CC.Source.Camera,showIcon:S,onChange:T,onDeviceError:e=>null==o?void 0:o({source:i.CC.Source.Camera,error:e})},b&&"Camera"),r.createElement("div",{className:"lk-button-group-menu"},r.createElement(u,{kind:"videoinput",onActiveDeviceChange:(e,t)=>I(t??"default")}))),E.screenShare&&w&&r.createElement(l.T,{source:i.CC.Source.ScreenShare,captureOptions:{audio:!0,selfBrowserSurface:"include"},showIcon:S,onChange:N,onDeviceError:e=>null==o?void 0:o({source:i.CC.Source.ScreenShare,error:e})},b&&(y?"Stop screen share":"Share screen")),E.chat&&r.createElement(l.C,null,S&&r.createElement(l.c,null),b&&"Chat"),E.settings&&r.createElement(m,null,S&&r.createElement(l.d,null),b&&"Settings"),E.leave&&r.createElement(l.D,null,S&&r.createElement(l.e,null),b&&"Leave"),r.createElement(l.f,null))}},4020:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])}}]);