(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82,578],{636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{KeylessCookieSync:()=>a});var n=r(5695),i=r(2115),o=r(9331);function a(e){var t;let a=(null==(t=(0,n.useSelectedLayoutSegments)()[0])?void 0:t.startsWith("/_not-found"))||!1;return(0,i.useEffect)(()=>{o.I&&!a&&r.e(65).then(r.bind(r,8065)).then(t=>t.syncKeylessConfigAction({...e,returnUrl:window.location.href}))},[a]),e.children}},671:(e,t,r)=>{"use strict";r.d(t,{AuthenticateWithRedirectCallback:()=>n.B$,ClerkDegraded:()=>n.wF,ClerkFailed:()=>n.lT,ClerkLoaded:()=>n.z0,ClerkLoading:()=>n.A0,RedirectToCreateOrganization:()=>n.rm,RedirectToOrganizationProfile:()=>n.m2,RedirectToSignIn:()=>n.W5,RedirectToSignUp:()=>n.mO,RedirectToUserProfile:()=>n.eG});var n=r(1976);r(8572)},1657:(e,t,r)=>{"use strict";r.d(t,{APIKeys:()=>n.Lq,CreateOrganization:()=>n.ul,GoogleOneTap:()=>n.PQ,OrganizationList:()=>n.oE,OrganizationProfile:()=>d,OrganizationSwitcher:()=>n.NC,PricingTable:()=>n.nm,SignIn:()=>g,SignInButton:()=>n.hZ,SignInWithMetamaskButton:()=>n.M_,SignOutButton:()=>n.ct,SignUp:()=>m,SignUpButton:()=>n.Ny,UserButton:()=>n.uF,UserProfile:()=>h,Waitlist:()=>n.cP});var n=r(1976),i=r(2115),o=r(8572),a=r(8416),s=r(6299);let l=(e,t,r,o=!0)=>{let l=i.useRef(0),{pagesRouter:c}=(0,s.r)(),{session:u,isLoaded:h}=(0,n.wV)();(0,a.Fj)()||i.useEffect(()=>{if(!h||r&&"path"!==r||o&&!u)return;let n=new AbortController,i=()=>{let r=c?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${r}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return c?c.pathname.match(/\[\[\.\.\..+]]/)||i():(async()=>{let t;if(l.current++,!(l.current>1)){try{let r=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(r,{signal:n.signal})}catch{}(null==t?void 0:t.status)===404&&i()}})(),()=>{l.current>1&&n.abort()}},[h])},c=()=>{let e=i.useRef(),{pagesRouter:t}=(0,s.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let n=r(5695).usePathname,o=r(5695).useParams,a=(n()||"").split("/").filter(Boolean),l=Object.values(o()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${a.slice(0,a.length-l.length).join("/")}`),e.current};function u(e,t,r=!0){let n=c(),i=(0,o.yC)(e,t,{path:n});return l(e,n,i.routing,r),i}let h=Object.assign(e=>i.createElement(n.Fv,{...u("UserProfile",e)}),{...n.Fv}),d=Object.assign(e=>i.createElement(n.nC,{...u("OrganizationProfile",e)}),{...n.nC}),g=e=>i.createElement(n.Ls,{...u("SignIn",e,!1)}),m=e=>i.createElement(n.Hx,{...u("SignUp",e,!1)})},5256:(e,t,r)=>{"use strict";r.d(t,{__experimental_PaymentElement:()=>n.cl,__experimental_PaymentElementProvider:()=>n.Tn,__experimental_usePaymentElement:()=>n.Jl,useAuth:()=>i.d,useClerk:()=>n.ho,useEmailLink:()=>n.ui,useOrganization:()=>n.Z5,useOrganizationList:()=>n.D_,useReverification:()=>n.Wp,useSession:()=>n.wV,useSessionList:()=>n.g7,useSignIn:()=>n.go,useSignUp:()=>n.yC,useUser:()=>n.Jd});var n=r(1976);r(3789);var i=r(7016)},8181:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var n=r(4477);let i=(0,n.createServerReference)("7f2049dbe655e5196d542e38101773d6697a94154a",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},8996:(e,t,r)=>{Promise.resolve().then(r.bind(r,1987)),Promise.resolve().then(r.bind(r,636)),Promise.resolve().then(r.bind(r,671)),Promise.resolve().then(r.bind(r,5256)),Promise.resolve().then(r.bind(r,7016)),Promise.resolve().then(r.bind(r,1657))}},e=>{var t=t=>e(e.s=t);e.O(0,[90,874,441,684,358],()=>t(8996)),_N_E=e.O()}]);