(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1057:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(5155),a=t(4761),l=t(6857),n=t(1976),i=t(6874),c=t.n(i),d=t(2115),o=t(5695),x=t(9803),m=t(7580),h=t(381);function g(){let{user:e}=(0,n.Jd)(),s=(0,a.IT)(l.F.rooms.getMyRooms),[t,i]=(0,d.useState)(""),g=(0,o.useRouter)(),u=()=>{if(t.trim()){let e=t.trim().toLowerCase().replace(/\s+/g,"-");g.push("/rooms/".concat(e))}},b=()=>{if(t.trim()){let e=t.trim().toLowerCase().replace(/\s+/g,"-");g.push("/rooms/".concat(e))}};return e?(0,r.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 px-4 md:px-6 py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Meet Clone"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2 md:space-x-4",children:(0,r.jsxs)("span",{className:"text-gray-600 hidden md:inline",children:["Welcome back, ",e.firstName]})})]})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 md:px-6 py-12",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Premium video meetings."}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Now free for everyone."})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"New Meeting"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Start an instant meeting"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("input",{type:"text",placeholder:"Enter a room name",value:t,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none",onKeyPress:e=>"Enter"===e.key&&u()}),(0,r.jsx)("button",{onClick:u,disabled:!t.trim(),className:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Start meeting"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Join Meeting"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Join with a meeting ID"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("input",{type:"text",placeholder:"Enter meeting ID or room name",value:t,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none",onKeyPress:e=>"Enter"===e.key&&b()}),(0,r.jsx)("button",{onClick:b,disabled:!t.trim(),className:"w-full px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Join"})]})]})]}),s&&s.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent meetings"}),(0,r.jsx)("div",{className:"space-y-3",children:s.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"w-4 h-4 text-gray-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e._creationTime).toLocaleDateString()})]})]}),(0,r.jsx)(c(),{href:"/rooms/".concat(e.name),className:"px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg font-medium transition-colors",children:"Join"})]},e._id))})]})]})]}):(0,r.jsx)("main",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Meet Clone"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(c(),{href:"/sign-in",className:"text-gray-600 hover:text-gray-900 font-medium",children:"Sign In"}),(0,r.jsx)(c(),{href:"/sign-up",className:"px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors",children:"Get Started"})]})]})}),(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 py-12",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:"Video meetings for everyone"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings."}),(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,r.jsx)(c(),{href:"/sign-up",className:"px-8 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors",children:"Start a meeting"}),(0,r.jsx)(c(),{href:"/sign-in",className:"px-8 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors",children:"Sign In"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"HD Video & Audio"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Crystal clear video and audio quality for professional meetings."})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Up to 100 participants"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Host large meetings with participants from around the world."})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Easy to use"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Simple interface that works on any device, no downloads required."})]})]})]})})]})})}},3886:(e,s,t)=>{Promise.resolve().then(t.bind(t,1057))},6857:(e,s,t)=>{"use strict";t.d(s,{F:()=>r});let r=t(3312).dp}},e=>{var s=s=>e(e.s=s);e.O(0,[90,761,253,493,441,684,358],()=>s(3886)),_N_E=e.O()}]);