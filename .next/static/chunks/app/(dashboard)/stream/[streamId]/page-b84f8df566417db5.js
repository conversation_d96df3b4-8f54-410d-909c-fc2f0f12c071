(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[843],{6857:(e,s,t)=>{"use strict";t.d(s,{F:()=>a});let a=t(3312).dp},7052:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eV});var a=t(5155),l=t(5695),r=t(1976),i=t(2115),n=t(4761),c=t(6857);function d(e){let{streamId:s,currentUserId:t}=e;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Moderation Panel"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Moderation features coming soon..."})]})}var o=t(1007),x=t(7580),m=t(8564),h=t(6618),u=t(4738),g=t(1066),b=t(4940),p=t(2525),j=t(4616),y=t(2965),N=t(133),f=t(4416),v=t(4229);let w=[{id:"single",name:"Single",type:"preset",icon:(0,a.jsx)(o.A,{className:"w-5 h-5"}),description:"Highlights one participant",config:{participants:1,arrangement:"single",showScreen:!1}},{id:"group",name:"Group",type:"preset",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"}),description:"Grid layout for multiple participants",config:{participants:4,arrangement:"grid",showScreen:!1}},{id:"spotlight",name:"Spotlight",type:"preset",icon:(0,a.jsx)(m.A,{className:"w-5 h-5"}),description:"Highlights main speaker with others below",config:{participants:4,arrangement:"spotlight",showScreen:!1}},{id:"news",name:"News",type:"preset",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"}),description:"Side-by-side presenter and screen",config:{participants:1,arrangement:"side-by-side",showScreen:!0,screenPosition:"side"}},{id:"screen",name:"Screen",type:"preset",icon:(0,a.jsx)(u.A,{className:"w-5 h-5"}),description:"Full screen share with small presenter",config:{participants:1,arrangement:"pip",showScreen:!0,screenPosition:"main"}},{id:"cinema",name:"Cinema",type:"preset",icon:(0,a.jsx)(g.A,{className:"w-5 h-5"}),description:"Full screen presentation only",config:{participants:0,arrangement:"custom",showScreen:!0,screenPosition:"main"}}];function k(e){var s,t;let{selectedLayout:l,onLayoutChange:r,onCreateCustomLayout:n,customLayouts:c=[]}=e,[d,o]=(0,i.useState)(!1),[x,m]=(0,i.useState)(null),h=[...w,...c],u=e=>{"custom"===e.type&&(m(e),o(!0))},g=e=>{console.log("Delete layout:",e)};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2",children:[h.map(e=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("button",{onClick:()=>r(e.id),className:"flex flex-col items-center p-3 rounded-lg border-2 transition-all min-w-[80px] ".concat(l===e.id?"border-blue-500 bg-blue-500/20 text-blue-400":"border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500"),title:e.description,children:[(0,a.jsx)("div",{className:"mb-2",children:e.icon}),(0,a.jsx)("span",{className:"text-xs font-medium",children:e.name})]}),"custom"===e.type&&(0,a.jsx)("div",{className:"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>u(e),className:"p-1 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors",children:(0,a.jsx)(b.A,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:()=>g(e.id),className:"p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})})]})})]},e.id)),(0,a.jsxs)("button",{onClick:n,className:"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 text-gray-400 hover:border-gray-500 hover:text-gray-300 transition-colors min-w-[80px]",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mb-2"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Custom"})]})]}),l&&(0,a.jsx)("div",{className:"bg-gray-700 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:null==(s=h.find(e=>e.id===l))?void 0:s.name}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:null==(t=h.find(e=>e.id===l))?void 0:t.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(y.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(N.A,{className:"w-4 h-4"})})]})]})}),d&&x&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Edit Layout"}),(0,a.jsx)("button",{onClick:()=>o(!1),className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(f.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Layout Name"}),(0,a.jsx)("input",{type:"text",value:x.name,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,a.jsx)("input",{type:"text",value:x.description,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>o(!1),className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{o(!1),m(null)},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save"})]})]})]})]})})]})}var C=t(4653),A=t(4357);function S(e){let{isOpen:s,onClose:t,onSave:l,existingLayout:r}=e,[n,c]=(0,i.useState)([]),[d,x]=(0,i.useState)(null),[m,h]=(0,i.useState)(null),[g,b]=(0,i.useState)(""),[y,N]=(0,i.useState)(!0),w=(0,i.useRef)(null),k=e=>{let s={id:"element-".concat(Date.now()),type:e,x:50,y:50,width:"dynamic-grid"===e?300:150,height:"dynamic-grid"===e?200:100,zIndex:n.length,shape:"rectangle",fit:"cover"};c([...n,s]),x(s.id)},S=(e,s)=>{c(n.map(t=>t.id===e?{...t,...s}:t))},M=e=>{c(n.filter(s=>s.id!==e)),d===e&&x(null)},F=e=>{let s=n.find(s=>s.id===e);s&&c([...n,{...s,id:"element-".concat(Date.now()),x:s.x+20,y:s.y+20}])},P=(e,s)=>{e.preventDefault(),x(s),h(s)};if(!s)return null;let L=n.find(e=>e.id===d);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/80 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Custom Layout Builder"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("input",{type:"text",placeholder:"Layout name...",value:g,onChange:e=>b(e.target.value),className:"px-3 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm focus:border-blue-500 focus:outline-none"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>N(!y),className:"px-3 py-1 rounded text-sm transition-colors ".concat(y?"bg-purple-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"),children:y?"Preview":"Edit"}),(0,a.jsxs)("button",{onClick:()=>{if(!g.trim())return void alert("Please enter a layout name");l({id:"custom-".concat(Date.now()),name:g,type:"custom",elements:n,createdAt:Date.now()}),t()},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save"})]}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(f.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[y&&(0,a.jsxs)("div",{className:"w-64 bg-gray-800 border-r border-gray-700 p-4 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-white mb-3",children:"Add Elements"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("button",{onClick:()=>k("camera"),className:"w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Camera Slot"})]}),(0,a.jsxs)("button",{onClick:()=>k("dynamic-grid"),className:"w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(C.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Dynamic Grid"})]}),(0,a.jsxs)("button",{onClick:()=>k("screen"),className:"w-full flex items-center space-x-2 p-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Media Slot"})]})]})]}),L&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-white mb-3",children:"Properties"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Shape"}),(0,a.jsxs)("select",{value:L.shape,onChange:e=>S(L.id,{shape:e.target.value}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:"rectangle",children:"Rectangle"}),(0,a.jsx)("option",{value:"rounded",children:"Rounded"}),(0,a.jsx)("option",{value:"circle",children:"Circle"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Fit"}),(0,a.jsxs)("select",{value:L.fit,onChange:e=>S(L.id,{fit:e.target.value}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:"cover",children:"Fill"}),(0,a.jsx)("option",{value:"contain",children:"Fit"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>F(L.id),className:"flex-1 flex items-center justify-center space-x-1 p-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(A.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Copy"})]}),(0,a.jsxs)("button",{onClick:()=>M(L.id),className:"flex-1 flex items-center justify-center space-x-1 p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:[(0,a.jsx)(p.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Delete"})]})]})]})]})]}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsxs)("div",{ref:w,className:"w-full h-full bg-black rounded-lg relative overflow-hidden border-2 border-dashed border-gray-600",onMouseMove:e=>{if(!m||!w.current)return;let s=w.current.getBoundingClientRect();S(m,{x:e.clientX-s.left,y:e.clientY-s.top})},onMouseUp:()=>{h(null)},style:{aspectRatio:"16/9"},children:[n.map(e=>(0,a.jsxs)("div",{className:"absolute border-2 transition-all cursor-move ".concat(d===e.id?"border-blue-500 bg-blue-500/20":"border-gray-500 bg-gray-500/20 hover:border-gray-400"," ").concat("circle"===e.shape?"rounded-full":"rounded"===e.shape?"rounded-lg":""),style:{left:e.x,top:e.y,width:e.width,height:e.height,zIndex:e.zIndex},onMouseDown:s=>P(s,e.id),children:[(0,a.jsxs)("div",{className:"flex items-center justify-center h-full text-white text-sm",children:["camera"===e.type&&(0,a.jsx)(o.A,{className:"w-6 h-6"}),"screen"===e.type&&(0,a.jsx)(u.A,{className:"w-6 h-6"}),"dynamic-grid"===e.type&&(0,a.jsx)(C.A,{className:"w-6 h-6"})]}),d===e.id&&y&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-se-resize"}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-ne-resize"}),(0,a.jsx)("div",{className:"absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-nw-resize"}),(0,a.jsx)("div",{className:"absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-sw-resize"})]})]},e.id)),0===n.length&&(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(j.A,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Add elements to create your layout"})]})})]})})]})]})})}var M=t(7213),F=t(3500),P=t(4498),L=t(3127),D=t(9869),E=t(7325),I=t(7733),R=t(8702),T=t(2657),z=t(8749);function U(e){let{isOpen:s,onClose:t,elements:l,onUpdateElements:r}=e,[n,c]=(0,i.useState)(null),[d,o]=(0,i.useState)("logos"),x=e=>{let s={id:"element-".concat(Date.now()),type:e,content:"text"===e?"Sample Text":"",x:50,y:50,width:"text"===e?200:100,height:"text"===e?40:100,visible:!0,zIndex:l.length,style:{fontSize:24,fontWeight:"normal",color:"#ffffff",backgroundColor:"banner"===e?"#000000":"transparent",opacity:1,borderRadius:0,textAlign:"left",fontFamily:"Arial"}};r([...l,s]),c(s.id)},m=(e,s)=>{r(l.map(t=>t.id===e?{...t,...s}:t))},h=(e,s)=>{let t=l.find(s=>s.id===e);t&&m(e,{style:{...t.style,...s}})},u=e=>{r(l.filter(s=>s.id!==e)),n===e&&c(null)},g=e=>{let s=l.find(s=>s.id===e);s&&m(e,{visible:!s.visible})};if(!s)return null;let b=l.find(e=>e.id===n);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Branding & Graphics"}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(f.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsxs)("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[(0,a.jsx)("div",{className:"flex border-b border-gray-700",children:[{id:"logos",label:"Logos",icon:M.A},{id:"text",label:"Text",icon:F.A},{id:"banners",label:"Banners",icon:P.A},{id:"backgrounds",label:"Backgrounds",icon:L.A}].map(e=>{let{id:s,label:t,icon:l}=e;return(0,a.jsxs)("button",{onClick:()=>o(s),className:"flex-1 flex items-center justify-center space-x-2 p-3 transition-colors ".concat(d===s?"bg-blue-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"),children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:t})]},s)})}),(0,a.jsxs)("div",{className:"flex-1 p-4 overflow-y-auto",children:["logos"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>x("logo"),className:"w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-gray-600 text-gray-400 rounded-lg hover:border-gray-500 hover:text-gray-300 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Upload Logo"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Recommended: PNG with transparent background, 300x300px max"})]}),"text"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>x("text"),className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Text"})]}),(null==b?void 0:b.type)==="text"&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Text Content"}),(0,a.jsx)("input",{type:"text",value:b.content,onChange:e=>m(b.id,{content:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Font Size"}),(0,a.jsx)("input",{type:"number",value:b.style.fontSize,onChange:e=>h(b.id,{fontSize:parseInt(e.target.value)}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Color"}),(0,a.jsx)("input",{type:"color",value:b.style.color,onChange:e=>h(b.id,{color:e.target.value}),className:"w-full h-8 bg-gray-700 border border-gray-600 rounded"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Font Weight"}),(0,a.jsxs)("select",{value:b.style.fontWeight,onChange:e=>h(b.id,{fontWeight:e.target.value}),className:"w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:"normal",children:"Normal"}),(0,a.jsx)("option",{value:"bold",children:"Bold"}),(0,a.jsx)("option",{value:"lighter",children:"Light"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-400 mb-1",children:"Alignment"}),(0,a.jsx)("div",{className:"flex space-x-1",children:[{value:"left",icon:E.A},{value:"center",icon:I.A},{value:"right",icon:R.A}].map(e=>{let{value:s,icon:t}=e;return(0,a.jsx)("button",{onClick:()=>h(b.id,{textAlign:s}),className:"flex-1 p-2 rounded transition-colors ".concat(b.style.textAlign===s?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"),children:(0,a.jsx)(t,{className:"w-4 h-4 mx-auto"})},s)})})]})]})]}),"banners"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>x("banner"),className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Banner"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Create scrolling text banners and lower thirds"})]}),"backgrounds"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:()=>x("background"),className:"w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-gray-600 text-gray-400 rounded-lg hover:border-gray-500 hover:text-gray-300 transition-colors",children:[(0,a.jsx)(D.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Upload Background"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-blue-500 to-purple-600 rounded cursor-pointer"}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-green-500 to-blue-500 rounded cursor-pointer"}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-purple-500 to-pink-500 rounded cursor-pointer"}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-orange-500 to-red-500 rounded cursor-pointer"})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-700 p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-white mb-2",children:"Elements"}),(0,a.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:l.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 rounded transition-colors cursor-pointer ".concat(n===e.id?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"),onClick:()=>c(e.id),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs",children:e.type}),(0,a.jsx)("span",{className:"text-xs opacity-75 truncate max-w-20",children:e.content||"Untitled"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{onClick:s=>{s.stopPropagation(),g(e.id)},className:"p-1 hover:bg-gray-600 rounded",children:e.visible?(0,a.jsx)(T.A,{className:"w-3 h-3"}):(0,a.jsx)(z.A,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:s=>{s.stopPropagation(),u(e.id)},className:"p-1 hover:bg-red-600 rounded",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})})]})]},e.id))})]})]}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"w-full h-full bg-black rounded-lg relative overflow-hidden",children:(0,a.jsx)("div",{className:"absolute inset-4 border border-dashed border-gray-600 rounded",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(P.A,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Preview your branding elements"})]})})})})})]})]})})}var O=t(4861),B=t(2318),G=t(2486),V=t(7951),K=t(5525),_=t(1951),H=t(9588),W=t(5203),Y=t(9803),q=t(646),J=t(3786);function X(e){let{isOpen:s,onClose:t,guests:l,onUpdateGuests:r,streamId:n}=e,[c,d]=(0,i.useState)("invite"),[m,h]=(0,i.useState)(""),[u,g]=(0,i.useState)("guest"),[b,j]=(0,i.useState)(""),[y,N]=(0,i.useState)([]),f=(e,s)=>{r(l.map(t=>t.id===e?{...t,status:s}:t))},v=(e,s)=>{r(l.map(t=>t.id===e?{...t,...s}:t))},w=e=>{r(l.filter(s=>s.id!==e))},k=e=>{f(e,"on-stage")},C=e=>{f(e,"backstage")},S=()=>"".concat(window.location.origin,"/join/").concat(n);if(!s)return null;let M=l.filter(e=>"pending"===e.status),F=l.filter(e=>"backstage"===e.status),P=l.filter(e=>"on-stage"===e.status);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Guest Management"}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(O.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"flex border-b border-gray-700",children:[{id:"invite",label:"Invite Guests",icon:B.A},{id:"manage",label:"Manage Guests",icon:x.A},{id:"backstage",label:"Backstage",icon:T.A}].map(e=>{let{id:s,label:t,icon:l}=e;return(0,a.jsxs)("button",{onClick:()=>d(s),className:"flex items-center space-x-2 px-6 py-3 transition-colors ".concat(c===s?"bg-blue-600 text-white border-b-2 border-blue-400":"text-gray-400 hover:text-white hover:bg-gray-800"),children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:t}),"backstage"===s&&F.length>0&&(0,a.jsx)("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:F.length})]},s)})}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:["invite"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-3",children:"Share Invite Link"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:S(),readOnly:!0,className:"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"}),(0,a.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(S())},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Copy"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"Anyone with this link can request to join your stream"})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-3",children:"Send Email Invitation"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:m,onChange:e=>h(e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Role"}),(0,a.jsxs)("select",{value:u,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none",children:[(0,a.jsx)("option",{value:"guest",children:"Guest"}),(0,a.jsx)("option",{value:"moderator",children:"Moderator"}),(0,a.jsx)("option",{value:"co-host",children:"Co-Host"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Personal Message (Optional)"}),(0,a.jsx)("textarea",{value:b,onChange:e=>j(e.target.value),placeholder:"Add a personal message to your invitation...",rows:3,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-blue-500 focus:outline-none"})]}),(0,a.jsxs)("button",{onClick:()=>{m.trim()&&(r([...l,{id:"guest-".concat(Date.now()),name:m.split("@")[0],email:m,status:"invited",role:u,isAudioMuted:!1,isVideoOff:!1,isVisible:!0}]),h(""),j(""),console.log("Sending invitation to:",m))},disabled:!m.trim(),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)(G.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Send Invitation"})]})]})]})]}),"manage"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-white mb-3 flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"On Stage"}),(0,a.jsx)("span",{className:"bg-green-500 text-white text-xs px-2 py-1 rounded-full",children:P.length})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[P.map(e=>(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["co-host"===e.role&&(0,a.jsx)(V.A,{className:"w-4 h-4 text-yellow-500"}),"moderator"===e.role&&(0,a.jsx)(K.A,{className:"w-4 h-4 text-blue-500"}),"guest"===e.role&&(0,a.jsx)(o.A,{className:"w-4 h-4 text-gray-400"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>v(e.id,{isAudioMuted:!e.isAudioMuted}),className:"p-2 rounded transition-colors ".concat(e.isAudioMuted?"bg-red-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"),children:e.isAudioMuted?(0,a.jsx)(_.A,{className:"w-4 h-4"}):(0,a.jsx)(H.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>v(e.id,{isVideoOff:!e.isVideoOff}),className:"p-2 rounded transition-colors ".concat(e.isVideoOff?"bg-red-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"),children:e.isVideoOff?(0,a.jsx)(W.A,{className:"w-4 h-4"}):(0,a.jsx)(Y.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>C(e.id),className:"px-3 py-1 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors text-sm",children:"Move to Backstage"}),(0,a.jsx)("button",{onClick:()=>w(e.id),className:"p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:(0,a.jsx)(p.A,{className:"w-4 h-4"})})]})]})},e.id)),0===P.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No guests on stage"})]})]}),M.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-white mb-3 flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"Pending Approval"}),(0,a.jsx)("span",{className:"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full",children:M.length})]}),(0,a.jsx)("div",{className:"space-y-2",children:M.map(e=>(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>f(e.id,"backstage"),className:"flex items-center space-x-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors",children:[(0,a.jsx)(q.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Approve"})]}),(0,a.jsxs)("button",{onClick:()=>w(e.id),className:"flex items-center space-x-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:[(0,a.jsx)(O.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Deny"})]})]})]})},e.id))})]})]}),"backstage"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white",children:"Backstage Area"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Guests can prepare here before going live"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[F.map(e=>(0,a.jsx)("div",{className:"bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Ready to go live"})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("button",{onClick:()=>k(e.id),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(J.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Bring to Stage"})]})})]})},e.id)),0===F.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No guests in backstage"})]})]})]})]})})}var $=t(5690),Q=t(6516),Z=t(4355),ee=t(1497),es=t(7576),et=t(4869),ea=t(3904);let el={youtube:$.A,facebook:Q.A,twitch:h.A,instagram:Z.A,twitter:ee.A,linkedin:es.A,custom:et.A},er={youtube:"text-red-600 bg-red-100",facebook:"text-blue-600 bg-blue-100",twitch:"text-purple-600 bg-purple-100",instagram:"text-pink-600 bg-pink-100",twitter:"text-blue-400 bg-blue-50",linkedin:"text-blue-700 bg-blue-100",custom:"text-gray-600 bg-gray-100"};function ei(e){let{isOpen:s,onClose:t,destinations:l,onUpdateDestinations:r}=e,[n,c]=(0,i.useState)(!1),[d,o]=(0,i.useState)("youtube"),[x,m]=(0,i.useState)(""),[h,u]=(0,i.useState)(""),[g,b]=(0,i.useState)(""),[y,N]=(0,i.useState)(!1),f=e=>({youtube:"rtmp://a.rtmp.youtube.com/live2",facebook:"rtmps://live-api-s.facebook.com:443/rtmp",twitch:"rtmp://live.twitch.tv/live",instagram:"rtmps://live-upload.instagram.com:443/rtmp",twitter:"rtmp://live.twitter.com/live",linkedin:"rtmp://live.linkedin.com/live"})[e]||"",v=e=>{r(l.map(s=>s.id===e?{...s,isEnabled:!s.isEnabled}:s))},w=e=>{r(l.filter(s=>s.id!==e))},k=e=>{r(l.map(s=>s.id===e?{...s,status:"connected"}:s))};return s?(0,a.jsxs)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:[(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Stream Destinations"}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(O.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white",children:"Connected Platforms"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Manage where your stream will be broadcast"})]}),(0,a.jsxs)("button",{onClick:()=>c(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Destination"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:l.map(e=>{let s=el[e.platform],t=er[e.platform];return(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(t),children:(0,a.jsx)(s,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-white",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("connected"===e.status?"bg-green-500":"streaming"===e.status?"bg-red-500 animate-pulse":"error"===e.status?"bg-red-500":"bg-gray-500")}),(0,a.jsx)("span",{className:"text-xs text-gray-400 capitalize",children:e.status}),"streaming"===e.status&&void 0!==e.viewerCount&&(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",e.viewerCount," viewers"]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>v(e.id),className:"p-2 rounded transition-colors ".concat(e.isEnabled?"bg-green-600 text-white":"bg-gray-600 text-gray-300"),children:e.isEnabled?(0,a.jsx)(q.A,{className:"w-4 h-4"}):(0,a.jsx)(O.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>k(e.id),className:"p-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:(0,a.jsx)(ea.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>w(e.id),className:"p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:(0,a.jsx)(p.A,{className:"w-4 h-4"})})]})]}),e.streamKey&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-gray-700 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Stream Key"}),(0,a.jsx)("button",{onClick:()=>N(!y),className:"text-gray-400 hover:text-white",children:y?(0,a.jsx)(z.A,{className:"w-3 h-3"}):(0,a.jsx)(T.A,{className:"w-3 h-3"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("code",{className:"text-xs text-gray-300 font-mono flex-1 truncate",children:y?e.streamKey:"••••••••••••••••"}),(0,a.jsx)("button",{onClick:()=>navigator.clipboard.writeText(e.streamKey||""),className:"p-1 text-gray-400 hover:text-white",children:(0,a.jsx)(A.A,{className:"w-3 h-3"})})]})]})]},e.id)})}),0===l.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(et.A,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No destinations configured"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Add streaming destinations to broadcast your content"}),(0,a.jsxs)("button",{onClick:()=>c(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add Your First Destination"})]})]})]})]}),n&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-60",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg w-full max-w-md p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Add Stream Destination"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Platform"}),(0,a.jsxs)("select",{value:d,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white",children:[(0,a.jsx)("option",{value:"youtube",children:"YouTube"}),(0,a.jsx)("option",{value:"facebook",children:"Facebook"}),(0,a.jsx)("option",{value:"twitch",children:"Twitch"}),(0,a.jsx)("option",{value:"instagram",children:"Instagram"}),(0,a.jsx)("option",{value:"twitter",children:"Twitter"}),(0,a.jsx)("option",{value:"linkedin",children:"LinkedIn"}),(0,a.jsx)("option",{value:"custom",children:"Custom RTMP"})]})]}),"custom"===d&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Name"}),(0,a.jsx)("input",{type:"text",value:g,onChange:e=>b(e.target.value),placeholder:"Custom destination name",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Stream Key"}),(0,a.jsx)("input",{type:"password",value:x,onChange:e=>m(e.target.value),placeholder:"Enter your stream key",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"})]}),"custom"===d&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"RTMP URL"}),(0,a.jsx)("input",{type:"text",value:h,onChange:e=>u(e.target.value),placeholder:"rtmp://your-server.com/live",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)("button",{onClick:()=>c(!1),className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{r([...l,{id:"dest-".concat(Date.now()),platform:d,name:"custom"===d?g:d.charAt(0).toUpperCase()+d.slice(1),status:"disconnected",streamKey:x,rtmpUrl:"custom"===d?h:f(d),isEnabled:!0}]),c(!1),m(""),u(""),b("")},disabled:!x||"custom"===d&&(!g||!h),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Add Destination"})]})]})]})})]}):null}var en=t(1243),ec=t(4449),ed=t(1539),eo=t(4186),ex=t(3109),em=t(8500),eh=t(2713),eu=t(9397),eg=t(5273);function eb(e){let{isOpen:s,onClose:t,streamId:l,isLive:r}=e,[n,c]=(0,i.useState)({status:"offline",bitrate:0,fps:0,resolution:"1920x1080",latency:0,droppedFrames:0,bandwidth:0,cpuUsage:0,memoryUsage:0}),[d,o]=(0,i.useState)({current:0,peak:0,total:0,trend:"stable",chatMessages:0,engagement:0}),[x,m]=(0,i.useState)(5);if((0,i.useEffect)(()=>{if(!r)return;let e=setInterval(()=>{c({status:Math.random()>.1?"excellent":"good",bitrate:2500+500*Math.random(),fps:30,resolution:"1920x1080",latency:50+100*Math.random(),droppedFrames:Math.floor(5*Math.random()),bandwidth:3e3+1e3*Math.random(),cpuUsage:30+40*Math.random(),memoryUsage:40+30*Math.random()}),o(e=>{let s=Math.floor(10*Math.random())-5,t=Math.max(0,e.current+s);return{current:t,peak:Math.max(e.peak,t),total:e.total+Math.max(0,s),trend:s>0?"up":s<0?"down":"stable",chatMessages:e.chatMessages+Math.floor(3*Math.random()),engagement:Math.min(100,Math.max(0,e.engagement+(10*Math.random()-5)))}})},1e3*x);return()=>clearInterval(e)},[r,x]),!s)return null;let h=(e=>{switch(e){case"excellent":case"good":return q.A;case"fair":return en.A;case"poor":return O.A;default:return ec.A}})(n.status);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg w-full max-w-6xl h-[80vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Stream Monitor"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 rounded-full ".concat((e=>{switch(e){case"excellent":return"text-green-500 bg-green-100";case"good":return"text-blue-500 bg-blue-100";case"fair":return"text-yellow-500 bg-yellow-100";case"poor":return"text-orange-500 bg-orange-100";default:return"text-gray-500 bg-gray-100"}})(n.status)),children:[(0,a.jsx)(h,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:n.status})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:x,onChange:e=>m(Number(e.target.value)),className:"px-3 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm",children:[(0,a.jsx)("option",{value:1,children:"1s"}),(0,a.jsx)("option",{value:5,children:"5s"}),(0,a.jsx)("option",{value:10,children:"10s"}),(0,a.jsx)("option",{value:30,children:"30s"})]}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(O.A,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Stream Health"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Bitrate"}),(0,a.jsx)(ed.A,{className:"w-4 h-4 text-blue-400"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-white",children:[n.bitrate.toFixed(0)," ",(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"kbps"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"FPS"}),(0,a.jsx)(Y.A,{className:"w-4 h-4 text-green-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:n.fps})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Latency"}),(0,a.jsx)(eo.A,{className:"w-4 h-4 text-yellow-400"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-white",children:[n.latency.toFixed(0)," ",(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"ms"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Dropped Frames"}),(0,a.jsx)(en.A,{className:"w-4 h-4 text-red-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:n.droppedFrames})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"System Resources"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"CPU Usage"}),(0,a.jsxs)("span",{className:"text-white",children:[n.cpuUsage.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(n.cpuUsage,"%")}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Memory Usage"}),(0,a.jsxs)("span",{className:"text-white",children:[n.memoryUsage.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(n.memoryUsage,"%")}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Bandwidth"}),(0,a.jsxs)("span",{className:"text-white",children:[(n.bandwidth/1e3).toFixed(1)," Mbps"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-purple-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(100,n.bandwidth/5e3*100),"%")}})})]})]})]})]})}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Viewer Analytics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Current Viewers"}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 text-blue-400"}),"up"===d.trend&&(0,a.jsx)(ex.A,{className:"w-3 h-3 text-green-400"}),"down"===d.trend&&(0,a.jsx)(em.A,{className:"w-3 h-3 text-red-400"})]})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:d.current})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Peak Viewers"}),(0,a.jsx)(eh.A,{className:"w-4 h-4 text-green-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:d.peak})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Total Views"}),(0,a.jsx)(eu.A,{className:"w-4 h-4 text-purple-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:d.total})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Chat Messages"}),(0,a.jsx)(eg.A,{className:"w-4 h-4 text-yellow-400"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:d.chatMessages})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"Engagement"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Engagement Rate"}),(0,a.jsxs)("span",{className:"text-white",children:[d.engagement.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(d.engagement,"%")}})}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-2",children:"Based on chat activity, viewer retention, and interaction"})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"Stream Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Resolution"}),(0,a.jsx)("span",{className:"text-white",children:n.resolution})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Duration"}),(0,a.jsx)("span",{className:"text-white",children:r?"Live":"Offline"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Status"}),(0,a.jsx)("span",{className:"capitalize ".concat("excellent"===n.status?"text-green-400":"good"===n.status?"text-blue-400":"fair"===n.status?"text-yellow-400":"poor"===n.status?"text-orange-400":"text-gray-400"),children:n.status})]})]})]})]})})]})})]})})}var ep=t(238),ej=t(9595),ey=t(1366),eN=t(381),ef=t(9963),ev=t(6877),ew=t(9424);function ek(e){let{streamId:s,currentUserId:t,isModerator:l,isVisible:r,onToggleVisibility:n}=e,[c,d]=(0,i.useState)([]),[o,x]=(0,i.useState)(""),[h,u]=(0,i.useState)(!1),[g,b]=(0,i.useState)(5),[j,y]=(0,i.useState)(!1),[N,f]=(0,i.useState)([]),[v,w]=(0,i.useState)(!1),[k,C]=(0,i.useState)("all"),A=(0,i.useRef)(null);(0,i.useEffect)(()=>{d([{id:"1",username:"StreamFan123",message:"Great stream! Love the content \uD83D\uDD25",timestamp:new Date(Date.now()-3e5),type:"message",reactions:[{emoji:"\uD83D\uDC4D",count:3},{emoji:"❤️",count:1}]},{id:"2",username:"System",message:"NewViewer just followed!",timestamp:new Date(Date.now()-24e4),type:"follow"},{id:"3",username:"GenerousViewer",message:"Keep up the amazing work!",timestamp:new Date(Date.now()-18e4),type:"donation",amount:5,isHighlighted:!0},{id:"4",username:"Moderator",message:"Welcome everyone to the stream!",timestamp:new Date(Date.now()-12e4),type:"message",isModerator:!0,isPinned:!0}])},[]);let S=()=>{var e;null==(e=A.current)||e.scrollIntoView({behavior:"smooth"})};(0,i.useEffect)(()=>{S()},[c]);let M=()=>{o.trim()&&(d([...c,{id:Date.now().toString(),username:"You",message:o,timestamp:new Date,type:"message",isOwner:!0}]),x(""))},F=e=>{d(c.filter(s=>s.id!==e))},P=e=>{d(c.map(s=>s.id===e?{...s,isPinned:!s.isPinned}:s))},L=e=>{console.log("Banning user:",e)},D=c.filter(e=>"all"===k||("donations"===k?"donation"===e.type:"follows"===k?"follow"===e.type||"subscription"===e.type:"messages"!==k||"message"===e.type)),E=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),I=e=>{switch(e){case"donation":return(0,a.jsx)(ep.A,{className:"w-4 h-4 text-yellow-500"});case"follow":return(0,a.jsx)(ej.A,{className:"w-4 h-4 text-red-500"});case"subscription":return(0,a.jsx)(m.A,{className:"w-4 h-4 text-purple-500"});default:return null}};return(0,a.jsxs)("div",{className:"h-full flex flex-col bg-gray-900",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(ey.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Stream Chat"}),(0,a.jsxs)("span",{className:"text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded",children:[c.length," messages"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:k,onChange:e=>C(e.target.value),className:"text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white",children:[(0,a.jsx)("option",{value:"all",children:"All"}),(0,a.jsx)("option",{value:"messages",children:"Messages"}),(0,a.jsx)("option",{value:"donations",children:"Donations"}),(0,a.jsx)("option",{value:"follows",children:"Follows"})]}),l&&(0,a.jsx)("button",{onClick:()=>w(!v),className:"p-1 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(eN.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:n,className:"p-1 text-gray-400 hover:text-white transition-colors",children:r?(0,a.jsx)(T.A,{className:"w-4 h-4"}):(0,a.jsx)(z.A,{className:"w-4 h-4"})})]})]}),v&&l&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-gray-800 rounded-lg space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"Slow Mode"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:h,onChange:e=>u(e.target.checked),className:"rounded"}),h&&(0,a.jsxs)("select",{value:g,onChange:e=>b(Number(e.target.value)),className:"text-xs bg-gray-700 border border-gray-600 rounded px-1 py-1 text-white",children:[(0,a.jsx)("option",{value:5,children:"5s"}),(0,a.jsx)("option",{value:10,children:"10s"}),(0,a.jsx)("option",{value:30,children:"30s"}),(0,a.jsx)("option",{value:60,children:"1m"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"Followers Only"}),(0,a.jsx)("input",{type:"checkbox",checked:j,onChange:e=>y(e.target.checked),className:"rounded"})]})]})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:[D.map(e=>(0,a.jsxs)("div",{className:"group relative ".concat(e.isHighlighted?"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-2":""," ").concat(e.isPinned?"bg-blue-500/10 border border-blue-500/30 rounded-lg p-2":""),children:[e.isPinned&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-blue-400 mb-1",children:[(0,a.jsx)(ef.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Pinned Message"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:I(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium ".concat(e.isOwner?"text-green-400":e.isModerator?"text-blue-400":"text-gray-300"),children:e.username}),e.isModerator&&(0,a.jsx)(K.A,{className:"w-3 h-3 text-blue-400"}),"donation"===e.type&&e.amount&&(0,a.jsxs)("span",{className:"text-xs bg-yellow-500 text-black px-2 py-1 rounded font-medium",children:["$",e.amount.toFixed(2)]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:E(e.timestamp)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-200 break-words",children:e.message}),e.reactions&&e.reactions.length>0&&(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-2",children:e.reactions.map((e,s)=>(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-xs bg-gray-700 hover:bg-gray-600 rounded-full px-2 py-1 transition-colors",children:[(0,a.jsx)("span",{children:e.emoji}),(0,a.jsx)("span",{className:"text-gray-300",children:e.count})]},s))})]}),l&&(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{onClick:()=>P(e.id),className:"p-1 text-gray-400 hover:text-blue-400 transition-colors",children:(0,a.jsx)(ef.A,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:()=>F(e.id),className:"p-1 text-gray-400 hover:text-red-400 transition-colors",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})}),(0,a.jsx)("button",{onClick:()=>L(e.username),className:"p-1 text-gray-400 hover:text-red-400 transition-colors",children:(0,a.jsx)(ev.A,{className:"w-3 h-3"})})]})})]})]},e.id)),(0,a.jsx)("div",{ref:A})]}),(0,a.jsxs)("div",{className:"p-4 border-t border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)("input",{type:"text",value:o,onChange:e=>x(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),M())},placeholder:"Type a message...",className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none",maxLength:500}),(0,a.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1",children:(0,a.jsx)("button",{className:"p-1 text-gray-400 hover:text-yellow-400 transition-colors",children:(0,a.jsx)(ew.A,{className:"w-4 h-4"})})})]}),(0,a.jsx)("button",{onClick:M,disabled:!o.trim(),className:"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:(0,a.jsx)(G.A,{className:"w-4 h-4"})})]}),h&&(0,a.jsxs)("p",{className:"text-xs text-yellow-400 mt-2",children:["Slow mode is enabled (",g,"s between messages)"]})]})]})}var eC=t(5339),eA=t(1154),eS=t(7162),eM=t(9428),eF=t(5623),eP=t(3273),eL=t(6515),eD=t(4267);function eE(){let e=(0,eP.f)(),{localParticipant:s}=(0,eL.C)(),[t,a]=(0,i.useState)({isMuted:!1,isVideoOff:!1,isScreenSharing:!1,isRecording:!1,isLoading:{audio:!1,video:!1,screenShare:!1,recording:!1},error:null}),l=(0,eL.t)([{source:eD.CC.Source.Camera,withPlaceholder:!1},{source:eD.CC.Source.Microphone,withPlaceholder:!1},{source:eD.CC.Source.ScreenShare,withPlaceholder:!1}]);(0,i.useEffect)(()=>{if(!s)return;let e=s.getTrackPublication(eD.CC.Source.Microphone),t=s.getTrackPublication(eD.CC.Source.Camera),l=s.getTrackPublication(eD.CC.Source.ScreenShare);a(s=>({...s,isMuted:!e||e.isMuted,isVideoOff:!t||t.isMuted,isScreenSharing:!!l&&!l.isMuted}))},[s,l]),(0,i.useEffect)(()=>{if(!e)return;let s=e=>{a(s=>({...s,isRecording:e,isLoading:{...s.isLoading,recording:!1}}))};return e.on(eD.u9.RecordingStatusChanged,s),()=>{e.off(eD.u9.RecordingStatusChanged,s)}},[e]);let r=(0,i.useCallback)((e,s)=>{a(t=>({...t,isLoading:{...t.isLoading,[e]:s}}))},[]),n=(0,i.useCallback)(e=>{a(s=>({...s,error:e}))},[]),c=(0,i.useCallback)(()=>{n(null)},[n]),d=(0,i.useCallback)(async()=>{if(!s)return void n("Not connected to room");try{r("audio",!0),n(null);let e=s.getTrackPublication(eD.CC.Source.Microphone);e?await s.setMicrophoneEnabled(e.isMuted):await s.setMicrophoneEnabled(!0)}catch(e){console.error("Failed to toggle microphone:",e),n("Failed to toggle microphone")}finally{r("audio",!1)}},[s,r,n]),o=(0,i.useCallback)(async()=>{if(!s)return void n("Not connected to room");try{r("video",!0),n(null);let e=s.getTrackPublication(eD.CC.Source.Camera);e?await s.setCameraEnabled(e.isMuted):await s.setCameraEnabled(!0)}catch(e){console.error("Failed to toggle camera:",e),n("Failed to toggle camera")}finally{r("video",!1)}},[s,r,n]),x=(0,i.useCallback)(async()=>{if(!s)return void n("Not connected to room");try{r("screenShare",!0),n(null);let e=s.getTrackPublication(eD.CC.Source.ScreenShare);e&&!e.isMuted?await s.setScreenShareEnabled(!1):await s.setScreenShareEnabled(!0)}catch(e){console.error("Failed to toggle screen share:",e),n("Failed to toggle screen share")}finally{r("screenShare",!1)}},[s,r,n]),m=(0,i.useCallback)(async()=>{if(!e)return void n("Not connected to room");try{r("recording",!0),n(null),a(e=>({...e,isRecording:!e.isRecording})),console.log(t.isRecording?"Stop recording":"Start recording")}catch(e){console.error("Failed to toggle recording:",e),n("Failed to toggle recording")}finally{r("recording",!1)}},[e,t.isRecording,r,n]);return{...t,toggleMute:d,toggleVideo:o,toggleScreenShare:x,toggleRecording:m,clearError:c}}function eI(e){let{onToggleChat:s,onToggleParticipants:t,onShowMore:l,className:r=""}=e,{isMuted:i,isVideoOff:n,isScreenSharing:c,isRecording:d,isLoading:o,error:m,toggleMute:h,toggleVideo:u,toggleScreenShare:g,toggleRecording:b,clearError:p}=eE(),j=async()=>{await h()},y=async()=>{await u()},N=async()=>{await g()},v=async()=>{await b()};return(0,a.jsxs)("div",{className:"bg-gray-800 border-t border-gray-700 p-2 md:p-4 ".concat(r),children:[m&&(0,a.jsxs)("div",{className:"mb-3 p-3 bg-red-900/50 border border-red-500/50 rounded-lg flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eC.A,{className:"w-4 h-4 text-red-400"}),(0,a.jsx)("span",{className:"text-sm text-red-200",children:m})]}),(0,a.jsx)("button",{onClick:p,className:"p-1 text-red-400 hover:text-red-300 transition-colors",children:(0,a.jsx)(f.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,a.jsx)("button",{onClick:j,disabled:o.audio,className:"relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ".concat(i?"bg-red-600 text-white hover:bg-red-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"),title:i?"Unmute microphone":"Mute microphone",children:o.audio?(0,a.jsx)(eA.A,{className:"w-4 h-4 md:w-5 md:h-5 animate-spin"}):i?(0,a.jsx)(_.A,{className:"w-4 h-4 md:w-5 md:h-5"}):(0,a.jsx)(H.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:y,disabled:o.video,className:"relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ".concat(n?"bg-red-600 text-white hover:bg-red-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"),title:n?"Turn on camera":"Turn off camera",children:o.video?(0,a.jsx)(eA.A,{className:"w-4 h-4 md:w-5 md:h-5 animate-spin"}):n?(0,a.jsx)(W.A,{className:"w-4 h-4 md:w-5 md:h-5"}):(0,a.jsx)(Y.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:N,disabled:o.screenShare,className:"relative p-2 md:p-3 rounded-lg transition-colors disabled:opacity-50 ".concat(c?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"),title:c?"Stop screen sharing":"Start screen sharing",children:o.screenShare?(0,a.jsx)(eA.A,{className:"w-4 h-4 md:w-5 md:h-5 animate-spin"}):(0,a.jsx)(eS.A,{className:"w-4 h-4 md:w-5 md:h-5"})})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 md:space-x-3",children:(0,a.jsxs)("button",{onClick:v,disabled:o.recording,className:"flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 rounded-lg transition-colors text-sm disabled:opacity-50 ".concat(d?"bg-red-600 text-white hover:bg-red-700":"bg-gray-700 text-gray-300 hover:bg-gray-600"),title:d?"Stop recording":"Start recording",children:[o.recording?(0,a.jsx)(eA.A,{className:"w-4 h-4 animate-spin"}):(0,a.jsx)(eM.A,{className:"w-4 h-4 ".concat(d?"fill-current":"")}),(0,a.jsx)("span",{className:"hidden sm:inline",children:d?"Stop Recording":"Record"}),(0,a.jsx)("span",{className:"sm:hidden",children:d?"Stop":"Rec"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,a.jsx)("button",{onClick:s,className:"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors",title:"Toggle chat",children:(0,a.jsx)(ey.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:t,className:"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors",title:"Manage participants",children:(0,a.jsx)(x.A,{className:"w-4 h-4 md:w-5 md:h-5"})}),(0,a.jsx)("button",{onClick:l,className:"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors",title:"More options",children:(0,a.jsx)(eF.A,{className:"w-4 h-4 md:w-5 md:h-5"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center mt-2 space-x-4 text-xs text-gray-400",children:[d&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{children:"Recording"})]}),c&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{children:"Screen sharing"})]}),i&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("span",{children:"Muted"})]}),n&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("span",{children:"Camera off"})]})]})]})}var eR=t(8530),eT=t(8293);function ez(e){let{token:s,room:t,children:l}=e;return(0,a.jsxs)(eR.L,{video:!0,audio:!0,token:s,serverUrl:"wss://streamyard-clonez-1zofz2li.livekit.cloud","data-lk-theme":"default",onConnected:()=>console.log("Connected to room:",t),onDisconnected:()=>console.log("Disconnected from room:",t),onError:e=>console.error("LiveKit error:",e),children:[l,(0,a.jsx)(eT.R,{})]})}function eU(e){let{onShowBranding:s}=e,t=(0,eL.t)([{source:eD.CC.Source.Camera,withPlaceholder:!0},{source:eD.CC.Source.ScreenShare,withPlaceholder:!1}],{onlySubscribed:!1}),{isRecording:l}=eE();return(0,a.jsxs)("div",{className:"w-full h-full bg-black rounded-lg overflow-hidden relative",children:[(0,a.jsx)(eT.G,{tracks:t,style:{height:"100%"},children:(0,a.jsx)(eT.P,{})}),(0,a.jsxs)("div",{className:"absolute top-4 left-4 flex space-x-2",children:[(0,a.jsx)("button",{onClick:s,className:"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors",title:"Branding & Graphics",children:(0,a.jsx)(P.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:s,className:"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors",title:"Add Logo",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:s,className:"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors",title:"Add Text",children:(0,a.jsx)(F.A,{className:"w-4 h-4"})})]}),l&&(0,a.jsxs)("div",{className:"absolute top-4 right-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-lg",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"REC"})]})]})}t(1392);var eO=t(8979),eB=t(4311);function eG(e){let{streamId:s,currentUserId:t}=e,l=(0,n.IT)(c.F.streams.get,{streamId:s}),r=(0,n.IT)(c.F.users.getCurrentUser),o=(0,n.n_)(c.F.streams.startStream),m=(0,n.n_)(c.F.streams.endStream),h=(0,n.y3)(c.F.livekit.createToken),[g,b]=(0,i.useState)("none"),[p,j]=(0,i.useState)("single"),[y,N]=(0,i.useState)(!1),[f,v]=(0,i.useState)(!1),[w,C]=(0,i.useState)(!1),[A,M]=(0,i.useState)(!1),[F,P]=(0,i.useState)(!1),[L,D]=(0,i.useState)([]),[E,I]=(0,i.useState)([]),[R,z]=(0,i.useState)([]),[O,G]=(0,i.useState)([]),[V,K]=(0,i.useState)(!0),[_,H]=(0,i.useState)(null);i.useEffect(()=>{let e=async()=>{try{let e=await h({roomName:s,participantName:(null==r?void 0:r.username)||"Anonymous"});H(e)}catch(e){console.error("Failed to generate token:",e)}};r&&e()},[h,s,r]);let W=async()=>{try{await o({streamId:s})}catch(e){console.error("Failed to start stream:",e)}},Y=async()=>{try{await m({streamId:s})}catch(e){console.error("Failed to end stream:",e)}};if(!l||!r)return(0,a.jsx)("div",{className:"flex items-center justify-center h-screen bg-gray-900",children:(0,a.jsx)("div",{className:"text-white text-lg",children:"Loading studio..."})});let q="master"===r.globalRole||"admin"===r.globalRole||l.hostId===t;return _?(0,a.jsx)(ez,{token:_,room:s,children:(0,a.jsxs)("div",{className:"h-screen flex flex-col bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-gray-800 border-b border-gray-700 px-4 md:px-6 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4 min-w-0 flex-1",children:[(0,a.jsx)("h1",{className:"text-lg md:text-xl font-semibold text-white truncate",children:l.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(l.isLive?"bg-red-500 animate-pulse":"bg-gray-500")}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:l.isLive?"LIVE":"OFFLINE"}),void 0!==l.participantCount&&(0,a.jsxs)("span",{className:"text-sm text-gray-400 hidden sm:flex items-center ml-4",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-1"}),l.participantCount]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[(0,a.jsxs)("button",{onClick:()=>P(!0),className:"flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors",children:[(0,a.jsx)(T.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"0 viewers"})]}),(0,a.jsxs)("button",{onClick:()=>M(!0),className:"flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden md:inline",children:"Destinations"})]})]}),q&&(0,a.jsx)("div",{className:"flex space-x-2",children:l.isLive?(0,a.jsxs)("button",{onClick:Y,className:"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,a.jsx)(eO.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"End Stream"})]}):(0,a.jsxs)("button",{onClick:W,className:"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[(0,a.jsx)($.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Go Live"})]})}),(0,a.jsx)("button",{onClick:()=>b("settings"===g?"none":"settings"),className:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:(0,a.jsx)(eN.A,{className:"w-5 h-5"})})]})]})}),(0,a.jsxs)("div",{className:"flex-1 flex relative",children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"h-full bg-black rounded-lg relative overflow-hidden border border-gray-700",children:(0,a.jsx)(eU,{onShowBranding:()=>v(!0)})})}),(0,a.jsx)("div",{className:"bg-gray-800 border-t border-gray-700 p-2 md:p-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0",children:[(0,a.jsx)("div",{className:"flex-1 overflow-x-auto",children:(0,a.jsx)(k,{selectedLayout:p,onLayoutChange:e=>{j(e),console.log("Layout changed to:",e)},onCreateCustomLayout:()=>{N(!0)},customLayouts:L})}),(0,a.jsx)("div",{className:"flex items-center justify-center md:justify-start space-x-2 md:ml-4",children:(0,a.jsxs)("button",{onClick:()=>C(!0),className:"flex items-center space-x-2 px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors text-sm",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Invite Guests"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Invite"})]})})]})})]}),"none"!==g&&(0,a.jsxs)("div",{className:"w-full md:w-80 bg-gray-800 border-l border-gray-700 flex flex-col absolute md:relative inset-0 md:inset-auto z-10 md:z-auto",children:[(0,a.jsx)("div",{className:"p-4 border-b border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["chat"===g&&"Chat","participants"===g&&"Participants","settings"===g&&"Settings"]}),(0,a.jsx)("button",{onClick:()=>b("none"),className:"p-1 text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(eB.A,{className:"w-4 h-4"})})]})}),(0,a.jsxs)("div",{className:"flex-1 p-4 overflow-y-auto",children:["chat"===g&&(0,a.jsx)("div",{className:"h-full -m-4",children:(0,a.jsx)(ek,{streamId:s,currentUserId:t,isModerator:q,isVisible:V,onToggleVisibility:()=>K(!V)})}),"participants"===g&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Invite Guests"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"No participants yet"})]}),"settings"===g&&q&&(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(d,{streamId:s,currentUserId:t})})]})]})]}),(0,a.jsx)(eI,{onToggleChat:()=>b("chat"===g?"none":"chat"),onToggleParticipants:()=>b("participants"===g?"none":"participants"),onShowMore:()=>{}}),(0,a.jsx)(S,{isOpen:y,onClose:()=>N(!1),onSave:e=>{D([...L,e]),j(e.id)}}),(0,a.jsx)(U,{isOpen:f,onClose:()=>v(!1),elements:E,onUpdateElements:I}),(0,a.jsx)(X,{isOpen:w,onClose:()=>C(!1),guests:R,onUpdateGuests:z,streamId:s}),(0,a.jsx)(ei,{isOpen:A,onClose:()=>M(!1),destinations:O,onUpdateDestinations:G}),(0,a.jsx)(eb,{isOpen:F,onClose:()=>P(!1),streamId:s,isLive:l.isLive||!1})]})}):(0,a.jsx)("div",{className:"flex items-center justify-center h-screen bg-gray-900",children:(0,a.jsx)("div",{className:"text-white text-lg",children:"Connecting to LiveKit..."})})}function eV(){let e=(0,l.useParams)().streamId,{user:s}=(0,r.Jd)();return s?(0,a.jsx)(eG,{streamId:e,currentUserId:s.id}):(0,a.jsx)("div",{children:"Please sign in to access the stream."})}},8649:(e,s,t)=>{Promise.resolve().then(t.bind(t,7052))}},e=>{var s=s=>e(e.s=s);e.O(0,[886,501,90,761,253,955,505,441,684,358],()=>s(8649)),_N_E=e.O()}]);