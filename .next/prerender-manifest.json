{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "f64b623bc0b6a7af838d685ae0338400", "previewModeSigningKey": "6601960a1d8ae775133d393b9a01e3cf8ef01b7cd20f5a9c024af464678dc11e", "previewModeEncryptionKey": "ec1017078e2e025822ef0a30829890d0eeddafa829d0551bb93eda7c6dddd9c6"}}