{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cross-Origin-Embedder-Policy", "value": "credentialless"}, {"key": "Cross-Origin-Opener-Policy", "value": "same-origin"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/rooms/[roomName]", "regex": "^/rooms/([^/]+?)(?:/)?$", "routeKeys": {"nxtProomName": "nxtProomName"}, "namedRegex": "^/rooms/(?<nxtProomName>[^/]+?)(?:/)?$"}, {"page": "/sign-in/[[...sign-in]]", "regex": "^/sign\\-in(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignin": "nxtPsign-in"}, "namedRegex": "^/sign\\-in(?:/(?<nxtPsignin>.+?))?(?:/)?$"}, {"page": "/sign-up/[[...sign-up]]", "regex": "^/sign\\-up(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignup": "nxtPsign-up"}, "namedRegex": "^/sign\\-up(?:/(?<nxtPsignup>.+?))?(?:/)?$"}, {"page": "/stream/[streamId]", "regex": "^/stream/([^/]+?)(?:/)?$", "routeKeys": {"nxtPstreamId": "nxtPstreamId"}, "namedRegex": "^/stream/(?<nxtPstreamId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}