"use client";

import React, { useState, useEffect } from 'react';
import {
  LiveKitRoom,
  GridLayout,
  ParticipantTile,
  ControlBar,
  <PERSON><PERSON>,
  RoomAudioRenderer,
  useParticipants,
  useTracks,
  useLocalParticipant,
  useRoomContext,
} from '@livekit/components-react';
import { Track, RoomEvent } from 'livekit-client';
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  PhoneOff,
  MessageSquare,
  Users,
  Monitor,
  MoreVertical,
  Settings,
  Hand,
  Shield,
  Clock,
  Copy,
  Share2
} from 'lucide-react';
import { useLiveKitControls } from '../../hooks/useLiveKitControls';

interface VideoConferenceProps {
  roomName: string;
  token: string;
  userName: string;
}

interface MeetingInfo {
  title: string;
  startTime: Date;
  duration: string;
}

function MeetingRoom() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isParticipantsOpen, setIsParticipantsOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [layout, setLayout] = useState<'grid' | 'speaker' | 'presentation'>('grid');
  const [meetingInfo, setMeetingInfo] = useState<MeetingInfo>({
    title: 'Video Meeting',
    startTime: new Date(),
    duration: '00:00'
  });

  const participants = useParticipants();
  const { localParticipant } = useLocalParticipant();
  const room = useRoomContext();
  const {
    isMuted,
    isVideoOff,
    isScreenSharing,
    isRecording,
    isLoading,
    error,
    toggleMute,
    toggleVideo,
    toggleScreenShare,
    toggleRecording,
    clearError
  } = useLiveKitControls();

  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  // Calculate meeting duration
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const diff = now.getTime() - meetingInfo.startTime.getTime();
      const minutes = Math.floor(diff / 60000);
      const seconds = Math.floor((diff % 60000) / 1000);
      setMeetingInfo(prev => ({
        ...prev,
        duration: `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [meetingInfo.startTime]);

  // Get current user role (simplified - in real app would come from database)
  const isHost = localParticipant?.identity === room?.localParticipant?.identity;
  const canModerate = isHost; // Simplified for now

  const copyMeetingLink = () => {
    navigator.clipboard.writeText(window.location.href);
    // TODO: Show toast notification
  };

  const leaveMeeting = () => {
    if (room) {
      room.disconnect();
    }
  };

  return (
    <div className="h-screen bg-gray-900 flex flex-col">
      {/* Professional Meeting Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Meeting Info */}
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">{meetingInfo.title}</h1>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{meetingInfo.duration}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4" />
                  <span>{participants.length} participants</span>
                </div>
                {isRecording && (
                  <div className="flex items-center space-x-1 text-red-600">
                    <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse" />
                    <span>Recording</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-2">
            {/* Layout Selector */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setLayout('grid')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  layout === 'grid' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Grid
              </button>
              <button
                onClick={() => setLayout('speaker')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  layout === 'speaker' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Speaker
              </button>
            </div>

            {/* Meeting Actions */}
            <button
              onClick={copyMeetingLink}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="Copy meeting link"
            >
              <Share2 className="w-5 h-5" />
            </button>

            <button
              onClick={() => setIsParticipantsOpen(!isParticipantsOpen)}
              className={`p-2 rounded-lg transition-colors ${
                isParticipantsOpen ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title="Participants"
            >
              <Users className="w-5 h-5" />
            </button>

            <button
              onClick={() => setIsChatOpen(!isChatOpen)}
              className={`p-2 rounded-lg transition-colors ${
                isChatOpen ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title="Chat"
            >
              <MessageSquare className="w-5 h-5" />
            </button>

            <button
              onClick={() => setIsSettingsOpen(!isSettingsOpen)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="Settings"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex relative overflow-hidden">
        {/* Video Area */}
        <div className="flex-1 bg-gray-900 relative">
          {/* Video Layout */}
          <div className="h-full p-4">
            {layout === 'grid' ? (
              <GridLayout tracks={tracks} style={{ height: '100%' }}>
                <ParticipantTile />
              </GridLayout>
            ) : layout === 'speaker' ? (
              <SpeakerLayout tracks={tracks} />
            ) : (
              <PresentationLayout tracks={tracks} />
            )}
          </div>

          {/* Floating Controls Overlay */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
            <div className="bg-white rounded-full shadow-lg px-4 py-3 flex items-center space-x-3">
              {/* Microphone */}
              <button
                onClick={toggleMute}
                disabled={isLoading.audio}
                className={`p-3 rounded-full transition-colors ${
                  isMuted
                    ? 'bg-red-500 text-white hover:bg-red-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                title={isMuted ? 'Unmute' : 'Mute'}
              >
                {isMuted ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              </button>

              {/* Camera */}
              <button
                onClick={toggleVideo}
                disabled={isLoading.video}
                className={`p-3 rounded-full transition-colors ${
                  isVideoOff
                    ? 'bg-red-500 text-white hover:bg-red-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
              >
                {isVideoOff ? <VideoOff className="w-5 h-5" /> : <Video className="w-5 h-5" />}
              </button>

              {/* Screen Share */}
              <button
                onClick={toggleScreenShare}
                disabled={isLoading.screenShare}
                className={`p-3 rounded-full transition-colors ${
                  isScreenSharing
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                title={isScreenSharing ? 'Stop sharing' : 'Share screen'}
              >
                <Monitor className="w-5 h-5" />
              </button>

              {/* Recording (Host only) */}
              {canModerate && (
                <button
                  onClick={toggleRecording}
                  disabled={isLoading.recording}
                  className={`p-3 rounded-full transition-colors ${
                    isRecording
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  title={isRecording ? 'Stop recording' : 'Start recording'}
                >
                  <div className={`w-5 h-5 rounded ${isRecording ? 'bg-white' : 'bg-red-500'}`} />
                </button>
              )}

              {/* Leave Meeting */}
              <button
                onClick={leaveMeeting}
                className="p-3 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                title="Leave meeting"
              >
                <PhoneOff className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="absolute top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm">{error}</span>
                <button onClick={clearError} className="ml-2 text-red-500 hover:text-red-700">
                  ×
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Side Panel */}
        {(isChatOpen || isParticipantsOpen) && (
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
            {/* Panel Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex space-x-4">
                <button
                  onClick={() => {
                    setIsParticipantsOpen(true);
                    setIsChatOpen(false);
                  }}
                  className={`pb-2 border-b-2 transition-colors ${
                    isParticipantsOpen
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Participants ({participants.length})
                </button>
                <button
                  onClick={() => {
                    setIsChatOpen(true);
                    setIsParticipantsOpen(false);
                  }}
                  className={`pb-2 border-b-2 transition-colors ${
                    isChatOpen
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Chat
                </button>
              </div>
            </div>

            {/* Sidebar Content */}
            <div className="flex-1 overflow-hidden">
              {isParticipantsOpen && <ParticipantsList participants={participants} canModerate={canModerate} />}
              {isChatOpen && (
                <div className="h-full">
                  <Chat style={{ height: '100%' }} />
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="bg-gray-800 p-4">
        <div className="flex items-center justify-center">
          <ControlBar variation="minimal" />
        </div>
      </div>

      {/* Audio Renderer */}
      <RoomAudioRenderer />
    </div>
  );
}

export function VideoConference({ roomName, token, userName }: VideoConferenceProps) {
  const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://streamyard-clonez-1zofz2li.livekit.cloud';
  
  console.log('VideoConference connecting:', {
    serverUrl,
    roomName,
    userName,
    hasToken: !!token
  });

  return (
    <LiveKitRoom
      video={true}
      audio={true}
      token={token}
      serverUrl={serverUrl}
      data-lk-theme="default"
      style={{ height: '100vh' }}
    >
      <MeetingRoom />
    </LiveKitRoom>
  );
}