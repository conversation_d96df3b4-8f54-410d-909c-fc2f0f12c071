"use client";

import React from 'react';
import { ParticipantTile, useTracks, useParticipants } from '@livekit/components-react';
import { Track, TrackPublication } from 'livekit-client';
import { Mic, MicOff, Video, VideoOff, Monitor } from 'lucide-react';

interface VideoLayoutProps {
  tracks: TrackPublication[];
}

export function SpeakerLayout({ tracks }: VideoLayoutProps) {
  const participants = useParticipants();
  
  // Find the active speaker or screen share
  const screenShareTrack = tracks.find(track => track.source === Track.Source.ScreenShare);
  const activeSpeaker = participants.find(p => p.isSpeaking) || participants[0];
  const otherParticipants = participants.filter(p => p !== activeSpeaker);

  const mainTrack = screenShareTrack || tracks.find(track => 
    track.participant === activeSpeaker && track.source === Track.Source.Camera
  );

  return (
    <div className="h-full flex flex-col">
      {/* Main Speaker/Screen Share Area */}
      <div className="flex-1 p-2">
        <div className="h-full bg-black rounded-lg relative overflow-hidden">
          {mainTrack ? (
            <ParticipantTile
              participant={mainTrack.participant}
              source={mainTrack.source}
              className="w-full h-full"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center text-gray-400">
                <Video className="w-16 h-16 mx-auto mb-4" />
                <p className="text-lg">No video available</p>
              </div>
            </div>
          )}
          
          {/* Main Speaker Info */}
          {activeSpeaker && (
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-60 text-white px-3 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                {screenShareTrack ? (
                  <>
                    <Monitor className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {screenShareTrack.participant.name || screenShareTrack.participant.identity} is sharing
                    </span>
                  </>
                ) : (
                  <>
                    <div className="flex items-center space-x-1">
                      {activeSpeaker.isMicrophoneEnabled ? (
                        <Mic className="w-4 h-4 text-green-400" />
                      ) : (
                        <MicOff className="w-4 h-4 text-red-400" />
                      )}
                    </div>
                    <span className="text-sm font-medium">
                      {activeSpeaker.name || activeSpeaker.identity}
                    </span>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Thumbnail Strip */}
      {otherParticipants.length > 0 && (
        <div className="h-24 p-2">
          <div className="flex space-x-2 h-full overflow-x-auto">
            {otherParticipants.map((participant) => (
              <div key={participant.identity} className="flex-shrink-0 w-32 h-full">
                <div className="w-full h-full bg-black rounded-lg relative overflow-hidden">
                  <ParticipantTile
                    participant={participant}
                    source={Track.Source.Camera}
                    className="w-full h-full"
                  />
                  
                  {/* Thumbnail Info */}
                  <div className="absolute bottom-1 left-1 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs">
                    <div className="flex items-center space-x-1">
                      {participant.isMicrophoneEnabled ? (
                        <Mic className="w-3 h-3 text-green-400" />
                      ) : (
                        <MicOff className="w-3 h-3 text-red-400" />
                      )}
                      <span className="truncate max-w-16">
                        {participant.name || participant.identity}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export function PresentationLayout({ tracks }: VideoLayoutProps) {
  const participants = useParticipants();
  const screenShareTrack = tracks.find(track => track.source === Track.Source.ScreenShare);
  
  if (!screenShareTrack) {
    // Fallback to speaker layout if no screen share
    return <SpeakerLayout tracks={tracks} />;
  }

  const presenter = screenShareTrack.participant;
  const otherParticipants = participants.filter(p => p !== presenter);

  return (
    <div className="h-full flex">
      {/* Main Screen Share Area */}
      <div className="flex-1 p-2">
        <div className="h-full bg-black rounded-lg relative overflow-hidden">
          <ParticipantTile
            participant={presenter}
            source={Track.Source.ScreenShare}
            className="w-full h-full"
          />
          
          {/* Presenter Info */}
          <div className="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-2 rounded-lg">
            <div className="flex items-center space-x-2">
              <Monitor className="w-4 h-4" />
              <span className="text-sm font-medium">
                {presenter.name || presenter.identity} is presenting
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Participants Sidebar */}
      <div className="w-64 p-2">
        <div className="h-full flex flex-col space-y-2">
          {/* Presenter Video */}
          <div className="h-36 bg-black rounded-lg relative overflow-hidden">
            <ParticipantTile
              participant={presenter}
              source={Track.Source.Camera}
              className="w-full h-full"
            />
            
            <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs">
              <div className="flex items-center space-x-1">
                {presenter.isMicrophoneEnabled ? (
                  <Mic className="w-3 h-3 text-green-400" />
                ) : (
                  <MicOff className="w-3 h-3 text-red-400" />
                )}
                <span>{presenter.name || presenter.identity} (Presenter)</span>
              </div>
            </div>
          </div>

          {/* Other Participants */}
          <div className="flex-1 overflow-y-auto space-y-2">
            {otherParticipants.map((participant) => (
              <div key={participant.identity} className="h-24 bg-black rounded-lg relative overflow-hidden">
                <ParticipantTile
                  participant={participant}
                  source={Track.Source.Camera}
                  className="w-full h-full"
                />
                
                <div className="absolute bottom-1 left-1 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs">
                  <div className="flex items-center space-x-1">
                    {participant.isMicrophoneEnabled ? (
                      <Mic className="w-3 h-3 text-green-400" />
                    ) : (
                      <MicOff className="w-3 h-3 text-red-400" />
                    )}
                    <span className="truncate">
                      {participant.name || participant.identity}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Enhanced Grid Layout with better responsive design
export function EnhancedGridLayout({ tracks }: VideoLayoutProps) {
  const participants = useParticipants();
  
  const getGridClass = () => {
    const count = participants.length;
    if (count <= 1) return 'grid-cols-1';
    if (count <= 4) return 'grid-cols-2';
    if (count <= 9) return 'grid-cols-3';
    if (count <= 16) return 'grid-cols-4';
    return 'grid-cols-5';
  };

  return (
    <div className={`h-full p-4 grid gap-4 ${getGridClass()}`}>
      {participants.map((participant) => (
        <div key={participant.identity} className="bg-black rounded-lg relative overflow-hidden">
          <ParticipantTile
            participant={participant}
            source={Track.Source.Camera}
            className="w-full h-full"
          />
          
          {/* Participant Info Overlay */}
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-sm">
            <div className="flex items-center space-x-1">
              {participant.isMicrophoneEnabled ? (
                <Mic className="w-3 h-3 text-green-400" />
              ) : (
                <MicOff className="w-3 h-3 text-red-400" />
              )}
              <span className="truncate max-w-24">
                {participant.name || participant.identity}
                {participant.isLocal && ' (You)'}
              </span>
            </div>
          </div>
          
          {/* Video Status */}
          {!participant.isCameraEnabled && (
            <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
              <div className="text-center text-gray-400">
                <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-xl font-semibold text-white">
                    {(participant.name || participant.identity)[0]?.toUpperCase() || 'A'}
                  </span>
                </div>
                <p className="text-sm">{participant.name || participant.identity}</p>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
