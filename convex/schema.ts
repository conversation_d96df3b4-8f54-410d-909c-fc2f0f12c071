import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  rooms: defineTable({
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    isActive: v.boolean(),
    maxParticipants: v.optional(v.number()),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.boolean(),
      videoOnEntry: v.boolean(),
      recordingEnabled: v.boolean(),
      waitingRoom: v.boolean(),
      moderatorApproval: v.boolean(),
      handRaiseEnabled: v.boolean(),
    })),
    layout: v.optional(v.union(
      v.literal("grid"),
      v.literal("speaker"),
      v.literal("sidebar"),
      v.literal("floating")
    )),
    recordingSettings: v.optional(v.object({
      autoRecord: v.boolean(),
      recordAudio: v.boolean(),
      recordVideo: v.boolean(),
      recordScreen: v.boolean(),
    })),
  }).index("by_name", ["name"]).index("by_host", ["hostId"]),
  
  streams: defineTable({
    title: v.string(),
    description: v.string(),
    hostId: v.string(),
    isLive: v.boolean(),
    isChatEnabled: v.boolean(),
    isChatDelayed: v.boolean(),
    isChatFollowersOnly: v.boolean(),
    maxParticipants: v.optional(v.number()),
    streamKey: v.optional(v.string()),
  }).index("by_host", ["hostId"]),
  
  users: defineTable({
    userId: v.string(),
    globalRole: v.union(v.literal("master"), v.literal("admin"), v.literal("user")),
    username: v.string(),
    email: v.string(),
    isBanned: v.boolean(),
  }).index("by_user_id", ["userId"]),
  
  streamParticipants: defineTable({
    streamId: v.id("streams"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
    joinedAt: v.number(),
    isActive: v.boolean(),
  })
    .index("by_stream", ["streamId"])
    .index("by_user_stream", ["userId", "streamId"]),
  
  moderationLogs: defineTable({
    streamId: v.id("streams"),
    moderatorId: v.string(),
    targetUserId: v.string(),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("timeout"),
      v.literal("kick"),
      v.literal("ban")
    ),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()),
    timestamp: v.number(),
  })
    .index("by_stream", ["streamId"])
    .index("by_target", ["targetUserId"]),

  roomParticipants: defineTable({
    roomId: v.id("rooms"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("speaker"),
      v.literal("attendee")
    ),
    permissions: v.object({
      canSpeak: v.boolean(),
      canVideo: v.boolean(),
      canScreenShare: v.boolean(),
      canChat: v.boolean(),
      canInvite: v.boolean(),
      canMute: v.boolean(),
      canKick: v.boolean(),
    }),
    status: v.union(
      v.literal("waiting"),
      v.literal("approved"),
      v.literal("denied"),
      v.literal("active"),
      v.literal("muted"),
      v.literal("kicked")
    ),
    handRaised: v.boolean(),
    joinedAt: v.number(),
    lastSeen: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_user_room", ["userId", "roomId"]),

  roomActions: defineTable({
    roomId: v.id("rooms"),
    performedBy: v.string(),
    targetUserId: v.optional(v.string()),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("muteAll"),
      v.literal("unmuteAll"),
      v.literal("kick"),
      v.literal("promote"),
      v.literal("demote"),
      v.literal("allowSpeak"),
      v.literal("denySpeak"),
      v.literal("startRecording"),
      v.literal("stopRecording"),
      v.literal("changeLayout"),
      v.literal("lockRoom"),
      v.literal("unlockRoom")
    ),
    metadata: v.optional(v.any()),
    timestamp: v.number(),
  }).index("by_room", ["roomId"]),
});