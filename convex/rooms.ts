import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const create = mutation({
  args: {
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.boolean(),
      videoOnEntry: v.boolean(),
      recordingEnabled: v.boolean(),
      waitingRoom: v.boolean(),
      moderatorApproval: v.boolean(),
      handRaiseEnabled: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    // Check if room with this name already exists
    const existingRoom = await ctx.db
      .query("rooms")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    if (existingRoom) {
      return existingRoom._id;
    }

    // Create new room
    const roomId = await ctx.db.insert("rooms", {
      name: args.name,
      title: args.title,
      description: args.description,
      hostId: identity.subject,
      isActive: true,
      maxParticipants: 50,
      layout: "grid",
      settings: args.settings || {
        allowScreenShare: true,
        allowChat: true,
        requireAuth: false,
        muteOnEntry: false,
        videoOnEntry: true,
        recordingEnabled: false,
        waitingRoom: false,
        moderatorApproval: false,
        handRaiseEnabled: true,
      },
      recordingSettings: {
        autoRecord: false,
        recordAudio: true,
        recordVideo: true,
        recordScreen: false,
      },
    });

    return roomId;
  },
});

export const getRoom = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    const room = await ctx.db
      .query("rooms")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    return room;
  },
});

export const getMyRooms = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const rooms = await ctx.db
      .query("rooms")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .order("desc")
      .take(10);

    return rooms;
  },
});

export const updateRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    if (room.hostId !== identity.subject) {
      throw new Error("Not authorized to update this room");
    }

    const updateData: any = {};
    if (args.title !== undefined) updateData.title = args.title;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.isActive !== undefined) updateData.isActive = args.isActive;
    if (args.settings !== undefined) updateData.settings = args.settings;

    await ctx.db.patch(args.roomId, updateData);
    return room;
  },
});

export const deleteRoom = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    if (room.hostId !== identity.subject) {
      throw new Error("Not authorized to delete this room");
    }

    await ctx.db.delete(args.roomId);
    return { success: true };
  },
});

export const updateRoomSettings = mutation({
  args: {
    roomId: v.id("rooms"),
    settings: v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.boolean(),
      videoOnEntry: v.boolean(),
      recordingEnabled: v.boolean(),
      waitingRoom: v.boolean(),
      moderatorApproval: v.boolean(),
      handRaiseEnabled: v.boolean(),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    await ctx.db.patch(args.roomId, { settings: args.settings });
    
    // Log the action
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      action: "changeLayout", // This should be "changeSettings" but keeping consistent with schema
      metadata: args.settings,
      timestamp: Date.now(),
    });
  },
});

export const muteParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
    duration: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    // Update participant status
    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { status: "muted" });
    }
    
    // Log the action
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      targetUserId: args.userId,
      action: "mute",
      metadata: { duration: args.duration },
      timestamp: Date.now(),
    });
  },
});

export const muteAll = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    // Get all active participants except hosts
    const participants = await ctx.db.query("roomParticipants")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.neq(q.field("role"), "host"))
      .collect();
    
    // Mute all participants
    await Promise.all(
      participants.map(p => 
        ctx.db.patch(p._id, { status: "muted" })
      )
    );
    
    // Log the action
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      action: "muteAll",
      timestamp: Date.now(),
    });
  },
});

export const changeLayout = mutation({
  args: {
    roomId: v.id("rooms"),
    layout: v.union(v.literal("grid"), v.literal("speaker"), v.literal("sidebar"), v.literal("floating")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    await ctx.db.patch(args.roomId, { layout: args.layout });
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      action: "changeLayout",
      metadata: { layout: args.layout },
      timestamp: Date.now(),
    });
  },
});

export const toggleRecording = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) throw new Error("Room not found");
    
    const newRecordingState = !room.recordingSettings?.autoRecord;
    
    await ctx.db.patch(args.roomId, {
      recordingSettings: {
        ...room.recordingSettings,
        autoRecord: newRecordingState,
      },
    });
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      action: newRecordingState ? "startRecording" : "stopRecording",
      timestamp: Date.now(),
    });
  },
});

export const toggleRoomLock = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) throw new Error("Room not found");
    
    const newLockState = !room.settings?.requireAuth;
    
    await ctx.db.patch(args.roomId, {
      settings: {
        ...room.settings,
        requireAuth: newLockState,
      },
    });
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      action: newLockState ? "lockRoom" : "unlockRoom",
      timestamp: Date.now(),
    });
  },
});

export const kickParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { status: "kicked" });
    }
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      targetUserId: args.userId,
      action: "kick",
      timestamp: Date.now(),
    });
  },
});

export const promoteParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("speaker"),
      v.literal("attendee")
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, { role: args.role });
    }
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      targetUserId: args.userId,
      action: "promote",
      metadata: { newRole: args.role },
      timestamp: Date.now(),
    });
  },
});

export const approveHandRaise = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const participant = await ctx.db.query("roomParticipants")
      .withIndex("by_user_room", (q) => q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();
    
    if (participant) {
      await ctx.db.patch(participant._id, {
        handRaised: false,
        permissions: {
          ...participant.permissions,
          canSpeak: true,
          canVideo: true,
        },
      });
    }
    
    await ctx.db.insert("roomActions", {
      roomId: args.roomId,
      performedBy: identity.subject,
      targetUserId: args.userId,
      action: "allowSpeak",
      timestamp: Date.now(),
    });
  },
});

export const getRoomAnalytics = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return {
        totalRooms: 0,
        activeRooms: 0,
        totalParticipants: 0,
        growth: "0%",
      };
    }

    const rooms = await ctx.db
      .query("rooms")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .collect();

    const activeRooms = rooms.filter(room => room.isActive).length;

    return {
      totalRooms: rooms.length,
      activeRooms,
      totalParticipants: 0, // This would need to be calculated from participant data
      growth: "0%", // This would need historical data
    };
  },
});